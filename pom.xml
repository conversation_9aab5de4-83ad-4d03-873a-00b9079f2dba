<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>pp-parent-pom</artifactId>
        <groupId>com.phonepe</groupId>
        <version>0.115</version>
    </parent>

    <groupId>com.phonepe.verified</groupId>
    <artifactId>kaizen</artifactId>
    <version>0.0.6-stratos-SNAPSHOT</version>

    <packaging>pom</packaging>

    <modules>
        <module>kaizen-models</module>
        <module>kaizen-core</module>
    </modules>

    <properties>
        <!-- Maven Config -->
        <maven.release.scm.main.branch>main</maven.release.scm.main.branch>
        <aerospike.client.version>6.3.0</aerospike.client.version>
        <apache.httpclient.version>4.5.13</apache.httpclient.version>
        <api.metrics.version>0.0.11</api.metrics.version>
        <asm.version>9.2</asm.version>
        <aspectj.version>1.9.8</aspectj.version>
        <awaitility.version>3.1.6</awaitility.version>
        <spotify.docker.plugin.version>1.2.2</spotify.docker.plugin.version>

        <!-- Dropwizard 2.x Bundles -->
        <cglib.version>3.3.0</cglib.version>
        <clockwork.version>2.3.14</clockwork.version>
        <commons.lang3.version>3.12.0</commons.lang3.version>
        <curator.version>4.2.0</curator.version>
        <data.provider.version>2.15</data.provider.version>
        <docstore-client.version>2.9</docstore-client.version>
        <dropwizard.aerospike.version>3.0.5</dropwizard.aerospike.version>
        <dropwizard.db.sharding.bundle.version>2.0.28-9
        </dropwizard.db.sharding.bundle.version>
        <dropwizard.event.ingestion.version>2.1.0-4</dropwizard.event.ingestion.version>
        <dropwizard.oor.bundle.version>2.0.18-1</dropwizard.oor.bundle.version>
        <dropwizard.metric.bundle.version>1.80</dropwizard.metric.bundle.version>
        <ranger.discovery.version>1.0-RC18</ranger.discovery.version>
        <dropwizard.swagger.version>2.0.28-1</dropwizard.swagger.version>
        <dropwizard.rabbitmq.version>2.0.28-2</dropwizard.rabbitmq.version>
        <dropwizard.request.info.version>2.0.23-23</dropwizard.request.info.version>

        <!-- Libraries -->
        <columbus.version>1.0.4</columbus.version>
        <dropwizard.version>2.0.28</dropwizard.version>
        <dropwizard.primer.version>1.0.1</dropwizard.primer.version>
        <fuction.metrics.version>1.0.12</fuction.metrics.version>
        <gandalf.client.version>2.0.40</gandalf.client.version>
        <olympus-im-client.version>1.1.100</olympus-im-client.version>
        <graphviz.version>0.18.1</graphviz.version>
        <guava.version>32.1.2-jre</guava.version>
        <guice.version>5.3.0</guice.version>
        <hibernate.validator.version>6.2.0.Final</hibernate.validator.version>
        <hibernate.version>5.6.4.Final</hibernate.version>
        <http.client.version>4.0.39</http.client.version>
        <commons.jcs.version>2.2.2</commons.jcs.version>
        <hystrix.dropwizard.version>1.0.2</hystrix.dropwizard.version>
        <hystrix.function.wrapper.version>1.0.0</hystrix.function.wrapper.version>
        <hystrix.configurator.version>0.0.8</hystrix.configurator.version>
        <hystrix.version>1.5.18</hystrix.version>
        <instrumented.okhttp.version>0.5.5-PP</instrumented.okhttp.version>
        <jackson.version>2.13.1</jackson.version>
        <javassist.version>3.28.0-GA</javassist.version>
        <junit.jupiter.version>5.8.2</junit.jupiter.version>
        <lombok.maven.version>1.18.20.0</lombok.maven.version>
        <lombok.version>1.18.30</lombok.version>
        <mariadb.client.version>2.7.2</mariadb.client.version>
        <api.killer.version>1.26</api.killer.version>
        <killswitch.version>2.0.7</killswitch.version>

        <maven.compiler.release>17</maven.compiler.release>
        <maven.compiler.models.release>8</maven.compiler.models.release>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.compliance.level>17</maven.compiler.compliance.level>
        <maven.compiler.version>3.9.0</maven.compiler.version>
        <maven.shade.version>3.2.4</maven.shade.version>
        <aspectj.maven.plugin.version>1.14.0</aspectj.maven.plugin.version>
        <metrics.version>4.0.5</metrics.version>
        <hope.version>2.0.3</hope.version>
        <handlebars.version>4.3.0</handlebars.version>

        <!-- Test Libraries -->
        <mockito.version>4.5.1</mockito.version>
        <okhttp3.version>4.11.0-PPE</okhttp3.version>
        <openfeign.version>11.8</openfeign.version>
        <feign.ranger.version>0.1.8</feign.ranger.version>
        <hamcrest.version>1.3</hamcrest.version>
        <junit.testcontainers.version>1.0.14</junit.testcontainers.version>
        <testcontainers.version>1.17.1</testcontainers.version>

        <!-- PhonePe Bundles -->
        <payment.model.version>1.1.32</payment.model.version>
        <zeus.version>2.0.61</zeus.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <rabbitmq.client.version>5.12.0</rabbitmq.client.version>
        <reflections.version>0.9.11</reflections.version>
        <rosey.dropwizard.config>2.0.86</rosey.dropwizard.config>
        <shadow.v2.version>1.0.104</shadow.v2.version>

        <maven.javadoc.skip>true</maven.javadoc.skip>
        <maven.deploy.skip>false</maven.deploy.skip>

        <spring.statemachine.version>2.4.0</spring.statemachine.version>
        <spring.data.version>2.7.5</spring.data.version>
        <spring.version>5.3.3</spring.version>
        <validation.api.version>2.0.1.Final</validation.api.version>
        <validation.bundle.version>********</validation.bundle.version>
        <vision.version>2.1.106</vision.version>
        <drishti.version>1.0.8</drishti.version>
        <heimdall.version>1.0.73</heimdall.version>
        <sentinel.version>1.161</sentinel.version>
        <atlas.version>2.1.148</atlas.version>
        <hawkeye.version>1.2.244</hawkeye.version>
        <wiremock.version>2.24.1</wiremock.version>
        <assertj-core.version>3.23.1</assertj-core.version>
        <zookeeper.version>3.4.6</zookeeper.version>
        <userservice.model.version>2.0.221</userservice.model.version>
        <db.sandbox.bundle.version>1.0.3</db.sandbox.bundle.version>


        <!-- Sonar Settings -->
        <sonar.coverage.exclusions>
            **/*
        </sonar.coverage.exclusions>

        <sonar.cpd.exclusions>
            **com/phonepe/verified/kaizen/storage/mariadb/entities/*,
            **com/phonepe/verified/kaizen/storage/mariadb/entities/converters/*,
            **com/phonepe/verified/kaizen/storage/mariadb/entities/metadatas/*,
            **com/phonepe/verified/kaizen/storage/mariadb/entities/metadatas/ocr/*,
            **com/phonepe/verified/kaizen/storage/mariadb/entities/metadatas/sourcedocument/*,
            **com/phonepe/verified/kaizen/storage/mariadb/entities/profile/*,
            **com/phonepe/verified/kaizen/storage/mariadb/entities/session/*,
            **com/phonepe/verified/kaizen/storage/mariadb/repositories/*,
            **/resources/**/*,
            **com/phonepe/verified/kaizen/services/visitors/*
        </sonar.cpd.exclusions>

        <sonar.issue.ignore.multicriteria>e1, e2, e3</sonar.issue.ignore.multicriteria>
        <sonar.issue.ignore.multicriteria.e1.ruleKey>
            java:S1135
        </sonar.issue.ignore.multicriteria.e1.ruleKey>
        <sonar.issue.ignore.multicriteria.e2.ruleKey>
            java:S107
        </sonar.issue.ignore.multicriteria.e2.ruleKey>
        <sonar.issue.ignore.multicriteria.e3.ruleKey>
            java:S119
        </sonar.issue.ignore.multicriteria.e3.ruleKey>
        <sonar.issue.ignore.multicriteria.e1.resourceKey>
            **/*.java
        </sonar.issue.ignore.multicriteria.e1.resourceKey>
        <sonar.issue.ignore.multicriteria.e2.resourceKey>
            **/*.java
        </sonar.issue.ignore.multicriteria.e2.resourceKey>
        <sonar.issue.ignore.multicriteria.e3.resourceKey>
            **/*.java
        </sonar.issue.ignore.multicriteria.e3.resourceKey>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <artifactId>reflections</artifactId>
                <groupId>org.reflections</groupId>
                <version>${reflections.version}</version>
            </dependency>
            <dependency>
                <artifactId>javassist</artifactId>
                <groupId>org.javassist</groupId>
                <version>${javassist.version}</version>
            </dependency>
            <dependency>
                <artifactId>cglib-nodep</artifactId>
                <groupId>cglib</groupId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <artifactId>asm</artifactId>
                <groupId>org.ow2.asm</groupId>
                <version>${asm.version}</version>
            </dependency>

            <dependency>
                <artifactId>lombok</artifactId>
                <groupId>org.projectlombok</groupId>
                <scope>provided</scope>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <artifactId>jackson-databind</artifactId>
                <groupId>com.fasterxml.jackson.core</groupId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <artifactId>guava</artifactId>
                <groupId>com.google.guava</groupId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <artifactId>dropwizard-guicey</artifactId>
                <groupId>ru.vyarus</groupId>
                <version>${guice.version}</version>
            </dependency>
            <dependency>
                <artifactId>dropwizard-core</artifactId>
                <groupId>io.dropwizard</groupId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <artifactId>dropwizard-metrics</artifactId>
                <groupId>io.dropwizard</groupId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <artifactId>dropwizard-jackson</artifactId>
                <groupId>io.dropwizard</groupId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <artifactId>dropwizard-validation</artifactId>
                <groupId>io.dropwizard</groupId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <artifactId>dropwizard-forms</artifactId>
                <groupId>io.dropwizard</groupId>
                <version>${dropwizard.version}</version>
            </dependency>
            <dependency>
                <artifactId>hystrix-core</artifactId>
                <groupId>com.netflix.hystrix</groupId>
                <version>${hystrix.version}</version>
            </dependency>
            <dependency>
                <artifactId>okhttp</artifactId>
                <groupId>com.squareup.okhttp3</groupId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <artifactId>hystrix-function-wrapper</artifactId>
                <groupId>io.appform.core</groupId>
                <version>${hystrix.function.wrapper.version}</version>
            </dependency>
            <dependency>
                <artifactId>zookeeper</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
                <groupId>org.apache.zookeeper</groupId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <artifactId>curator-framework</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>zookeeper</artifactId>
                        <groupId>org.apache.zookeeper</groupId>
                    </exclusion>
                </exclusions>
                <groupId>org.apache.curator</groupId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <artifactId>hibernate-core</artifactId>
                <groupId>org.hibernate</groupId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <artifactId>dropwizard-rabbitmq-actors</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-dataformat-yaml</artifactId>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                    </exclusion>
                </exclusions>
                <groupId>io.appform.dropwizard.actors</groupId>
                <version>${dropwizard.rabbitmq.version}</version>
            </dependency>
            <dependency>
                <artifactId>commons-lang3</artifactId>
                <groupId>org.apache.commons</groupId>
                <version>${commons.lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>feign.ranger</groupId>
                <artifactId>feign-ranger</artifactId>
                <version>${feign.ranger.version}</version>
            </dependency>

            <dependency>
                <artifactId>metrics-core</artifactId>
                <groupId>io.dropwizard</groupId>
                <version>${metrics.version}</version>
            </dependency>

            <dependency>
                <artifactId>metrics-okhttp</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>metrics-core</artifactId>
                        <groupId>io.dropwizard.metrics</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>okhttp</artifactId>
                        <groupId>com.squareup.okhttp3</groupId>
                    </exclusion>
                </exclusions>
                <groupId>com.raskasa.metrics</groupId>
                <version>${instrumented.okhttp.version}</version>
            </dependency>

            <dependency>
                <artifactId>httpclient</artifactId>
                <groupId>org.apache.httpcomponents</groupId>
                <version>${apache.httpclient.version}</version>
            </dependency>

            <dependency>
                <artifactId>hibernate-validator</artifactId>
                <groupId>org.hibernate.validator</groupId>
                <version>${hibernate.validator.version}</version>
            </dependency>

            <dependency>
                <artifactId>aerospike-client</artifactId>
                <groupId>com.aerospike</groupId>
                <version>${aerospike.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform</groupId>
                <artifactId>zeus-models</artifactId>
                <version>${zeus.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
            <scope>provided</scope>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <artifactId>dropwizard-testing</artifactId>
            <groupId>io.dropwizard</groupId>
            <scope>test</scope>
            <version>${dropwizard.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit.jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <artifactId>mockito-junit-jupiter</artifactId>
            <groupId>org.mockito</groupId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <artifactId>mockito-core</artifactId>
            <groupId>org.mockito</groupId>
            <scope>test</scope>
            <version>${mockito.version}</version>
        </dependency>
        <dependency>
            <artifactId>mockito-inline</artifactId>
            <groupId>org.mockito</groupId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <artifactId>junit-jupiter-engine</artifactId>
            <groupId>org.junit.jupiter</groupId>
            <version>${junit.jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj-core.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <release>${maven.compiler.release}</release>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                    <generatedSourcesDirectory>${project.build.directory}/generated-sources/
                    </generatedSourcesDirectory>
                </configuration>
                <dependencies>
                    <dependency>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                        <version>${asm.version}</version>
                    </dependency>
                </dependencies>
                <groupId>org.apache.maven.plugins</groupId>
                <version>${maven.compiler.version}</version>
            </plugin>
        </plugins>
    </build>
</project>
