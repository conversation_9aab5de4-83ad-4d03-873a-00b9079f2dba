variables:
  STAGE_BRANCH: stage

.default: &default
  only:
    - merge_requests
    - web

.default_tag: &default_tag
  tags:
    - backend-docker-large

image: docker.phonepe.com/ci/ubuntu/jammy/jdk/openjdk/17/maven/3.9.9:latest

stages:
  - build
  - quality
  - deploy
  - merge
  - release

build_package:
  stage: build
  <<: *default
  <<: *default_tag
  when: manual
  allow_failure: false
  script:
    - mvn clean package -U -DskipTests=true -Dmaven.install.skip=true
  except:
    refs:
      - master
      - develop
      - stage

merge_ready:
  stage: quality
  <<: *default_tag
  script:
    - export DC_ID="nb6"
    - export MAVEN_OPTS="--add-opens java.base/java.lang=ALL-UNNAMED -Xms3g -Xmx3g"
    - mvn -U clean install -Pquality_check -Dproject.version='CI-TEST-${CI_JOB_ID}-SNAPSHOT' -Dsonar.pullrequest.key=$CI_MERGE_REQUEST_IID -Dsonar.pullrequest.branch=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME -Dsonar.pullrequest.base=$CI_MERGE_REQUEST_TARGET_BRANCH_NAME
  artifacts:
    reports:
      junit:
        - kaizen-core/target/surefire-reports/TEST-*.xml
  only:
    refs:
      - merge_requests
    variables:
      - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
  except:
    refs:
      - master
      - develop

deploy_feature_snapshot:
  stage: deploy
  <<: *default
  <<: *default_tag
  when: manual
  allow_failure: false
  script:
    - export MAVEN_OPTS="-Xms3g -Xmx3g"
    - "VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)"
    - "BRANCH_NAME=${CI_COMMIT_BRANCH/feature\\//}-SNAPSHOT"
    - "mvn versions:set -DgenerateBackupPoms=false -DnewVersion=${VERSION/SNAPSHOT/$BRANCH_NAME}"
    - mvn -U clean package deploy -DskipTests=true -Dmaven.install.skip=true
  only:
    refs:
      - merge_requests
      - web
    variables:
      - $CI_COMMIT_BRANCH =~ /^feature.*$/i

deploy_snapshot:
  stage: deploy
  <<: *default
  <<: *default_tag
  when: manual
  allow_failure: false
  script:
    - export MAVEN_OPTS="-Xms3g -Xmx3g"
    - mvn -U clean package deploy -DskipTests=true -Dmaven.install.skip=true
  only:
    refs:
      - merge_requests
      - web
  except:
    refs:
      - master
      - develop
      - stage
    variables:
      - $CI_COMMIT_BRANCH =~ /^feature.*$/i

build_and_deploy_stage_snapshot:
  stage: deploy
  <<: *default_tag
  script:
    - export MAVEN_OPTS="-Xms3g -Xmx3g"
    - "VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)"
    - "mvn versions:set -DgenerateBackupPoms=false -DnewVersion=${VERSION/SNAPSHOT/STAGE-SNAPSHOT}"
    - mvn -U clean package deploy -DskipTests=true -Dmaven.install.skip=true
  only:
    refs:
      - stage

stage_merge_status_check:
  stage: merge
  <<: *default_tag
  when: manual
  allow_failure: false
  script:
    - git merge-base --is-ancestor ${CI_COMMIT_SHA} origin/${STAGE_BRANCH}
  only:
    refs:
      - merge_requests
    variables:
      - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
  except:
    refs:
      - master
      - develop

merge:
  stage: merge
  <<: *default_tag
  script:
    - export DC_ID="nb6"
    - export MAVEN_OPTS="--add-opens java.base/java.lang=ALL-UNNAMED -Xms3g -Xmx3g"
    - mvn clean install -U -Pquality_check
  only:
    refs:
      - develop

release:
  <<: *default_tag
  stage: release
  script:
    - echo "test" | gpg --clearsign
    - export DC_ID="nb6"
    - bash build-scripts/release.sh PVER-2241
  when: manual
  only:
    refs:
      - develop