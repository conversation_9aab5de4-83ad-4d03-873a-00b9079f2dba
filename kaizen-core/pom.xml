<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.phonepe.verified</groupId>
        <artifactId>kaizen</artifactId>
        <version>0.0.6-stratos-SNAPSHOT</version>
    </parent>

    <artifactId>kaizen-core</artifactId>

    <dependencies>
        <dependency>
            <artifactId>kaizen-models</artifactId>
            <groupId>com.phonepe.verified</groupId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jcs-core</artifactId>
            <version>${commons.jcs.version}</version>
        </dependency>

        <dependency>
            <artifactId>http-client-all</artifactId>
            <groupId>com.phonepe.platform.http.v2</groupId>
            <version>${http.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.appform.dropwizard.discovery</groupId>
                    <artifactId>dropwizard-service-discovery</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.appform.ranger</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Basics -->
        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
        </dependency>
        <dependency>
            <artifactId>guava</artifactId>
            <groupId>com.google.guava</groupId>
        </dependency>
        <dependency>
            <artifactId>reflections</artifactId>
            <groupId>org.reflections</groupId>
        </dependency>
        <dependency>
            <artifactId>jackson-databind</artifactId>
            <groupId>com.fasterxml.jackson.core</groupId>
        </dependency>

        <!-- Dropwizard -->
        <dependency>
            <artifactId>dropwizard-primer-bundle</artifactId>
            <groupId>com.phonepe.platform</groupId>
            <version>${dropwizard.primer.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>feign.ranger</groupId>
                    <artifactId>feign-ranger</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>dropwizard-jackson</artifactId>
            <groupId>io.dropwizard</groupId>
        </dependency>
        <dependency>
            <artifactId>dropwizard-core</artifactId>
            <groupId>io.dropwizard</groupId>
        </dependency>
        <dependency>
            <artifactId>dropwizard-validation</artifactId>
            <groupId>io.dropwizard</groupId>
        </dependency>
        <dependency>
            <artifactId>dropwizard-metrics</artifactId>
            <groupId>io.dropwizard</groupId>
        </dependency>
        <dependency>
            <artifactId>dropwizard-forms</artifactId>
            <groupId>io.dropwizard</groupId>
        </dependency>

        <!-- Dropwizard Bundles -->
        <dependency>
            <artifactId>dropwizard-guicey</artifactId>
            <groupId>ru.vyarus</groupId>
        </dependency>

        <dependency>
            <groupId>io.appform.ranger</groupId>
            <artifactId>ranger-discovery-bundle</artifactId>
            <version>${ranger.discovery.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>requestinfo-bundle</artifactId>
            <groupId>com.phonepe.platform</groupId>
            <version>${dropwizard.request.info.version}</version>
        </dependency>
        <dependency>
            <artifactId>requestinfo-models</artifactId>
            <groupId>com.phonepe.platform</groupId>
            <version>${dropwizard.request.info.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>metric-ingestion-bundle</artifactId>
            <version>${dropwizard.metric.bundle.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                    <artifactId>http-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>feign.ranger</groupId>
                    <artifactId>feign-ranger</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>dropwizard-oor</artifactId>
            <groupId>io.raven.dropwizard</groupId>
            <version>${dropwizard.oor.bundle.version}</version>
        </dependency>

        <dependency>
            <artifactId>dropwizard-hibernate</artifactId>
            <groupId>io.dropwizard</groupId>
            <version>${dropwizard.version}</version>
        </dependency>
        <dependency>
            <artifactId>hibernate-envers</artifactId>
            <groupId>org.hibernate</groupId>
            <version>${hibernate.version}</version>
        </dependency>
        <dependency>
            <artifactId>tika-core</artifactId>
            <groupId>org.apache.tika</groupId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <artifactId>db-sharding-bundle</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>reflections</artifactId>
                    <groupId>org.reflections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>io.dropwizard</groupId>
                </exclusion>
            </exclusions>
            <groupId>io.appform.dropwizard.sharding</groupId>
            <version>${dropwizard.db.sharding.bundle.version}</version>
        </dependency>

        <dependency>
            <artifactId>rosey-data-provider-bundle</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.glassfish.jersey.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.glassfish.jersey.inject</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform.http</groupId>
                    <artifactId>http-client</artifactId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.data.provider</groupId>
            <version>${data.provider.version}</version>
        </dependency>

        <dependency>
            <groupId>io.appform.hope</groupId>
            <artifactId>hope-lang</artifactId>
            <version>${hope.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.jknack</groupId>
            <artifactId>handlebars</artifactId>
            <version>${handlebars.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.jknack</groupId>
            <artifactId>handlebars-jackson2</artifactId>
            <version>${handlebars.version}</version>
        </dependency>

        <!-- Queue -->
        <dependency>
            <artifactId>dropwizard-rabbitmq-actors</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-dataformat-yaml</artifactId>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                </exclusion>
            </exclusions>
            <groupId>io.appform.dropwizard.actors</groupId>
            <version>${dropwizard.rabbitmq.version}</version>
        </dependency>
        <dependency>
            <artifactId>amqp-client</artifactId>
            <groupId>com.rabbitmq</groupId>
            <version>${rabbitmq.client.version}</version>
        </dependency>


        <!-- Hystrix -->
        <dependency>
            <artifactId>hystrix-core</artifactId>
            <groupId>com.netflix.hystrix</groupId>
        </dependency>
        <dependency>
            <groupId>com.hystrix</groupId>
            <artifactId>hystrix-configurator</artifactId>
            <version>${hystrix.configurator.version}</version>
        </dependency>
        <dependency>
            <artifactId>hystrix-function-wrapper</artifactId>
            <groupId>io.appform.core</groupId>
        </dependency>
        <dependency>
            <artifactId>hystrix-dropwizard-bundle</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dropwizard-core</artifactId>
                    <groupId>io.dropwizard</groupId>
                </exclusion>
            </exclusions>
            <groupId>org.zapodot</groupId>
            <version>${hystrix.dropwizard.version}</version>
        </dependency>

        <!--PhonePe Dependencies-->
        <dependency>
            <artifactId>docstore-client</artifactId>
            <groupId>com.phonepe.platform.docstore</groupId>
            <exclusions>
                <exclusion>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                    <artifactId>http-client-all</artifactId>
                </exclusion>
            </exclusions>
            <version>${docstore-client.version}</version>
        </dependency>
        <dependency>
            <artifactId>olympus-im-client</artifactId>
            <groupId>com.phonepe.olympus-im</groupId>
            <version>${olympus-im-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                    <artifactId>http-client-all</artifactId>
                </exclusion>

                <exclusion>
                    <artifactId>dropwizard-primer</artifactId>
                    <groupId>io.raven.dropwizard</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>gandalf-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                    <artifactId>http-client-all</artifactId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.gandalf</groupId>
            <version>${gandalf.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.verified</groupId>
            <artifactId>oncall-dropwizard-bundle</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <artifactId>vision-models</artifactId>
            <groupId>com.phonepe.platform</groupId>
            <version>${vision.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.shadow</groupId>
            <artifactId>shadow-v2-services</artifactId>
            <version>${shadow.v2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.phonepe.shadow</groupId>
            <artifactId>shadow-v2-core</artifactId>
            <version>${shadow.v2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>drishti-models</artifactId>
            <groupId>com.phonepe.verified</groupId>
            <version>${drishti.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>heimdall-models</artifactId>
            <groupId>com.phonepe.platform</groupId>
            <version>${heimdall.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>sentinel-client</artifactId>
            <version>${sentinel.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform.http</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform.filters</groupId>
            <artifactId>api-killer-core</artifactId>
            <version>${api.killer.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform.filters</groupId>
            <artifactId>api-killer-killswitch</artifactId>
            <version>${api.killer.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform.killswitch</groupId>
            <artifactId>killswitch-client</artifactId>
            <version>${killswitch.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.dropwizard</groupId>
                    <artifactId>dropwizard-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.ben-manes.caffeine</groupId>
                    <artifactId>caffeine</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>columbus-core-models</artifactId>
            <version>${columbus.version}</version>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>atlas-model</artifactId>
            <version>${atlas.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>clockwork-models</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.platform</groupId>
            <version>${clockwork.version}</version>
        </dependency>

        <dependency>
            <artifactId>rosey-dropwizard-config</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform.http</groupId>
                    <artifactId>http-client</artifactId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.platform</groupId>
            <version>${rosey.dropwizard.config}</version>
        </dependency>
        <dependency>
            <artifactId>event-ingestion-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.raskasa.metrics</groupId>
                    <artifactId>metrics-okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.models</groupId>
                    <artifactId>phonepe-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-framework</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-recipes</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <groupId>io.appform.dropwizard.discovery</groupId>
                    <artifactId>dropwizard-service-discovery-bundle</artifactId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.dataplatform</groupId>
            <version>${dropwizard.event.ingestion.version}</version>
        </dependency>

        <dependency>
            <artifactId>api-metrics</artifactId>
            <groupId>com.phonepe.platform.http.server.metrics</groupId>
            <version>${api.metrics.version}</version>
        </dependency>

        <dependency>
            <artifactId>executors</artifactId>
            <groupId>com.phonepe.platform</groupId>
            <version>0.0.5</version>
        </dependency>

        <!-- Libs -->
        <dependency>
            <artifactId>validation-bundle</artifactId>
            <groupId>com.platform</groupId>
            <version>${validation.bundle.version}</version>
        </dependency>
        <dependency>
            <artifactId>aspectjrt</artifactId>
            <groupId>org.aspectj</groupId>
            <version>${aspectj.version}</version>
        </dependency>
        <dependency>
            <artifactId>function-metrics</artifactId>
            <groupId>io.appform.functionmetrics</groupId>
            <version>${fuction.metrics.version}</version>
        </dependency>
        <dependency>
            <artifactId>zookeeper</artifactId>
            <groupId>org.apache.zookeeper</groupId>
        </dependency>
        <dependency>
            <artifactId>mariadb-java-client</artifactId>
            <groupId>org.mariadb.jdbc</groupId>
            <version>${mariadb.client.version}</version>
        </dependency>

        <dependency>
            <artifactId>spring-statemachine-core</artifactId>
            <groupId>org.springframework.statemachine</groupId>
            <version>${spring.statemachine.version}</version>
        </dependency>
        <dependency>
            <artifactId>spring-context</artifactId>
            <groupId>org.springframework</groupId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <artifactId>spring-data-commons</artifactId>
            <groupId>org.springframework.data</groupId>
            <version>${spring.data.version}</version>
        </dependency>

        <dependency>
            <artifactId>jackson-dataformat-csv</artifactId>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <artifactId>feign-core</artifactId>
            <groupId>io.github.openfeign</groupId>
            <version>${openfeign.version}</version>
        </dependency>
        <dependency>
            <artifactId>feign-jackson</artifactId>
            <groupId>io.github.openfeign</groupId>
            <version>${openfeign.version}</version>
        </dependency>
        <dependency>
            <artifactId>feign-httpclient</artifactId>
            <groupId>io.github.openfeign</groupId>
            <version>${openfeign.version}</version>
        </dependency>
        <dependency>
            <artifactId>feign-okhttp</artifactId>
            <groupId>io.github.openfeign</groupId>
            <version>${openfeign.version}</version>
        </dependency>

        <dependency>
            <artifactId>graphviz-java</artifactId>
            <groupId>guru.nidi</groupId>
            <version>${graphviz.version}</version>
        </dependency>

        <!-- Aerospike -->
        <dependency>
            <artifactId>aerospike-bundle</artifactId>
            <groupId>com.phonepe.platform</groupId>
            <version>${dropwizard.aerospike.version}</version>
        </dependency>

        <!-- hawkeye client dependency start-->
        <dependency>
            <artifactId>mustang</artifactId>
            <groupId>com.phonepe.growth</groupId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <artifactId>bullhorn-models</artifactId>
            <groupId>com.phonepe.platform.bullhorn</groupId>
            <version>1.0.132</version>
        </dependency>
        <dependency>
            <artifactId>zencast-model</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.growth</groupId>
            <version>1.1.79</version>
        </dependency>
        <dependency>
            <artifactId>campaign-store-models</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.aspectj</groupId>
                    <artifactId>aspectjweaver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform.http.v2</groupId>
                    <artifactId>http-client-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.growth</groupId>
            <version>2.0.29</version>
        </dependency>
        <dependency>
            <artifactId>json-rules</artifactId>
            <groupId>io.appform.rules</groupId>
            <version>1.0.12</version>
        </dependency>

        <dependency>
            <groupId>com.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-pdfbox</artifactId>
            <version>1.0.6</version>
        </dependency>

        <dependency>
            <artifactId>hawkeye-model</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.growth</groupId>
            <version>${hawkeye.version}</version>
        </dependency>
        <dependency>
            <artifactId>hawkeye-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.growth</groupId>
            <version>${hawkeye.version}</version>
        </dependency>
        <dependency>
            <artifactId>hawkeye-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.phonepe.growth</groupId>
            <version>${hawkeye.version}</version>
        </dependency>
        <!-- hawkeye dependency end-->

        <!-- Test -->
        <dependency>
            <artifactId>testcontainers</artifactId>
            <groupId>org.testcontainers</groupId>
            <scope>test</scope>
            <version>${testcontainers.version}</version>
        </dependency>
        <dependency>
            <artifactId>rabbitmq</artifactId>
            <groupId>org.testcontainers</groupId>
            <scope>test</scope>
            <version>${testcontainers.version}</version>
        </dependency>
        <dependency>
            <artifactId>mariadb</artifactId>
            <groupId>org.testcontainers</groupId>
            <scope>test</scope>
            <version>${testcontainers.version}</version>
        </dependency>
        <dependency>
            <groupId>io.appform.testcontainer</groupId>
            <artifactId>junit-testcontainer-mariadb</artifactId>
            <version>${junit.testcontainers.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.appform.testcontainer</groupId>
            <artifactId>junit-testcontainer-aerospike</artifactId>
            <version>${junit.testcontainers.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.appform.testcontainer</groupId>
            <artifactId>junit-testcontainer-rabbitmq</artifactId>
            <version>${junit.testcontainers.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-api</artifactId>
            <version>3.3.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-transport-zerodep</artifactId>
            <version>3.3.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>4.2.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <artifactId>wiremock</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>json-path</artifactId>
                    <groupId>com.jayway.jsonpath</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty-server</artifactId>
                    <groupId>org.eclipse.jetty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty-servlet</artifactId>
                    <groupId>org.eclipse.jetty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty-servlets</artifactId>
                    <groupId>org.eclipse.jetty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty-webapp</artifactId>
                    <groupId>org.eclipse.jetty</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.github.tomakehurst</groupId>
            <version>${wiremock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <artifactId>hamcrest-all</artifactId>
            <groupId>org.hamcrest</groupId>
            <scope>test</scope>
            <version>${hamcrest.version}</version>
        </dependency>
        <dependency>
            <groupId>in.vectorpro.dropwizard</groupId>
            <artifactId>dropwizard-swagger</artifactId>
            <version>${dropwizard.swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>db-sandbox-bundle</artifactId>
            <version>${db.sandbox.bundle.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <release>${maven.compiler.release}</release>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                    <generatedSourcesDirectory>${project.build.directory}/generated-sources/
                    </generatedSourcesDirectory>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm</artifactId>
                        <version>${asm.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
