package com.phonepe.verified.kaizen.services.impl;

import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.platform.atlas.model.pincodev2.PincodeResolution;
import com.phonepe.verified.kaizen.clients.internal.AtlasClient;
import com.phonepe.verified.kaizen.configs.AtlasPincodeSourceConfig;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.common.PincodeDetails;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.PincodeAndStateMatchingService;
import io.dropwizard.util.Strings;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class PincodeAndStateMatchingServiceImpl implements PincodeAndStateMatchingService {

    private final AtlasClient atlasClient;

    private final DataProvider<KaizenConfig> appConfigDataProvider;

    @Override
    public Optional<String> getCityFromPincodeDetails(final String pincode,
                                                      final Set<String> pincodeSources) {

        return getPincodeDetailsFromPincode(pincode, pincodeSources).map(PincodeDetails::getCity);
    }

    @Override
    public Optional<String> getStateFromPincodeDetails(final String pincode,
                                                       final Set<String> pincodeSources) {

        return getPincodeDetailsFromPincode(pincode, pincodeSources).map(PincodeDetails::getState);
    }

    @NonNull
    private List<String> getPincodeSourcesListFromAppConfigDataProvider() {
        return appConfigDataProvider.getData()
                .getAtlasPincodeSourcesList()
                .stream()
                .sorted(Comparator.comparing(AtlasPincodeSourceConfig::getPriority))
                .map(AtlasPincodeSourceConfig::getSource)
                .toList();
    }

    @Override
    public PincodeDetails getPincodeDetailsFromPincodeWithException(final String pincode,
                                                                    final Set<String> pincodeSources) {
        return getPincodeDetailsFromPincode(pincode, pincodeSources).orElseThrow(
                () -> KaizenException.create(KaizenResponseCode.PINCODE_DETAILS_NOT_FOUND, Map.of()));
    }

    @Override
    public Optional<PincodeDetails> getPincodeDetailsFromPincode(final String pincode,
                                                                 final Set<String> pincodeSources) {
        if (Strings.isNullOrEmpty(pincode)) {
            return Optional.empty();
        }

        final var pincodeSourceList = pincodeSources.isEmpty()
                                      ? getPincodeSourcesListFromAppConfigDataProvider()
                                      : pincodeSources;

        final var pincodeResolutionMap = getPincodeResolutionMap(pincode, pincodeSources);

        final var pincodeDetailsResultSet = pincodeSourceList.stream()
                .filter(pincodeResolutionMap::containsKey)
                .map(pincodeResolutionMap::get)
                .map(ps -> PincodeDetails.builder()
                        .state(ps.getState())
                        .city(ps.getCity())
                        .stateCode(ps.getStateCode())
                        .cityCode(ps.getCityCode())
                        .district(ps.getDistrict())
                        .pincodeSourceName(ps.getSource()
                                .getName())
                        .build())
                .collect(Collectors.toSet());

        return pincodeDetailsResultSet.stream()
                .filter(e -> (!Strings.isNullOrEmpty(e.getCity()) && !Strings.isNullOrEmpty(e.getState())))
                .findFirst();
    }

    private Map<String, PincodeResolution> getPincodeResolutionMap(final String pincode,
                                                                   final Set<String> pincodeSourceList) {

        final var pincodeResolutionResponse = atlasClient.fetchPincodeDetails(pincode, List.copyOf(pincodeSourceList));

        if (Objects.isNull(pincodeResolutionResponse) || !pincodeResolutionResponse.isSuccess() || Objects.isNull(
                pincodeResolutionResponse.getData())) {
            return Collections.emptyMap();
        }

        return pincodeResolutionResponse.getData()
                .getPincodeResolutionMap();
    }
}
