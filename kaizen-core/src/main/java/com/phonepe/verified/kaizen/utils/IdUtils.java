package com.phonepe.verified.kaizen.utils;

import com.phonepe.zeus.models.Farm;
import io.appform.ranger.discovery.bundle.id.Id;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.List;
import java.util.function.ToIntFunction;
import lombok.experimental.UtilityClass;

@UtilityClass
public class IdUtils {

    public static final String ALPHABET_ZONE_ID = Farm.valueOf(System.getenv("DC_ID")
                    .toUpperCase())
            .alphabetZoneId();

    public String generateId(final String prefix) {
        return IdGenerator.generate(prefix.concat(ALPHABET_ZONE_ID))
                .getId();
    }

    public String createIdInSameShard(final String prefix,
                                      final String entityId,
                                      final ToIntFunction<String> shardingFunction) {
        return IdGenerator.generateWithConstraints(prefix.concat(ALPHABET_ZONE_ID),
                        List.of(id -> shardingFunction.applyAsInt(id.getId()) == shardingFunction.applyAsInt(entityId)))
                .map(Id::getId)
                .orElse(null);
    }
}
