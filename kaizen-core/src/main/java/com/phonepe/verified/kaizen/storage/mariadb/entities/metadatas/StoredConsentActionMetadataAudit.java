package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadataAudit;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Getter
@Setter
@Entity
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.CONSENT)
public class StoredConsentActionMetadataAudit extends StoredActionMetadataAudit {

    private static final long serialVersionUID = 6730271254841205668L;

    @Column(name = "name", columnDefinition = "varchar(128)")
    private String consentGranted;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String consentDetailsUrl;

    @Column(name = "state", columnDefinition = "varchar(32)")
    private String language;

    @Column(name = "source_type", columnDefinition = "varchar(16)")
    private String sourceType;

    @Column(name = "source_version", columnDefinition = "varchar(16)")
    private String sourceVersion;

    @Column(name = "source_platform", columnDefinition = "varchar(16)")
    private String sourcePlatform;

    @Column(name = "approver_id", columnDefinition = "varchar(45)")
    private String consentGrantedBy;

    @Column(name = "reference_type", columnDefinition = "char(40)")
    private String consentType;

    @Builder

    public StoredConsentActionMetadataAudit(@NonNull final String actionId,
                                            final int revType,
                                            final LocalDateTime createdAt,
                                            final LocalDateTime lastUpdatedAt,
                                            final String consentGranted,
                                            final String consentDetailsUrl,
                                            final String language,
                                            final String sourceType,
                                            final String sourceVersion,
                                            final String sourcePlatform,
                                            final String consentGrantedBy,
                                            final String consentType) {
        super(BuildUtils.auditPrimaryKeyForUpdate(), revType, actionId, ActionMetadataType.CONSENT, createdAt,
                lastUpdatedAt);
        this.consentGranted = consentGranted;
        this.consentDetailsUrl = consentDetailsUrl;
        this.language = language;
        this.sourceType = sourceType;
        this.sourceVersion = sourceVersion;
        this.sourcePlatform = sourcePlatform;
        this.consentGrantedBy = consentGrantedBy;
        this.consentType = consentType;
    }
}
