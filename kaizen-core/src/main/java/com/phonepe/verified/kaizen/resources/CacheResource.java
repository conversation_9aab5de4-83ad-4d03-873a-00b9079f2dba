package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.registries.CacheRegistry;
import com.phonepe.verified.kaizen.services.CacheManagementService;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.annotation.security.RolesAllowed;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/cache")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@Tag(name = "Cache", description = "Cache related APIs")
public class CacheResource {

    private final CacheRegistry cacheRegistry;

    private final CacheManagementService cacheManagementService;

    @GET
    @Path("/invalidate/{cacheName}")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @RolesAllowed(OlympusPermissionNames.CACHE_REFRESH)
    @Operation(summary = "Invalidate cache for given cacheName")
    public void invalidate(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                           @PathParam("cacheName") final CacheName cacheName) {
        cacheRegistry.invalidateCache(cacheName);
    }

    @POST
    @Path("/invalidate/all")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @RolesAllowed(OlympusPermissionNames.CACHE_REFRESH)
    @Operation(summary = "Invalidate cache for given cacheName")
    public void invalidateAll(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                              final List<CacheName> cacheNames) {
        cacheManagementService.invalidateCacheOnAllNodes(cacheNames);
    }

    @POST
    @Path("/profile/invalidate/all")
    @RolesAllowed(OlympusPermissionNames.CACHE_REFRESH)
    @Operation(summary = "Invalidates all PV Profile-related caches")
    public void invalidateProfileCaches(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {

        cacheManagementService.invalidateCacheOnAllNodes(CacheName.PROFILE_RELATED_CACHES);
    }
}