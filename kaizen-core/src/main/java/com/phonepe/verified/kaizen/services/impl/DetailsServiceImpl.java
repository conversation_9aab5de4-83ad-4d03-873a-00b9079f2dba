package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.requests.details.EntityDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.FetchDetailsFromSecondarySources;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetail;
import com.phonepe.verified.kaizen.models.requests.details.WorkflowDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.WorkflowStepsDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.requests.workflow.LatestWorkflowDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.LatestWorkflowRequestV2;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.details.ActionLevelDetails;
import com.phonepe.verified.kaizen.models.responses.details.Detail;
import com.phonepe.verified.kaizen.models.responses.details.EntityDetailsResponse;
import com.phonepe.verified.kaizen.models.responses.details.WorkflowDetailsResponse;
import com.phonepe.verified.kaizen.models.responses.details.WorkflowStepsDetailsResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.ActionFailureResponse;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DetailsService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ManualRetryService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.CalculateWhetherToFetchDetailsFromSecondarySourcesVisitor;
import com.phonepe.verified.kaizen.services.visitors.DetailClassificationKeyDetailVisitor;
import com.phonepe.verified.kaizen.services.visitors.RequiredDetailFallbackVisitorForEntityTypeVisitor;
import com.phonepe.verified.kaizen.services.visitors.StoredActionMetadataToDetailVisitor;
import com.phonepe.verified.kaizen.storage.aerospike.commands.EntityDetailsCommand;
import com.phonepe.verified.kaizen.storage.aerospike.keys.EntityDetailsKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.WorkflowUtils;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class DetailsServiceImpl implements DetailsService {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final ManualRetryService manualRetryService;

    private final DataProvider<KaizenConfig> appConfigDataProvider;

    private final EntityDetailsCommand entityDetailsCommand;

    private final WorkflowStepService workflowStepService;

    private final WorkflowContextStore workflowContextStore;

    private final ActionMetadataService actionMetadataService;

    private final StoredActionMetadataToDetailVisitor storedActionMetadataToDetailVisitor;

    private final RequiredDetailFallbackVisitorForEntityTypeVisitor requiredDetailFallbackVisitorForEntityTypeVisitor;

    @Override
    public WorkflowDetailsResponse getDetails(final WorkflowDetailsRequest workflowDetailsRequest) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowDetailsRequest.getWorkflowId());

        WorkflowUtils.validateEntityAgainstWorkflow(storedWorkflow, workflowDetailsRequest.getEntityId(),
                workflowDetailsRequest.getEntityType());

        final var workflowDetailsResponseList = getWorkflowDetailsResponse(List.of(storedWorkflow),
                workflowDetailsRequest.getRequiredDetails());

        if (workflowDetailsResponseList.isEmpty()) {
            throwRequiredDetailsNotFoundException(workflowDetailsRequest);
        }

        return workflowDetailsResponseList.iterator()
                .next();
    }

    public EntityDetailsResponse getEntityDetailsFromCache(final String workflowId) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var entityDetailsKey = EntityDetailsKey.builder()
                .entityId(storedWorkflow.getEntityId())
                .entityType(storedWorkflow.getEntityType())
                .namespace(profile.getNamespace())
                .organization(profile.getOrganization())
                .type(profile.getType())
//                .requiredDetails(Constants.ENTITY_ALL_REQUIRED_DETAILS)
                .build();

        final var entityDetailsCachedResponse = entityDetailsCommand.get(entityDetailsKey);

        if (Objects.nonNull(entityDetailsCachedResponse)) {
            return entityDetailsCachedResponse;
        }

        final var entityDetailsProfileCriteriaList = appConfigDataProvider.getData()
                .getEntityDetailsConfig()
                .getEntityDetailsProfileCriteriaList(profile.getOrganization(), profile.getNamespace(),
                        profile.getType());

        if (entityDetailsProfileCriteriaList.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.ENTITY_DETAILS_PROFILE_CRITERIA_TYPE_NOT_CONFIGURED,
                    Map.of());
        }

        final var entityDetailsResponse = getEntityDetails(EntityDetailsRequest.builder()
                .entityId(storedWorkflow.getEntityId())
                .entityType(storedWorkflow.getEntityType())
                .profileCriteria(entityDetailsProfileCriteriaList)
                .fetchDetailsFromSecondarySources(FetchDetailsFromSecondarySources.NEVER)
//                .requiredDetails(Constants.ENTITY_ALL_REQUIRED_DETAILS)
                .build());

        entityDetailsCommand.save(entityDetailsKey, entityDetailsResponse);

        return entityDetailsResponse;
    }


    @Override
    public EntityDetailsResponse getEntityDetails(final EntityDetailsRequest entityDetailsRequest) {

        try {
            final var storedWorkflows = getAllSuccessfulWorkflows(entityDetailsRequest);

            final var details = getWorkflowDetailsResponse(storedWorkflows,
                    entityDetailsRequest.getRequiredDetails()).stream()
                    .map(WorkflowDetailsResponse::getDetails)
                    .flatMap(Collection::stream)
                    .filter(detail -> State.SUCCESS == detail.getActionCompletionState())
                    .collect(Collectors.toMap(detail -> detail.accept(DetailClassificationKeyDetailVisitor.INSTANCE),
                            Function.identity(), BinaryOperator.maxBy(Comparator.comparing(Detail::getLastUpdatedAt))))
                    .values()
                    .stream()
                    .toList();

            return getEntityDetailsResponseAfterFallbackCheck(entityDetailsRequest, details, true);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.REQUIRED_DETAILS_NOT_FOUND, e);
        }
    }

    @Override
    public EntityDetailsResponse getAllEntityDetails(final EntityDetailsRequest entityDetailsRequest) {

        try {
            final var storedWorkflows = workflowService.getAllWorkflows(LatestWorkflowRequestV2.builder()
                            .profileCriteria(entityDetailsRequest.getProfileCriteria())
                            .entityId(entityDetailsRequest.getEntityId())
                            .entityType(entityDetailsRequest.getEntityType())
                            .build())
                    .stream()
                    .filter(storedWorkflow -> entityDetailsRequest.getValidWorkflowStates()
                            .contains(State.valueOf(storedWorkflow.getCurrentState()
                                    .name())))
                    .toList();

            final var details = getWorkflowDetailsResponse(storedWorkflows,
                    entityDetailsRequest.getRequiredDetails()).stream()
                    .map(WorkflowDetailsResponse::getDetails)
                    .flatMap(Collection::stream)
                    .filter(detail -> entityDetailsRequest.getValidKycDetailActionStates()
                            .contains(detail.getActionCompletionState()))
                    .toList();

            return getEntityDetailsResponseAfterFallbackCheck(entityDetailsRequest, details, false);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.REQUIRED_DETAILS_NOT_FOUND, e);
        }
    }


    private EntityDetailsResponse getEntityDetailsResponseAfterFallbackCheck(final EntityDetailsRequest entityDetailsRequest,
                                                                             final List<Detail> pvDetails,
                                                                             final boolean filterLatestDetail) {

        if (Boolean.TRUE.equals(entityDetailsRequest.getFetchDetailsFromSecondarySources()
                .accept(CalculateWhetherToFetchDetailsFromSecondarySourcesVisitor.INSTANCE, pvDetails.isEmpty()))) {

            var combinedDetailsList = Stream.concat(pvDetails.stream(),
                            fetchDetailsViaFallback(entityDetailsRequest).stream())
                    .toList();

            if (filterLatestDetail) {
                combinedDetailsList = combinedDetailsList.stream()
                        .filter(detail -> Objects.nonNull(detail.getLastUpdatedAt()))
                        .collect(
                                Collectors.toMap(detail -> detail.accept(DetailClassificationKeyDetailVisitor.INSTANCE),
                                        Function.identity(),
                                        BinaryOperator.maxBy(Comparator.comparing(Detail::getLastUpdatedAt))))
                        .values()
                        .stream()
                        .toList();
            }

            return EntityDetailsResponse.builder()
                    .details(combinedDetailsList)
                    .entityId(entityDetailsRequest.getEntityId())
                    .entityType(entityDetailsRequest.getEntityType())
                    .build();
        }

        return EntityDetailsResponse.builder()
                .details(pvDetails)
                .entityId(entityDetailsRequest.getEntityId())
                .entityType(entityDetailsRequest.getEntityType())
                .build();
    }


    private List<Detail> fetchDetailsViaFallback(final EntityDetailsRequest entityDetailsRequest) {

        final var fallbackResponseVisitor = entityDetailsRequest.getEntityType()
                .accept(requiredDetailFallbackVisitorForEntityTypeVisitor);

        return entityDetailsRequest.getRequiredDetails()
                .stream()
                .map(t -> t.accept(fallbackResponseVisitor, entityDetailsRequest))
                .flatMap(Optional::stream)
                .map(Detail.class::cast)
                .toList();
    }

    @NotNull
    private List<StoredWorkflow> getAllSuccessfulWorkflows(final EntityDetailsRequest entityDetailsRequest) {

        return workflowService.getAllWorkflows(LatestWorkflowRequestV2.builder()
                        .profileCriteria(entityDetailsRequest.getProfileCriteria())
                        .entityId(entityDetailsRequest.getEntityId())
                        .entityType(entityDetailsRequest.getEntityType())
                        .build())
                .stream()
                .filter(sw -> TransitionState.COMPLETION_STATES_TO_BE_TAGGED.contains(sw.getCurrentState()))
                .toList();
    }

    private void throwRequiredDetailsNotFoundException(final WorkflowDetailsRequest workflowDetailsRequest) {

        throw KaizenException.create(KaizenResponseCode.REQUIRED_DETAILS_NOT_FOUND_FOR_WORKFLOW, Map.ofEntries(
                Map.entry(Constants.MESSAGE, "Unable to find required details of workflow for given inputs"),
                Map.entry(Fields.entityId, workflowDetailsRequest.getEntityId()),
                Map.entry(Fields.entityType, workflowDetailsRequest.getEntityType()),
                Map.entry(Fields.workflowId, workflowDetailsRequest.getWorkflowId()),
                Map.entry("requiredDetails", workflowDetailsRequest.getRequiredDetails())));
    }

    @Override
    public List<WorkflowDetailsResponse> getWorkflowDetailsResponse(final List<StoredWorkflow> storedWorkflows,
                                                                    final List<RequiredDetail> requiredDetails) {

        final var workflowWithoutDetailsResponse = storedWorkflows.stream()
                .map(storedWorkflow -> buildWorkflowDetailsResponse(storedWorkflow, List.of()))
                .toList();

        if (Objects.isNull(requiredDetails) || requiredDetails.isEmpty()) {
            return workflowWithoutDetailsResponse;
        }

        final var storedWorkflowIds = storedWorkflows.stream()
                .map(StoredWorkflow::getWorkflowId)
                .collect(Collectors.toSet());

        final var storedWorkflowSteps = workflowStepService.getWorkflowStepsFromWorkflowIds(storedWorkflowIds);

        final var workflowIdToWorkflowStepIdMapping = storedWorkflowSteps.stream()
                .collect(Collectors.groupingBy(StoredWorkflowStep::getWorkflowId,
                        Collectors.mapping(StoredWorkflowStep::getWorkflowStepId, Collectors.toSet())));

        final var storedWorkflowStepIds = storedWorkflowSteps.stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        final var storedActions = actionService.getActions(storedWorkflowStepIds);

        final var actionIdToStoredActionMap = storedActions.stream()
                .collect(Collectors.toMap(StoredAction::getActionId, Function.identity()));

        final var workflowStepIdToStoredActionsMapping = storedActions.stream()
                .collect(Collectors.groupingBy(StoredAction::getWorkflowStepId));

        final var storedActionMetadata = actionMetadataService.getActionMetadataList(
                actionIdToStoredActionMap.keySet());

        final var actionIdToActionMetadataMapping = storedActionMetadata.stream()
                .collect(Collectors.groupingBy(StoredActionMetadata::getActionId));

        final var workflowDetailsResponses = storedWorkflows.stream()

                // Stored Workflow to Set of Workflow Step Ids
                .map(storedWorkflow -> Map.entry(storedWorkflow,
                        workflowIdToWorkflowStepIdMapping.getOrDefault(storedWorkflow.getWorkflowId(), Set.of())))

                // Filtering if Workflow Step Ids not found for any Workflow
                .filter(entry -> !entry.getValue()
                        .isEmpty())

                // Building Stored Workflow to List of Stored Action using previously generated Set of Workflow Step Ids
                // Looping over each Workflow Step Id and identifying linked Stored Action and building List
                .map(storedWorkflowToWorkflowStepIdsMapping -> Map.entry(
                        storedWorkflowToWorkflowStepIdsMapping.getKey(),
                        storedWorkflowToWorkflowStepIdsMapping.getValue()
                                .stream()
                                .map(workflowStepId -> workflowStepIdToStoredActionsMapping.getOrDefault(workflowStepId,
                                        List.of()))
                                .filter(list -> !list.isEmpty())
                                .flatMap(Collection::stream)
                                .toList()))

                // Filtering if Action Ids not found for any Workflow
                .filter(entry -> !entry.getValue()
                        .isEmpty())

                // Building Stored Workflow to List of Action Metadata using previously generated List of Actions
                // Filtering the latest Action for all unique actionMappingIds in the list
                // Looping over each filtered the latest Action and Identifying linked Action Metadata and building List
                .map(storedWorkflowToStoredActionsMapping -> Map.entry(storedWorkflowToStoredActionsMapping.getKey(),
                        storedWorkflowToStoredActionsMapping.getValue()
                                .stream()
                                .collect(Collectors.groupingBy(StoredAction::getActionMappingId,
                                        Collectors.maxBy(Comparator.comparing(StoredAction::getCreatedAt))))
                                .values()
                                .stream()
                                .filter(Optional::isPresent)
                                .map(Optional::get)
                                .filter(storedAction -> CompletionState.TERMINAL_STATES.contains(
                                        storedAction.getCompletionState()))
                                .map(storedAction -> actionIdToActionMetadataMapping.getOrDefault(
                                        storedAction.getActionId(), List.of()))
                                .filter(list -> !list.isEmpty())
                                .flatMap(Collection::stream)
                                .collect(Collectors.groupingBy(StoredActionMetadata::getActionMetadataType))))

                // Filtering if Action Metadata not found for any Workflow
                .filter(entry -> !entry.getValue()
                        .isEmpty())

                // Building Stored Workflow to Detail list
                // Looping over each Required Detail and converting available Metadata to Detail
                .map(storedWorkflowToActionMetadataMapping -> Map.entry(storedWorkflowToActionMetadataMapping.getKey(),
                        requiredDetails.stream()
                                .map(requiredDetail -> requiredDetail.accept(storedActionMetadataToDetailVisitor,
                                        new StoredActionMetadataToDetailVisitor.StoredActionMetadataToDetailVisitorData(
                                                storedWorkflowToActionMetadataMapping.getKey()
                                                        .getEntityId(), storedWorkflowToActionMetadataMapping.getKey()
                                                .getWorkflowId(), actionIdToStoredActionMap,
                                                storedWorkflowToActionMetadataMapping.getValue())))
                                .filter(list -> !list.isEmpty())
                                .flatMap(Collection::stream)
                                .toList()))

                // Filtering if Detail not found for any Workflow
                .filter(entry -> !entry.getValue()
                        .isEmpty())

                // Building response object
                .map(entry -> buildWorkflowDetailsResponse(entry.getKey(), entry.getValue()))

                // Collecting to List
                .toList();
        return workflowDetailsResponses.isEmpty()
               ? workflowWithoutDetailsResponse
               : workflowDetailsResponses;
    }

    private WorkflowDetailsResponse buildWorkflowDetailsResponse(final StoredWorkflow storedWorkflow,
                                                                 final List<Detail> details) {

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var actionFailureResponseOptional = workflowService.fetchWorkflowActionFailure(storedWorkflow);

        return WorkflowDetailsResponse.builder()
                .entityId(storedWorkflow.getEntityId())
                .entityType(storedWorkflow.getEntityType())
                .organization(profile.getOrganization())
                .namespace(profile.getNamespace())
                .workflowType(profile.getType())
                .workflowVersion(profile.getVersion())
                .workflowId(storedWorkflow.getWorkflowId())
                .workflowState(State.valueOf(storedWorkflow.getCurrentState()
                        .name()))
                .workflowTag(storedWorkflow.getTag())
                .failureActionMappingId(
                        actionFailureResponseOptional.map(ActionFailureResponse::getActionFailureMappingId)
                                .orElse(null))
                .failureErrorCode(actionFailureResponseOptional.map(ActionFailureResponse::getActionFailureErrorCode)
                        .orElse(null))
                .failureReason(actionFailureResponseOptional.map(ActionFailureResponse::getReason)
                        .orElse(null))
                .details(details)
                .profileType(profile.getProfileType())
                .addOnType(profile.getAddOnType())
                .createdAt(storedWorkflow.getCreatedAt())
                .lastUpdatedAt(storedWorkflow.getLastUpdatedAt())
                .build();
    }

    @Override
    public List<WorkflowStepsDetailsResponse> getDetails(final WorkflowStepsDetailsRequest workflowStepsDetailsRequest,
                                                         final boolean fetchActionLevelDetails) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowStepsDetailsRequest.getWorkflowId());

        WorkflowUtils.validateEntityAgainstWorkflow(storedWorkflow, workflowStepsDetailsRequest.getEntityId(),
                workflowStepsDetailsRequest.getEntityType());

        final var storedWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        final var pendingStateStoredWorkflowStepOptional = storedWorkflowSteps.stream()
                .filter(ws -> TransitionState.PENDING_STATES.contains(ws.getCurrentState()))
                .findFirst();

        final var workflowStepsDetailsResponses = storedWorkflowSteps.stream()
                .map(storedWorkflowStep -> buildWorkflowStepsDetailsResponse(storedWorkflowStep,
                        fetchActionLevelDetails))
                .toList();

        if (pendingStateStoredWorkflowStepOptional.isPresent() || !TransitionState.PENDING_STATES.contains(
                storedWorkflow.getCurrentState())) {
            return workflowStepsDetailsResponses;
        }

        final var notStartedExecutableProfileSteps = getNotStartedExecutableProfileSteps(storedWorkflow,
                storedWorkflowSteps);

        if (!notStartedExecutableProfileSteps.isEmpty()) {

            final var profileStep = notStartedExecutableProfileSteps.iterator()
                    .next();

            final var workflowStepsDetailsResponse = WorkflowStepsDetailsResponse.builder()
                    .title(profileStep.getTitle())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .workflowStepState(State.NOT_STARTED)
                    .build();

            return Stream.concat(workflowStepsDetailsResponses.stream(), Stream.of(workflowStepsDetailsResponse))
                    .toList();

        } else {
            return workflowStepsDetailsResponses;
        }
    }

    @Override
    public WorkflowDetailsResponse getDetails(final LatestWorkflowDetailsRequest latestWorkflowDetailsRequest) {

        final var latestWorkflowResponse = workflowService.getLatestWorkflow(
                BuildUtils.toLatestWorkflowRequest(latestWorkflowDetailsRequest));

        if (Objects.isNull(latestWorkflowResponse)) {
            throw KaizenException.create(KaizenResponseCode.WORKFLOW_NOT_FOUND,
                    Map.ofEntries(Map.entry(Constants.MESSAGE, "Unable to find workflow for given inputs"),
                            Map.entry(Fields.entityId, latestWorkflowDetailsRequest.getEntityId()),
                            Map.entry(Fields.entityType, latestWorkflowDetailsRequest.getEntityType())));
        }

        final var storedWorkflow = workflowService.validateAndGetWorkflow(latestWorkflowResponse.getWorkflowId());

        final var workflowWithoutDetailsResponse = buildWorkflowDetailsResponse(storedWorkflow, null);

        final var requiredDetails = latestWorkflowDetailsRequest.getRequiredDetails();

        if (Objects.isNull(requiredDetails) || requiredDetails.isEmpty()) {
            return workflowWithoutDetailsResponse;
        }

        final var workflowDetailsResponseList = getWorkflowDetailsResponse(List.of(storedWorkflow), requiredDetails);

        return workflowDetailsResponseList.stream()
                .findFirst()
                .orElse(workflowWithoutDetailsResponse);
    }

    private List<ProfileStep> getNotStartedExecutableProfileSteps(final StoredWorkflow storedWorkflow,
                                                                  final List<StoredWorkflowStep> storedWorkflowSteps) {
        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflow.getWorkflowId());

        final var workflowContextNode = MapperUtils.convertToJsonNode(workflowContext);

        final var workflowStepMap = storedWorkflowSteps.stream()
                .collect(Collectors.toMap(StoredWorkflowStep::getProfileStepId, Function.identity()));

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var profileSteps = profile.getProfileSteps();

        return profileSteps.stream()
                .filter(ps -> !workflowStepMap.containsKey(ps.getProfileStepId()))
                .filter(ps -> hopeLangService.evaluate(ps.getExecutionRule(), workflowContextNode))
                .toList();
    }

    private WorkflowStepsDetailsResponse buildWorkflowStepsDetailsResponse(final StoredWorkflowStep storedWorkflowStep,
                                                                           final boolean fetchActionLevelDetails) {

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var storedActionList = actionService.getActions(Set.of(storedWorkflowStep.getWorkflowStepId()));

        final var workflowStepsDetailsResponseBuilder = WorkflowStepsDetailsResponse.builder()
                .title(profileStep.getTitle())
                .profileStepMappingId(profileStep.getProfileStepMappingId())
                .workflowStepState(State.valueOf(storedWorkflowStep.getCurrentState()
                        .name()))
                .createdAt(storedWorkflowStep.getCreatedAt())
                .lastUpdatedAt(storedWorkflowStep.getLastUpdatedAt());

        if (TransitionState.NON_SUCCESS_COMPLETED_STATES.contains(storedWorkflowStep.getCurrentState())) {
            final var actionFailureErrorCodeReasonResponse = storedActionList.stream()
                    .filter(storedAction -> CompletionState.FAILURE == storedAction.getCompletionState())
                    .max(Comparator.comparing(StoredAction::getLastUpdatedAt))
                    .flatMap(actionService::getFailureDetailsFromFailedAction);

            workflowStepsDetailsResponseBuilder.failureErrorCode(
                            actionFailureErrorCodeReasonResponse.map(ActionFailureResponse::getActionFailureErrorCode)
                                    .orElse(null))
                    .failureReason(actionFailureErrorCodeReasonResponse.map(ActionFailureResponse::getReason)
                            .orElse(null));
        }

        if (fetchActionLevelDetails) {
            workflowStepsDetailsResponseBuilder.actionLevelDetailsList(storedActionList.stream()
                    .map(this::buildActionLevelDetails)
                    .toList());
        }

        return workflowStepsDetailsResponseBuilder.build();
    }

    private ActionLevelDetails buildActionLevelDetails(final StoredAction storedAction) {

        final var actionFailureDetailsResponse = actionService.getFailureDetailsFromFailedAction(storedAction);

        final var isManualRetryEnabled = manualRetryService.isActionManuallyRetriable(storedAction.getActionId());

        return ActionLevelDetails.builder()
                .actionId(storedAction.getActionId())
                .actionMappingId(storedAction.getActionMappingId())
                .actionType(storedAction.getActionType())
                .screenMappingId(storedAction.getScreenMappingId())
                .failureErrorCode(actionFailureDetailsResponse.map(ActionFailureResponse::getActionFailureErrorCode)
                        .orElse(null))
                .failureReason(actionFailureDetailsResponse.map(ActionFailureResponse::getReason)
                        .orElse(null))
                .lastUpdatedBy(storedAction.getLastUpdatedBy())
                .completionState(State.valueOf(storedAction.getCompletionState()
                        .name()))
                .createdAt(storedAction.getCreatedAt())
                .lastUpdatedAt(storedAction.getLastUpdatedAt())
                .lastUpdaterType(Optional.ofNullable(storedAction.getLastUpdaterType())
                        .map(Enum::name)
                        .orElse(null))
                .manualRetryEnabled(isManualRetryEnabled)
                .workflowStepId(storedAction.getWorkflowStepId())
                .build();

    }

}
