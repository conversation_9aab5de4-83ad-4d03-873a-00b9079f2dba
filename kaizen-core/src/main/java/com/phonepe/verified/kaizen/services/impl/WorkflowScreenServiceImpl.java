package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowScreenService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.ActionToTriggerFirstTime;
import com.phonepe.verified.kaizen.services.visitors.EvaluateProfileScreenCompletionAndTriggerNextActionVisitor;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class WorkflowScreenServiceImpl implements WorkflowScreenService {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowContextStore workflowContextStore;

    private final Provider<WorkflowStepService> workflowStepServiceProvider;

    @Override
    public void triggerStepActionInProfileScreen(final StandardProfileScreenConfig standardProfileScreenConfigToTrigger,
                                                 final StoredWorkflowStep storedWorkflowStep,
                                                 final String screenMappingId,
                                                 final UserDetails userDetails) {

        final var standardStepActionConfig = standardProfileScreenConfigToTrigger.getStepActionConfig()
                .accept(ActionToTriggerFirstTime.INSTANCE, null);

        final var uiRequestContext = workflowStepServiceProvider.get()
                .getUiRequestContext(storedWorkflowStep.getWorkflowStepId());

        final var shadowV2UiRequestContext = Objects.nonNull(uiRequestContext)
                                             ? (ShadowV2UiRequestContext) uiRequestContext
                                             : null;

        final var transitionContext = actionService.buildTransitionContext(
                standardStepActionConfig.getTransitionContextTemplate(),
                standardStepActionConfig.getTransitionContextTemplateAsString(), shadowV2UiRequestContext);

        actionService.createAction(storedWorkflowStep.getWorkflowStepId(), standardStepActionConfig.getActionType(),
                standardStepActionConfig.getStateMachineVersion(), standardStepActionConfig.getActionMappingId(),
                transitionContext, screenMappingId, userDetails, standardStepActionConfig.getStepActionContext(),
                standardStepActionConfig.getTtlConfig(), standardStepActionConfig.getDependencyConfig());
    }

    @Override
    public void handleActionCompletion(final String completedActionId) {

        final var storedAction = actionService.validateAndGetAction(completedActionId);

        final var workflowStepId = storedAction.getWorkflowStepId();

        final var storedWorkflowStep = workflowStepServiceProvider.get()
                .validateAndGetWorkflowStep(workflowStepId);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        /* Only below-mentioned states can be returned for any screen:
         * SUCCESS, FAILURE, PSEUDO_SUCCESS --> Due to Action completion (Screen completed)
         * NOT_STARTED --> Due to previous Action completed under AND (others) StepActionConfig
         * INVALIDATED --> Due to back button handling
         * ---
         * Below-mentioned state cannot be returned in any condition:
         * IN_PROGRESS --> Not possible because this flow is only triggered as part of handshake
         */
        final var profileScreenCompletionStatus = profileStep.getProfileScreenConfig()
                .accept(new EvaluateProfileScreenCompletionAndTriggerNextActionVisitor(storedAction, actionService,
                        workflowService, hopeLangService, workflowContextStore));

        if (!profileScreenCompletionStatus.isMappingIdFound()) {

            throw KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                    Map.of(Fields.workflowStepId, workflowStepId, StoredAction.Fields.actionId,
                            storedAction.getActionId(), StoredAction.Fields.screenMappingId,
                            storedAction.getScreenMappingId()));
        }

        if (!CompletionState.NON_COMPLETED_STATES.contains(profileScreenCompletionStatus.getCompletionState())) {
            workflowStepServiceProvider.get()
                    .handleScreenCompletion(profileScreenCompletionStatus.getCompletionState(), completedActionId);
        }
    }
}
