package com.phonepe.verified.kaizen.resources.v2;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.authz.annotations.WorkflowId;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.data.common.MailboxApiName;
import com.phonepe.verified.kaizen.queue.actors.ConfirmationActionActor;
import com.phonepe.verified.kaizen.queue.messages.ConfirmationActionMessage;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.constraints.NotEmpty;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v2/confirm")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Confirmation V2", description = "User details confirmation related V2 APIs")
public class ConfirmationV2Resource {

    private final AuthZService authZService;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final ConfirmationActionActor confirmationActionActor;

    private final SessionManagementService sessionManagementService;

    @POST
    @AuthZ
    @SneakyThrows
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/{actionId}/{intent}/{componentKitVersion}")
    @Operation(summary = "Confirm for actionId")
    public Response confirmAction(@NotEmpty @PathParam("intent") final String intent,
                                  @NotEmpty @PathParam("actionId") final String actionId,
                                  @PathParam("componentKitVersion") final long componentKitVersion,
                                  @Parameter(hidden = true) @WorkflowId final String workflowId,
                                  @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                  @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                  @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken) {

        authZService.authorizeActionIdBelongsToWorkflowId(actionId, workflowId);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        confirmationActionActor.publish(ConfirmationActionMessage.builder()
                .intent(intent)
                .actionId(actionId)
                .requestInfo(requestInfo)
                .userDetails(userDetails)
                .componentKitVersion(componentKitVersion)
                .build());

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.CONFIRM_ACTION)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.CONFIRM_ACTION)
                        .toMilliseconds())
                .build();
    }
}
