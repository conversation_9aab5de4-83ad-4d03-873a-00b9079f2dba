package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.drishti.models.commons.documents.DocumentType.DocumentTypeVisitor;
import com.phonepe.verified.drishti.models.commons.documents.impl.Base64EncodedDocument;
import com.phonepe.verified.drishti.models.requests.masking.MaskingContext;
import com.phonepe.verified.drishti.models.requests.masking.impl.AadhaarMaskingContext;
import com.phonepe.verified.kaizen.models.data.DocumentIdentifierAndLabel;
import com.phonepe.verified.kaizen.services.DocumentService;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
public class DrishtiDocumentTypeToMaskingContextVisitor implements
        DocumentTypeVisitor<MaskingContext, List<DocumentIdentifierAndLabel>> {

    private final DocumentService documentService;

    public DrishtiDocumentTypeToMaskingContextVisitor(final DocumentService documentService) {
        this.documentService = documentService;
    }

    @Override
    public MaskingContext visitPan(final List<DocumentIdentifierAndLabel> documents) {

        return null;
    }

    @Override
    public MaskingContext visitVoter(final List<DocumentIdentifierAndLabel> documents) {

        return null;
    }

    @Override
    public MaskingContext visitDrivingLicense(final List<DocumentIdentifierAndLabel> documents) {

        return null;
    }

    @Override
    @SneakyThrows
    public MaskingContext visitAadhaar(final List<DocumentIdentifierAndLabel> documents) {

        final var aadhaarMaskingContextBuilder = AadhaarMaskingContext.builder()
                .documentFront(getBase64EncodedDocument(documentService.getBase64EncodedImage(documents.get(0)
                        .getDocumentId())));

        if (documents.size() >= 2) {
            aadhaarMaskingContextBuilder.documentBack(getBase64EncodedDocument(documentService.getBase64EncodedImage(
                    documents.get(1)
                            .getDocumentId())));
        }

        return aadhaarMaskingContextBuilder.build();
    }

    @Override
    public MaskingContext visitPassport(final List<DocumentIdentifierAndLabel> documents) {

        return null;
    }

    @Override
    public MaskingContext visitVehicleRegistrationCertificate(final List<DocumentIdentifierAndLabel> documents) {

        return null;
    }

    @Override
    public MaskingContext visitGstCertificate(final List<DocumentIdentifierAndLabel> documents) {
        return null;
    }

    @Override
    public MaskingContext visitSelfie(final List<DocumentIdentifierAndLabel> documents) {
        return null;
    }

    private Base64EncodedDocument getBase64EncodedDocument(final String base64EncodedImage) {

        return Base64EncodedDocument.builder()
                .content(base64EncodedImage)
                .build();
    }
}
