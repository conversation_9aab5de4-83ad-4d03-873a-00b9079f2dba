package com.phonepe.verified.kaizen.services.impl;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Sets;
import com.google.inject.Provider;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.keys.StateMachineRegistryKey;
import com.phonepe.verified.kaizen.models.requests.statemachines.AddStateMachineRegistryRequest;
import com.phonepe.verified.kaizen.models.requests.statemachines.StateMachineTransition;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.statemachines.StateMachineCount;
import com.phonepe.verified.kaizen.models.responses.statemachines.StateMachineTallyResponse;
import com.phonepe.verified.kaizen.registries.SpringActionRegistry;
import com.phonepe.verified.kaizen.registries.StateMachineRegistry;
import com.phonepe.verified.kaizen.services.StateMachineService;
import com.phonepe.verified.kaizen.statemachines.BaseStateMachine;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredStateMachineTransition;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.StateMachineRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.ActionKeys;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.States;
import com.phonepe.verified.kaizen.utils.StateMachineUtils;
import guru.nidi.graphviz.attribute.Attributes;
import guru.nidi.graphviz.attribute.Label;
import guru.nidi.graphviz.attribute.Rank;
import guru.nidi.graphviz.attribute.Shape;
import guru.nidi.graphviz.engine.Format;
import guru.nidi.graphviz.engine.Graphviz;
import guru.nidi.graphviz.model.Factory;
import guru.nidi.graphviz.model.Link;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class StateMachineServiceImpl implements StateMachineService {

    private final SpringActionRegistry springActionRegistry;

    private final StateMachineRepository stateMachineRepository;

    private final Provider<StateMachineRegistry> stateMachineRegistry;

    private final LoadingCache<StateMachineRegistryKey, String> stateMachineSvgGraphCache;

    @Inject
    public StateMachineServiceImpl(final StateMachineRepository stateMachineRepository,
                                   final SpringActionRegistry springActionRegistry,
                                   final Provider<StateMachineRegistry> stateMachineRegistry) {
        this.stateMachineRepository = stateMachineRepository;
        this.springActionRegistry = springActionRegistry;
        this.stateMachineRegistry = stateMachineRegistry;
        this.stateMachineSvgGraphCache = Caffeine.newBuilder()
                .maximumSize(1_000)
                .build(this::loadSvgGraph);
    }

    @Override
    public boolean addRegistry(final AddStateMachineRegistryRequest addStateMachineRegistryRequest,
                               final String addedBy) {

        if (stateMachineRepository.select(addStateMachineRegistryRequest.getActionType(),
                        addStateMachineRegistryRequest.getVersion())
                .isPresent()) {
            throw KaizenException.create(KaizenResponseCode.STATE_MACHINE_ALREADY_EXISTS, Map.of());
        }

        final var storedStateMachineTransitions = BuildUtils.toStoredStateMachineTransitionList(
                addStateMachineRegistryRequest, addedBy);

        if (!StateMachineUtils.validateInitialState(storedStateMachineTransitions)) {
            throw KaizenException.create(KaizenResponseCode.INVALID_STATE_MACHINE_INITIAL_STATE, Map.of());
        }

        if (!StateMachineUtils.validateTerminalStates(storedStateMachineTransitions)) {
            throw KaizenException.create(KaizenResponseCode.INVALID_STATE_MACHINE_TERMINAL_STATES, Map.of());
        }

        if (!StateMachineUtils.validatePseudoSuccessState(storedStateMachineTransitions)) {
            throw KaizenException.create(KaizenResponseCode.STATE_MACHINE_INVALID_PSEUDO_SUCCESS_STATE, Map.of());
        }

        final var allSpringActions = springActionRegistry.getAllSpringActions();

        final var springActionKeys = storedStateMachineTransitions.stream()
                .map(StoredStateMachineTransition::getActionKey)
                .collect(Collectors.toSet());

        if (!allSpringActions.containsAll(springActionKeys)) {
            throw KaizenException.create(KaizenResponseCode.INVALID_STATE_MACHINE_ACTIONS,
                    Map.of("missingActions", Sets.difference(springActionKeys, allSpringActions)));
        }

        final var allStates = StateMachineUtils.getAllStates(storedStateMachineTransitions);

        final List<StoredStateMachineTransition> invalidatedStoredStateMachineTransitions = getAutoGeneratedTransitions(
                addStateMachineRegistryRequest, addedBy, storedStateMachineTransitions, allStates,
                Events.INVALIDATE_ACTION, States.INVALIDATED, ActionKeys.INVALIDATE_STATE_ACTION_KEY);

        final List<StoredStateMachineTransition> abortedStoredStateMachineTransitions = getAutoGeneratedTransitions(
                addStateMachineRegistryRequest, addedBy, storedStateMachineTransitions, allStates, Events.ABORT_ACTION,
                States.ABORTED, ActionKeys.ABORT_STATE_ACTION_KEY);

        final List<StoredStateMachineTransition> discardedStoredStateMachineTransitions = getAutoGeneratedTransitions(
                addStateMachineRegistryRequest, addedBy, storedStateMachineTransitions, allStates,
                Events.DISCARD_ACTION, States.DISCARDED, ActionKeys.DISCARD_STATE_ACTION_KEY);

        final var allStateMachineTransitions = Stream.of(storedStateMachineTransitions.stream(),
                        invalidatedStoredStateMachineTransitions.stream(), abortedStoredStateMachineTransitions.stream(),
                        discardedStoredStateMachineTransitions.stream())
                .flatMap(Function.identity())
                .toList();

        return stateMachineRepository.saveAll(Constants.STATE_MACHINE_SHARD_KEY, allStateMachineTransitions);
    }

    @Override
    public List<StateMachineTransitions> getAllActionTransitions() {

        return stateMachineRepository.selectAll()
                .stream()
                .map(machine -> StateMachineData.builder()
                        .key(StateMachineRegistryKey.builder()
                                .actionType(machine.getActionType())
                                .stateMachineVersion(machine.getVersion())
                                .build())
                        .stateMachineTransitions(StateMachineTransition.builder()
                                .source(machine.getSource())
                                .target(machine.getTarget())
                                .event(machine.getEvent())
                                .actionKey(machine.getActionKey())
                                .build())
                        .build())
                .collect(Collectors.groupingBy(StateMachineData::key,
                        Collectors.mapping(StateMachineData::stateMachineTransitions, Collectors.toList())))
                .entrySet()
                .stream()
                .map(entry -> StateMachineTransitions.builder()
                        .actionType(entry.getKey()
                                .getActionType())
                        .version(entry.getKey()
                                .getStateMachineVersion())
                        .stateMachineTransitions(entry.getValue())
                        .build())
                .toList();
    }

    private List<StoredStateMachineTransition> getAutoGeneratedTransitions(final AddStateMachineRegistryRequest addStateMachineRegistryRequest,
                                                                           final String addedBy,
                                                                           final List<StoredStateMachineTransition> storedStateMachineTransitions,
                                                                           final Set<String> allStates,
                                                                           final String event,
                                                                           final String targetState,
                                                                           final String actionKey) {

        final var sourceStatesHavingInvalidatedEvent = storedStateMachineTransitions.stream()
                .filter(storedStateMachineTransition -> storedStateMachineTransition.getEvent()
                        .equals(event))
                .map(StoredStateMachineTransition::getSource)
                .collect(Collectors.toSet());

        final var targetStatesHavingInvalidatedEvent = storedStateMachineTransitions.stream()
                .filter(storedStateMachineTransition -> storedStateMachineTransition.getEvent()
                        .equals(event))
                .map(StoredStateMachineTransition::getTarget)
                .collect(Collectors.toSet());

        return allStates.stream()
                .filter(currentState -> !sourceStatesHavingInvalidatedEvent.contains(currentState))
                .filter(currentState -> !targetStatesHavingInvalidatedEvent.contains(currentState))
                .map(currentState -> StoredStateMachineTransition.builder()
                        .source(currentState)
                        .target(targetState)
                        .event(event)
                        .actionType(addStateMachineRegistryRequest.getActionType())
                        .version(addStateMachineRegistryRequest.getVersion())
                        .lastUpdatedBy(addedBy)
                        .actionKey(actionKey)
                        .build())
                .toList();
    }

    @Override
    public String getStateMachineSvgGraph(final ActionType actionType,
                                          final String stateMachineVersion) {
        return stateMachineSvgGraphCache.get(StateMachineRegistryKey.newInstance(actionType, stateMachineVersion));
    }

    @Override
    public boolean checkIfExists(final ActionType actionType,
                                 final String stateMachineVersion) {

        try {
            stateMachineRegistry.get()
                    .getSpringStateMachine(actionType, stateMachineVersion);
        } catch (final Exception e) {
            return false;
        }

        return true;
    }

    @Override
    public StateMachineTallyResponse tallyStateMachines() {

        final var storedStateMachineTransitions = stateMachineRepository.selectAll();
        final var stateMachinesInDB = storedStateMachineTransitions.stream()
                .map(storedStateMachineTransition -> StateMachineRegistryKey.newInstance(
                        storedStateMachineTransition.getActionType(), storedStateMachineTransition.getVersion()))
                .collect(Collectors.toSet());

        final var stateMachinesInRegistry = stateMachineRegistry.get()
                .getAllStateMachineRegistryKeysFromStore();

        return StateMachineTallyResponse.builder()
                .stateMachineCountInDB(StateMachineCount.builder()
                        .count(stateMachinesInDB.size())
                        .stateMachineRegistryKeys(stateMachinesInDB)
                        .build())
                .stateMachineCountInRegistry(StateMachineCount.builder()
                        .count(stateMachinesInRegistry.size())
                        .stateMachineRegistryKeys(stateMachinesInRegistry)
                        .build())
                .build();
    }

    private String loadSvgGraph(final StateMachineRegistryKey key) {
        final var stateMachine = stateMachineRegistry.get()
                .getBaseStateMachine(key.getActionType(), key.getStateMachineVersion());
        return generateGraph(stateMachine);
    }

    private String generateGraph(final BaseStateMachine<String, String> baseStateMachine) {

        final var stateMachine = baseStateMachine.internalApiGetStateMachine();

        final var graph = Factory.mutGraph(baseStateMachine.getClass()
                        .getSimpleName())
                .setDirected(true)
                .graphAttrs()
                .add(Rank.sepEqually(1.0))
                .graphAttrs()
                .add(Attributes.attr("nodesep", 2.0));

        final var graphMap = stateMachine.getTransitions()
                .stream()
                .collect(Collectors.groupingBy(t -> t.getSource()
                        .getId(), Collectors.groupingBy(t -> t.getTarget()
                        .getId(), Collectors.mapping(t -> t.getTrigger()
                        .getEvent(), Collectors.toSet()))));

        graphMap.forEach((sourceState, transitions) -> {
            final var node = Factory.node(sourceState)
                    .with(Shape.RECTANGLE);
            transitions.forEach((targetState, events) -> events.forEach(event -> graph.add(node.link(
                    Link.to(Factory.node(targetState))
                            .with(Label.of(event.toLowerCase()))))));
        });

        return Graphviz.fromGraph(graph)
                .render(Format.SVG)
                .toString();
    }
}
