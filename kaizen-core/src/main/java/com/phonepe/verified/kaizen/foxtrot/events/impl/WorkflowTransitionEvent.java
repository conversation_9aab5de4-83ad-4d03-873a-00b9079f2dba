package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.zeus.models.Farm;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowTransitionEvent extends BaseEvent {

    private TransitionState currentState;

    private TransitionState previousState;

    private TransitionEvent currentEvent;

    private TransitionEvent previousEvent;

    private UpdaterType updaterType;

    private String lastUpdatedBy;

    private String tag;

    private Farm callerFarmId;

    private String failureReason;

    @Builder
    public WorkflowTransitionEvent(final EventType eventType,
                                   final String intent,
                                   final String entityId,
                                   final String namespace,
                                   final String workflowId,
                                   @NonNull final String groupingKey,
                                   final String organization,
                                   final String workflowType,
                                   final String workflowStepId,
                                   final EntityType entityType,
                                   final String workflowVersion,
                                   final String screenMappingId,
                                   final String actionMappingId,
                                   final long componentKitVersion,
                                   final String profileStepMappingId,
                                   final TransitionState currentState,
                                   final TransitionState previousState,
                                   final TransitionEvent currentEvent,
                                   final TransitionEvent previousEvent,
                                   final UpdaterType updaterType,
                                   final String lastUpdatedBy,
                                   final ProfileType profileType,
                                   final String addOnType,
                                   final String tag,
                                   final Farm callerFarmId,
                                   final String failureReason) {
        super(eventType, intent, entityId, namespace, workflowId, profileType, addOnType, groupingKey, organization,
                workflowType, workflowStepId, entityType, workflowVersion, screenMappingId, actionMappingId,
                componentKitVersion, profileStepMappingId);
        this.currentState = currentState;
        this.previousState = previousState;
        this.currentEvent = currentEvent;
        this.previousEvent = previousEvent;
        this.updaterType = updaterType;
        this.lastUpdatedBy = lastUpdatedBy;
        this.tag = tag;
        this.callerFarmId = callerFarmId;
        this.failureReason = failureReason;
    }
}
