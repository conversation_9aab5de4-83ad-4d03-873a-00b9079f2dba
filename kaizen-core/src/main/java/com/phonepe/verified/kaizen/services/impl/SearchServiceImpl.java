package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.request.SearchFieldRequest;
import com.phonepe.shadow.page.field.FullScreenSearchFieldV2Response;
import com.phonepe.shadow.page.field.impl.FullScreenSearchFieldV2;
import com.phonepe.shadow.services.TemplateServiceV2;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.search.SearchSourceConfig;
import com.phonepe.verified.kaizen.models.responses.ApiResponse;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.RevolverCallbackActor;
import com.phonepe.verified.kaizen.queue.messages.RevolverCallbackMessage;
import com.phonepe.verified.kaizen.services.CatalogueService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.SearchService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.SearchSourceTypeDataVisitor;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class SearchServiceImpl implements SearchService {

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final TemplateServiceV2 templateService;

    private final CatalogueService catalogueService;

    private final RevolverCallbackActor revolverCallbackActor;

    @Override
    @SneakyThrows
    public void fetchSearchDetails(final String workflowId,
                                   final String fieldId,
                                   final String intent,
                                   final long componentKitVersion,
                                   final RequestInfo requestInfo,
                                   final SearchFieldRequest searchFieldRequest) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var templateWorkflowType = BuildUtils.getTemplateWorkflowType(profile);

        final var template = templateService.getTemplate(templateWorkflowType, intent, componentKitVersion);

        final var fullScreenSearchFieldV2 = template.getSectionMappings()
                .stream()
                .flatMap(templateSectionMapping -> templateSectionMapping.getSection()
                        .getFieldGroups()
                        .stream())
                .flatMap(fieldGroup -> fieldGroup.getFields()
                        .stream())
                .filter(field -> Objects.equals(fieldId, field.getId()))
                .map(FullScreenSearchFieldV2.class::cast)
                .findFirst();

        if (fullScreenSearchFieldV2.isPresent()) {

            final var sourceConfigJsonNode = fullScreenSearchFieldV2.get()
                    .getRequestDetails()
                    .getSourceConfig();

            final var serializedSourceConfig = MapperUtils.serializeToString(sourceConfigJsonNode);

            final var sourceConfig = MapperUtils.deserialize(serializedSourceConfig, SearchSourceConfig.class);

            final var searchResultDetailsForApp = sourceConfig.accept(new SearchSourceTypeDataVisitor(catalogueService),
                    searchFieldRequest);

            revolverCallbackActor.publish(RevolverCallbackMessage.builder()
                    .requestId(requestInfo.getRequestId())
                    .status(200)
                    .data(ApiResponse.<FullScreenSearchFieldV2Response>builder()
                            .success(true)
                            .data(searchResultDetailsForApp)
                            .build())
                    .build());
        } else {
            throw KaizenException.create(KaizenResponseCode.FIELD_ID_NOT_FOUND_IN_TEMPLATE,
                    Map.of("fieldId", fieldId, "templateId", template.getTemplateId()));
        }
    }
}
