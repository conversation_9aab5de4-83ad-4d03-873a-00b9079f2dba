package com.phonepe.verified.kaizen.utils;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class PvcoreTenant {

    @Nullable
    private final OlympusTenant olympusTenant;

    @NotEmpty
    private final String organization;

    @NotEmpty
    private final String namespace;
}
