package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.configs.retry.CompositeRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.CountBasedRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.HopeRuleBasedRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.ManualOpsBasedRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.RetryConfigVisitor;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.RetryConfigExecutor.RetryConfigExecutorData;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class RetryConfigExecutor implements RetryConfigVisitor<Boolean, RetryConfigExecutorData> {

    private final HopeLangService hopeLangService;

    private final WorkflowStepService workflowStepService;

    private final WorkflowContextStore workflowContextStore;

    @Override
    public Boolean visit(final CountBasedRetryConfig countBasedRetryConfig,
                         final RetryConfigExecutorData retryConfigExecutorData) {

        return countBasedRetryConfig.isEnabled()
                && countBasedRetryConfig.getRetryCount() >= retryConfigExecutorData.getFailedActionCount();
    }

    @Override
    public Boolean visit(final HopeRuleBasedRetryConfig hopeRuleBasedRetryConfig,
                         final RetryConfigExecutorData retryConfigExecutorData) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(
                retryConfigExecutorData.getWorkflowStepId());

        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflowStep.getWorkflowId());

        final var ruleEvaluationResult = hopeLangService.evaluate(hopeRuleBasedRetryConfig.getEvaluationRule(),
                MapperUtils.convertToJsonNode(workflowContext));

        return ruleEvaluationResult
                && hopeRuleBasedRetryConfig.getRetryCount() >= retryConfigExecutorData.getFailedActionCount();
    }

    @Override
    public Boolean visit(final ManualOpsBasedRetryConfig manualOpsBasedRetryConfig,
                         final RetryConfigExecutorData retryConfigExecutorData) {
        return true;
    }

    @Override
    public Boolean visit(final CompositeRetryConfig compositeRetryConfig,
                         final RetryConfigExecutorData retryConfigExecutorData) {

        final var evaluatedRetryConfig = compositeRetryConfig.getRetryConfig()
                .accept(this, retryConfigExecutorData);

        final var manualOpsRetryConfig = compositeRetryConfig.getOpsBasedRetryConfig()
                .accept(this, retryConfigExecutorData);

        return evaluatedRetryConfig && manualOpsRetryConfig;
    }

    @Data
    @Builder
    public static class RetryConfigExecutorData {

        private final int failedActionCount;

        private final String workflowStepId;
    }
}
