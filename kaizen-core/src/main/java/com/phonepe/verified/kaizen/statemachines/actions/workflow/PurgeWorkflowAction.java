package com.phonepe.verified.kaizen.statemachines.actions.workflow;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.actors.ClientWorkflowCallbackActor;
import com.phonepe.verified.kaizen.queue.actors.PostCompletionActionConfigProcessorActor;
import com.phonepe.verified.kaizen.queue.messages.PostCompletionActionConfigProcessorMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import java.util.Objects;
import java.util.Optional;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class PurgeWorkflowAction extends UpdateWorkflowBaseAction {

    private final WorkflowStepService workflowStepService;

    private final ProfileService profileService;

    private final ActionService actionService;

    private final Provider<ClientWorkflowCallbackActor> clientWorkflowCallbackActorProvider;

    private final PostCompletionActionConfigProcessorActor postCompletionActionConfigProcessorActor;

    @Inject
    public PurgeWorkflowAction(final ProfileService profileService,
                               final ActionService actionService,
                               final WorkflowService workflowService,
                               final WorkflowRepository workflowRepository,
                               final EventIngestionCommand eventIngestionCommand,
                               final Provider<WorkflowContextStore> workflowContextStore,
                               final WorkflowStepService workflowStepService,
                               final Provider<ClientWorkflowCallbackActor> clientWorkflowCallbackActorProvider,
                               final PostCompletionActionConfigProcessorActor postCompletionActionConfigProcessorActor) {
        super(profileService, workflowService, workflowRepository, eventIngestionCommand, workflowContextStore);
        this.profileService = profileService;
        this.actionService = actionService;
        this.workflowStepService = workflowStepService;
        this.clientWorkflowCallbackActorProvider = clientWorkflowCallbackActorProvider;
        this.postCompletionActionConfigProcessorActor = postCompletionActionConfigProcessorActor;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final TransitionState previousState,
                                  final StoredWorkflow storedWorkflow,
                                  final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var actionId = stateContext.getExtendedState()
                .get(StoredAction.Fields.actionId, String.class);

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var failureErrorCodeOptional = getFailureErrorCodeForTheAction(storedWorkflow, actionId);

        final var workflowClientCallbackMessage = WorkflowMessage.builder()
                .failureErrorCode(failureErrorCodeOptional.orElse(null))
                .workflowId(storedWorkflow.getWorkflowId())
                .build();

        if (Objects.isNull(profile.getPostCompletionActionConfig())) {

            clientWorkflowCallbackActorProvider.get()
                    .publish(workflowClientCallbackMessage);
        } else {

            postCompletionActionConfigProcessorActor.publish(PostCompletionActionConfigProcessorMessage.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .failureErrorCode(failureErrorCodeOptional.orElse(null))
                    .build());
        }
    }


    @Override
    protected void transition(final StoredWorkflow storedWorkflow,
                              final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        workflowStepService.purgeWorkflowSteps(storedWorkflow.getWorkflowId(), userDetails);

    }

    private Optional<ActionFailureErrorCode> getFailureErrorCodeForTheAction(final StoredWorkflow storedWorkflow,
                                                                             final String actionId) {

        if (Objects.nonNull(actionId) && TransitionState.FAILURE == storedWorkflow.getCurrentState()) {

            return Optional.of(actionService.validateAndGetAction(actionId)
                    .getFailureErrorCode());
        }

        return Optional.empty();
    }
}
