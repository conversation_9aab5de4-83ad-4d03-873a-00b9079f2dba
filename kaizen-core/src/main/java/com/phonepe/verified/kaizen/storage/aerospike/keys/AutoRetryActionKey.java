package com.phonepe.verified.kaizen.storage.aerospike.keys;

import com.phonepe.verified.kaizen.storage.aerospike.AerospikeKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoRetryAction<PERSON>ey implements AerospikeKey {

    @NonNull
    private String workflowStepId;

    @NonNull
    private String actionMappingId;

    @Override
    public String getKey() {
        return String.format("%s_%s", workflowStepId, actionMappingId);
    }
}
