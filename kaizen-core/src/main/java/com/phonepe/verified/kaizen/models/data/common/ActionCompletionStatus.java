package com.phonepe.verified.kaizen.models.data.common;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionCompletionStatus {

    @With
    private CompletionState completionState;

    private LocalDateTime actionCreationTimestamp;
}
