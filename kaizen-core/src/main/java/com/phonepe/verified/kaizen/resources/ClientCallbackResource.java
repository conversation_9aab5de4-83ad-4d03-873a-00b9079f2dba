package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.models.configs.callback.ClientCallbackConfig;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.ClientCallbackService;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.UserDetailsUtils;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/client/callback")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Client Callback Management", description = "Client Callback related APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ClientCallbackResource {

    private static final String RESOURCE_CLIENT_CALLBACK_CONFIG = "CLIENT_CALLBACK_CONFIG";

    private static final String OPERATION_CLIENT_CALLBACK_CONFIG_CREATE = "CREATE";
    private static final String OPERATION_CLIENT_CALLBACK_CONFIG_UPDATE = "UPDATE";
    private static final String OPERATION_CLIENT_CALLBACK_CONFIG_GET = "GET";


    private final ClientCallbackService clientCallbackService;

    private final AuthZService authZService;

    @POST
    @Path("/config")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Create new client callback config")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ClientCallbackConfig createConfig(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                             @Valid @NotNull final ClientCallbackConfig clientCallbackConfig) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenantFromProfile(clientCallbackConfig.getProfileId()), RESOURCE_CLIENT_CALLBACK_CONFIG,
                OPERATION_CLIENT_CALLBACK_CONFIG_CREATE, serviceUserPrincipal);

        return clientCallbackService.createClientCallbackConfig(clientCallbackConfig,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                        .getUserId());
    }

    @PUT
    @Path("/config")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Update client callback config")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ClientCallbackConfig updateConfig(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                             @Valid @NotNull final ClientCallbackConfig clientCallbackConfig) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenantFromProfile(clientCallbackConfig.getProfileId()), RESOURCE_CLIENT_CALLBACK_CONFIG,
                OPERATION_CLIENT_CALLBACK_CONFIG_UPDATE, serviceUserPrincipal);

        return clientCallbackService.updateClientCallbackConfig(clientCallbackConfig,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                        .getUserId());
    }

    @GET
    @Path("/config/{profileId}")
    @Operation(summary = "Get client callback config")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ClientCallbackConfig getConfig(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                          @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                          @PathParam("profileId") @NotEmpty final String profileId) {

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenantFromProfile(profileId),
                RESOURCE_CLIENT_CALLBACK_CONFIG, OPERATION_CLIENT_CALLBACK_CONFIG_GET, serviceUserPrincipal);

        return clientCallbackService.getClientCallbackConfig(profileId);
    }
}
