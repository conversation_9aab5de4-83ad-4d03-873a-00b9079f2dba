package com.phonepe.verified.kaizen.caches.key;

import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import javax.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class ProfileCacheKey {

    @NotEmpty
    private String organization;

    @NotEmpty
    private String namespace;

    @NotEmpty
    private String type;

    @NotEmpty
    private String version;

    @NonNull
    private ProfileType profileType;

}
