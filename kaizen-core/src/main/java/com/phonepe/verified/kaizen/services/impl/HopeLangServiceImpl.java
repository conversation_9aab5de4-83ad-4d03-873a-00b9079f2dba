package com.phonepe.verified.kaizen.services.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.hopelangfunctions.CalculateActionErrorCodeCountLowerThanGivenThresholdAndErrorCode;
import com.phonepe.verified.kaizen.hopelangfunctions.ComputeAgeFromDob;
import com.phonepe.verified.kaizen.hopelangfunctions.ComputeAgeFromDobString;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.utils.Utils;
import io.appform.hope.core.Evaluatable;
import io.appform.hope.core.exceptions.errorstrategy.InjectValueErrorHandlingStrategy;
import io.appform.hope.lang.HopeLangEngine;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class HopeLangServiceImpl implements HopeLangService {

    private final HopeLangEngine hopeLangEngine;
    private final LoadingCache<String, Evaluatable> ruleCache;

    @Inject
    public HopeLangServiceImpl() {
        hopeLangEngine = HopeLangEngine.builder()
                .errorHandlingStrategy(new InjectValueErrorHandlingStrategy())
                .registerFunction(ComputeAgeFromDob.class)
                .registerFunction(ComputeAgeFromDobString.class)
                .registerFunction(CalculateActionErrorCodeCountLowerThanGivenThresholdAndErrorCode.class)
                .build();
        ruleCache = Caffeine.newBuilder()
                .maximumSize(10_000)
                .build(hopeLangEngine::parse);
    }

    @Override
    public boolean evaluate(final String rule,
                            final JsonNode context) {

        return hopeLangEngine.evaluate(ruleCache.get(rule), context);
    }

    @Override
    public boolean isParsable(final String rule) {

        if (!Utils.isBalancedBrackets(rule)) {
            return false;
        }

        try {
            hopeLangEngine.parse(rule);
        } catch (final Exception var3) {
            return false;
        }

        return true;
    }
}
