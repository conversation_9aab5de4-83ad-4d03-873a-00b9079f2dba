package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.contexts.AuxiliaryWorkflowContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import java.util.Map;

public interface DynamicUiResponseService {

    void sendSuccessResponseToUi(final String completedActionId,
                                 final UiRequestContext uiRequestContext,
                                 final StandardProfileScreenConfig currentProfileScreenConfig,
                                 final ProfileScreenConfig nextProfileScreenConfig);

    void sendSkipWorkflowStepResponseToUi(final String completedActionId,
                                          final UiRequestContext uiRequestContext,
                                          final StandardProfileScreenConfig currentProfileScreenConfig,
                                          final ProfileScreenConfig nextProfileScreenConfig);

    void sendSkipWorkflowResponseToUi(final UiRequestContext uiRequestContext,
                                      final StandardProfileScreenConfig currentProfileScreenConfig,
                                      final String workflowId);

    void sendWorkflowCompletionResponseToUi(final UiRequestContext uiRequestContext,
                                            final StandardProfileScreenConfig currentProfileScreenConfig,
                                            final String workflowId);

    void sendFailureResponseToUi(final String completedActionId,
                                 final UiRequestContext uiRequestContext,
                                 final StandardProfileScreenConfig currentProfileScreenConfig,
                                 final ProfileScreenConfig nextProfileScreenConfig,
                                 final boolean retryNotAllowedOrRetryExhausted);

    void sendFailureResponseToUi(final StoredAction storedAction,
                                 final UiRequestContext uiRequestContext,
                                 final StandardProfileScreenConfig currentProfileScreenConfig,
                                 final ProfileScreenConfig nextProfileScreenConfig,
                                 final StandardStepActionConfig standardStepActionConfig,
                                 final boolean retryNotAllowedOrRetryExhausted,
                                 final String workflowId);

    void sendFailureResponseToUi(final String actionMappingId,
                                 final String workflowId,
                                 final ActionFailureErrorCode actionFailureErrorCode,
                                 final UiRequestContext uiRequestContext,
                                 final StandardProfileScreenConfig currentProfileScreenConfig,
                                 final AuxiliaryWorkflowContext auxiliaryWorkflowContext,
                                 final boolean retryNotAllowedOrRetryExhausted);

    void sendFailureResponseToUi(final String workflowId,
                                 final ActionFailureErrorCode actionFailureErrorCode,
                                 final UiRequestContext uiRequestContext,
                                 final StandardProfileScreenConfig currentProfileScreenConfig,
                                 final ProfileScreenConfig nextProfileScreenConfig,
                                 final StandardStepActionConfig standardStepActionConfig,
                                 final AuxiliaryWorkflowContext auxiliaryWorkflowContext,
                                 final boolean retryNotAllowedOrRetryExhausted);

    void sendResponseToUi(final UiRequestContext uiRequestContext,
                          final Map<TemplateType, UiResponseConfig> uiResponseConfigMap,
                          final String workflowId);

    void sendResponseToUi(final UiRequestContext uiRequestContext,
                          final Map<TemplateType, UiResponseConfig> uiResponseConfigMap,
                          final AuxiliaryWorkflowContext auxiliaryWorkflowContext,
                          final String workflowId);

    void sendResponseToUi(final String workflowId,
                          final ActionFailureErrorCode actionFailureErrorCode,
                          final UiRequestContext uiRequestContext,
                          final StandardProfileScreenConfig currentProfileScreenConfig,
                          final ProfileScreenConfig nextProfileScreenConfig,
                          final Map<ActionFailureErrorCode, Map<TemplateType, UiResponseConfig>> uiResponseConfig,
                          final AuxiliaryWorkflowContext auxiliaryWorkflowContext,
                          final boolean retryNotAllowedOrRetryExhausted);
}
