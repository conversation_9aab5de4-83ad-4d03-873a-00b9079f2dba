package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.EvaluationRuleResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GetAllEvaluationRuleShadowV2ResponseVisitor implements
        ShadowV2ResponseVisitor<List<EvaluationRuleResponseConfig>, Void> {

    public static final GetAllEvaluationRuleShadowV2ResponseVisitor INSTANCE = new GetAllEvaluationRuleShadowV2ResponseVisitor();

    @Override
    public List<EvaluationRuleResponseConfig> visit(final MoveBackActionShadowV2ResponseConfig moveBackActionShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }


    @Override
    public List<EvaluationRuleResponseConfig> visit(final MoveToSectionShadowV2ResponseConfig moveToSectionUiResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final MoveToSectionAndClearBackstackShadowV2ResponseConfig moveToSectionAndClearBackstackShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final OpenBottomSheetShadowV2ResponseConfig openBottomSheetUiResponseConfig,
                                                    final Void input) {

        final List<EvaluationRuleResponseConfig> leftActionEvaluationRuleResponseList = Objects.isNull(
                openBottomSheetUiResponseConfig.getLeftAction())
                                                                                        ? List.of()
                                                                                        : openBottomSheetUiResponseConfig.getLeftAction()
                                                                                                .accept(INSTANCE,
                                                                                                        input);

        final List<EvaluationRuleResponseConfig> rightActionEvaluationRuleResponseList = Objects.isNull(
                openBottomSheetUiResponseConfig.getRightAction())
                                                                                         ? List.of()
                                                                                         : openBottomSheetUiResponseConfig.getRightAction()
                                                                                                 .accept(INSTANCE,
                                                                                                         input);

        return Stream.concat(leftActionEvaluationRuleResponseList.stream(),
                        rightActionEvaluationRuleResponseList.stream())
                .toList();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final OpenBottomSheetV2ShadowV2ResponseConfig openBottomSheetV2ShadowV2ResponseConfig,
                                                    final Void input) {

        final List<EvaluationRuleResponseConfig> leftActionEvaluationRuleResponseList = Objects.isNull(
                openBottomSheetV2ShadowV2ResponseConfig.getLeftAction())
                                                                                        ? List.of()
                                                                                        : openBottomSheetV2ShadowV2ResponseConfig.getLeftAction()
                                                                                                .accept(INSTANCE,
                                                                                                        input);

        final List<EvaluationRuleResponseConfig> rightActionEvaluationRuleResponseList = Objects.isNull(
                openBottomSheetV2ShadowV2ResponseConfig.getRightAction())
                                                                                         ? List.of()
                                                                                         : openBottomSheetV2ShadowV2ResponseConfig.getRightAction()
                                                                                                 .accept(INSTANCE,
                                                                                                         input);

        return Stream.concat(leftActionEvaluationRuleResponseList.stream(),
                        rightActionEvaluationRuleResponseList.stream())
                .toList();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final OpenStatusPageShadowV2ResponseConfig openStatusPageUiResponseConfig,
                                                    final Void input) {
        return Objects.isNull(openStatusPageUiResponseConfig.getAction())
               ? List.of()
               : openStatusPageUiResponseConfig.getAction()
                       .accept(INSTANCE, input);
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final OtpHurdleShadowV2ResponseConfig otpHurdleShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final ValidationActionShadowV2ResponseConfig validationShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final TerminalActionShadowV2ResponseConfig terminalActionShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final EvaluatedShadowV2ResponseConfig evaluatedShadowV2ResponseConfig,
                                                    final Void input) {
        return evaluatedShadowV2ResponseConfig.getEvaluationRuleResponseConfigs();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final SectionRefreshShadowV2ResponseConfig sectionRefreshShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final RichTextBottomSheetShadowV2ResponseConfig richTextBottomSheetShadowV2ResponseConfig,
                                                    final Void input) {

        final List<EvaluationRuleResponseConfig> leftActionEvaluationRuleResponseList = Objects.isNull(
                richTextBottomSheetShadowV2ResponseConfig.getLeftAction())
                                                                                        ? List.of()
                                                                                        : richTextBottomSheetShadowV2ResponseConfig.getLeftAction()
                                                                                                .accept(INSTANCE,
                                                                                                        input);

        final List<EvaluationRuleResponseConfig> rightActionEvaluationRuleResponseList = Objects.isNull(
                richTextBottomSheetShadowV2ResponseConfig.getRightAction())
                                                                                         ? List.of()
                                                                                         : richTextBottomSheetShadowV2ResponseConfig.getRightAction()
                                                                                                 .accept(INSTANCE,
                                                                                                         input);

        return Stream.concat(leftActionEvaluationRuleResponseList.stream(),
                        rightActionEvaluationRuleResponseList.stream())
                .toList();

    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final OpenGenericDialogActionShadowV2ResponseConfig openGenericDialogShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final UpdateFieldsActionShadowV2ResponseConfig updateFieldsActionShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final ApiCallShadowV2ResponseConfig apiCallShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final OpenWebViewActionShadowV2ResponseConfig openWebViewActionShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final SelfieHurdleShadowV2ResponseConfig selfieHurdleShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final MoveToPreSdkScreenActionShadowV2ResponseConfig moveToPreSdkScreenActionShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final SendSmsHurdleShadowV2ResponseConfig sendSmsHurdleShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final OpenPopupWithTimerShadowV2ResponseConfig openPopupWithTimerShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(MoveToSectionWithScreenMappingIdShadowV2ResponseConfig moveToSectionWithScreenMappingIdShadowV2ResponseConfig, Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(MoveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig moveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig, Void input) {
        return List.of();
    }

    @Override
    public List<EvaluationRuleResponseConfig> visit(final ShadowV2ActionShadowV2ResponseConfig shadowV2ActionShadowV2ResponseConfig,
                                                    final Void input) {
        return List.of();
    }
}