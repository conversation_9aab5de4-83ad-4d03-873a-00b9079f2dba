package com.phonepe.verified.kaizen.storage.aerospike.commands;

import com.aerospike.client.IAerospikeClient;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import com.phonepe.verified.kaizen.models.data.common.WorkflowInitAsyncDetails;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeCommand;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeSet;
import com.phonepe.verified.kaizen.storage.aerospike.keys.WorkflowContextKey;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class WorkflowInitAsyncDataForWorkflowIdCommand extends
        AerospikeCommand<WorkflowContextKey, WorkflowInitAsyncDetails> {

    @Inject
    public WorkflowInitAsyncDataForWorkflowIdCommand(final IAerospikeClient aerospikeClient,
                                                     final AerospikeConfig aerospikeConfig) {
        super(aerospikeClient, aerospikeConfig, AerospikeSet.WORKFLOW_INIT_ASYNC_FOR_WORKFLOW_ID,
                WorkflowInitAsyncDetails.class);
    }
}