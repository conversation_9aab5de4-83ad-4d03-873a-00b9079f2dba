package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.services.impl.ProfileServiceImpl;
import java.util.List;

@Singleton
public class ProfileIdProfileStepsCache extends Cache<String, List<ProfileStep>> {

    private final Provider<ProfileServiceImpl> profileService;

    @Inject
    public ProfileIdProfileStepsCache(final CaffeineCacheConfig caffeineCacheConfig,
                                      final MetricRegistry metricRegistry,
                                      final Provider<ProfileServiceImpl> profileService) {
        super(CacheName.PROFILE_ID_PROFILE_STEPS_CACHE, caffeineCacheConfig, metricRegistry);
        this.profileService = profileService;
    }

    @Override
    protected List<ProfileStep> build(final String profileId) {
        return profileService.get()
                .getProfileStepsFromDb(profileId);
    }
}
