package com.phonepe.verified.kaizen.statemachines.actions.selfiehurdle;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.platform.vision.data.DocumentLabel;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.SuccessStateBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "selfieHurdleSuccessAction")
public class SelfieHurdleSuccessAction extends SuccessStateBaseAction {

    private final ActionMetadataService actionMetadataService;

    @Inject
    public SelfieHurdleSuccessAction(final ActionService actionService,
                                     final ActionRepository actionRepository,
                                     final AutoRetryActionService autoRetryActionService,
                                     final Provider<AutoRetryActionActor> autoRetryActionActorProvider,
                                     final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider,
                                     final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider,
                                     final Provider<WorkflowContextStore> workflowContextStore,
                                     final Provider<EventIngestionActor> eventIngestionActorProvider,
                                     final ActionMetadataService actionMetadataService) {
        super(actionService, actionRepository, autoRetryActionService, autoRetryActionActorProvider,
                handleActionCompletionActorProvider, handlePseudoSuccessActionCompletionActorProvider,
                workflowContextStore, eventIngestionActorProvider);
        this.actionMetadataService = actionMetadataService;
    }

    @Override
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var documentId = stateContext.getExtendedState()
                .get(String.class, String.class);

        final var metaData = StoredDocumentUploadActionMetadata.builder()
                .documentId(documentId)
                .documentType(DocumentType.SELFIE)
                .actionId(storedAction.getActionId())
                .documentLabel(DocumentLabel.FULL.name())
                .build();

        actionMetadataService.save(metaData);
    }
}
