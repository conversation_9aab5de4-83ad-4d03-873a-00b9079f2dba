package com.phonepe.verified.kaizen.guice;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.data.provider.rosey.bundle.RoseyConfigProviderBundle;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import io.dropwizard.Configuration;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class CoreModule<T extends Configuration & KaizenConfig> extends AbstractModule {

    private final RoseyConfigProviderBundle<T> roseyConfigProviderBundle;
    private final ObjectMapper objectMapper;
    private final MetricRegistry metricRegistry;

    @Provides
    public ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    @Provides
    public MetricRegistry metricRegistry() {
        return metricRegistry;
    }

    @Provides
    public DataProvider<T> getDataProvider() {
        return roseyConfigProviderBundle.getDataProvider();
    }
}
