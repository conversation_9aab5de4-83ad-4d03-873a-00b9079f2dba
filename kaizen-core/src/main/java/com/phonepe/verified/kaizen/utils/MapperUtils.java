package com.phonepe.verified.kaizen.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import lombok.experimental.UtilityClass;

@UtilityClass
public class MapperUtils {

    @Inject
    private static ObjectMapper mapper;

    public <T> T deserialize(final byte[] data,
                             final Class<T> valueType) {
        try {
            return mapper.readValue(data, valueType);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T deserialize(final String data,
                             final Class<T> valueType) {
        try {
            return String.class.equals(valueType) && !(data.startsWith("\"") && data.endsWith("\""))
                   ? valueType.cast(data)
                   : mapper.readValue(data, valueType);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T deserialize(final byte[] data,
                             final TypeReference<T> typeReference) {
        try {
            return mapper.readValue(data, typeReference);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T deserialize(final String data,
                             final TypeReference<T> typeReference) {
        try {
            return mapper.readValue(data, typeReference);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DESERIALIZATION_ERROR, e);
        }
    }

    public String serializeToString(final Object data) {
        try {
            return mapper.writeValueAsString(data);
        } catch (final JsonProcessingException e) {
            throw KaizenException.propagate(KaizenResponseCode.SERIALIZATION_ERROR, e);
        }
    }

    public JsonNode convertToJsonNode(final Object data) {
        return mapper.valueToTree(data);
    }

    public byte[] serializeToBytes(final Object data) {
        try {
            return mapper.writeValueAsBytes(data);
        } catch (final JsonProcessingException e) {
            throw KaizenException.propagate(KaizenResponseCode.SERIALIZATION_ERROR, e);
        }
    }

    public <T> T convertValue(final Object fromValue,
                              final Class<T> toValueType) {
        try {
            return mapper.convertValue(fromValue, toValueType);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T convertValue(final Object fromValue,
                              final TypeReference<T> typeReference) {
        try {
            return mapper.convertValue(fromValue, typeReference);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T clone(final T obj,
                       final TypeReference<T> typeReference) {
        try {
            return mapper.readValue(mapper.writeValueAsBytes(obj), typeReference);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T clone(final T obj,
                       final Class<T> clazz) {
        try {
            return mapper.readValue(mapper.writeValueAsBytes(obj), clazz);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DESERIALIZATION_ERROR, e);
        }
    }
}
