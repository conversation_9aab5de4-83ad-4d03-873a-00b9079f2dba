package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.caches.impl.SessionManagementConfigCache;
import com.phonepe.verified.kaizen.caches.key.SessionManagementConfigCacheKey;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.session.SessionManagementConfig;
import com.phonepe.verified.kaizen.models.requests.session.CreateClientSessionRequest;
import com.phonepe.verified.kaizen.models.requests.session.EndClientSessionRequest;
import com.phonepe.verified.kaizen.models.responses.GenericErrorResponse;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.SessionManagementConfigToDbVisitor;
import com.phonepe.verified.kaizen.services.visitors.SessionManagementDbToConfigVisitor;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ClientSessionCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.session.ClientSessionData;
import com.phonepe.verified.kaizen.storage.aerospike.data.sessioncontext.SessionContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.SessionKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredSessionManagementConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredSessionManagementConfig.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.SessionManagementConfigRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BooleanSupplier;
import javax.ws.rs.ClientErrorException;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class SessionManagementServiceImpl implements SessionManagementService {

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final HandleBarsService handleBarsService;

    private final ClientSessionCommand clientSessionCommand;

    private final SessionManagementConfigRepository sessionManagementConfigRepository;

    private final SessionManagementConfigCache sessionManagementConfigCache;

    @Override
    public Optional<StoredSessionManagementConfig> getSessionManagementConfigFromDb(final String profileId,
                                                                                    final String sourceType) {
        return sessionManagementConfigRepository.select(profileId, sourceType);
    }

    @Override
    public boolean createSession(final CreateClientSessionRequest createClientSessionRequest) {
        final var clientSessionKey = SessionKey.builder()
                .principal(createClientSessionRequest.getPrincipal())
                .build();

        //do we want fetch workflowId and get entityId and entityType from it and store in aerospike?
        final var clientSessionData = ClientSessionData.builder()
                .sessionToken(createClientSessionRequest.getSessionToken())
                .sessionEndTimeInEpochMs(createClientSessionRequest.getSessionEndTimeInEpochMs())
                .workflowId(createClientSessionRequest.getWorkflowId())
                .build();

        //not using strict save to enable override
        clientSessionCommand.save(clientSessionKey, clientSessionData);
        return true;
    }

    @Override
    public boolean endSession(final String requestId,
                              final String userId,
                              final EndClientSessionRequest endClientSessionRequest) {
        //todo raise event
        final var clientSessionKey = SessionKey.builder()
                .principal(endClientSessionRequest.getPrincipal())
                .build();

        clientSessionCommand.delete(clientSessionKey);

        return true;
    }

    private boolean validateClientSession(final ClientSessionData clientSessionData,
                                          final String sessionToken,
                                          final boolean validateTimer,
                                          final boolean validateToken) {
        if (validateTimer && clientSessionData.getSessionEndTimeInEpochMs() < System.currentTimeMillis()) {
            return false;
        }

        return !validateToken || clientSessionData.getSessionToken()
                .equals(sessionToken);
    }

    private boolean validateSession(final StoredWorkflow storedWorkflow,
                                    final RequestInfo requestInfo,
                                    final UserDetails userDetails,
                                    final String sessionToken) {
        final var sessionContext = buildSessionContext(storedWorkflow, requestInfo, userDetails, sessionToken);

        final var storedSessionManagementConfigOptional = sessionManagementConfigCache.get(
                SessionManagementConfigCacheKey.builder()
                        .profileId(storedWorkflow.getProfileId())
                        .sourceType(requestInfo.getSourceType())
                        .build());

        if (storedSessionManagementConfigOptional.isPresent() && !storedSessionManagementConfigOptional.get()
                .isDisabled()) {
            final var storedSessionManagementConfig = storedSessionManagementConfigOptional.get();
            final var principal = storedSessionManagementConfig.accept(
                    clientManagedSession -> handleBarsService.transform(clientManagedSession.getPrincipalBuilder(),
                            sessionContext));

            final var clientSessionData = getClientSessionDataFromAS(principal);

            if (Objects.nonNull(clientSessionData) && clientSessionData.getWorkflowId()
                    .equals(storedWorkflow.getWorkflowId())) {

                return storedSessionManagementConfig.accept(
                        clientManagedSession -> validateClientSession(clientSessionData, sessionToken,
                                clientManagedSession.isValidateTimer(), clientManagedSession.isValidateToken()));

            } else {
                //if not found in Aerospike that means session expired
                return false;
            }
        }

        //no session management required. returning true
        return true;
    }

    private SessionContext buildSessionContext(final StoredWorkflow storedWorkflow,
                                               final RequestInfo requestInfo,
                                               final UserDetails userDetails,
                                               final String sessionToken) {
        return SessionContext.builder()
                .entityId(storedWorkflow.getEntityId())
                .entityType(storedWorkflow.getEntityType())
                .phoneNumber(storedWorkflow.getPhoneNumber())
                .profileId(storedWorkflow.getProfileId())
                .requestInfo(requestInfo)
                .userDetails(userDetails)
                .sessionToken(sessionToken)
                .workflowId(storedWorkflow.getWorkflowId())
                .build();

    }

    private boolean validateSession(final String workflowId,
                                    final RequestInfo requestInfo,
                                    final UserDetails userDetails,
                                    final String sessionToken) {
        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);
        return validateSession(storedWorkflow, requestInfo, userDetails, sessionToken);
    }

    @Override
    public void validateSessionAndThrowException(final String workflowId,
                                                 final RequestInfo requestInfo,
                                                 final UserDetails userDetails,
                                                 final String sessionToken) {
        validateSessionAndThrowException(() -> validateSession(workflowId, requestInfo, userDetails, sessionToken),
                workflowId);
    }


    @Override
    public void validateSessionAndThrowException(final StoredWorkflow storedWorkflow,
                                                 final RequestInfo requestInfo,
                                                 final UserDetails userDetails,
                                                 final String sessionToken) {
        validateSessionAndThrowException(() -> validateSession(storedWorkflow, requestInfo, userDetails, sessionToken),
                storedWorkflow.getWorkflowId());
    }

    private void validateSessionAndThrowException(final BooleanSupplier booleanSupplier,
                                                  final String workflowId) {
        final var isValidSession = booleanSupplier.getAsBoolean();
        if (!isValidSession) {
            throwSessionExpiredResponse(workflowId);
        }
    }

    private void throwSessionExpiredResponse(final String workflowId) {
        throw new ClientErrorException(
                Response.status(KaizenResponseCode.SMS_VERIFICATION_EXPIRED_PVSDK.getStatusCode())
                        .entity(GenericErrorResponse.builder()
                                .workflowId(workflowId)
                                .code(KaizenResponseCode.SMS_VERIFICATION_EXPIRED_PVSDK.name())
                                .message(KaizenResponseCode.SMS_VERIFICATION_EXPIRED_PVSDK.getMessage())
                                .build())
                        .build());
    }


    @Override
    public Optional<StoredSessionManagementConfig> createSessionConfig(final SessionManagementConfig createSessionConfigRequest,
                                                                       final String userId) {

        // Validation for profile exists
        profileService.get(createSessionConfigRequest.getProfileId(), false);

        return sessionManagementConfigRepository.save(
                createSessionConfigRequest.accept(SessionManagementConfigToDbVisitor.INSTANCE, userId));
    }

    @Override
    @SuppressWarnings("java:S2201")
    public void updateSessionConfig(final SessionManagementConfig updateSessionConfigRequest,
                                    final String userId) {
        sessionManagementConfigRepository.select(updateSessionConfigRequest.getProfileId(),
                        updateSessionConfigRequest.getSourceType())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.SESSION_CONFIG_NOT_FOUND,
                        Map.of(Constants.MESSAGE, "Unable to find a session config for the profileId", Fields.profileId,
                                updateSessionConfigRequest.getProfileId())));

        sessionManagementConfigRepository.save(
                updateSessionConfigRequest.accept(SessionManagementConfigToDbVisitor.INSTANCE, userId));
    }

    @Override
    public void toggleDisabled(final String profileId,
                               final String sourceType,
                               final boolean disabled) {
        final var storedSessionConfig = sessionManagementConfigRepository.select(profileId, sourceType)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.SESSION_CONFIG_NOT_FOUND,
                        Map.of(Constants.MESSAGE, "Unable to find session config for given inputs", Fields.profileId,
                                profileId)));

        storedSessionConfig.setDisabled(disabled);

        sessionManagementConfigRepository.save(storedSessionConfig);
    }

    @Override
    public SessionManagementConfig getSessionConfig(final String profileId,
                                                    final String sourceType) {
        final var storedSessionManagementConfig = sessionManagementConfigRepository.select(profileId, sourceType)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.SESSION_CONFIG_NOT_FOUND,
                        Map.of(Constants.MESSAGE, "Unable to find a session config for the profileId", Fields.profileId,
                                profileId)));

        return storedSessionManagementConfig.accept(SessionManagementDbToConfigVisitor.INSTANCE);
    }

    private ClientSessionData getClientSessionDataFromAS(final String principal) {
        final var clientSessionKey = SessionKey.builder()
                .principal(principal)
                .build();

        return clientSessionCommand.get(clientSessionKey);
    }
}
