package com.phonepe.verified.kaizen.utils;

import com.phonepe.gandalf.models.authn.UserType;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowInfo;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import io.dropwizard.util.Strings;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

@Slf4j
@UtilityClass
public class Utils {

    public boolean isNullOrEmpty(final byte[] b) {
        return b == null || b.length == 0;
    }

    public <T> boolean isNullOrEmpty(final Collection<T> collection) {
        return null == collection || collection.isEmpty();
    }

    public <K, V> boolean isNullOrEmpty(final Map<K, V> map) {
        return null == map || map.isEmpty();
    }

    public Entry<String, String> convertToMapEntry(final String key,
                                                   final String value) {

        return Objects.nonNull(value)
               ? Map.entry(key, value)
               : null;
    }

    public Entry<String, String> convertToMapEntryString(final String key,
                                                         final Object value) {

        return Objects.nonNull(value)
               ? Map.entry(key, value.toString())
               : null;
    }

    public UserDetails buildHumanUserDetails(final String userId) {
        return UserDetails.builder()
                .userType(UserType.USER)
                .userId(userId)
                .build();
    }

    public UserDetails buildSystemUserDetails(final String userId) {
        return UserDetails.builder()
                .userType(UserType.SYSTEM)
                .userId(userId)
                .build();
    }

    public boolean isBalancedBrackets(final String expression) {

        var balance = 0;

        for (int position = 0; position < expression.length(); position++) {
            final char charAtPosition = expression.charAt(position);

            if (charAtPosition == '(') {
                balance += 1;
            } else if (charAtPosition == ')') {
                balance -= 1;
                if (balance < 0) {
                    return false;
                }
            }
        }

        return balance == 0;
    }

    public String getMaskedIdentifierNumber(final String identifierNumber) {

        if (!Strings.isNullOrEmpty(identifierNumber)
                && identifierNumber.length() > Constants.MASKED_IDENTIFIER_DEFAULT_LENGTH) {

            return StringUtils.repeat("X", identifierNumber.length() - Constants.MASKED_IDENTIFIER_DEFAULT_LENGTH)
                    + identifierNumber.substring(
                    identifierNumber.length() - Constants.MASKED_IDENTIFIER_DEFAULT_LENGTH);
        }
        return identifierNumber;
    }

    public EventType getEventType(final TransitionState currentState) {

        return switch (currentState) {
            case SUCCESS -> EventType.WORKFLOW_SUCCESS;
            case PSEUDO_SUCCESS -> EventType.WORKFLOW_PSEUDO_SUCCESS;
            case IN_PROGRESS -> EventType.WORKFLOW_IN_PROGRESS;
            case INITIAL_ACTION_IN_PROGRESS -> EventType.WORKFLOW_INITIAL_ACTION_IN_PROGRESS;
            case READY -> EventType.WORKFLOW_READY;
            case FAILURE -> EventType.WORKFLOW_FAILURE;
            case CREATED -> EventType.VERIFICATION_WORKFLOW_INIT;
            case ABORTED -> EventType.WORKFLOW_ABORT;
            case SKIPPED -> EventType.WORKFLOW_SKIPPED;
            case AUTO_SKIPPED -> EventType.WORKFLOW_AUTO_SKIPPED;
            case DISCARDED -> EventType.WORKFLOW_DISCARD;
            case INVALIDATED -> throw KaizenException.create(KaizenResponseCode.TRANSITION_NOT_ALLOWED,
                    Map.of(Constants.MESSAGE, "Workflow can't be in invalidated state", Fields.currentState,
                            currentState));
            case PURGED -> EventType.WORKFLOW_PURGED;
        };
    }

    public boolean hasNewerWorkflowSteps(final StoredWorkflowStep currentWorkflowStep,
                                         final List<StoredWorkflowStep> workflowSteps) {

        //check if previous workflowSteps invalidated and might have new values than time of redirectionId generation
        return workflowSteps.stream()
                .filter(workflowStep -> !Objects.equals(workflowStep.getWorkflowStepId(),
                        currentWorkflowStep.getWorkflowStepId()))
                .anyMatch(workflowStep -> workflowStep.getLastUpdatedAt()
                        .isAfter(currentWorkflowStep.getCreatedAt()));
    }

    public WorkflowInfo convertToWorkflowInfo(final StoredWorkflow storedWorkflow) {
        return WorkflowInfo.builder()
                .workflowId(storedWorkflow.getWorkflowId())
                .profileId(storedWorkflow.getProfileId())
                .entityId(storedWorkflow.getEntityId())
                .entityType(storedWorkflow.getEntityType())
                .tag(storedWorkflow.getTag())
                .createdAt(storedWorkflow.getCreatedAt())
                .lastUpdatedAt(storedWorkflow.getLastUpdatedAt())
                .build();
    }
}