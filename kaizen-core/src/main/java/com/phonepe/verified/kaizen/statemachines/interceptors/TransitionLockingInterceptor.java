package com.phonepe.verified.kaizen.statemachines.interceptors;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.statemachines.StateMachineContextKeys;
import com.phonepe.verified.kaizen.storage.aerospike.commands.TransitionLockCommand;
import com.phonepe.verified.kaizen.storage.aerospike.keys.TransitionLockKey;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.support.DefaultStateMachineExecutor;
import org.springframework.statemachine.support.StateMachineInterceptor;
import org.springframework.statemachine.support.StateMachineInterceptorAdapter;

@RequiredArgsConstructor
public class TransitionLockingInterceptor<S, E> extends StateMachineInterceptorAdapter<S, E> implements
        StateMachineInterceptor<S, E> {

    private final TransitionLockCommand transitionLockCommand;

    /**
     * This method is invoked from {@link DefaultStateMachineExecutor} before Action is triggered as
     * a part of Event processing and Transition from current state to next state
     * <p>
     * If this method returns null or throws Exception then Transition is aborted and Action will
     * not be triggered by StateMachine
     * <p>
     * Once preTransition is successful it is guaranteed that postTransition will be triggered even
     * if the Action fails with Exception
     * <p>
     * We take distributed transition lock via AeroSpike before we start Transition and once it's
     * completed we release the distributed lock
     */
    @Override
    public StateContext<S, E> preTransition(final StateContext<S, E> stateContext) {

        final var variables = stateContext.getExtendedState()
                .getVariables();
        variables.put(StateMachineContextKeys.TARGET_STATE, stateContext.getTarget()
                .getId());

        final var transitionLockKey = stateContext.getExtendedState()
                .get(TransitionLockKey.class, TransitionLockKey.class);

        if (Objects.isNull(transitionLockKey)) {
            final var error = KaizenException.create(KaizenResponseCode.TRANSITION_LOCK_KEY_NOT_FOUND, Map.of());
            stateContext.getStateMachine()
                    .setStateMachineError(error);
            return null;
        }

        try {
            transitionLockCommand.strictSave(transitionLockKey, transitionLockKey.getTransitionKey());
        } catch (final Exception e) {
            final var error = KaizenException.create(KaizenResponseCode.UNABLE_TO_ACQUIRE_TRANSITION_LOCK, e, Map.of());
            stateContext.getStateMachine()
                    .setStateMachineError(error);
            return null;
        }

        return stateContext;
    }

    @Override
    public StateContext<S, E> postTransition(final StateContext<S, E> stateContext) {

        final var transitionLockKey = stateContext.getExtendedState()
                .get(TransitionLockKey.class, TransitionLockKey.class);

        transitionLockCommand.delete(transitionLockKey);

        return stateContext;
    }
}
