package com.phonepe.verified.kaizen.statemachines.actions.persistforensics;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.platform.atlas.model.common.Place;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.persistforensics.PersistForensicsActionContext;
import com.phonepe.verified.kaizen.models.data.common.ForensicFields;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.GeolocationService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.commands.UiRequestContextCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.KeyValuePairsActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.aerospike.keys.UiRequestContextKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import java.util.Map;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "createPersistForensicsAction")
public class CreatePersistForensicsAction extends CreateEntryBaseAction {

    private final HopeLangService hopeLangService;
    private final GeolocationService geolocationService;
    private final UiRequestContextCommand uiRequestContextCommand;
    private final ActionMetadataStoreCommand actionMetadataStoreCommand;
    private final Provider<ActionExecutorActor> actionExecutorActorProvider;

    @Inject
    public CreatePersistForensicsAction(final WorkflowService workflowService,
                                        final HopeLangService hopeLangService,
                                        final ActionService actionService,
                                        final ClockworkClient clockworkClient,
                                        final ActionRepository actionRepository,
                                        final GeolocationService geolocationService,
                                        final WorkflowStepService workflowStepService,
                                        final DataProvider<KaizenConfig> appConfigDataProvider,
                                        final UiRequestContextCommand uiRequestContextCommand,
                                        final Provider<WorkflowContextStore> workflowContextStore,
                                        final ActionMetadataStoreCommand actionMetadataStoreCommand,
                                        final Provider<EventIngestionActor> eventIngestionActorProvider,
                                        final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                        final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.hopeLangService = hopeLangService;
        this.geolocationService = geolocationService;
        this.uiRequestContextCommand = uiRequestContextCommand;
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
        this.actionExecutorActorProvider = actionExecutorActorProvider;
    }

    @Override
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var uiRequestContext = uiRequestContextCommand.get(UiRequestContextKey.builder()
                .workflowStepId(storedAction.getWorkflowStepId())
                .build());
        final var requestInfo = uiRequestContext.getRequestInfo();

        final var forensicsKeyValuePairs = this.getForensicsFields(requestInfo);

        final var actionMetadata = KeyValuePairsActionMetadata.builder()
                .actionId(storedAction.getActionId())
                .keyValuePairs(forensicsKeyValuePairs)
                .build();
        actionMetadataStoreCommand.save(ActionMetadataStoreKey.builder()
                .actionId(storedAction.getActionId())
                .build(), actionMetadata);
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {
        final var persistForensicsActionContext = stateContext.getExtendedState()
                .get(StepActionContext.class, PersistForensicsActionContext.class);
        this.validateForensicsAndTriggerNextAction(storedAction, persistForensicsActionContext);
    }

    private Map<String, String> getForensicsFields(@NonNull final RequestInfo requestInfo) {
        final var placeDetails = geolocationService.fetchGeolocationDetails(requestInfo.getIp());

        return Map.ofEntries(Map.entry(ForensicFields.IP, requestInfo.getIp()), Map.entry(ForensicFields.LATITUDE,
                String.valueOf(placeDetails.map(Place::getLatitude)
                        .orElse(0.0))), Map.entry(ForensicFields.LONGITUDE, String.valueOf(
                placeDetails.map(Place::getLongitude)
                        .orElse(0.0))), Map.entry(ForensicFields.CITY, placeDetails.map(Place::getCity)
                .orElse(StringUtils.EMPTY)), Map.entry(ForensicFields.STATE, placeDetails.map(Place::getState)
                .orElse(StringUtils.EMPTY)), Map.entry(ForensicFields.COUNTRY, placeDetails.map(Place::getCountry)
                .orElse(StringUtils.EMPTY)), Map.entry(ForensicFields.POSTAL_CODE, placeDetails.map(Place::getPincode)
                .orElse(StringUtils.EMPTY)));
    }

    @SneakyThrows
    private void validateForensicsAndTriggerNextAction(final StoredAction storedAction,
                                                       final PersistForensicsActionContext persistForensicsActionContext) {
        final var keyValuePairsActionMetadata = (KeyValuePairsActionMetadata) actionMetadataStoreCommand.get(
                ActionMetadataStoreKey.builder()
                        .actionId(storedAction.getActionId())
                        .build());
        final var jsonNode = MapperUtils.convertToJsonNode(keyValuePairsActionMetadata);
        final var matchedRule = persistForensicsActionContext.getForensicsValidationRules()
                .stream()
                .filter(forensicsValidationRule -> hopeLangService.evaluate(forensicsValidationRule.getEvaluationRule(),
                        jsonNode))
                .findFirst();
        if (matchedRule.isPresent()) {
            final var actionFailureErrorCode = matchedRule.get()
                    .getErrorCode();
            actionExecutorActorProvider.get()
                    .publish(ActionExecutionMessage.builder()
                            .actionId(storedAction.getActionId())
                            .eventToTrigger(Events.FAIL_FORENSICS)
                            .userDetails(Constants.PVCORE_SYSTEM_USER)
                            .actionFailureErrorCode(actionFailureErrorCode)
                            .build());
        } else {
            actionExecutorActorProvider.get()
                    .publish(ActionExecutionMessage.builder()
                            .actionId(storedAction.getActionId())
                            .eventToTrigger(Events.PERSIST_FORENSICS_DATA)
                            .userDetails(Constants.PVCORE_SYSTEM_USER)
                            .build());
        }
    }
}
