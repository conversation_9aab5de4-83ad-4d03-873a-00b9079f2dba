package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.SelfieActionContext;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.requests.SelfieDocumentIdSubmitRequest;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.SelfieHurdleService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class SelfieHurdleServiceImpl implements SelfieHurdleService {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final DocumentService documentService;

    private final WorkflowStepService workflowStepService;

    private final DynamicUiResponseService dynamicUiResponseService;


    @Override
    public void triggerSelfieHurdle(final String actionId) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedAction.getWorkflowStepId());

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var selfieActionContext = (SelfieActionContext) actionService.extractStepActionContext(
                storedAction.getActionId(), profileStep.getProfileScreenConfig());

        dynamicUiResponseService.sendResponseToUi(uiRequestContext, selfieActionContext.getSelfieResponseMap(),
                storedWorkflowStep.getWorkflowId());

    }

    @Override
    @SneakyThrows
    public void submitSelfieDocumentId(final String actionId,
                                       final String intent,
                                       final RequestInfo requestInfo,
                                       final long componentKitVersion,
                                       final UserDetails userDetails,
                                       final SelfieDocumentIdSubmitRequest selfieDocumentIdSubmitRequest) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        if (!CompletionState.NON_COMPLETED_STATES.contains(storedAction.getCompletionState())) {
            return;
        }

        if (!documentService.isDocumentPresentOnDocStore(selfieDocumentIdSubmitRequest.getDocumentId())) {
            throw KaizenException.create(KaizenResponseCode.DOCUMENT_NOT_FOUND,
                    Map.of("documentIds", selfieDocumentIdSubmitRequest.getDocumentId()));
        }

        updateUiRequestContext(intent, requestInfo, componentKitVersion, storedAction);

        final var auxiliaryStateMachineTransitionMap = new HashMap<>();

        auxiliaryStateMachineTransitionMap.put(String.class, selfieDocumentIdSubmitRequest.getDocumentId());

        actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                storedAction.getStateMachineVersion(), Events.SELFIE_HURDLE_COMPLETE,
                Constants.EMPTY_TRANSITION_CONTEXT, userDetails, auxiliaryStateMachineTransitionMap);

    }

    private void updateUiRequestContext(final String intent,
                                        final RequestInfo requestInfo,
                                        final long componentKitVersion,
                                        final StoredAction storedAction) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedWorkflowStep.getWorkflowStepId());

        final var sectionInputData = Objects.nonNull(uiRequestContext)
                                     ? ((ShadowV2UiRequestContext) uiRequestContext).getSectionInputData()
                                     : null;

        final var apiVersion = Objects.nonNull(uiRequestContext)
                               ? uiRequestContext.getApiVersion()
                               : null;

        workflowStepService.getUiRequestContextAndSaveNewUiRequestContextWithExistingRequestDetails(
                storedWorkflowStep.getWorkflowStepId(), sectionInputData, requestInfo, componentKitVersion,
                storedWorkflow.getEntityId(), intent, apiVersion, storedWorkflow.getWorkflowId());
    }
}
