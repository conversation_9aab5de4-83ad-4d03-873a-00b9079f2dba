package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetailVisitor;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredConsentDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredDocumentUploadDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredKeyValueDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredOtpDetails;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.details.Detail;
import com.phonepe.verified.kaizen.models.responses.details.details.ConsentDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.DocumentUploadDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.KeyValueDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.OtpDetails;
import com.phonepe.verified.kaizen.services.visitors.StoredActionMetadataToDetailVisitor.StoredActionMetadataToDetailVisitorData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredConsentActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredKeyValueMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredOtpHurdleActionMetaData;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class StoredActionMetadataToDetailVisitor implements
        RequiredDetailVisitor<List<Detail>, StoredActionMetadataToDetailVisitorData> {

    @Override
    public List<Detail> visit(final RequiredKeyValueDetail requiredKeyValueDetail,
                              final StoredActionMetadataToDetailVisitorData storedActionMetadataToDetailVisitorData) {

        return storedActionMetadataToDetailVisitorData.mapOfActionMetadataTypeToListOfStoredActionMetadata()
                .getOrDefault(ActionMetadataType.KEY_VALUE, List.of())
                .stream()
                .filter(StoredKeyValueMetadata.class::isInstance)
                .map(StoredKeyValueMetadata.class::cast)
                .filter(storedKeyValueMetadata -> requiredKeyValueDetail.getRequiredKeys()
                        .contains(storedKeyValueMetadata.getKey()))
                .map(storedKeyValueMetadata -> (Detail) KeyValueDetail.builder()
                        .workflowId(storedActionMetadataToDetailVisitorData.workflowId())
                        .workflowStepId(storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap.get(
                                        storedKeyValueMetadata.getActionId())
                                .getWorkflowStepId())
                        .actionMappingId(storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap.get(
                                        storedKeyValueMetadata.getActionId())
                                .getActionMappingId())
                        .actionId(storedKeyValueMetadata.getActionId())
                        .actionCompletionState(getActionState(storedActionMetadataToDetailVisitorData,
                                storedKeyValueMetadata.getActionId()))
                        .key(storedKeyValueMetadata.getKey())
                        .value(storedKeyValueMetadata.getValue())
                        .createdAt(storedKeyValueMetadata.getCreatedAt())
                        .lastUpdatedAt(storedKeyValueMetadata.getLastUpdatedAt())
                        .build())
                .toList();
    }

    @Override
    public List<Detail> visit(final RequiredOtpDetails requiredOtpDetails,
                              final StoredActionMetadataToDetailVisitorData storedActionMetadataToDetailVisitorData) {

        return storedActionMetadataToDetailVisitorData.mapOfActionMetadataTypeToListOfStoredActionMetadata()
                .getOrDefault(ActionMetadataType.OTP_DETAILS, List.of())
                .stream()
                .filter(StoredOtpHurdleActionMetaData.class::isInstance)
                .map(StoredOtpHurdleActionMetaData.class::cast)
                .map(storedOtpHurdleActionMetaData -> (Detail) OtpDetails.builder()
                        .workflowId(storedActionMetadataToDetailVisitorData.workflowId())
                        .workflowStepId(storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap.get(
                                        storedOtpHurdleActionMetaData.getActionId())
                                .getWorkflowStepId())
                        .actionMappingId(storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap.get(
                                        storedOtpHurdleActionMetaData.getActionId())
                                .getActionMappingId())
                        .actionId(storedOtpHurdleActionMetaData.getActionId())
                        .actionCompletionState(getActionState(storedActionMetadataToDetailVisitorData,
                                storedOtpHurdleActionMetaData.getActionId()))
                        .otpReferenceId(storedOtpHurdleActionMetaData.getOtpReferenceId())
                        .createdAt(storedOtpHurdleActionMetaData.getCreatedAt())
                        .lastUpdatedAt(storedOtpHurdleActionMetaData.getLastUpdatedAt())
                        .build())
                .toList();
    }

    @Override
    public List<Detail> visit(final RequiredDocumentUploadDetail requiredDocumentUploadDetail,
                              final StoredActionMetadataToDetailVisitorData storedActionMetadataToDetailVisitorData) {

        return storedActionMetadataToDetailVisitorData.mapOfActionMetadataTypeToListOfStoredActionMetadata()
                .getOrDefault(ActionMetadataType.DOCUMENT_UPLOAD, List.of())
                .stream()
                .filter(StoredDocumentUploadActionMetadata.class::isInstance)
                .map(StoredDocumentUploadActionMetadata.class::cast)
                .filter(storedDocumentUploadActionMetadata -> requiredDocumentUploadDetail.getRequiredDocumentTypes()
                        .contains(storedDocumentUploadActionMetadata.getDocumentType()))
                .map(storedDocumentUploadActionMetadata -> (Detail) DocumentUploadDetail.builder()
                        .workflowId(storedActionMetadataToDetailVisitorData.workflowId())
                        .workflowStepId(storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap.get(
                                        storedDocumentUploadActionMetadata.getActionId())
                                .getWorkflowStepId())
                        .actionMappingId(storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap.get(
                                        storedDocumentUploadActionMetadata.getActionId())
                                .getActionMappingId())
                        .actionId(storedDocumentUploadActionMetadata.getActionId())
                        .actionCompletionState(getActionState(storedActionMetadataToDetailVisitorData,
                                storedDocumentUploadActionMetadata.getActionId()))
                        .documentId(storedDocumentUploadActionMetadata.getDocumentId())
                        .documentType(storedDocumentUploadActionMetadata.getDocumentType())
                        .documentLabel(storedDocumentUploadActionMetadata.getDocumentLabel())
                        .createdAt(storedDocumentUploadActionMetadata.getCreatedAt())
                        .lastUpdatedAt(storedDocumentUploadActionMetadata.getLastUpdatedAt())
                        .build())
                .toList();
    }

    @Override
    public List<Detail> visit(final RequiredConsentDetail requiredConsentDetail,
                              final StoredActionMetadataToDetailVisitorData storedActionMetadataToDetailVisitorData) {

        return storedActionMetadataToDetailVisitorData.mapOfActionMetadataTypeToListOfStoredActionMetadata()
                .getOrDefault(ActionMetadataType.CONSENT, List.of())
                .stream()
                .filter(StoredConsentActionMetadata.class::isInstance)
                .map(StoredConsentActionMetadata.class::cast)
                .filter(storedEncryptedKeyValueMetadata -> requiredConsentDetail.getRequiredConsentTypes()
                        .contains(storedEncryptedKeyValueMetadata.getConsentType()))
                .map(storedConsentActionMetadata -> (Detail) ConsentDetail.builder()
                        .workflowId(storedActionMetadataToDetailVisitorData.workflowId())
                        .workflowStepId(storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap.get(
                                        storedConsentActionMetadata.getActionId())
                                .getWorkflowStepId())
                        .actionMappingId(storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap.get(
                                        storedConsentActionMetadata.getActionId())
                                .getActionMappingId())
                        .actionId(storedConsentActionMetadata.getActionId())
                        .actionCompletionState(getActionState(storedActionMetadataToDetailVisitorData,
                                storedConsentActionMetadata.getActionId()))
                        .consentGranted(Boolean.parseBoolean(storedConsentActionMetadata.getConsentGranted()))
                        .consentGrantedBy(storedConsentActionMetadata.getConsentGrantedBy())
                        .consentPrivacyPolicyUrl(storedConsentActionMetadata.getConsentDetailsUrl())
                        .consentType(storedConsentActionMetadata.getConsentType())
                        .sourcePlatform(storedConsentActionMetadata.getSourcePlatform())
                        .sourceType(storedConsentActionMetadata.getSourceType())
                        .sourceVersion(storedConsentActionMetadata.getSourceVersion())
                        .createdAt(storedConsentActionMetadata.getCreatedAt())
                        .lastUpdatedAt(storedConsentActionMetadata.getLastUpdatedAt())
                        .build())
                .toList();
    }

    private State getActionState(final StoredActionMetadataToDetailVisitorData storedActionMetadataToDetailVisitorData,
                                 final String actionId) {

        if (!storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap()
                .containsKey(actionId)) {
            return null;
        }

        final var storedAction = storedActionMetadataToDetailVisitorData.actionIdToStoredActionMap()
                .get(actionId);

        if (Objects.isNull(storedAction.getCompletionState())) {
            return null;
        }

        return State.valueOf(storedAction.getCompletionState()
                .name());
    }

    public record StoredActionMetadataToDetailVisitorData(String entityId, String workflowId,
                                                          Map<String, StoredAction> actionIdToStoredActionMap,
                                                          Map<ActionMetadataType, List<StoredActionMetadata>> mapOfActionMetadataTypeToListOfStoredActionMetadata) {

    }
}
