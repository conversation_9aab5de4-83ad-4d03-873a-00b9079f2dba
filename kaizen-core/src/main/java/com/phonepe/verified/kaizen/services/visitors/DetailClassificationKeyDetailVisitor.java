package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.responses.details.DetailVisitor;
import com.phonepe.verified.kaizen.models.responses.details.details.ConsentDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.DocumentUploadDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.KeyValueDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.OtpDetails;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DetailClassificationKeyDetailVisitor implements DetailVisitor<String> {

    public static final DetailClassificationKeyDetailVisitor INSTANCE = new DetailClassificationKeyDetailVisitor();

    @Override
    public String visit(final KeyValueDetail keyValueDetail) {
        return keyValueDetail.getType()
                .name() + keyValueDetail.getKey();
    }

    @Override
    public String visit(final DocumentUploadDetail documentUploadDetail) {
        return documentUploadDetail.getType()
                .name() + documentUploadDetail.getDocumentType()
                .name() + documentUploadDetail.getDocumentLabel();
    }

    @Override
    public String visit(final ConsentDetail consentDetail) {
        return consentDetail.getType()
                .name() + consentDetail.getConsentType();
    }

    @Override
    public String visit(final OtpDetails otpDetails) {
        return otpDetails.getType()
                .name();
    }
}
