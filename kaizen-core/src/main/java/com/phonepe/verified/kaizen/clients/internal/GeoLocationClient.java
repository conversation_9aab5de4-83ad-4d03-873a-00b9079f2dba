package com.phonepe.verified.kaizen.clients.internal;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.atlas.model.places.dto.FetchPlaceResponse;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class GeoLocationClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    private final OlympusIMClient olympusIMClient;

    @Inject
    public GeoLocationClient(final HttpClientRegistry httpClientRegistry,
                             final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(
                ClientIds.GEOLOCATION);
        this.olympusIMClient = olympusIMClient;
    }

    public FetchPlaceResponse fetchLocationInfoViaIpAddress(final String ipAddress) {
        final var url = String.format("/v2/geolocation/ipAddress/%s", ipAddress);

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "fetchLocationInfoViaIpAddress", url,
                List.of(authHeader), FetchPlaceResponse.class, getClass());
    }
}
