package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.data.DocumentIdentifier;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.FetchDocumentsResponse;
import com.phonepe.verified.kaizen.models.data.contexts.DocumentUploadActionTransitionContext;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import java.io.InputStream;
import javax.ws.rs.core.Response;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;

public interface DocumentService {

    DocumentIdentifier uploadWithNewExternalReferenceId(FormDataContentDisposition fileMetaData,
                                                        InputStream fileInputStream,
                                                        String userId,
                                                        String workflowId,
                                                        String actionMappingId,
                                                        DocumentType documentType,
                                                        String documentLabel);

    DocumentIdentifier uploadWithNewExternalReferenceId(FormDataContentDisposition fileMetaData,
                                                        InputStream fileInputStream,
                                                        String password,
                                                        String userId,
                                                        String workflowId,
                                                        String actionMappingId,
                                                        DocumentType documentType,
                                                        String documentLabel);

    DocumentIdentifier uploadWithNewExternalReferenceId(FormDataContentDisposition fileMetaData,
                                                        InputStream fileInputStream,
                                                        String userId,
                                                        String workflowId,
                                                        String actionMappingId,
                                                        DocumentType documentType,
                                                        String documentLabel,
                                                        long timeToLiveInSec);

    DocumentIdentifier uploadWithNewExternalReferenceId(FormDataContentDisposition fileMetaData,
                                                        InputStream fileInputStream,
                                                        String password,
                                                        String userId,
                                                        String workflowId,
                                                        String actionMappingId,
                                                        DocumentType documentType,
                                                        String documentLabel,
                                                        long timeToLiveInSec);

    DocumentIdentifier uploadWithNewExternalReferenceId(FormDataContentDisposition fileMetaData,
                                                        InputStream fileInputStream,
                                                        String password,
                                                        String userId,
                                                        String workflowId,
                                                        String actionMappingId,
                                                        DocumentType documentType,
                                                        String documentLabel,
                                                        String intent,
                                                        long componentKitVersion);

    Response download(String actionId,
                      String documentId);

    String getBase64EncodedImage(String documentId);

    void delete(String documentId,
                String userId);

    boolean isDocumentPresentOnDocStore(String documentId);

    byte[] getImagebytes(final String documentId);

    FetchDocumentsResponse fetchDocuments(String actionId,
                                          DocumentType documentType);

    StandardStepActionConfig getStandardStepActionConfigForGivenActionMappingId(Profile profile,
                                                                                String actionMappingId);

    DocumentIdentifier uploadWithExistingExternalReferenceId(FormDataContentDisposition fileMetaData,
                                                             InputStream fileInputStream,
                                                             String userId,
                                                             String workflowId,
                                                             String actionMappingId,
                                                             DocumentType documentType,
                                                             String documentLabel,
                                                             String existingDocumentId);

    void validateAllDocumentsPresentOnDocStore(DocumentUploadActionTransitionContext documentActionContext);
}
