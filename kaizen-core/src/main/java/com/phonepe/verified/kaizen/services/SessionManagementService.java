package com.phonepe.verified.kaizen.services;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.configs.session.SessionManagementConfig;
import com.phonepe.verified.kaizen.models.requests.session.CreateClientSessionRequest;
import com.phonepe.verified.kaizen.models.requests.session.EndClientSessionRequest;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredSessionManagementConfig;
import java.util.Optional;

public interface SessionManagementService {

    Optional<StoredSessionManagementConfig> getSessionManagementConfigFromDb(final String profileId,
                                                                             final String sourceType);

    boolean createSession(final CreateClientSessionRequest createClientSessionRequest);

    boolean endSession(final String requestId,
                       final String userId,
                       final EndClientSessionRequest endClientSessionRequest);

    void validateSessionAndThrowException(final StoredWorkflow storedWorkflow,
                                          final RequestInfo requestInfo,
                                          final UserDetails userDetails,
                                          final String sessionToken);

    void validateSessionAndThrowException(final String workflowId,
                                          final RequestInfo requestInfo,
                                          final UserDetails userDetails,
                                          final String sessionToken);


    Optional<StoredSessionManagementConfig> createSessionConfig(final SessionManagementConfig createSessionConfigRequest,
                                                                final String userId);

    void updateSessionConfig(final SessionManagementConfig sessionConfig,
                             final String userId);

    void toggleDisabled(final String profileId,
                        final String sourceType,
                        final boolean disabled);

    SessionManagementConfig getSessionConfig(final String profileId,
                                             final String sourceType);

}
