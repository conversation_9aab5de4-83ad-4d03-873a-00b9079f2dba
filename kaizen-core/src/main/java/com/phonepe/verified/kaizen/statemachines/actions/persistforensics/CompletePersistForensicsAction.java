package com.phonepe.verified.kaizen.statemachines.actions.persistforensics;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.SuccessStateBaseAction;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.KeyValuePairsActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredKeyValueMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "completePersistForensicsAction")
public class CompletePersistForensicsAction extends SuccessStateBaseAction {

    private final ActionMetadataRepository actionMetadataRepository;
    private final ActionMetadataStoreCommand actionMetadataStoreCommand;

    @Inject
    public CompletePersistForensicsAction(final ActionService actionService,
                                          final ActionRepository actionRepository,
                                          final AutoRetryActionService autoRetryActionService,
                                          final ActionMetadataRepository actionMetadataRepository,
                                          final Provider<WorkflowContextStore> workflowContextStore,
                                          final ActionMetadataStoreCommand actionMetadataStoreCommand,
                                          final Provider<EventIngestionActor> eventIngestionActorProvider,
                                          final Provider<AutoRetryActionActor> autoRetryActionActorProvider,
                                          final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider,
                                          final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider) {
        super(actionService, actionRepository, autoRetryActionService, autoRetryActionActorProvider,
                handleActionCompletionActorProvider, handlePseudoSuccessActionCompletionActorProvider,
                workflowContextStore, eventIngestionActorProvider);
        this.actionMetadataRepository = actionMetadataRepository;
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
    }

    @Override
    public void transition(final StoredAction storedAction,
                           final StateContext<String, String> stateContext) {

        final var keyValuePairsActionMetadata = (KeyValuePairsActionMetadata) actionMetadataStoreCommand.get(
                ActionMetadataStoreKey.builder()
                        .actionId(storedAction.getActionId())
                        .build());

        final var storedKeyValueMetadataList = keyValuePairsActionMetadata.getKeyValuePairs()
                .entrySet()
                .stream()
                .map(e -> (StoredActionMetadata) StoredKeyValueMetadata.builder()
                        .actionId(storedAction.getActionId())
                        .key(e.getKey())
                        .value(e.getValue())
                        .build())
                .toList();

        storedKeyValueMetadataList.forEach(actionMetadataRepository::save);
    }

}
