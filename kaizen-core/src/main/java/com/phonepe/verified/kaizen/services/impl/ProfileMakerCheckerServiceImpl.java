package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.platform.docstore.client.DocstoreClient;
import com.phonepe.platform.docstore.model.request.documents.upload.FileUploadRequest;
import com.phonepe.platform.docstore.model.request.documents.upload.context.Models.TTLConfig;
import com.phonepe.platform.docstore.model.request.documents.upload.context.Models.TTLPriority;
import com.phonepe.platform.docstore.model.request.documents.upload.context.external.CreateOnlyExternalRefContext;
import com.phonepe.platform.docstore.model.request.documents.upload.context.fileupload.InternalFileUploadContext;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.profile.ProfileStageData;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileChangeStatus;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileIdentifier;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.profiles.ProfileChange;
import com.phonepe.verified.kaizen.models.responses.profiles.ProfileChangeMetadata;
import com.phonepe.verified.kaizen.services.MakerCheckerService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ProfileChangeLockCommand;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ProfileStageCommand;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ProfileIdentifierKey;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ProfileStageKey;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import javax.annotation.Nullable;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ProfileMakerCheckerServiceImpl implements
        MakerCheckerService<ProfileChange, ProfileChangeMetadata, Profile> {

    private final ProfileService profileService;

    private final DocstoreClient docstoreClient;

    private final ProfileStageCommand profileStageCommand;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final ProfileChangeLockCommand profileChangeLockCommand;

    @Override
    public ProfileChange approve(final String requestId,
                                 final String approvedBy) {

        final var profileStageData = profileStageCommand.get(ProfileStageKey.builder()
                .requestId(requestId)
                .build());
        if (profileStageData.getProfileChangeMetadata()
                .getStatus() != ProfileChangeStatus.SUBMITTED) {
            throw new KaizenException(KaizenResponseCode.OPERATION_NOT_ALLOWED, Map.of());
        }

        if (approvedBy.equals(profileStageData.getProfileChangeMetadata()
                .getRequestedBy())) {
            log.warn("User: {} is trying to approve their own change", approvedBy);
            throw new KaizenException(KaizenResponseCode.UNAUTHORIZED_OPERATION, Map.of());
        }

        final var approvedProfile = getProfileFromDocstore(profileStageData.getProposedProfileDocstoreId());
        final Profile updatedProfile;
        if (Objects.isNull(profileStageData.getPreviousProfileDocstoreId())) {
            updatedProfile = profileService.create(approvedProfile, profileStageData.getProfileChangeMetadata()
                    .getRequestedBy(), approvedBy);
        } else {
            updatedProfile = profileService.update(approvedProfile, profileStageData.getProfileChangeMetadata()
                    .getRequestedBy(), approvedBy);
        }

        final var docStoreId = uploadProfileToDocStore(updatedProfile, requestId + "_approved");
        profileStageCommand.save(ProfileStageKey.builder()
                .requestId(requestId)
                .build(), ProfileStageData.builder()
                .previousProfileDocstoreId(profileStageData.getPreviousProfileDocstoreId())
                .proposedProfileDocstoreId(docStoreId)
                .profileChangeMetadata(ProfileChangeMetadata.builder()
                        .requestedBy(profileStageData.getProfileChangeMetadata()
                                .getRequestedBy())
                        .requestId(profileStageData.getProfileChangeMetadata()
                                .getRequestId())
                        .requestCreatedAt(profileStageData.getProfileChangeMetadata()
                                .getRequestCreatedAt())
                        .profileIdentifier(profileStageData.getProfileChangeMetadata()
                                .getProfileIdentifier())
                        .status(ProfileChangeStatus.APPROVED)
                        .reviewedBy(approvedBy)
                        .build())
                .build());
        log.info("Request {} for profile id {}, approved by {}", requestId, updatedProfile.getProfileId(), approvedBy);
        releaseProfileLock(ProfileIdentifierKey.builder()
                .profileIdentifier(profileStageData.getProfileChangeMetadata()
                        .getProfileIdentifier())
                .build());
        return getStagedChange(requestId);
    }

    @Override
    public List<ProfileChangeMetadata> getAllChangeMetadata() {

        return profileStageCommand.scan()
                .values()
                .stream()
                .map(ProfileStageData::getProfileChangeMetadata)
                .toList();
    }

    @Override
    public List<ProfileChange> getAllChanges() {

        return profileStageCommand.scan()
                .values()
                .stream()
                .map(this::convertStagedDataToProfileChange)
                .toList();
    }

    @Override
    public ProfileChangeMetadata getChangeMetadata(final String requestId) {

        try {
            return profileStageCommand.get(ProfileStageKey.builder()
                            .requestId(requestId)
                            .build())
                    .getProfileChangeMetadata();
        } catch (final Exception ex) {
            log.error("Unable to find the request {}", requestId, ex);
            throw new KaizenException(KaizenResponseCode.INVALID_REQUEST, Map.of());
        }
    }

    @Override
    public ProfileChange getStagedChange(final String requestId) {

        log.info("Fetching staged change for request {}", requestId);
        final ProfileStageData profileStageData;
        try {
            profileStageData = profileStageCommand.get(ProfileStageKey.builder()
                    .requestId(requestId)
                    .build());
        } catch (final Exception ex) {
            log.error("Unable to find the request {}", requestId, ex);
            throw new KaizenException(KaizenResponseCode.INVALID_REQUEST, Map.of());
        }

        return convertStagedDataToProfileChange(profileStageData);
    }

    @Override
    public ProfileChange reject(final String requestId,
                                final String rejectedBy) {

        final var profileStageData = profileStageCommand.get(ProfileStageKey.builder()
                .requestId(requestId)
                .build());

        if (profileStageData.getProfileChangeMetadata()
                .getStatus() != ProfileChangeStatus.SUBMITTED) {
            throw new KaizenException(KaizenResponseCode.OPERATION_NOT_ALLOWED, Map.of());
        }

        profileStageCommand.save(ProfileStageKey.builder()
                .requestId(requestId)
                .build(), ProfileStageData.builder()
                .previousProfileDocstoreId(profileStageData.getPreviousProfileDocstoreId())
                .proposedProfileDocstoreId(profileStageData.getProposedProfileDocstoreId())
                .profileChangeMetadata(ProfileChangeMetadata.builder()
                        .requestedBy(profileStageData.getProfileChangeMetadata()
                                .getRequestedBy())
                        .requestId(profileStageData.getProfileChangeMetadata()
                                .getRequestId())
                        .requestCreatedAt(profileStageData.getProfileChangeMetadata()
                                .getRequestCreatedAt())
                        .profileIdentifier(profileStageData.getProfileChangeMetadata()
                                .getProfileIdentifier())
                        .status(ProfileChangeStatus.REJECTED)
                        .reviewedBy(rejectedBy)
                        .build())
                .build());
        log.info("Profile change rejected for requestId {} by {}", requestId, rejectedBy);
        releaseProfileLock(ProfileIdentifierKey.builder()
                .profileIdentifier(profileStageData.getProfileChangeMetadata()
                        .getProfileIdentifier())
                .build());
        return getStagedChange(requestId);
    }

    @Override
    public ProfileChange stage(final Profile profile,
                               final String requester,
                               @Nullable final String existingProfileId) {

        profileService.validateUniqueProfileStepMappingIds(profile);
        profileService.validateUniqueScreenMappingIdsAndActionMappingIds(profile);
        profileService.validateProfileActionTypeExists(profile);
        profileService.validateProfileEvaluationAndExecutionRules(profile);
        if (Objects.nonNull(existingProfileId)) {
            final var existingProfile = profileService.get(existingProfileId, false);
            profileService.validateProfileIdentifierUnmodified(profile, existingProfile);
        }

        final var requestId = UUID.randomUUID()
                .toString();

        final var profileIdentifier = ProfileIdentifier.builder()
                .organization(profile.getOrganization())
                .namespace(profile.getNamespace())
                .type(profile.getType())
                .version(profile.getVersion())
                .profileType(profile.getProfileType())
                .addOnType(profile.getAddOnType())
                .build();

        final var profileIdentifierKey = ProfileIdentifierKey.builder()
                .profileIdentifier(profileIdentifier)
                .build();
        try {
            takeLock(requestId, profileIdentifierKey);
            log.info("Received valid profile, setting profile {} to stage with requestId: {}", profile, requestId);

            final var proposedProfileDocStoreId = uploadProfileToDocStore(profile, requestId + "_proposed");
            final String previousProfileDocStoreId;
            if (Objects.nonNull(existingProfileId)) {
                previousProfileDocStoreId = uploadProfileToDocStore(profileService.get(existingProfileId, true),
                        requestId + "_previous");
            } else {
                previousProfileDocStoreId = null;
            }

            // store the request details in aerospike
            profileStageCommand.save(ProfileStageKey.builder()
                    .requestId(requestId)
                    .build(), ProfileStageData.builder()
                    .previousProfileDocstoreId(previousProfileDocStoreId)
                    .proposedProfileDocstoreId(proposedProfileDocStoreId)
                    .profileChangeMetadata(ProfileChangeMetadata.builder()
                            .requestCreatedAt(LocalDateTime.now())
                            .status(ProfileChangeStatus.SUBMITTED)
                            .requestedBy(requester)
                            .requestId(requestId)
                            .profileIdentifier(profileIdentifier)
                            .build())
                    .build());
        } catch (final Exception ex) {
            log.error("Error while staging request, for profile {}, requester {}", profileIdentifier, requester, ex);
            releaseProfileLock(profileIdentifierKey);
            throw ex;
        }

        return getStagedChange(requestId);
    }

    private ProfileChange convertStagedDataToProfileChange(final ProfileStageData profileStageData) {

        final var proposedProfile = getProfileFromDocstore(profileStageData.getProposedProfileDocstoreId());
        final Profile previousProfile;
        if (Objects.nonNull(profileStageData.getPreviousProfileDocstoreId())) {
            previousProfile = getProfileFromDocstore(profileStageData.getPreviousProfileDocstoreId());
        } else {
            previousProfile = null;
        }

        return ProfileChange.builder()
                .profileChangeMetadata(profileStageData.getProfileChangeMetadata())
                .proposedProfile(proposedProfile)
                .previousProfile(previousProfile)
                .build();
    }

    private Profile getProfileFromDocstore(final String docStoreId) {

        try {
            return MapperUtils.deserialize(docstoreClient.getfile(docStoreId)
                    .getFileContent(), Profile.class);
        } catch (final Exception e) {
            log.error("Failed during document download for doc store id {}", docStoreId, e);
            throw new KaizenException(KaizenResponseCode.DOCSTORE_FILE_DOWNLOAD_ERROR, Map.of());
        }
    }

    private void releaseProfileLock(final ProfileIdentifierKey profileIdentifierKey) {

        try {
            profileChangeLockCommand.delete(profileIdentifierKey);
        } catch (final Exception ex) {
            log.error("Error while removing lock", ex);
        }
    }

    private void takeLock(final String requestId,
                          final ProfileIdentifierKey profileIdentifierKey) {

        try {
            profileChangeLockCommand.strictSave(profileIdentifierKey, requestId);
        } catch (final Exception ex) {
            log.error("Error while taking lock", ex);
            final var existingRequestId = profileChangeLockCommand.get(profileIdentifierKey);
            throw KaizenException.create(KaizenResponseCode.UNABLE_TO_ACQUIRE_PROFILE_LOCK,
                    Map.of("existingRequestId", existingRequestId));
        }
    }

    @SuppressWarnings("java:S4790")
    private String uploadProfileToDocStore(final Profile profile,
                                           final String requestId) {

        try {
            final var serializedProfile = MapperUtils.serializeToBytes(profile);
            final var fileUploadRequest = FileUploadRequest.builder()
                    .namespace(appConfigProvider.getData()
                            .getDocstoreNamespace())
                    .fileUploadContext(new InternalFileUploadContext())
                    .meta(Map.ofEntries(Map.entry(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)))
                    .docCreationTime(System.currentTimeMillis())
                    .md5Checksum(DigestUtils.md5Hex(serializedProfile))
                    .customRespHeaders(Map.of(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON))
                    .externalContext(CreateOnlyExternalRefContext.builder()
                            .externalRefId(requestId)
                            .build())
                    .ttlConfig(TTLConfig.builder()
                            .priority(TTLPriority.HIGH)
                            .timeToLiveInSec(appConfigProvider.getData()
                                    .getProfileMakerCheckerTTLInSec())
                            .build())
                    .build();
            final var docStoreResponse = docstoreClient.uploadFileV2(requestId, serializedProfile,
                    MediaType.APPLICATION_JSON, fileUploadRequest, appConfigProvider.getData()
                            .getDocstoreNamespace());

            if (docStoreResponse.isSuccess()) {
                return docStoreResponse.getContext()
                        .getId();
            } else {
                log.error("Failed to stage profile, failed during docstore upload, requestId:{}, context: {}",
                        requestId, docStoreResponse.getContext());
                throw new KaizenException(KaizenResponseCode.DOCSTORE_FILE_UPLOAD_ERROR, Map.of());
            }
        } catch (final Exception ex) {
            log.error("Failed to stage profile, failed during docstore upload, requestId:{}", requestId, ex);
            throw new KaizenException(KaizenResponseCode.DOCSTORE_FILE_UPLOAD_ERROR, Map.of());
        }
    }
}
