package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.OtpProviderConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.SentinelOtpProviderConfig;
import com.phonepe.verified.kaizen.services.OtpService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import javax.inject.Singleton;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class OtpProviderConfigToTriggerOtpHurdleActionVisitor implements OtpProviderConfigVisitor<Void, StoredAction> {

    private final OtpService otpService;

    @Override
    public Void visit(final SentinelOtpProviderConfig sentinelOtpProviderConfig,
                      final StoredAction storedAction) {

        otpService.triggerOtpHurdle(storedAction.getActionId());
        return null;
    }
}
