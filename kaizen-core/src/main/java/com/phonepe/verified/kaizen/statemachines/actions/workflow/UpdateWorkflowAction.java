package com.phonepe.verified.kaizen.statemachines.actions.workflow;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class UpdateWorkflowAction extends UpdateWorkflowBaseAction {

    @Inject
    public UpdateWorkflowAction(final ProfileService profileService,
                                final WorkflowService workflowService,
                                final WorkflowRepository workflowRepository,
                                final EventIngestionCommand eventIngestionCommand,
                                final Provider<WorkflowContextStore> workflowContextStore) {
        super(profileService, workflowService, workflowRepository, eventIngestionCommand, workflowContextStore);
    }
}
