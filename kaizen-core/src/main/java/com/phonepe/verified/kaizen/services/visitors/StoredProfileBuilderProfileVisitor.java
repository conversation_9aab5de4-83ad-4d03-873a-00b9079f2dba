package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileVisitor;
import com.phonepe.verified.kaizen.services.visitors.StoredProfileBuilderProfileVisitor.StoredProfileBuilderProfileVisitorMessage;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredAddOnProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredPrimaryProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredProfile;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StoredProfileBuilderProfileVisitor implements
        ProfileVisitor<StoredProfile, StoredProfileBuilderProfileVisitorMessage> {

    public static final StoredProfileBuilderProfileVisitor INSTANCE = new StoredProfileBuilderProfileVisitor();

    @Override
    public StoredProfile visit(final AddOnProfile addOnProfile,
                               final StoredProfileBuilderProfileVisitorMessage data) {

        return StoredAddOnProfile.builder()
                .id(data.getId())
                .profileId(data.getProfileId())
                .organization(addOnProfile.getOrganization())
                .namespace(addOnProfile.getNamespace())
                .type(addOnProfile.getType())
                .version(addOnProfile.getVersion())
                .summaryViewConfig(addOnProfile.getSummaryViewConfig())
                .getTemplateConfig(addOnProfile.getGetTemplateConfig())
                .postCompletionActionConfig(addOnProfile.getPostCompletionActionConfig())
                .postWorkflowCreationActionConfigs(addOnProfile.getPostWorkflowCreationActionConfigs())
                .rule(addOnProfile.getRule())
                .addOnType(addOnProfile.getAddOnType())
                .priority(addOnProfile.getPriority())
                .lastUpdatedBy(data.getLastUpdatedBy())
                .approvedBy(data.getApprovedBy())
                .build();
    }

    @Override
    public StoredProfile visit(final PrimaryProfile primaryProfile,
                               final StoredProfileBuilderProfileVisitorMessage data) {

        return StoredPrimaryProfile.builder()
                .id(data.getId())
                .profileId(data.getProfileId())
                .organization(primaryProfile.getOrganization())
                .namespace(primaryProfile.getNamespace())
                .type(primaryProfile.getType())
                .version(primaryProfile.getVersion())
                .workflowTagConfig(primaryProfile.getWorkflowTagConfig())
                .summaryViewConfig(primaryProfile.getSummaryViewConfig())
                .getTemplateConfig(primaryProfile.getGetTemplateConfig())
                .postCompletionActionConfig(primaryProfile.getPostCompletionActionConfig())
                .postWorkflowCreationActionConfigs(primaryProfile.getPostWorkflowCreationActionConfigs())
                .lastUpdatedBy(data.getLastUpdatedBy())
                .approvedBy(data.getApprovedBy())
                .build();
    }

    @Getter
    @Builder
    public static class StoredProfileBuilderProfileVisitorMessage {

        private String profileId;

        private String lastUpdatedBy;

        private String approvedBy;

        private long id;

    }
}
