package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadataAudit;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Getter
@Setter
@Entity
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.KEY_VALUE)
public class StoredKeyValueMetadataAudit extends StoredActionMetadataAudit {

    private static final long serialVersionUID = 5187515290459944372L;

    @Column(name = "name", columnDefinition = "varchar(128)")
    private String key;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String value;

    @Builder
    public StoredKeyValueMetadataAudit(@NonNull final String actionId,
                                       final int revType,
                                       final LocalDateTime createdAt,
                                       final LocalDateTime lastUpdatedAt,
                                       final String key,
                                       final String value) {
        super(BuildUtils.auditPrimaryKeyForUpdate(), revType, actionId, ActionMetadataType.KEY_VALUE, createdAt,
                lastUpdatedAt);
        this.key = key;
        this.value = value;
    }
}
