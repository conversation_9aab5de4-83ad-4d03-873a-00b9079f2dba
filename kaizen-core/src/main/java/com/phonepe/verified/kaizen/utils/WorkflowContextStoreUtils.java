package com.phonepe.verified.kaizen.utils;

import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.ActionContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowScreenContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowStepContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import java.util.HashMap;
import java.util.Map;
import lombok.NonNull;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class WorkflowContextStoreUtils {

    public WorkflowContext buildWorkflowContext(@NonNull final StoredWorkflow storedWorkflow,
                                                final Map<String, WorkflowStepContext> workflowStepContextMap) {
        final var workflowContextBuilder = WorkflowContext.builder()
                .workflowId(storedWorkflow.getWorkflowId())
                .currentEvent(storedWorkflow.getCurrentEvent())
                .currentState(storedWorkflow.getCurrentState())
                .entityId(storedWorkflow.getEntityId())
                .entityType(storedWorkflow.getEntityType())
                .lastUpdatedBy(storedWorkflow.getLastUpdatedBy())
                .phoneNumber(storedWorkflow.getPhoneNumber())
                .profileId(storedWorkflow.getProfileId())
                .updaterType(storedWorkflow.getLastUpdaterType())
                .createdAt(storedWorkflow.getCreatedAt())
                .lastUpdatedAt(storedWorkflow.getLastUpdatedAt())
                .tag(storedWorkflow.getTag())
                .email(storedWorkflow.getEmailId())
                .callerFarmId(storedWorkflow.getCallerFarmId());

        if (workflowStepContextMap != null) {
            workflowContextBuilder.workflowStepMap(workflowStepContextMap);
        }
        return workflowContextBuilder.build();
    }


    private WorkflowScreenContext buildActionContextAndWorkflowScreenContext(final StoredAction storedAction) {
        final var actionContext = buildActionContextFromStoredAction(storedAction, null);
        return buildWorkflowScreenContext(storedAction, actionContext);
    }

    public WorkflowContext addActionContextInWorkflowContext(final WorkflowContext workflowContext,
                                                             final StoredAction storedAction,
                                                             final String profileStepMappingId) {
        final var workflowStepContext = workflowContext.getWorkflowStepMap()
                .get(profileStepMappingId);
        if (workflowStepContext != null) {
            final var workflowScreenContext = workflowStepContext.getWorkflowScreenContextMap()
                    .computeIfAbsent(storedAction.getScreenMappingId(),
                            k -> buildActionContextAndWorkflowScreenContext(storedAction));

            workflowScreenContext.getActionContextMap()
                    .computeIfAbsent(storedAction.getActionMappingId(),
                            k -> buildActionContextFromStoredAction(storedAction, null));
        }
        return workflowContext;
    }

    private WorkflowScreenContext buildWorkflowScreenContext(final StoredAction storedAction,
                                                             final ActionContext actionContext) {
        final var actionContextMap = new HashMap<String, ActionContext>();
        actionContextMap.put(storedAction.getActionMappingId(), actionContext);

        return WorkflowScreenContext.builder()
                .actionContextMap(actionContextMap)
                .workflowStepId(storedAction.getWorkflowStepId())
                .screenMappingId(storedAction.getScreenMappingId())
                .build();
    }

    public ActionContext buildActionContextFromStoredAction(final StoredAction storedAction,
                                                            final Map<String, StoredActionMetadata> storedActionMetadataMap) {
        final var actionContextBuilder = ActionContext.builder()
                .updaterType(storedAction.getLastUpdaterType())
                .workflowStepId(storedAction.getWorkflowStepId())
                .stateMachineVersion(storedAction.getStateMachineVersion())
                .screenMappingId(storedAction.getScreenMappingId())
                .lastUpdatedBy(storedAction.getLastUpdatedBy())
                .failureErrorCode(storedAction.getFailureErrorCode())
                .currentState(storedAction.getCurrentState())
                .currentEvent(storedAction.getCurrentEvent())
                .completionState(storedAction.getCompletionState())
                .completed(storedAction.isCompleted())
                .actionType(storedAction.getActionType())
                .actionId(storedAction.getActionId())
                .createdAt(storedAction.getCreatedAt())
                .lastUpdatedAt(storedAction.getLastUpdatedAt());

        if (storedActionMetadataMap != null) {
            actionContextBuilder.actionMetadataMap(storedActionMetadataMap);
        }

        return actionContextBuilder.build();
    }

    public WorkflowStepContext buildWorkflowStepContextFromStoredWorkflowStep(final StoredWorkflowStep storedWorkflowStep,
                                                                              final Map<String, WorkflowScreenContext> workflowScreenContextMap) {
        final var workflowStepContextBuilder = WorkflowStepContext.builder()
                .workflowId(storedWorkflowStep.getWorkflowId())
                .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                .currentEvent(storedWorkflowStep.getCurrentEvent())
                .currentState(storedWorkflowStep.getCurrentState())
                .profileStepId(storedWorkflowStep.getProfileStepId())
                .lastUpdatedBy(storedWorkflowStep.getLastUpdatedBy())
                .updaterType(storedWorkflowStep.getLastUpdaterType())
                .createdAt(storedWorkflowStep.getCreatedAt())
                .lastUpdatedAt(storedWorkflowStep.getLastUpdatedAt());

        if (workflowScreenContextMap != null) {
            workflowStepContextBuilder.workflowScreenContextMap(workflowScreenContextMap);
        }

        return workflowStepContextBuilder.build();
    }
}
