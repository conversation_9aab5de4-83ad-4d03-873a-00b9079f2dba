package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.foxtrot.events.subevents.CallbackReceivedFromClientSubEventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class CallbackReceivedFromDrishtiEvent extends BaseEvent {

    private String actionId;

    private Boolean success;

    private String requestId;

    private ActionType actionType;

    private ActionMetadataType actionMetadataType;

    private CompletionState completionState;

    private ActionFailureErrorCode actionFailureErrorCode;

    private CallbackReceivedFromClientSubEventType callbackReceivedFromClientSubEventType;

    @Builder
    public CallbackReceivedFromDrishtiEvent(final String intent,
                                            final String entityId,
                                            final String namespace,
                                            final String workflowId,
                                            @NonNull final String groupingKey,
                                            final String organization,
                                            final String workflowType,
                                            final String workflowStepId,
                                            final EntityType entityType,
                                            final String workflowVersion,
                                            final String screenMappingId,
                                            final String actionMappingId,
                                            final long componentKitVersion,
                                            final String profileStepMappingId,
                                            final String actionId,
                                            final Boolean success,
                                            final String requestId,
                                            final ActionType actionType,
                                            final CompletionState completionState,
                                            final ActionFailureErrorCode actionFailureErrorCode,
                                            final CallbackReceivedFromClientSubEventType callbackReceivedFromClientSubEventType,
                                            final ProfileType profileType,
                                            final String addOnType,
                                            final ActionMetadataType actionMetadataType) {
        super(EventType.CALLBACK_RECEIVED_FROM_DRISHTI, intent, entityId, namespace, workflowId, profileType, addOnType,
                groupingKey, organization, workflowType, workflowStepId, entityType, workflowVersion, screenMappingId,
                actionMappingId, componentKitVersion, profileStepMappingId);
        this.actionId = actionId;
        this.success = success;
        this.requestId = requestId;
        this.actionType = actionType;
        this.completionState = completionState;
        this.actionFailureErrorCode = actionFailureErrorCode;
        this.callbackReceivedFromClientSubEventType = callbackReceivedFromClientSubEventType;
        this.actionMetadataType = actionMetadataType;
    }
}
