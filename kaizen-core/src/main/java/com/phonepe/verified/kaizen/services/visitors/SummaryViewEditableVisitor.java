package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.DefaultEditableConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.HopeRuleEditableConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.visitor.SummaryViewEditableConfigVisitor;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.visitors.SummaryViewEditableVisitor.SummaryViewEditableVisitorData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Singleton
@AllArgsConstructor(onConstructor = @__(@Inject))
public class SummaryViewEditableVisitor implements
        SummaryViewEditableConfigVisitor<Boolean, SummaryViewEditableVisitorData> {

    private HopeLangService hopeLangService;

    @Override
    public Boolean visitDefault(DefaultEditableConfig defaultEditableConfig,
                                SummaryViewEditableVisitorData summaryViewEditableVisitorData) {

        return summaryViewEditableVisitorData.isEditable();
    }

    @Override
    public Boolean visitHopeRule(HopeRuleEditableConfig hopeRuleEditableConfig,
                                 SummaryViewEditableVisitorData summaryViewEditableVisitorData) {

        return hopeLangService.evaluate(hopeRuleEditableConfig.getRule(),
                summaryViewEditableVisitorData.getWorkflowContextJsonNode());
    }

    @Data
    @Builder
    public static class SummaryViewEditableVisitorData {

        private boolean editable; // Default value
        private ObjectNode workflowContextJsonNode;
    }
}
