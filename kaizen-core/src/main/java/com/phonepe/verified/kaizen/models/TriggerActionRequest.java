package com.phonepe.verified.kaizen.models;

import java.util.Set;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class TriggerActionRequest {

    @NotEmpty
    private String workflowId;

    @NotEmpty
    private String profileStepMappingId;

    @NotEmpty
    private String screenMappingId;

    @NotEmpty
    private String actionMappingIdToTrigger;

    private Set<String> actionMappingIdsToInvalidate;

    private boolean validateExistingAction;
}
