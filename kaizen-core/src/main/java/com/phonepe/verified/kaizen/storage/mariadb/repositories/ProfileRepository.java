package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredAddOnProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredPrimaryProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredProfile.Fields;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.List;
import java.util.Optional;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class ProfileRepository extends CrudRepository<StoredProfile> {

    @Inject
    public ProfileRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredProfile.class), null);
    }

    public List<StoredProfile> select(final String organization,
                                      final String namespace,
                                      final String type,
                                      final String version,
                                      final ProfileType profileType) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.namespace, namespace))
                .add(Restrictions.eq(Fields.type, type))
                .add(Restrictions.eq(Fields.version, version))
                .add(Restrictions.eq(Fields.profileType, profileType));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public List<StoredProfile> selectPrimaryProfile(final String organization,
                                                    final String namespace,
                                                    final String type,
                                                    final String version) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredPrimaryProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.namespace, namespace))
                .add(Restrictions.eq(Fields.type, type))
                .add(Restrictions.eq(Fields.version, version));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public List<StoredProfile> selectAddOnProfile(final String organization,
                                                  final String namespace,
                                                  final String type,
                                                  final String version,
                                                  final String addOnType) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredAddOnProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.namespace, namespace))
                .add(Restrictions.eq(Fields.type, type))
                .add(Restrictions.eq(Fields.version, version))
                .add(Restrictions.eq(StoredAddOnProfile.Fields.addOnType, addOnType));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public List<StoredProfile> selectStoredAddOnProfile(final String organization,
                                                        final String namespace,
                                                        final String type,
                                                        final String version) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredAddOnProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.namespace, namespace))
                .add(Restrictions.eq(Fields.type, type))
                .add(Restrictions.eq(Fields.version, version));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public List<StoredProfile> select(final String organization,
                                      final String namespace,
                                      final String type) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.namespace, namespace))
                .add(Restrictions.eq(Fields.type, type));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public List<StoredProfile> select(final String organization,
                                      final String namespace,
                                      final String type,
                                      final ProfileType profileType) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.namespace, namespace))
                .add(Restrictions.eq(Fields.type, type))
                .add(Restrictions.eq(Fields.profileType, profileType));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public List<StoredProfile> select(final String organization,
                                      final String namespace,
                                      final ProfileType profileType) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.namespace, namespace))
                .add(Restrictions.eq(Fields.profileType, profileType));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public List<StoredProfile> select(final String organization,
                                      final String namespace) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.namespace, namespace));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public List<StoredProfile> select(final String organization,
                                      ProfileType profileType) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredProfile.class)
                .add(Restrictions.eq(Fields.organization, organization))
                .add(Restrictions.eq(Fields.profileType, profileType));

        return select(Constants.PROFILE_SHARD_KEY, criteria);
    }

    public Optional<StoredProfile> select(final String profileId) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredProfile.class)
                .add(Restrictions.eq(Fields.profileId, profileId));

        return select(Constants.PROFILE_SHARD_KEY, criteria).stream()
                .findFirst();
    }

    public List<StoredProfile> selectAll() {
        return select(Constants.PROFILE_SHARD_KEY, DetachedCriteria.forClass(StoredProfile.class));
    }
}
