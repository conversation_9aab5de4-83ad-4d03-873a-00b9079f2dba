package com.phonepe.verified.kaizen.models.data.contexts.visitor;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.contexts.ConsentTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.DocumentUploadActionTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.EmptyTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.KeyValuePairsActionTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.PersistKeyValuePairsActionTransitionContext;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.util.Map;

public abstract class TransitionContextTypeBaseVisitor<T, J> implements TransitionContextTypeVisitor<T, J> {

    @Override
    public T visit(final ConsentTransitionContext consentTransitionContext,
                   final J data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visit(final DocumentUploadActionTransitionContext documentUploadActionTransitionContext,
                   final J data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visit(final EmptyTransitionContext emptyTransitionContext,
                   final J data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visit(final KeyValuePairsActionTransitionContext keyValuePairsActionTransitionContext,
                   final J data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visit(final PersistKeyValuePairsActionTransitionContext persistKeyValuePairsActionTransitionContext,
                   final J data) {
        return throwUnsupportedOperation();
    }

    private T throwUnsupportedOperation() {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }
}
