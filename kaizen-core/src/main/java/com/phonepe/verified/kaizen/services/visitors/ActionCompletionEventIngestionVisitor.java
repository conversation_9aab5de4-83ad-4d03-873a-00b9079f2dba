package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.data.ActionType.ActionTypeVisitor;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.visitors.data.ActionCompletionEventIngestionVisitorData;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@Singleton
@SuppressWarnings("java:S4144")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ActionCompletionEventIngestionVisitor implements
        ActionTypeVisitor<Void, ActionCompletionEventIngestionVisitorData> {

    private final EventIngestionCommand eventIngestionCommand;

    private final ActionMetadataService actionMetadataService;


    public void publishEvent(final ActionCompletionEventIngestionVisitorData visitorData) {

        final var storedAction = visitorData.getStoredAction();

        storedAction.getActionType()
                .accept(this, visitorData);
    }

    private void publishActionCompletion(final ActionCompletionEventIngestionVisitorData visitorData) {

        eventIngestionCommand.actionCompletionEvent(visitorData.getStoredAction(), visitorData.getStoredWorkflowStep(),
                visitorData.getStoredWorkflow(), visitorData.getProfile(), visitorData.getProfileStep(),
                visitorData.getShadowV2UiRequestContext(), visitorData.getEventIngestionMessage());
    }

    // to-do to be deleted with event cleanup.
    private void publishActionCompletionWithEventType(final ActionCompletionEventIngestionVisitorData visitorData,
                                                      final EventType eventType) {

        eventIngestionCommand.actionCompletionEventWithDifferentEventType(visitorData.getStoredAction(),
                visitorData.getStoredWorkflowStep(), visitorData.getStoredWorkflow(), visitorData.getProfile(),
                visitorData.getProfileStep(), visitorData.getShadowV2UiRequestContext(), eventType);
    }


    @Override
    public Void visitDocumentUpload(final ActionCompletionEventIngestionVisitorData data) {

        final StoredAction storedAction = data.getStoredAction();
        final Profile profile = data.getProfile();
        final ProfileStep profileStep = data.getProfileStep();
        final StoredWorkflow storedWorkflow = data.getStoredWorkflow();
        final StoredWorkflowStep storedWorkflowStep = data.getStoredWorkflowStep();
        final ShadowV2UiRequestContext shadowV2UiRequestContext = data.getShadowV2UiRequestContext();

        final var actionMetadataList = actionMetadataService.getActionMetadataList(storedAction.getActionId());

        final var documentTypeCountMap = actionMetadataList.stream()
                .filter(StoredDocumentUploadActionMetadata.class::isInstance)
                .map(StoredDocumentUploadActionMetadata.class::cast)
                .collect(Collectors.groupingBy(StoredDocumentUploadActionMetadata::getDocumentType,
                        Collectors.counting()));

        if (!documentTypeCountMap.isEmpty()) {
            eventIngestionCommand.documentUploadActionCompletionEvent(storedAction, storedWorkflowStep, storedWorkflow,
                    profile, profileStep, shadowV2UiRequestContext, EventType.ACTION_COMPLETION, documentTypeCountMap);
        } else {
            eventIngestionCommand.actionCompletionEvent(storedAction, storedWorkflowStep, storedWorkflow, profile,
                    profileStep, shadowV2UiRequestContext, data.getEventIngestionMessage());
        }

        // ACTION_COMPLETION is a standard eventType through which we can track user journey funnel and setup single ad-alert
        // consumers of the system don't need to memorise multiple eventTypes with one event type.
        // if we keep it separate for each action and that too for success and failure if we keep separate, it's inconvenience.
        // to-do below code is kept for backwards compatibility of events, we should stop firing these old eventTypes
        // once all the sources ( ad-alerts, echo, superset) are moved to ACTION_COMPLETION only.

        ingestOldDocumentUploadEvents(storedAction, profile, profileStep, storedWorkflow, storedWorkflowStep,
                shadowV2UiRequestContext, documentTypeCountMap);

        return null;
    }

    @Override
    public Void visitPersistKeyValuePairs(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);

        // to-do to be removed with event cleanup
        if (data.getStoredAction()
                .getCompletionState() == CompletionState.SUCCESS) {
            publishActionCompletionWithEventType(data, EventType.PERSIST_KEY_VALUE_PAIR_ACTION_SUCCEED);
        }

        return null;
    }

    @Override
    public Void visitPersistForensics(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);

        // to-do to be removed with event cleanup
        if (data.getStoredAction()
                .getCompletionState() == CompletionState.SUCCESS) {
            publishActionCompletionWithEventType(data, EventType.PERSIST_FORENSICS_ACTION_SUCCEED);
        }
        return null;
    }

    @Override
    public Void visitHopeRuleValidation(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitSkipWorkflow(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitSkipWorkflowStep(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitConsent(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);

        // to-do to be removed with event cleanup
        if (data.getStoredAction()
                .getCompletionState() == CompletionState.SUCCESS) {
            publishActionCompletionWithEventType(data, EventType.CONSENT_ACTION_SUCCEED);
        }

        return null;
    }

    @Override
    public Void visitOtpHurdle(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitConfirmation(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitScheduleWorkflowAbort(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitSelfieHurdle(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitTriggerEventAction(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitWaitForCondition(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitOtpHurdleV2(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitApiCallAction(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitSendTerminalActionToSdk(final ActionCompletionEventIngestionVisitorData data) {

        publishActionCompletion(data);
        return null;
    }

    @Override
    public Void visitPersistKeyValuePairsFromApiCall(final ActionCompletionEventIngestionVisitorData data) {
        publishActionCompletion(data);
        return null;
    }

    private void ingestOldDocumentUploadEvents(final StoredAction storedAction,
                                               final Profile profile,
                                               final ProfileStep profileStep,
                                               final StoredWorkflow storedWorkflow,
                                               final StoredWorkflowStep storedWorkflowStep,
                                               final ShadowV2UiRequestContext shadowV2UiRequestContext,
                                               final Map<DocumentType, Long> documentTypeCountMap) {

        if (isSuccessOrFailure(storedAction)) {

            final var eventType = storedAction.getCompletionState() == CompletionState.SUCCESS
                                  ? EventType.DOCUMENT_UPLOAD_ACTION_SUCCEED
                                  : EventType.ACTION_COMPLETION;

            if (!documentTypeCountMap.isEmpty()) {
                eventIngestionCommand.documentUploadActionCompletionEvent(storedAction, storedWorkflowStep,
                        storedWorkflow, profile, profileStep, shadowV2UiRequestContext, eventType,
                        documentTypeCountMap);
            } else {
                eventIngestionCommand.actionCompletionEventWithDifferentEventType(storedAction, storedWorkflowStep,
                        storedWorkflow, profile, profileStep, shadowV2UiRequestContext, eventType);
            }
        }
    }

    private boolean isSuccessOrFailure(final StoredAction storedAction) {
        return storedAction.getCompletionState() == CompletionState.SUCCESS
                || storedAction.getCompletionState() == CompletionState.FAILURE;
    }
}
