package com.phonepe.verified.kaizen.caches.key;

import javax.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@Builder
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public class SessionManagementConfigCacheKey {

    @NotEmpty
    private final String profileId;

    @NotEmpty
    private final String sourceType;

}
