package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.requests.workflow.AbortWorkflowRequest;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.AbortWorkflowMessage;
import com.phonepe.verified.kaizen.queue.messages.ScheduledWorkflowStepAbortCallbackMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.utils.Constants;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ScheduledWorkflowAbortForStepCallbackActor extends BaseActor<ScheduledWorkflowStepAbortCallbackMessage> {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final ClockworkClient clockworkClient;

    private final WorkflowService workflowService;

    private final AbortWorkflowActor abortWorkflowActor;

    private final WorkflowStepService workflowStepService;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final EventIngestionCommand eventIngestionCommand;

    @Inject
    protected ScheduledWorkflowAbortForStepCallbackActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                         final ConnectionRegistry connectionRegistry,
                                                         final ObjectMapper mapper,
                                                         final RetryStrategyFactory retryStrategyFactory,
                                                         final ExceptionHandlingFactory exceptionHandlingFactory,
                                                         final ActionService actionService,
                                                         final ProfileService profileService,
                                                         final ClockworkClient clockworkClient,
                                                         final WorkflowService workflowService,
                                                         final AbortWorkflowActor abortWorkflowActor,
                                                         final WorkflowStepService workflowStepService,
                                                         final EventIngestionCommand eventIngestionCommand,
                                                         final DataProvider<KaizenConfig> appConfigProvider) {
        super(ActorType.SCHEDULED_ABORT_CALLBACK_FOR_WORKFLOW_STEP,
                actorConfigMap.get(ActorType.SCHEDULED_ABORT_CALLBACK_FOR_WORKFLOW_STEP), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, ScheduledWorkflowStepAbortCallbackMessage.class);
        this.actionService = actionService;
        this.profileService = profileService;
        this.clockworkClient = clockworkClient;
        this.workflowService = workflowService;
        this.abortWorkflowActor = abortWorkflowActor;
        this.workflowStepService = workflowStepService;
        this.appConfigProvider = appConfigProvider;
        this.eventIngestionCommand = eventIngestionCommand;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final ScheduledWorkflowStepAbortCallbackMessage scheduledWorkflowStepAbortCallbackMessage) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(
                scheduledWorkflowStepAbortCallbackMessage.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var abortDuration = appConfigProvider.getData()
                .getAsyncWorkflowAbortDuration();

        if (!TransitionState.SUCCESS_STATES.contains(storedWorkflowStep.getCurrentState())) {

            eventIngestionCommand.ingestScheduledAbortWorkflowEvent(storedWorkflow, profile,
                    EventType.WORKFLOW_ABORT_TRIGGERED_ON_SCHEDULED_CALLBACK, null,
                    storedWorkflowStep.getWorkflowStepId(), profileStep.getProfileStepMappingId(),
                    Constants.ABORT_REASON_FOR_INCOMPLETE_WORKFLOW_STEP, abortDuration, null, null);

            final var abortWorkflowRequest = AbortWorkflowRequest.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .build();

            abortWorkflowActor.publish(AbortWorkflowMessage.builder()
                    .abortWorkflowRequest(abortWorkflowRequest)
                    .userDetails(scheduledWorkflowStepAbortCallbackMessage.getUserDetails())
                    .build());
        } else {

            eventIngestionCommand.ingestScheduledAbortWorkflowEvent(storedWorkflow, profile,
                    EventType.WORKFLOW_ABORT_NOT_TRIGGERED_ON_SCHEDULED_CALLBACK, null,
                    storedWorkflowStep.getWorkflowStepId(), profileStep.getProfileStepMappingId(),
                    Constants.ABORT_REASON_FOR_INCOMPLETE_WORKFLOW_STEP, abortDuration, null, null);
        }

        return true;
    }
}
