package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import java.util.Comparator;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GetAllScreenMappingIdVisitor implements ProfileScreenVisitor<Set<String>> {

    public static final GetAllScreenMappingIdVisitor INSTANCE = new GetAllScreenMappingIdVisitor();

    @Override
    public Set<String> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {
        return Set.of(standardProfileScreenConfig.getScreenMappingId());
    }

    @Override
    public Set<String> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        return sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .flatMap(orderedProfileScreen -> orderedProfileScreen.getProfileScreenConfig()
                        .accept(this)
                        .stream())
                .collect(Collectors.toSet());
    }
}
