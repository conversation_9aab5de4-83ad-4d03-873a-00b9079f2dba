package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.VerifyOtpHurdleMessage;
import com.phonepe.verified.kaizen.services.OtpService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class VerifyOtpHurdleActor extends BaseActor<VerifyOtpHurdleMessage> {

    private final OtpService otpService;

    @Inject
    protected VerifyOtpHurdleActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                   final ConnectionRegistry connectionRegistry,
                                   final ObjectMapper mapper,
                                   final RetryStrategyFactory retryStrategyFactory,
                                   final ExceptionHandlingFactory exceptionHandlingFactory,
                                   final OtpService otpService) {

        super(ActorType.VERIFY_OTP_HURDLE, actorConfigMap.get(ActorType.VERIFY_OTP_HURDLE), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, VerifyOtpHurdleMessage.class);
        this.otpService = otpService;
    }

    @Override
    protected boolean handleMessage(final VerifyOtpHurdleMessage verifyOtpHurdleMessage) {

        otpService.verifyOtpHurdle(verifyOtpHurdleMessage.getOtpVerificationRequest(),
                verifyOtpHurdleMessage.getActionId(), verifyOtpHurdleMessage.getIntent(),
                verifyOtpHurdleMessage.getComponentKitVersion(), verifyOtpHurdleMessage.getRequestInfo());
        return true;
    }
}
