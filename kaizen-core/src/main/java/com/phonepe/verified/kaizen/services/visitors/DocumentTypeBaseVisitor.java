package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.data.DocumentType.DocumentTypeVisitor;
import lombok.SneakyThrows;

public abstract class DocumentTypeBaseVisitor<T, J> implements DocumentTypeVisitor<T, J> {

    @Override
    public T visitPan(final J data) {
        return null;
    }

    @Override
    @SneakyThrows
    public T visitAadhaar(final J data) {
        return null;
    }

    @Override
    public T visitAadhaarVid(final J data) {
        return null;
    }

    @Override
    public T visitAadhaarUidToken(final J data) {
        return null;
    }

    @Override
    public T visitGst(final J data) {
        return null;
    }

    @Override
    public T visitVoter(final J data) {
        return null;
    }

    @Override
    public T visitDrivingLicense(final J data) {
        return null;
    }

    @Override
    public T visitPassport(final J data) {
        return null;
    }

    @Override
    public T visitElectricityBill(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryAadhaar(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryPassport(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryPan(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryDrivingLicense(final J data) {
        return null;
    }

    @Override
    public T visitCertificateOfIncorporation(final J data) {
        return null;
    }

    @Override
    public T visitPartnershipDeed(final J data) {
        return null;
    }

    @Override
    public T visitSeaCertificate(final J data) {
        return null;
    }

    @Override
    public T visitUaCertificate(final J data) {
        return null;
    }

    @Override
    public T visitVatCertificate(final J data) {
        return null;
    }

    @Override
    public T visitTradeLicense(final J data) {
        return null;
    }

    @Override
    public T visitMcdCertificate(final J data) {
        return null;
    }

    @Override
    public T visitFoodLicense(final J data) {
        return null;
    }

    @Override
    public T visitCstCertificate(final J data) {
        return null;
    }

    @Override
    public T visitPropertyTaxReceipt(final J data) {
        return null;
    }

    @Override
    public T visitMunicipalKhataReceipt(final J data) {
        return null;
    }

    @Override
    public T visitBankProof(final J data) {
        return null;
    }

    @Override
    public T visitIeCertificate(final J data) {
        return null;
    }

    @Override
    public T visitLlpCertificate(final J data) {
        return null;
    }

    @Override
    public T visitMunicipalCorporationTaxCertificate(final J data) {
        return null;
    }

    @Override
    public T visitTanCertificate(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryDeclaration(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryAppointmentLetter(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryMandateLetterResolution(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryManagingBodyResolution(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryBoardResolution(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryPartnerResolution(final J data) {
        return null;
    }

    @Override
    public T visitAuthorizedSignatoryKartaDeclaration(final J data) {
        return null;
    }

    @Override
    public T visitBrnCertificateOfTheStateOfRajasthan(final J data) {
        return null;
    }

    @Override
    public T visitSelfie(final J data) {
        return null;
    }

    @Override
    public T visitDigitalSignature(final J data) {
        return null;
    }

    @Override
    public T visitDrugLicense(final J data) {
        return null;
    }

    @Override
    public T visitCentralSalesTax(final J data) {
        return null;
    }

    @Override
    public T visitMunicipalCorporationDepartmentCertificate(final J data) {
        return null;
    }

    @Override
    public T visitTrustDeed(final J data) {
        return null;
    }

    @Override
    public T visitOtpBasedVideoKyc(final J data) {
        return null;
    }

    @Override
    public T visitEsignedAccountOpeningForm(final J data) {
        return null;
    }

    @Override
    public T visitDigioKycWorkflowDetails(final J data) {
        return null;
    }

    @Override
    public T visitTinCertificate(final J data) {
        return null;
    }

    @Override
    public T visitLabourCertificate(final J data) {
        return null;
    }

    @Override
    public T visitLiquorLicense(final J data) {
        return null;
    }

    @Override
    public T visitDiagnosticLicense(final J data) {
        return null;
    }

    @Override
    public T visitNregaJobCard(final J data) {
        return null;
    }

    @Override
    public T visitLetterFromNationalPopulationRegister(final J data) {
        return null;
    }

    @Override
    public T visitAadhaarFaceImage(final J data) {
        return null;
    }

    @Override
    public T visitCentralGovtId(final J data) {
        return null;
    }

    @Override
    public T visitStateGovtId(final J data) {
        return null;
    }

    @Override
    public T visitCkycJsonBlob(final J data) {
        return null;
    }

    @Override
    public T visitGramPanchayatDocuments(final J data) {
        return null;
    }

    @Override
    public T visitIncomeProof(final J data) {
        return null;
    }

    @Override
    public T visitCancelledCheck(final J data) {
        return null;
    }

    @Override
    public T visitBankStatement(final J data) {
        return null;
    }

    @Override
    public T visitDunsNumber(final J data) {
        return null;
    }

    @Override
    public T visitNationalIdCard(final J data) {
        return null;
    }

    @Override
    public T visitBankAccount(final J data) {
        return null;
    }

    @Override
    public T visitForm16(final J data) {
        return null;
    }

    @Override
    public T visitSalarySlip(final J data) {
        return null;
    }

    @Override
    public T visitInvoiceCopy(final J data) {
        return null;
    }

    @Override
    public T visitProofOfDelivery(final J data) {
        return null;
    }
}
