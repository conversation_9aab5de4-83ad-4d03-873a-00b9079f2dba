package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.configs.retry.CompositeRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.CountBasedRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.HopeRuleBasedRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.ManualOpsBasedRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.RetryConfigVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ManualRetryConfigExecutor implements RetryConfigVisitor<Boolean, Void> {

    @Override
    public Boolean visit(CountBasedRetryConfig countBasedRetryConfig,
                         Void data) {
        return false;
    }

    @Override
    public Boolean visit(HopeRuleBasedRetryConfig hopeRuleBasedRetryConfig,
                         Void data) {
        return false;
    }

    @Override
    public Boolean visit(ManualOpsBasedRetryConfig manualOpsBasedRetryConfig,
                         Void data) {
        return manualOpsBasedRetryConfig.isEnabled();
    }

    @Override
    public Boolean visit(CompositeRetryConfig compositeRetryConfig,
                         Void data) {

        final var evaluatedRetryConfig = compositeRetryConfig.getRetryConfig()
                .accept(this, data);

        final var manualOpsRetryConfig = compositeRetryConfig.getOpsBasedRetryConfig()
                .accept(this, data);

        return evaluatedRetryConfig || manualOpsRetryConfig;
    }

    @Data
    @Builder
    public static class ManualRetryConfigExecutorData {

        private final String workflowStepId;
    }

}
