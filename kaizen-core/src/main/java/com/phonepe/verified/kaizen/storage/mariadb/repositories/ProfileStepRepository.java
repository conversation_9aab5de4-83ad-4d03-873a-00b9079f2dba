package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredProfileStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredProfileStep.Fields;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.List;
import java.util.Optional;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class ProfileStepRepository extends CrudRepository<StoredProfileStep> {

    @Inject
    public ProfileStepRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredProfileStep.class), null);
    }

    public List<StoredProfileStep> selectProfileSteps(final String profileId) {
        final var detachedCriteria = DetachedCriteria.forClass(StoredProfileStep.class)
                .add(Restrictions.eq(Fields.profileId, profileId));
        return select(Constants.PROFILE_SHARD_KEY, detachedCriteria);
    }

    public Optional<StoredProfileStep> selectProfileStep(final String profileStepId) {
        final var detachedCriteria = DetachedCriteria.forClass(StoredProfileStep.class)
                .add(Restrictions.eq(Fields.profileStepId, profileStepId));
        return select(Constants.PROFILE_SHARD_KEY, detachedCriteria).stream()
                .findFirst();
    }

    public Optional<StoredProfileStep> select(final String profileStepId) {

        final DetachedCriteria criteria = DetachedCriteria.forClass(StoredProfileStep.class)
                .add(Restrictions.eq(Fields.profileStepId, profileStepId));

        return select(Constants.PROFILE_SHARD_KEY, criteria).stream()
                .findFirst();
    }

    public boolean saveAll(final List<StoredProfileStep> storedProfileSteps) {
        return saveAll(Constants.PROFILE_SHARD_KEY, storedProfileSteps);
    }
}