package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.OlympusTenant;
import com.phonepe.verified.kaizen.utils.PvcoreTenant;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class AuthZServiceImpl implements AuthZService {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final OlympusIMClient olympusIMClient;

    private final DataProvider<KaizenConfig> appConfigDataProvider;

    @Override
    public void authorizeActionIdBelongsToWorkflowId(final String actionId,
                                                     final String workflowId) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        if (!storedWorkflowStep.getWorkflowId()
                .equals(workflowId)) {
            throw KaizenException.create(KaizenResponseCode.AUTH_ERROR, Map.of());
        }

    }

    @Override
    public void authorizeOperationForTenant(final PvcoreTenant pvcoreTenant,
                                            final String resource,
                                            final String operation,
                                            final ServiceUserPrincipal serviceUserPrincipal) {

        authorizeOperationForTenant(pvcoreTenant, resource, operation, serviceUserPrincipal, false);
    }

    @Override
    public void authorizeOperationForTenantOrDefault(final PvcoreTenant pvcoreTenant,
                                                     final String resource,
                                                     final String operation,
                                                     final ServiceUserPrincipal serviceUserPrincipal) {

        authorizeOperationForTenant(pvcoreTenant, resource, operation, serviceUserPrincipal, true);
    }

    @Override
    public void authorizeUserWithActionId(final String actionId,
                                          final RequestInfo requestInfo,
                                          final UserDetails userDetails) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        authorizeUserWithWorkflowStepId(storedAction.getWorkflowStepId(), requestInfo, userDetails);
    }

    @Override
    public void authorizeUserWithEntityId(final String entityId,
                                          final RequestInfo requestInfo,
                                          final UserDetails userDetails) {

        final var role = requestInfo.getAuthorizationRole();

        if ((Constants.OCULUS_USER_ROLE.equals(role) && !entityId.equals(userDetails.getGroupValue())) || (
                Constants.CONSUMER_ROLE.equals(role) && !entityId.equals(requestInfo.getAuthorizeForId()))) {
            throw KaizenException.create(KaizenResponseCode.USER_NOT_AUTHORIZED_TO_PERFORM_THIS_ACTION, Map.of());
        }
    }

    @Override
    public void authorizeUserWithWorkflowId(final String workflowId,
                                            final RequestInfo requestInfo,
                                            final UserDetails userDetails) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        authorizeUserWithEntityId(storedWorkflow.getEntityId(), requestInfo, userDetails);
    }

    @Override
    public void authorizeUserWithWorkflowStepId(final String workflowStepId,
                                                final RequestInfo requestInfo,
                                                final UserDetails userDetails) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(workflowStepId);

        authorizeUserWithWorkflowId(storedWorkflowStep.getWorkflowId(), requestInfo, userDetails);
    }

    @Override
    @Nullable
    public PvcoreTenant getTenant(final String organization,
                                  final String namespace) {

        final var olympusTenancyConfig = appConfigDataProvider.getData()
                .getOlympusTenancyConfig();

        return Optional.ofNullable(olympusTenancyConfig.getOrganizationNamespaceToOlympusTenantMapping()
                        .getOrDefault(organization, Map.of())
                        .get(namespace))
                .map(olympusTenant -> PvcoreTenant.builder()
                        .olympusTenant(olympusTenant)
                        .organization(organization)
                        .namespace(namespace)
                        .build())
                .orElse(PvcoreTenant.builder()
                        .organization(organization)
                        .namespace(namespace)
                        .build());
    }

    @Override
    @Nullable
    public PvcoreTenant getTenantFromAction(final String actionId) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        return getTenantFromWorkflow(storedWorkflowStep.getWorkflowId());
    }

    @Override
    @Nullable
    public PvcoreTenant getTenantFromProfile(final String profileId) {

        final var profile = profileService.get(profileId, false);

        return getTenant(profile.getOrganization(), profile.getNamespace());
    }

    @Override
    @Nullable
    public PvcoreTenant getTenantFromWorkflow(final String workflowId) {
        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        return getTenantFromProfile(storedWorkflow.getProfileId());
    }

    private void authorizeOperationForTenant(final PvcoreTenant pvcoreTenant,
                                             final String resource,
                                             final String operation,
                                             final ServiceUserPrincipal serviceUserPrincipal,
                                             final boolean doDefaultTenancyCheck) {

        final var resolvedPermission = String.join(Constants.PERMISSION_SEPARATOR, resource, operation);

        final var defaultTenantOptional = fetchDefaultTenantIfAllowed(doDefaultTenancyCheck);

        final var olympusTenant = pvcoreTenant.getOlympusTenant();

        if (doesUserHaveTenantedPermission(olympusTenant, serviceUserPrincipal, resolvedPermission) || (
                defaultTenantOptional.isPresent() && doesUserHaveTenantedPermission(defaultTenantOptional.get(),
                        serviceUserPrincipal, resolvedPermission))) {
            return;
        }

        if (doesUserHaveOldNonTenantedPermission(pvcoreTenant, serviceUserPrincipal, resolvedPermission)) {

            log.warn("Client {} did not match {} tenancy for permission {}. Check for old permission passed through.",
                    serviceUserPrincipal.getName(), olympusTenant, resolvedPermission);
            return;
        }

        log.warn("Client {} did not match {} tenancy for permission {}", serviceUserPrincipal.getName(), olympusTenant,
                resolvedPermission);

        throw KaizenException.builder()
                .errorCode(KaizenResponseCode.UNAUTHORIZED_OPERATION)
                .message("Client %s not authorized for resource %s operation %s".formatted(
                        serviceUserPrincipal.getName(), resource, operation))
                .build();
    }

    private boolean doesUserHaveOldNonTenantedPermission(final PvcoreTenant pvcoreTenant,
                                                         final ServiceUserPrincipal serviceUserPrincipal,
                                                         final String resolvedPermission) {

        final var oldPermission = String.join(Constants.PERMISSION_SEPARATOR, pvcoreTenant.getOrganization(),
                pvcoreTenant.getNamespace(), resolvedPermission.replaceFirst("^DETAILS/", "WORKFLOW/DETAILS/"));

        return olympusIMClient.verifyPermission(serviceUserPrincipal.getUserAuthDetails(), oldPermission);
    }

    private boolean doesUserHaveTenantedPermission(final OlympusTenant olympusTenant,
                                                   final ServiceUserPrincipal serviceUserPrincipal,
                                                   final String resolvedPermission) {

        return Objects.nonNull(olympusTenant) && olympusIMClient.verifyPermissionForTenant(
                serviceUserPrincipal.getUserAuthDetails(), olympusTenant.getTenantId(), olympusTenant.getTenantType(),
                resolvedPermission);
    }

    private Optional<OlympusTenant> fetchDefaultTenantIfAllowed(final boolean doDefaultTenancyCheck) {

        if (Constants.STAGING_EQUIVALENT_ENVIRONMENTS.contains(Constants.CONFIG_ENV) || doDefaultTenancyCheck) {
            return Optional.of(appConfigDataProvider.getData()
                    .getOlympusTenancyConfig()
                    .getDefaultOlympusTenant());
        }

        return Optional.empty();
    }
}
