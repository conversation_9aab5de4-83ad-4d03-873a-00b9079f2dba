package com.phonepe.verified.kaizen.statemachines.actions.persistkeyvaluepairs;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.PersistKeyValuePairsActionContext;
import com.phonepe.verified.kaizen.models.data.contexts.PersistKeyValuePairsActionTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.KeyValuePairsActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "createPersistKeyValuePairsAction")
public class CreatePersistKeyValuePairsAction extends CreateEntryBaseAction {

    private final HandleBarsService handleBarsService;

    private final WorkflowStepService workflowStepService;

    private final Provider<WorkflowContextStore> workflowContextStore;

    private final ActionMetadataStoreCommand actionMetadataStoreCommand;

    private final Provider<ActionExecutorActor> actionExecutorActorProvider;

    @Inject
    public CreatePersistKeyValuePairsAction(final ActionService actionService,
                                            final WorkflowService workflowService,
                                            final ClockworkClient clockworkClient,
                                            final ActionRepository actionRepository,
                                            final HandleBarsService handleBarsService,
                                            final WorkflowStepService workflowStepService,
                                            final DataProvider<KaizenConfig> appConfigDataProvider,
                                            final Provider<WorkflowContextStore> workflowContextStore,
                                            final Provider<EventIngestionActor> eventIngestionActorProvider,
                                            final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                            final ActionMetadataStoreCommand actionMetadataStoreCommand,
                                            final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.workflowStepService = workflowStepService;
        this.workflowContextStore = workflowContextStore;
        this.handleBarsService = handleBarsService;
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
        this.actionExecutorActorProvider = actionExecutorActorProvider;
    }

    @Override
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var keyValuePairsFromTransitionContext = getKeyValuePairsFromTransitionContext(stateContext);

        final var keyValuePairsFromStepActionContext = getKeyValuePairsFromStepActionContext(storedAction,
                stateContext);

        final var keyValuePairs = Stream.concat(keyValuePairsFromTransitionContext.entrySet()
                        .stream(), keyValuePairsFromStepActionContext.entrySet()
                        .stream())
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

        final var actionMetadata = KeyValuePairsActionMetadata.builder()
                .actionId(storedAction.getActionId())
                .keyValuePairs(keyValuePairs)
                .build();

        actionMetadataStoreCommand.save(ActionMetadataStoreKey.builder()
                .actionId(storedAction.getActionId())
                .build(), actionMetadata);
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        actionExecutorActorProvider.get()
                .publish(ActionExecutionMessage.builder()
                        .actionId(storedAction.getActionId())
                        .eventToTrigger(StateMachineConstants.Events.PERSIST_METADATA)
                        .userDetails(Constants.PVCORE_SYSTEM_USER)
                        .build());
    }


    private Map<String, String> getKeyValuePairsFromStepActionContext(final StoredAction storedAction,
                                                                      final StateContext<String, String> stateContext) {
        final var keyValuePairsActionContextTemplate = stateContext.getExtendedState()
                .get(StepActionContext.class, PersistKeyValuePairsActionContext.class);
        if (Objects.isNull(keyValuePairsActionContextTemplate)) {
            return Map.of();
        }
        final var keyValuePairsActionContext = handleBarResolve(storedAction, keyValuePairsActionContextTemplate);

        return keyValuePairsActionContext.getKeyValuePairs();
    }

    private PersistKeyValuePairsActionContext handleBarResolve(final StoredAction storedAction,
                                                               final PersistKeyValuePairsActionContext keyValuePairsActionContextTemplate) {
        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());
        final var workflowContext = workflowContextStore.get()
                .getWorkflowContext(storedWorkflowStep.getWorkflowId());

        final var stringActionContext = handleBarsService.transform(
                MapperUtils.serializeToString(keyValuePairsActionContextTemplate), workflowContext);

        return MapperUtils.deserialize(stringActionContext, PersistKeyValuePairsActionContext.class);
    }

    private Map<String, String> getKeyValuePairsFromTransitionContext(final StateContext<String, String> stateContext) {
        final var keyValuePairsTransitionContext = stateContext.getExtendedState()
                .get(TransitionContext.class, PersistKeyValuePairsActionTransitionContext.class);
        return keyValuePairsTransitionContext.getKeyValuePairs();
    }

}
