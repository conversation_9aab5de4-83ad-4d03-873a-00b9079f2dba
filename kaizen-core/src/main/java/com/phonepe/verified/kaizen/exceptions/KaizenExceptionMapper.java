package com.phonepe.verified.kaizen.exceptions;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.responses.KaizenErrorResponse;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Provider
@NoArgsConstructor(onConstructor = @__(@Inject))
public class KaizenExceptionMapper implements ExceptionMapper<KaizenException> {

    @Override
    public Response toResponse(final KaizenException exception) {

        log.error("Error handling request: {}", MapperUtils.serializeToString(exception.getContext()), exception);

        return Response.status(exception.getErrorCode()
                        .getStatusCode())
                .entity(KaizenErrorResponse.builder()
                        .errorCode(exception.getErrorCode()
                                .name())
                        .errorMessage(exception.getErrorCode()
                                .getMessage())
                        .context(exception.getContext())
                        .build())
                .build();
    }
}
