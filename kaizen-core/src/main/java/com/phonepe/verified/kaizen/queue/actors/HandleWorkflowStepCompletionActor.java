package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepCompletionMessage;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class HandleWorkflowStepCompletionActor extends BaseActor<WorkflowStepCompletionMessage> {

    private final WorkflowStepService workflowStepService;

    private final WorkflowService workflowService;

    @Inject
    protected HandleWorkflowStepCompletionActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                final ConnectionRegistry connectionRegistry,
                                                final ObjectMapper mapper,
                                                final RetryStrategyFactory retryStrategyFactory,
                                                final ExceptionHandlingFactory exceptionHandlingFactory,
                                                final WorkflowStepService workflowStepService,
                                                final WorkflowService workflowService) {
        super(ActorType.HANDLE_WORKFLOW_STEP_COMPLETION, actorConfigMap.get(ActorType.HANDLE_WORKFLOW_STEP_COMPLETION),
                connectionRegistry, mapper, retryStrategyFactory, exceptionHandlingFactory,
                WorkflowStepCompletionMessage.class);
        this.workflowStepService = workflowStepService;
        this.workflowService = workflowService;
    }

    @Override
    protected boolean handleMessage(final WorkflowStepCompletionMessage message) {

        workflowService.handleWorkflowStepCompletion(message.getWorkflowStepId(), message.getCompletedActionId(),
                message.getCurrentProfileScreenConfig(), message.isRetryable());
        return true;
    }
}
