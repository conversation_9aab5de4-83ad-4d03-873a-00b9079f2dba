package com.phonepe.verified.kaizen.services;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.statemachine.StateMachine;

public interface WorkflowStepService {

    void createWorkflowStep(final String workflowId,
                            final String entityId,
                            final String workflowStepId,
                            final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                            final ProfileStep profileStep,
                            final UserDetails userDetails,
                            final boolean shouldMoveToInProgress);

    StoredWorkflowStep createAndGetWorkflowStep(final String workflowId,
                                                final String userId,
                                                final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                                final ProfileStep profileStep,
                                                final UserDetails userDetails);

    void createAndTriggerInitialActionWorkflowStep(final StoredWorkflow storedWorkflow);

    void triggerInitialActionWorkflowStep(final StoredWorkflowStep storedWorkflowStep);

    void triggerWorkflowStep(final StoredWorkflowStep storedWorkflowStep,
                             final String screenMappingId,
                             final UserDetails userDetails);

    Optional<StoredWorkflowStep> getValidWorkflowStep(final String workflowId,
                                                      final String profileStepId);

    List<StoredWorkflowStep> getWorkflowSteps(final String workflowId,
                                              final String profileStepId);

    List<StoredWorkflowStep> getValidWorkflowStepsFromWorkflowId(final String workflowId);

    List<StoredWorkflowStep> getWorkflowStepsFromWorkflowId(final String workflowId);

    List<StoredWorkflowStep> getWorkflowStepsFromWorkflowIds(final Set<String> workflowIds);

    StoredWorkflowStep validateAndGetWorkflowStep(final String workflowStepId);

    List<StoredWorkflowStep> getWorkflowStepsFromAllShards(final Set<String> workflowStepId);

    StateMachine<TransitionState, TransitionEvent> triggerMoveToInProgressEvent(final String workflowStepId,
                                                                                final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                                                                final UserDetails userDetails);

    StateMachine<TransitionState, TransitionEvent> triggerResubmitEvent(final String workflowStepId,
                                                                        final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                                                        final UserDetails userDetails);

    void handleScreenCompletion(CompletionState completionStateOfScreen,
                                String completedActionId);

    void skipWorkflowStep(StoredWorkflowStep storedWorkflowStep,
                          StandardProfileScreenConfig standardProfileScreenConfig,
                          UserDetails userDetails,
                          ActionType actionType,
                          TransitionEvent transitionEvent,
                          boolean invalidateActions);

    void autoSkipWorkflowStep(final StoredWorkflowStep storedWorkflowStep,
                              final UserDetails userDetails,
                              boolean invalidateAllActions);

    boolean isWorkflowStepRetryAvailable(final String workflowStepId,
                                         final String profileStepId);

    boolean isRetryAvailable(final StoredAction storedAction,
                             final StandardProfileScreenConfig standardProfileScreenConfig);

    void triggerEvent(final StoredWorkflowStep storedWorkflowStep,
                      final TransitionEvent transitionEvent);

    void triggerEvent(final StoredWorkflowStep storedWorkflowStep,
                      final TransitionEvent transitionEvent,
                      final String completionActionId);

    void abortWorkflowSteps(final String workflowId,
                            final UserDetails userDetails);

    void discardWorkflowSteps(String workflowId,
                              UserDetails userDetails);

    ShadowV2UiRequestContext getUiRequestContextAndSaveNewUiRequestContextWithExistingRequestDetails(String workflowStepId,
                                                                                                     SectionInputData sectionInputData,
                                                                                                     RequestInfo requestInfo,
                                                                                                     long componentKitVersion,
                                                                                                     String entityId,
                                                                                                     String intent,
                                                                                                     ApiVersion apiVersion,
                                                                                                     String workflowId);

    UiRequestContext getUiRequestContext(String workflowStepId);

    void saveUiRequestContext(String workflowStepId,
                              UiRequestContext uiRequestContext);

    UiRequestContext buildUiRequestContextWithDummySectionInputData(String userId,
                                                                    String workflowId,
                                                                    String actionMappingId,
                                                                    ApiVersion apiVersion,
                                                                    RequestInfo requestInfo,
                                                                    String intent,
                                                                    long componentKitVersion);

    void invalidateCurrentAndLaterWorkflowSteps(final String workflowId,
                                                final String mappedProfileStepId,
                                                final UserDetails userDetails);

    void invalidateWorkflowStepsAfterCurrentStep(final String workflowId,
                                                 final String mappedProfileStepId,
                                                 final UserDetails userDetails);

    void moveToPseudoSuccessAndRetryAction(final String workflowStepId,
                                           final String actionMappingId);

    void purgeWorkflowSteps(final String workflowId,
                            final UserDetails userDetails);
}
