package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.requests.statemachines.AddStateMachineRegistryRequest;
import com.phonepe.verified.kaizen.models.responses.ApiResponse;
import com.phonepe.verified.kaizen.models.responses.statemachines.StateMachineTallyResponse;
import com.phonepe.verified.kaizen.services.StateMachineService;
import com.phonepe.verified.kaizen.services.StateMachineService.StateMachineTransitions;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.UserDetailsUtils;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/state/machine")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "State Machine", description = "State Machine related APIs")
public class StateMachineResource {

    private final StateMachineService stateMachineService;

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Add State Machine")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @RolesAllowed(OlympusPermissionNames.STATE_MACHINE_MANAGEMENT)
    public ApiResponse<Boolean> addRegistry(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                            @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                            @Valid final AddStateMachineRegistryRequest addStateMachineRegistryRequest) {

        final var userDetails = UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal);
        log.info("State Machine Id: {} added by user: {}", addStateMachineRegistryRequest.getActionType(),
                userDetails.getUserId());

        final var success = stateMachineService.addRegistry(addStateMachineRegistryRequest, userDetails.getUserId());
        return ApiResponse.<Boolean>builder()
                .success(success)
                .build();
    }

    @GET
    @Produces("image/svg+xml")
    @Path("/{actionType}/{version}/graph")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @RolesAllowed(OlympusPermissionNames.STATE_MACHINE_MANAGEMENT)
    @Operation(summary = "Generate SVG Graph of State Machine given Action Type and Version")
    public String getStateMachineSvgGraph(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                          @PathParam("actionType") final ActionType actionType,
                                          @PathParam("version") final String stateMachineVersion) {

        return stateMachineService.getStateMachineSvgGraph(actionType, stateMachineVersion);
    }

    @GET
    @Path("/fetch/all")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Get a dump of all state machine data")
    @RolesAllowed(OlympusPermissionNames.STATE_MACHINE_MANAGEMENT)
    public List<StateMachineTransitions> generateStateMachineRequests(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {

        return stateMachineService.getAllActionTransitions();
    }

    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Get total SMs in DB and Registry")
    @RolesAllowed(OlympusPermissionNames.STATE_MACHINE_MANAGEMENT)
    public StateMachineTallyResponse totalStateMachines(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {

        return stateMachineService.tallyStateMachines();
    }
}
