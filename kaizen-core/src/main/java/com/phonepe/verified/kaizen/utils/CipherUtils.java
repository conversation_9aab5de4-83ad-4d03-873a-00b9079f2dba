package com.phonepe.verified.kaizen.utils;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.MimeType;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.visitors.RemoveDocumentPasswordProtectionVisitor;
import com.phonepe.verified.kaizen.services.visitors.RemoveDocumentPasswordProtectionVisitor.MimeTypeVisitorData;
import io.dropwizard.util.Strings;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
@SuppressWarnings("java:S5542")
public class CipherUtils {

    public final String AES = "AES";
    public final String AES_CBC_WITH_PKCS5_PADDING = "AES/CBC/PKCS5Padding";

    //Magic bytes will be used to hide some data in a byte array. PPED represents PhonePe Encrypted Data.
    private final byte[] magicBytes = "PPED".getBytes(StandardCharsets.UTF_8);

    private final int KEY_VERSION_BYTES_LENGTH = 2;

    private final int IV_LENGTH = 16;

    private final byte[] DEFAULT_KEY_VERSION_BYTES = convertNumberToByteArray(1, KEY_VERSION_BYTES_LENGTH);

    private final SecureRandom SECURE_RANDOM = new SecureRandom();


    public byte[] aesEncrypt(final byte[] plainText,
                             final String password,
                             final String mimeTypeString,
                             final Map.Entry<Integer, byte[]> cipherKey) {

        final var mimeType = MimeType.fromString(mimeTypeString);

        if (!Strings.isNullOrEmpty(password) && Objects.isNull(mimeType)) {

            throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_PASSWORD_PROTECTED_FILE_TYPE,
                    Map.of("MIME_TYPE", mimeTypeString));
        }

        if (!Strings.isNullOrEmpty(password) && Objects.nonNull(mimeType)) {

            final var fileContent = mimeType.accept(RemoveDocumentPasswordProtectionVisitor.INSTANCE,
                    MimeTypeVisitorData.builder()
                            .plainText(plainText)
                            .password(password)
                            .build());
            return aesEncrypt(fileContent, cipherKey);

        } else {
            return aesEncrypt(plainText, cipherKey);
        }
    }

    /**
     * Encrypts plaint text using AES algorithm
     *
     * @param plainText Plain text to be encrypted
     * @param cipherKey Latest private key and version to be used for encryption
     * @return Encrypted cipher text
     */
    public byte[] aesEncrypt(final byte[] plainText,
                             final Map.Entry<Integer, byte[]> cipherKey) {

        try {

            final var encryptCipher = Cipher.getInstance(AES_CBC_WITH_PKCS5_PADDING);
            final var secretKeySpec = new SecretKeySpec(cipherKey.getValue(), AES);
            final var keyVersionBytes = convertNumberToByteArray(cipherKey.getKey(), KEY_VERSION_BYTES_LENGTH);

            final var hiddenKeyVersion = hideByMagicBytes(keyVersionBytes);

            final var iv = new byte[IV_LENGTH];
            SECURE_RANDOM.nextBytes(iv);

            //updating first 6 bytes of iv with hidden key version
            System.arraycopy(hiddenKeyVersion, 0, iv, 0, hiddenKeyVersion.length);

            encryptCipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, new IvParameterSpec(iv));
            final var cipherText = encryptCipher.doFinal(plainText);

            //appending encrypted text to hidden key version with iv
            final var finalCipherText = new byte[iv.length + cipherText.length];
            System.arraycopy(iv, 0, finalCipherText, 0, iv.length);
            System.arraycopy(cipherText, 0, finalCipherText, iv.length, cipherText.length);

            return finalCipherText;

        } catch (final Exception e) {
            log.error("Error occurred while encryption using AES", e);
            throw KaizenException.propagate(KaizenResponseCode.INTERNAL_SERVER_ERROR, e);
        }
    }

    /**
     * Decrypts cipher text using AES algorithm
     *
     * @param cipherText Cipher text to be decrypted
     * @param cipherKeyConfig Private key and version map to be used for decryption
     * @return Decrypted plain text
     */
    public byte[] aesDecrypt(final byte[] cipherText,
                             final Map<Integer, byte[]> cipherKeyConfig) {

        try {
            final var hiddenBytes = getHiddenBytesFromTextOrDefault(cipherText, DEFAULT_KEY_VERSION_BYTES);
            final var cipherKeyVersion = convertByteArrayToNumber(hiddenBytes);
            final var cipherKey = cipherKeyConfig.get(cipherKeyVersion);

            final var decryptCipher = Cipher.getInstance(AES_CBC_WITH_PKCS5_PADDING);
            final var secretKeySpec = new SecretKeySpec(cipherKey, AES);

            final var iv = new byte[IV_LENGTH];
            final var cipherTextWithoutIv = new byte[cipherText.length - IV_LENGTH];

            System.arraycopy(cipherText, 0, iv, 0, IV_LENGTH);
            System.arraycopy(cipherText, IV_LENGTH, cipherTextWithoutIv, 0, cipherTextWithoutIv.length);

            decryptCipher.init(Cipher.DECRYPT_MODE, secretKeySpec, new IvParameterSpec(iv));

            return decryptCipher.doFinal(cipherTextWithoutIv);

        } catch (final Exception e) {
            log.error("Error occurred while decryption using AES", e);
            throw KaizenException.propagate(KaizenResponseCode.INTERNAL_SERVER_ERROR, e);
        }
    }

    /**
     * Converts a number into byte array
     *
     * @param num Number to be converted
     * @param size Length of the final byte array. size > 0
     * @return Converted byte array
     */
    public byte[] convertNumberToByteArray(int num,
                                           final int size) {

        if (size < 1) {
            throw new IllegalArgumentException("Byte array size must be greater than 0");
        }

        final byte[] byteArray = new byte[size];

        for (int i = size - 1; i >= 0; i--) {
            byteArray[i] = (byte) (num & 0xFF);
            num >>>= 8; // Shift 8 bits to the right
        }

        return byteArray;
    }

    /**
     * Converts a byte array to an integer
     *
     * @param byteArray Byte array to be converted
     * @return Converted Integer
     */
    public int convertByteArrayToNumber(final byte[] byteArray) {

        int num = 0;
        final int size = byteArray.length;
        for (int i = 0; i < size; i++) {
            num |= (byteArray[i] & 0xFF) << (8 * (size - 1 - i));
        }

        return num;
    }

    /**
     * Checks if the given byte array contains the Magic bytes prefix
     *
     * @param byteArray Byte array to be checked
     * @return True, if Magic bytes are present, else False
     */
    public boolean checkIfMagicBytesArePresent(final byte[] byteArray) {

        return Arrays.mismatch(magicBytes, 0, magicBytes.length, byteArray, 0, magicBytes.length) == -1;
    }

    /**
     * Hides the given byte array data by adding the Magic Bytes as prefix
     *
     * @param byteData Byte array data to be hidden
     * @return Hidden byte array
     */
    public byte[] hideByMagicBytes(final byte[] byteData) {

        //hiding key version with magic bytes
        final var hiddenKeyVersion = new byte[magicBytes.length + byteData.length];
        System.arraycopy(magicBytes, 0, hiddenKeyVersion, 0, magicBytes.length);
        System.arraycopy(byteData, 0, hiddenKeyVersion, magicBytes.length, byteData.length);
        return hiddenKeyVersion;
    }

    private byte[] getHiddenBytesFromTextOrDefault(final byte[] text,
                                                   final byte[] defaultBytes) {

        if (checkIfMagicBytesArePresent(text)) {

            final var hiddenBytes = new byte[KEY_VERSION_BYTES_LENGTH];
            System.arraycopy(text, magicBytes.length, hiddenBytes, 0, KEY_VERSION_BYTES_LENGTH);

            return hiddenBytes;
        }
        return defaultBytes;
    }
}
