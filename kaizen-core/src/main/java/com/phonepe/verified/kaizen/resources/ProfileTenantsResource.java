package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Set;
import javax.annotation.security.RolesAllowed;
import javax.validation.constraints.NotEmpty;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/profile/organization")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Profile Tenant", description = "Profile Tenant Related APIs")
public class ProfileTenantsResource {

    private final ProfileService profileService;

    @GET
    @RolesAllowed(OlympusPermissionNames.TENANT_DETAILS)
    public Set<String> getOrganisations(@QueryParam("profileType") final ProfileType profileType,
                                        @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {
        return profileService.getAllOrganisations(profileType);
    }

    @GET
    @Path("/{organization}/namespace")
    @RolesAllowed(OlympusPermissionNames.TENANT_DETAILS)
    public Set<String> getNamespaces(@PathParam("organization") @NotEmpty final String organization,
                                     @QueryParam("profileType") final ProfileType profileType,
                                     @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {
        return profileService.getNamespaces(organization, profileType);
    }

    @GET
    @Path("/{organization}/namespace/{namespace}/type")
    @RolesAllowed(OlympusPermissionNames.TENANT_DETAILS)
    public Set<String> getWorkflowTypes(@PathParam("organization") @NotEmpty final String organization,
                                        @PathParam("namespace") @NotEmpty final String namespace,
                                        @QueryParam("profileType") final ProfileType profileType,
                                        @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {
        return profileService.getTypes(organization, namespace, profileType);
    }

    @GET
    @Path("/{organization}/namespace/{namespace}/type/{type}/version")
    @RolesAllowed(OlympusPermissionNames.TENANT_DETAILS)
    public Set<String> getProfileVersions(@PathParam("organization") @NotEmpty final String organization,
                                          @PathParam("namespace") @NotEmpty final String namespace,
                                          @PathParam("type") @NotEmpty final String type,
                                          @QueryParam("profileType") final ProfileType profileType,
                                          @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {
        return profileService.getVersions(organization, namespace, type, profileType);
    }

    @GET
    @Path("/{organization}/namespace/{namespace}/type/{type}/version/{version}/add-on")
    @RolesAllowed(OlympusPermissionNames.TENANT_DETAILS)
    public Set<String> getProfileAddOnTypes(@PathParam("organization") @NotEmpty final String organization,
                                            @PathParam("namespace") @NotEmpty final String namespace,
                                            @PathParam("type") @NotEmpty final String type,
                                            @PathParam("version") @NotEmpty final String version,
                                            @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {
        return profileService.getAddOnTypes(organization, namespace, type, version);
    }
}
