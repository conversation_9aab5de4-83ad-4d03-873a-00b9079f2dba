package com.phonepe.verified.kaizen.models.states;

import java.util.Set;

public enum TransitionState {

    // todo{Ankit}:: Use State enum

    CREATED {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitCreated(data);
        }
    },
    INITIAL_ACTION_IN_PROGRESS {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitInitialActionInProgress(data);
        }
    },
    READY {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitReady(data);
        }
    },
    IN_PROGRESS {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitInProgress(data);
        }
    },
    SUCCESS {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSuccess(data);
        }
    },
    PSEUDO_SUCCESS {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPseudoSuccess(data);
        }
    },
    FAILURE {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitFailure(data);
        }
    },
    ABORTED {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAborted(data);
        }
    },
    SKIPPED {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSkipped(data);
        }
    },
    AUTO_SKIPPED {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAutoSkipped(data);
        }
    },
    DISCARDED {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitDiscarded(data);
        }
    },
    INVALIDATED {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitInvalidated(data);
        }
    },
    PURGED {
        @Override
        public <T, J> T accept(final TransitionStateVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPurged(data);
        }
    };

    public static final Set<TransitionState> STATES_FROM_WHICH_FAILURE_CAN_BE_TRIGGERED = Set.of(IN_PROGRESS,
            PSEUDO_SUCCESS);

    public static final Set<TransitionState> WORKFLOW_STATES_FROM_WHICH_WORKFLOW_STEPS_CAN_BE_INVALIDATED = Set.of(
            CREATED, IN_PROGRESS, FAILURE, PSEUDO_SUCCESS);

    public static final Set<TransitionState> PENDING_STATES = Set.of(CREATED, IN_PROGRESS);

    public static final Set<TransitionState> SUCCESS_STATES = Set.of(SUCCESS, PSEUDO_SUCCESS);

    public static final Set<TransitionState> COMPLETION_STATES_TO_BE_TAGGED = Set.of(SUCCESS, SKIPPED, AUTO_SKIPPED);

    public static final Set<TransitionState> FILTERED_STATES_FOR_CANCEL = Set.of(DISCARDED, ABORTED, INVALIDATED);

    public static final Set<TransitionState> NON_SUCCESS_COMPLETED_STATES = Set.of(FAILURE, ABORTED, DISCARDED);

    public static final Set<TransitionState> WORKFLOW_NOT_READY = Set.of(CREATED, INITIAL_ACTION_IN_PROGRESS);

    public static final Set<TransitionState> DEDUPE_CONSIDERABLE_WORKFLOW_STATES = Set.of(IN_PROGRESS, SUCCESS,
            PSEUDO_SUCCESS, FAILURE, SKIPPED, AUTO_SKIPPED, DISCARDED);

    public static final Set<TransitionState> DEDUPE_CONSIDERABLE_WORKFLOW_STEP_STATES = Set.of(IN_PROGRESS, SUCCESS,
            PSEUDO_SUCCESS, FAILURE, SKIPPED, AUTO_SKIPPED, DISCARDED);

    public abstract <T, J> T accept(TransitionStateVisitor<T, J> visitor,
                                    J data);

    public interface TransitionStateVisitor<T, J> {

        T visitCreated(J data);

        T visitInProgress(J data);

        T visitSuccess(J data);

        T visitFailure(J data);

        T visitAborted(J data);

        T visitSkipped(J data);

        T visitAutoSkipped(J data);

        T visitPseudoSuccess(J data);

        T visitInitialActionInProgress(J data);

        T visitReady(J data);

        T visitDiscarded(J data);

        T visitInvalidated(J data);

        T visitPurged(J data);
    }
}
