package com.phonepe.verified.kaizen.services;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.request.SearchFieldRequest;

public interface SearchService {

    void fetchSearchDetails(final String workflowId,
                            final String fieldId,
                            final String intent,
                            final long componentKitVersion,
                            final RequestInfo requestInfo,
                            final SearchFieldRequest searchFieldRequest);
}
