package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.shadow.page.field.Field;
import com.phonepe.shadow.page.field.properties.ApiProperties;
import com.phonepe.shadow.page.field.properties.BooleanProperties;
import com.phonepe.shadow.page.field.properties.Properties;
import com.phonepe.shadow.page.field.properties.PropertiesVisitor;
import com.phonepe.shadow.page.field.properties.RetryableProperties;
import com.phonepe.shadow.page.field.properties.SortProperties;
import com.phonepe.shadow.page.field.properties.WidgetActionProperties;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class FieldPropertyModifier implements PropertiesVisitor<Void, Field<?>> {

    private final boolean expressionResult;

    private final JsonNode workflowContext;

    private final HandleBarsService handleBarsService;

    @Override
    public Void visit(final ApiProperties apiProperties,
                      final Field<?> field) {
        log.warn("ApiProperties is currently not supported");
        return null;
    }

    @Override
    public Void visit(final Properties<?> properties,
                      final Field<?> field) {

        if (expressionResult) {

            if (Objects.nonNull(properties.getDefaultValue())) {

                field.visit(new DefaultValueConvertorFieldVisitor(properties.getDefaultValue(), workflowContext,
                        handleBarsService));
            }

            if (Objects.nonNull(properties.getValues())) {

                field.visit(
                        new ValuesConvertorFieldVisitor(properties.getValues(), workflowContext, handleBarsService));
            }

            if (Objects.nonNull(properties.getVisible())) {

                field.setVisible(properties.getVisible());
            }

            if (Objects.nonNull(properties.getOptional())) {

                field.setOptional(properties.getOptional());
            }

            if (Objects.nonNull(properties.getEditable())) {

                field.setEditable(properties.getEditable());
            }

            if (Objects.nonNull(properties.getEnabled())) {

                field.setEnabled(properties.getEnabled());
            }

        }
        return null;
    }

    @Override
    public Void visit(final SortProperties sortProperties,
                      final Field<?> field) {
        log.warn("SortProperties is currently not supported");
        return null;
    }

    @Override
    public Void visit(final BooleanProperties booleanProperties,
                      final Field<?> field) {
        booleanProperties.getProperties()
                .forEach(p -> setBooleanPropertyValueWithExpressionResult(field, p));
        return null;
    }

    @Override
    public Void visit(final RetryableProperties retryableProperties,
                      final Field<?> field) {
        log.warn("RetryableProperties is currently not supported");
        return null;
    }

    @Override
    public Void visit(WidgetActionProperties widgetActionProperties,
                      Field<?> field) {
        log.warn("widgetActionProperties is currently not supported");
        return null;
    }

    private void setBooleanPropertyValueWithExpressionResult(final Field<?> field,
                                                             final String property) {
        try {
            switch (property) {
                case "editable" -> field.setEditable(expressionResult);
                case "optional" -> field.setOptional(expressionResult);
                case "visible" -> field.setVisible(expressionResult);
                case "deferred" -> field.setDeferred(expressionResult);
                case "active" -> field.setActive(expressionResult);
                case "enabled" -> field.setEnabled(expressionResult);
                default -> log.warn("Property is not supported. property: {}", property);
            }
        } catch (final Exception e) {
            log.warn("Failed to set boolean property due to exception. property: {}, value: {}", property,
                    expressionResult, e);
        }
    }
}
