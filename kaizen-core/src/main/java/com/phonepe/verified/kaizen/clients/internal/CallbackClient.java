package com.phonepe.verified.kaizen.clients.internal;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.verified.kaizen.caches.impl.ClientCallbackCache;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.responses.WorkflowClientCallback;
import com.phonepe.verified.kaizen.queue.messages.WorkflowClientCallbackMessage;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.CallbackWorkflowMessageBuilderProfileVisitor;
import com.phonepe.verified.kaizen.services.visitors.CallbackWorkflowMessageBuilderProfileVisitor.CallbackWorkflowMessageBuilderProfileVisitorData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import com.phonepe.zeus.models.Farm;
import io.dropwizard.util.Strings;
import java.util.List;
import java.util.Optional;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class CallbackClient {

    private final ProfileService profileService;

    private final OlympusIMClient olympusIMClient;

    private final WorkflowService workflowService;

    private final HttpClientRegistry httpClientRegistry;

    private final ClientCallbackCache clientCallbackCache;

    private final EventIngestionCommand eventIngestionCommand;

    public void sendCallback(final WorkflowClientCallbackMessage workflowClientCallbackMessage) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(
                workflowClientCallbackMessage.getWorkflowId());

        final var clientCallbackConfigOptional = clientCallbackCache.get(storedWorkflow.getProfileId());

        if (clientCallbackConfigOptional.isEmpty() || Strings.isNullOrEmpty(clientCallbackConfigOptional.get()
                .getCallbackUrl()) || Strings.isNullOrEmpty(clientCallbackConfigOptional.get()
                .getCallbackService())) {

            // This is ideally neither an error nor a warning, its just a no-op. Keeping it for now.
            log.warn("Client Callback Config not available for profileId: {}, workflowId {}",
                    storedWorkflow.getProfileId(), storedWorkflow.getWorkflowId());
            return;
        }

        final var callbackServiceWithoutFarm = clientCallbackConfigOptional.get()
                .getCallbackService();

        sendCallback(workflowClientCallbackMessage, storedWorkflow, callbackServiceWithoutFarm,
                clientCallbackConfigOptional.get()
                        .getCallbackUrl(), storedWorkflow.getCallerFarmId());
    }

    public void sendCallback(final WorkflowClientCallbackMessage workflowClientCallbackMessage,
                             final String callbackService,
                             final String callbackUrl,
                             final Farm farm) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(
                workflowClientCallbackMessage.getWorkflowId());

        sendCallback(workflowClientCallbackMessage, storedWorkflow, callbackService, callbackUrl, farm);
    }

    public void sendCallback(final WorkflowClientCallbackMessage workflowClientCallbackMessage,
                             final StoredWorkflow storedWorkflow,
                             final String callbackService,
                             final String callbackUrl,
                             final Farm farm) {

        final var farmId = Optional.ofNullable(farm)
                .map(Enum::name)
                .orElse("");

        final var callbackServiceWithFarm = callbackService + farmId;

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var callbackWorkflowMessageBuilderProfileVisitorData = CallbackWorkflowMessageBuilderProfileVisitorData.builder()
                .storedWorkflow(storedWorkflow)
                .failureErrorCode(workflowClientCallbackMessage.getFailureErrorCode())
                .accessToken(workflowClientCallbackMessage.getAccessToken())
                .requestId(workflowClientCallbackMessage.getRequestId())
                .build();

        final WorkflowClientCallback workflowClientCallback = profile.accept(
                CallbackWorkflowMessageBuilderProfileVisitor.INSTANCE,
                callbackWorkflowMessageBuilderProfileVisitorData);

        final var httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(
                callbackServiceWithFarm);

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        final var command = String.format("sendCallback_%s", callbackServiceWithFarm);

        try {
            HttpClientUtils.executePost(httpExecutorBuilderFactory, command, callbackUrl,
                    new SerializableHttpData(MediaType.APPLICATION_JSON, workflowClientCallback), List.of(authHeader),
                    byte[].class, getClass());

            eventIngestionCommand.callbackSentToClientEvent(profile, true, null, callbackUrl, callbackServiceWithFarm,
                    storedWorkflow);
        } catch (final Exception e) {
            log.error("Client Callback failed for workflowId {}, callbackUrl {}, callbackService{} ",
                    storedWorkflow.getWorkflowId(), callbackUrl, callbackServiceWithFarm);

            eventIngestionCommand.callbackSentToClientEvent(profile, false, e.getMessage(), callbackUrl,
                    callbackServiceWithFarm, storedWorkflow);
            throw KaizenException.propagate(e);
        }
    }
}
