package com.phonepe.verified.kaizen.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.queue.ActorType;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.RabbitmqActorBundle;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class RabbitmqModule extends AbstractModule {

    private final RabbitmqActorBundle<? extends KaizenConfig> rabbitmqActorBundle;

    @Provides
    @Singleton
    public Map<ActorType, ActorConfig> actorConfigMap(final KaizenConfig config) {
        return config.getActorsConfig();
    }

    @Provides
    @Singleton
    public RetryStrategyFactory retryStrategyFactory() {
        return new RetryStrategyFactory();
    }

    @Provides
    @Singleton
    public ExceptionHandlingFactory exceptionHandlingFactory() {
        return new ExceptionHandlingFactory();
    }

    @Provides
    public ConnectionRegistry connectionRegistry() {
        return rabbitmqActorBundle.getConnectionRegistry();
    }
}
