package com.phonepe.verified.kaizen.queue.messages.eventingestion;

import com.phonepe.platform.vision.data.OperationStatus;
import com.phonepe.verified.kaizen.foxtrot.events.subevents.CallbackReceivedFromClientSubEventType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CallbackReceivedFromDrishtiMessage {

    private String requestId;

    private Boolean success;

    private Boolean clockworkCall;

    private OperationStatus operationStatus;

    private ActionMetadataType actionMetadataType;

    @NotNull
    private CallbackReceivedFromClientSubEventType callbackReceivedFromClientSubEventType;
}
