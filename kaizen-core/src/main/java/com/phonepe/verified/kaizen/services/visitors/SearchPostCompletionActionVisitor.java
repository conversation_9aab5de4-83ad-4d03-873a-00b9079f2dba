package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionType;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.AndPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.ClientCallbackPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.OrPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.TagCalculationOnEntityDetailsPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.TagCalculationOnWorkflowContextPostCompletionActionConfig;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SearchPostCompletionActionVisitor implements
        PostCompletionActionConfigVisitor<Boolean, PostCompletionActionType> {

    public static final SearchPostCompletionActionVisitor INSTANCE = new SearchPostCompletionActionVisitor();


    @Override
    public Boolean visit(final AndPostCompletionActionConfig andPostCompletionActionConfig,
                         final PostCompletionActionType postCompletionActionType) {

        final var leftPostCompletionAction = andPostCompletionActionConfig.getLeft()
                .accept(this, postCompletionActionType);

        if (Boolean.TRUE == leftPostCompletionAction) {
            return true;
        }

        return andPostCompletionActionConfig.getRight()
                .accept(this, postCompletionActionType);
    }

    @Override
    public Boolean visit(final OrPostCompletionActionConfig orPostCompletionActionConfig,
                         final PostCompletionActionType postCompletionActionType) {

        final var leftPostCompletionAction = orPostCompletionActionConfig.getLeft()
                .accept(this, postCompletionActionType);

        if (Boolean.TRUE == leftPostCompletionAction) {
            return true;
        }

        return orPostCompletionActionConfig.getRight()
                .accept(this, postCompletionActionType);
    }

    @Override
    public Boolean visit(final ClientCallbackPostCompletionActionConfig clientCallbackPostCompletionActionConfig,
                         final PostCompletionActionType postCompletionActionType) {

        return postCompletionActionType == clientCallbackPostCompletionActionConfig.getType();
    }

    @Override
    public Boolean visit(final TagCalculationOnWorkflowContextPostCompletionActionConfig tagCalculationOnWorkflowContextPostCompletionActionConfig,
                         final PostCompletionActionType postCompletionActionType) {

        return postCompletionActionType == tagCalculationOnWorkflowContextPostCompletionActionConfig.getType();
    }

    @Override
    public Boolean visit(final TagCalculationOnEntityDetailsPostCompletionActionConfig tagCalculationOnEntityDetailsPostCompletionActionConfig,
                         final PostCompletionActionType postCompletionActionType) {

        return postCompletionActionType == tagCalculationOnEntityDetailsPostCompletionActionConfig.getType();
    }
}
