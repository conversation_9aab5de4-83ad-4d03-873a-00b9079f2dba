package com.phonepe.verified.kaizen.clients.internal;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.atlas.model.pincodev2.responses.PincodeResolutionResponse;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.ErrorUtils;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import io.appform.functionmetrics.MonitoredFunction;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class AtlasClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    private final OlympusIMClient olympusIMClient;

    @Inject
    public AtlasClient(final HttpClientRegistry httpClientRegistry,
                       final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(ClientIds.ATLAS);
        this.olympusIMClient = olympusIMClient;
    }

    @MonitoredFunction
    public PincodeResolutionResponse fetchPincodeDetails(final String pincode,
                                                         final List<String> sources) {

        final var pincodeSources = String.join(",", sources);
        final var url = String.format("/v2/pincodes/%s?sources=%s", pincode, pincodeSources);

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "fetchPincodeDetails", url, List.of(authHeader),
                PincodeResolutionResponse.class, ErrorUtils.nonSuccess4xxGracefulResponseHandler(null), getClass());
    }

}
