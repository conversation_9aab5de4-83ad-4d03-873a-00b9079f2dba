package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RevolverCallbackMessage extends BaseMessage {

    @NonNull
    private final String requestId;

    private final int status;

    @NonNull
    private final Object data;

    @Builder
    @Jacksonized
    public RevolverCallbackMessage(final RequestInfo requestInfo,
                                   @NonNull final String requestId,
                                   final int status,
                                   @NonNull final Object data) {

        super(ActorMessageType.REVOLVER_CALLBACK, requestInfo);
        this.requestId = requestId;
        this.status = status;
        this.data = data;
    }
}

