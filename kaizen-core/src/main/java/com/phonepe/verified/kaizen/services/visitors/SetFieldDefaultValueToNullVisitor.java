package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.shadow.page.field.Document;
import com.phonepe.shadow.page.field.FieldVisitor;
import com.phonepe.shadow.page.field.impl.AddressField;
import com.phonepe.shadow.page.field.impl.AddressListField;
import com.phonepe.shadow.page.field.impl.AlertBarField;
import com.phonepe.shadow.page.field.impl.ButtonWithActionField;
import com.phonepe.shadow.page.field.impl.ButtonWithEventRuleField;
import com.phonepe.shadow.page.field.impl.CameraWidgetField;
import com.phonepe.shadow.page.field.impl.CameraWidgetV2Field;
import com.phonepe.shadow.page.field.impl.CardCheckboxField;
import com.phonepe.shadow.page.field.impl.CardInfoBadgeField;
import com.phonepe.shadow.page.field.impl.CardWithActionField;
import com.phonepe.shadow.page.field.impl.CarouselField;
import com.phonepe.shadow.page.field.impl.CheckboxField;
import com.phonepe.shadow.page.field.impl.ChipSelectionField;
import com.phonepe.shadow.page.field.impl.CollapsibleListField;
import com.phonepe.shadow.page.field.impl.CrossSellField;
import com.phonepe.shadow.page.field.impl.CurrencyQuickSelectField;
import com.phonepe.shadow.page.field.impl.CustomListField;
import com.phonepe.shadow.page.field.impl.DateField;
import com.phonepe.shadow.page.field.impl.DateRangeField;
import com.phonepe.shadow.page.field.impl.DateV2Field;
import com.phonepe.shadow.page.field.impl.DescriptiveRadioListField;
import com.phonepe.shadow.page.field.impl.DocVerifyWidgetField;
import com.phonepe.shadow.page.field.impl.DocumentFormField;
import com.phonepe.shadow.page.field.impl.DocumentNumberField;
import com.phonepe.shadow.page.field.impl.DrawWidgetField;
import com.phonepe.shadow.page.field.impl.DropdownField;
import com.phonepe.shadow.page.field.impl.DropdownV2Field;
import com.phonepe.shadow.page.field.impl.DropdownWithIconField;
import com.phonepe.shadow.page.field.impl.ErrorField;
import com.phonepe.shadow.page.field.impl.ExtendedFormField;
import com.phonepe.shadow.page.field.impl.FormField;
import com.phonepe.shadow.page.field.impl.FormFieldWithPrefixAndAction;
import com.phonepe.shadow.page.field.impl.FormV2Field;
import com.phonepe.shadow.page.field.impl.FormWithButtonField;
import com.phonepe.shadow.page.field.impl.FullScreenSearchField;
import com.phonepe.shadow.page.field.impl.FullScreenSearchFieldV2;
import com.phonepe.shadow.page.field.impl.FullScreenTransientViewField;
import com.phonepe.shadow.page.field.impl.FullScreenWebViewWidgetField;
import com.phonepe.shadow.page.field.impl.GenericImageWidgetField;
import com.phonepe.shadow.page.field.impl.HorizontalCompactCardListField;
import com.phonepe.shadow.page.field.impl.IconActionWidgetField;
import com.phonepe.shadow.page.field.impl.IconDropdownField;
import com.phonepe.shadow.page.field.impl.IconField;
import com.phonepe.shadow.page.field.impl.IconV2Field;
import com.phonepe.shadow.page.field.impl.IconWithInfoField;
import com.phonepe.shadow.page.field.impl.ImageField;
import com.phonepe.shadow.page.field.impl.InfoLabelField;
import com.phonepe.shadow.page.field.impl.InfoListWidgetField;
import com.phonepe.shadow.page.field.impl.InfoWithSliderField;
import com.phonepe.shadow.page.field.impl.InformationBottomSheet;
import com.phonepe.shadow.page.field.impl.ItemSelectionField;
import com.phonepe.shadow.page.field.impl.ItemSelectionV2Field;
import com.phonepe.shadow.page.field.impl.ItemSelectionV3Field;
import com.phonepe.shadow.page.field.impl.JsonField;
import com.phonepe.shadow.page.field.impl.LabelField;
import com.phonepe.shadow.page.field.impl.LabelWithShowDetail;
import com.phonepe.shadow.page.field.impl.LabelWithShowDetailV2;
import com.phonepe.shadow.page.field.impl.LinearProgressBarField;
import com.phonepe.shadow.page.field.impl.LinkField;
import com.phonepe.shadow.page.field.impl.ListCheckboxField;
import com.phonepe.shadow.page.field.impl.ListWithActionField;
import com.phonepe.shadow.page.field.impl.LoadClientWidgetField;
import com.phonepe.shadow.page.field.impl.LottieWidgetField;
import com.phonepe.shadow.page.field.impl.MediaUploadWidgetField;
import com.phonepe.shadow.page.field.impl.MultiButtonField;
import com.phonepe.shadow.page.field.impl.MultiButtonV2Field;
import com.phonepe.shadow.page.field.impl.MultiButtonV3Field;
import com.phonepe.shadow.page.field.impl.MultiListSearchableCheckboxField;
import com.phonepe.shadow.page.field.impl.MultiPickerField;
import com.phonepe.shadow.page.field.impl.MultiSelectionDropdownField;
import com.phonepe.shadow.page.field.impl.MultiSelectionDropdownFieldV2;
import com.phonepe.shadow.page.field.impl.NotificationBarField;
import com.phonepe.shadow.page.field.impl.OtpField;
import com.phonepe.shadow.page.field.impl.PopupButtonField;
import com.phonepe.shadow.page.field.impl.PopupButtonV2Field;
import com.phonepe.shadow.page.field.impl.ProductListField;
import com.phonepe.shadow.page.field.impl.ProgressTimelineField;
import com.phonepe.shadow.page.field.impl.ProviderLogoField;
import com.phonepe.shadow.page.field.impl.QrWidgetField;
import com.phonepe.shadow.page.field.impl.QuickSelectField;
import com.phonepe.shadow.page.field.impl.RadioButtonField;
import com.phonepe.shadow.page.field.impl.RadioButtonV2Field;
import com.phonepe.shadow.page.field.impl.RichCheckBoxField;
import com.phonepe.shadow.page.field.impl.RichCheckboxWithMultiLinkField;
import com.phonepe.shadow.page.field.impl.RichLabelField;
import com.phonepe.shadow.page.field.impl.SearchField;
import com.phonepe.shadow.page.field.impl.SearchableListField;
import com.phonepe.shadow.page.field.impl.SelectionBottomSheet;
import com.phonepe.shadow.page.field.impl.SelectionInformationWithButtonField;
import com.phonepe.shadow.page.field.impl.SelectionWidgetWithLabel;
import com.phonepe.shadow.page.field.impl.SelectionWidgetWithLabelV2;
import com.phonepe.shadow.page.field.impl.SingleSegmentedSelectField;
import com.phonepe.shadow.page.field.impl.SingleSegmentedSelectV2Field;
import com.phonepe.shadow.page.field.impl.SliderField;
import com.phonepe.shadow.page.field.impl.StepperField;
import com.phonepe.shadow.page.field.impl.StreamingDescriptiveListField;
import com.phonepe.shadow.page.field.impl.StyleLabelWidgetField;
import com.phonepe.shadow.page.field.impl.TableWidgetField;
import com.phonepe.shadow.page.field.impl.TemplatizedJsonField;
import com.phonepe.shadow.page.field.impl.ToggleButtonField;
import com.phonepe.shadow.page.field.impl.TooltipConsentField;
import com.phonepe.shadow.page.field.impl.WebviewField;
import com.phonepe.shadow.page.field.impl.YesNoField;
import com.phonepe.shadow.page.field.impl.insurance.DisclaimerField;
import com.phonepe.shadow.page.field.impl.insurance.InformativeCardField;
import com.phonepe.shadow.page.field.impl.insurance.InsurancePlanField;
import com.phonepe.shadow.page.field.impl.insurance.InsurancePriceDetailsField;
import com.phonepe.shadow.page.field.impl.insurance.InsurancePriceDetailsV2Field;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewWidgetField;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SetFieldDefaultValueToNullVisitor implements FieldVisitor<Void> {

    public static final SetFieldDefaultValueToNullVisitor INSTANCE = new SetFieldDefaultValueToNullVisitor();


    @Override
    public Void visit(final JsonField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CheckboxField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DateField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DateV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DropdownField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DropdownV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DropdownWithIconField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final FormField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final FormV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final OtpField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final RadioButtonField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CustomListField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final WebviewField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final AddressField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final LinkField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final QuickSelectField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ErrorField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final MultiListSearchableCheckboxField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DateRangeField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final StepperField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SliderField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final LabelField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final RichLabelField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final YesNoField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ListCheckboxField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CarouselField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CollapsibleListField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DescriptiveRadioListField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final InsurancePlanField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ToggleButtonField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final InsurancePriceDetailsField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final InsurancePriceDetailsV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final RichCheckBoxField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SelectionBottomSheet field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DisclaimerField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final InformationBottomSheet field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SelectionWidgetWithLabel field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final FullScreenSearchField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SelectionWidgetWithLabelV2 field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final TemplatizedJsonField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final MultiButtonField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final MultiButtonV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SearchField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final FormWithButtonField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ItemSelectionField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final FormFieldWithPrefixAndAction field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ItemSelectionV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CurrencyQuickSelectField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final RichCheckboxWithMultiLinkField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SelectionInformationWithButtonField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SingleSegmentedSelectField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SingleSegmentedSelectV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final LabelWithShowDetail field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final LabelWithShowDetailV2 field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final InfoLabelField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final InformativeCardField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DocumentNumberField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final MultiPickerField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CrossSellField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final IconDropdownField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final MultiButtonV3Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final RadioButtonV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final HorizontalCompactCardListField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final IconField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final IconV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ExtendedFormField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SearchableListField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ProductListField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CardCheckboxField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final LinearProgressBarField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final PopupButtonField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final AlertBarField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CardInfoBadgeField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DocVerifyWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final MediaUploadWidgetField field) {

        field.setDefaultValue(null);
        final List<Document> out = field.getDocumentTypes();
        out.forEach(x -> x.getPlaceholderList()
                .forEach(y -> y.setDocumentId(null)));
        return null;
    }

    @Override
    public Void visit(final StreamingDescriptiveListField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ProgressTimelineField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final MultiSelectionDropdownField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CameraWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DrawWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final GenericImageWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final StyleLabelWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ProviderLogoField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final TableWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ImageField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final NotificationBarField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final IconActionWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final InfoListWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final DocumentFormField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final SummaryViewWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final PopupButtonV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ButtonWithActionField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final LottieWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final IconWithInfoField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final LoadClientWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final FullScreenWebViewWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final FullScreenSearchFieldV2 field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final AddressListField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final CameraWidgetV2Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final InfoWithSliderField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final FullScreenTransientViewField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ButtonWithEventRuleField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final TooltipConsentField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ChipSelectionField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final MultiSelectionDropdownFieldV2 field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ItemSelectionV3Field field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final ListWithActionField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(final QrWidgetField field) {
        field.setDefaultValue(null);
        return null;
    }

    @Override
    public Void visit(CardWithActionField field) {
        field.setDefaultValue(null);
        return null;
    }
}
