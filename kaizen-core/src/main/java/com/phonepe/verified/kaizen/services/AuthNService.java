package com.phonepe.verified.kaizen.services;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.primer.PrimerTokenResponseV2;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2LoginRequest;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2LogoutRequest;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2RefreshRequest;
import com.phonepe.verified.kaizen.storage.aerospike.data.AccessTokenData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import org.jose4j.jwt.JwtClaims;

public interface AuthNService {

    PrimerTokenResponseV2 getPrimerV2Token(String actxHeader,
                                           RequestInfo requestInfo,
                                           StoredWorkflow storedWorkflow,
                                           AccessTokenData accessTokenData);

    String createAndGetAccessToken(String workflowId,
                                   String userReferenceId);

    void deleteAccessToken(String accessToken);

    AccessTokenData validateAndGetAccessToken(AuthenticationV2LoginRequest authenticationV2LoginRequest);

    void clear(AuthenticationV2LogoutRequest authenticationV2LogoutRequest);

    PrimerTokenResponseV2 refresh(String actxHeader,
                                  AuthenticationV2RefreshRequest authenticationV2RefreshRequest);

    String getEffectiveToken(String authToken);

    JwtClaims getJwtClaims(String authToken);
}
