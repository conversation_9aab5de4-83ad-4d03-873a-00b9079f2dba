package com.phonepe.verified.kaizen.statemachines.actions.documentuploadv3;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.contexts.DocumentUploadActionTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.*;
import com.phonepe.verified.kaizen.services.visitors.DocumentTypeMaskingVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.statemachines.actions.documentuploadv2.CreateDocumentUploadActionV2;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadWithMetadataActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants;
import io.dropwizard.setup.Environment;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

import javax.validation.Validator;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Singleton
@ActionKey(value = "createDocumentUploadActionV3")
public class CreateDocumentUploadActionV3 extends CreateDocumentUploadActionV2 {
    private final ActionMetadataStoreCommand actionMetadataStoreCommand;

    private final Provider<ActionExecutorActor> actionExecutorActorProvider;

    @Inject
    public CreateDocumentUploadActionV3(final ActionService actionService,
                                        final WorkflowService workflowService,
                                        final ClockworkClient clockworkClient,
                                        final ActionRepository actionRepository,
                                        final WorkflowStepService workflowStepService,
                                        final DataProvider<KaizenConfig> appConfigDataProvider,
                                        final Provider<WorkflowContextStore> workflowContextStore,
                                        final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                        final ActionMetadataStoreCommand actionMetadataStoreCommand,
                                        final Provider<EventIngestionActor> eventIngestionActorProvider,
                                        final DocumentService documentService,
                                        final Environment environment,
                                        final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor,
                                        final DocumentTypeMaskingVisitor documentTypeMaskingVisitor,
                                        final Provider<ActionExecutorActor> actionExecutorActorProvider1) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, actionExecutorActorProvider, actionMetadataStoreCommand,
                eventIngestionActorProvider, documentService, environment, getDependentActionMappingIdDependencyConfigVisitor,
                documentTypeMaskingVisitor, actionExecutorActorProvider1);
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
        this.actionExecutorActorProvider = actionExecutorActorProvider1;
    }

    @Override
    @SneakyThrows
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var documentUploadActionTransitionContext = stateContext.getExtendedState()
                .get(TransitionContext.class, DocumentUploadActionTransitionContext.class);

        final var documentActionMetadata = DocumentUploadWithMetadataActionMetadata.builder()
                .documents(documentUploadActionTransitionContext.getDocuments())
                .build();

        actionMetadataStoreCommand.save(ActionMetadataStoreKey.builder()
                .actionId(storedAction.getActionId())
                .build(), documentActionMetadata);
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        actionExecutorActorProvider.get()
                .publish(ActionExecutionMessage.builder()
                        .actionId(storedAction.getActionId())
                        .eventToTrigger(StateMachineConstants.Events.DOCUMENT_UPLOAD_SUCCEEDED)
                        .userDetails(Constants.PVCORE_SYSTEM_USER)
                        .build());
    }
}
