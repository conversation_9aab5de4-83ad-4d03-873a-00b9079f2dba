package com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext;

import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowStepContext {

    private String workflowStepId;

    private String workflowId;

    private String profileStepId;

    private TransitionState currentState;

    private TransitionEvent currentEvent;

    private String lastUpdatedBy;

    private UpdaterType updaterType;

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;

    //key storedAction.getScreenMappingId
    @Builder.Default
    private Map<String, WorkflowScreenContext> workflowScreenContextMap = new HashMap<>();

}
