package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class StepActionMessage extends BaseMessage {

    @NonNull
    private final String actionId;

    @Builder
    @Jacksonized
    public StepActionMessage(final RequestInfo requestInfo,
                             @NonNull final String actionId) {

        super(ActorMessageType.STEP_ACTION, requestInfo);
        this.actionId = actionId;
    }
}
