package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.annotation.Nullable;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WorkflowStepV2Message extends BaseMessage {

    @NonNull
    private final String workflowStepId;

    @Nullable
    private final String actionId;

    @Builder
    @Jacksonized
    public WorkflowStepV2Message(final RequestInfo requestInfo,
                                 @NonNull final String workflowStepId,
                                 @Nullable final String actionId) {

        super(ActorMessageType.WORKFLOW_STEP_V2, requestInfo);
        this.workflowStepId = workflowStepId;
        this.actionId = actionId;
    }
}
