package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.EvaluationRuleResponseConfig;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.template.impl.ShadowV2UiResponseConfig;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GetAllEvaluationRuleUiResponseConfigVisitor implements
        UiResponseConfigVisitor<List<EvaluationRuleResponseConfig>> {

    public static final GetAllEvaluationRuleUiResponseConfigVisitor INSTANCE = new GetAllEvaluationRuleUiResponseConfigVisitor();

    @Override
    @SneakyThrows
    public List<EvaluationRuleResponseConfig> visit(final ShadowV2UiResponseConfig shadowV2TemplateConfig) {
        return shadowV2TemplateConfig.getShadowV2ResponseConfig()
                .accept(GetAllEvaluationRuleShadowV2ResponseVisitor.INSTANCE, null);
    }

}