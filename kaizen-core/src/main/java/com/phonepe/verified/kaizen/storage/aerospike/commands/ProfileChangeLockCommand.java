package com.phonepe.verified.kaizen.storage.aerospike.commands;

import com.aerospike.client.IAerospikeClient;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeCommand;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeSet;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ProfileIdentifierKey;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class ProfileChangeLockCommand extends AerospikeCommand<ProfileIdentifierKey, String> {

    @Inject
    public ProfileChangeLockCommand(IAerospikeClient aerospikeClient,
                                    AerospikeConfig aerospikeConfig) {
        super(aerospikeClient, aerospikeConfig, AerospikeSet.PROFILE_CHANGE_LOCK, String.class);
    }
}
