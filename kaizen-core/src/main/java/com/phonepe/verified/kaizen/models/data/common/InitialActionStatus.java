package com.phonepe.verified.kaizen.models.data.common;

import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitialActionStatus {

    private StandardStepActionConfig standardStepActionConfig;

    @With
    private CompletionState completionState;

    private ActionFailureErrorCode actionFailureErrorCode;

}
