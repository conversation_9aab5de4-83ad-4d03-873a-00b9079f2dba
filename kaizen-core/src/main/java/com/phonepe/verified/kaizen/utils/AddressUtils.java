package com.phonepe.verified.kaizen.utils;

import com.google.common.base.Strings;
import java.util.Arrays;
import java.util.List;
import lombok.experimental.UtilityClass;
import org.apache.commons.math3.util.Pair;

@UtilityClass
public class AddressUtils {

    private final String COMMA_DELIMITER = ",";
    private final String COMMA_SPACE_DELIMITER = ", ";
    private final String SPACE_DELIMITER = " ";
    private final int MAX_FIELD_LENGTH_LINE1 = 40;
    private final int MAX_FIELD_LENGTH_LINE2 = 128;

    private Pair<Integer, List<String>> getPivotIndexWithListOfStringsSplittedOnDelimiter(final String address,
                                                                                          final String delimiter) {

        final var delimitedAddressList = Arrays.stream(address.split(delimiter))
                .map(String::trim)
                .filter(each -> !each.isEmpty())
                .toList();

        if (delimitedAddressList.size() > 1) {

            int index = -1;
            int allowedLength = MAX_FIELD_LENGTH_LINE1;

            while (index < delimitedAddressList.size() - 1) {
                if ((allowedLength - delimitedAddressList.get(index + 1)
                        .length() - 1) >= 0) {
                    index += 1;
                    allowedLength -= (delimitedAddressList.get(index)
                            .length() + 1);
                } else {
                    break;
                }
            }

            if (index == -1) {

                //when we are not able to split input string such that first line meets all the given conditions
                return new Pair<>(-1, List.of());
            } else {

                //when we are able to split input string into more than one lines
                return new Pair<>(index, delimitedAddressList);
            }

        } else {

            //when the given delimiter is missing in the input
            return new Pair<>(-1, List.of());
        }
    }

    private Pair<String, String> joinDelimitedStringListToLineLine2(final String address,
                                                                    final int pivotIndex,
                                                                    final List<String> delimitedAddressList,
                                                                    final String joiningDelimiter) {

        if (Strings.isNullOrEmpty(address)) {
            return new Pair<>("", "");
        }

        final var addressLine1 = String.join(joiningDelimiter, delimitedAddressList.subList(0, pivotIndex + 1));
        var addressLine2 = String.join(joiningDelimiter,
                delimitedAddressList.subList(pivotIndex + 1, delimitedAddressList.size()));
        addressLine2 = getSubString(addressLine2, 0, MAX_FIELD_LENGTH_LINE2);

        return new Pair<>(addressLine1, addressLine2);
    }

    private Pair<String, String> splitStringByLengthIntoLine1Line2(final String address) {

        return new Pair<>(getSubString(address.trim(), 0, MAX_FIELD_LENGTH_LINE1),
                getSubString(address.trim(), MAX_FIELD_LENGTH_LINE1, MAX_FIELD_LENGTH_LINE1 + MAX_FIELD_LENGTH_LINE2));
    }

    private String getSubString(final String address,
                                final int startIndex,
                                final int endIndex) {

        if (startIndex >= address.length()) {
            return "";
        }

        return endIndex >= address.length()
               ? address.substring(startIndex)
               : address.substring(startIndex, endIndex);
    }


    private Pair<String, String> splitAddressOnDelimitersWithFallbacks(final String address) {

        //Start with using comma as delimiter to split the string
        var pivotIndexDelimitedAddressListPair = getPivotIndexWithListOfStringsSplittedOnDelimiter(address,
                COMMA_DELIMITER);
        var pivotIndex = pivotIndexDelimitedAddressListPair.getKey();

        if (pivotIndex != -1) {

            return joinDelimitedStringListToLineLine2(address, pivotIndex,
                    pivotIndexDelimitedAddressListPair.getValue(), COMMA_SPACE_DELIMITER);
        } else {

            //fallback to split string using space as delimiter
            pivotIndexDelimitedAddressListPair = getPivotIndexWithListOfStringsSplittedOnDelimiter(address,
                    SPACE_DELIMITER);
            pivotIndex = pivotIndexDelimitedAddressListPair.getKey();

            if (pivotIndex != -1) {

                return joinDelimitedStringListToLineLine2(address, pivotIndex,
                        pivotIndexDelimitedAddressListPair.getValue(), SPACE_DELIMITER);
            } else {

                //fallback to split string by length
                return splitStringByLengthIntoLine1Line2(address);
            }
        }
    }

    public String splitAddress(final String address,
                               final boolean firstLine) {

        final var result = splitAddressOnDelimitersWithFallbacks(address);

        return firstLine
               ? result.getKey()
               : result.getValue();
    }
}
