package com.phonepe.verified.kaizen.services.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.platform.columbus.models.core.request.unit.UnitSearchRequest;
import com.phonepe.platform.columbus.models.core.search.query.FilterSelection;
import com.phonepe.platform.columbus.models.core.search.query.SearchQuery;
import com.phonepe.platform.columbus.models.core.search.query.sorter.UnidirectionalSorterSelection;
import com.phonepe.platform.columbus.models.core.search.value.ExactValue;
import com.phonepe.shadow.page.field.FullScreenSearchFieldV2Response;
import com.phonepe.shadow.page.field.Value;
import com.phonepe.verified.kaizen.clients.internal.CatalogueClient;
import com.phonepe.verified.kaizen.models.data.search.catalogue.CatalogueSearchSourceConfig;
import com.phonepe.verified.kaizen.models.responses.search.CatalogueSearchDataItem;
import com.phonepe.verified.kaizen.services.CatalogueService;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.dropwizard.util.Strings;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class CatalogueServiceImpl implements CatalogueService {

    private static final String FILTER_PATTERN_FOR_SEARCH_BY_PRIMARY_KEY = "#=:_id";

    private static final String SORT_PATTERN_FOR_SEARCH_BY_PRIMARY_KEY = "#+_id";

    private final CatalogueClient catalogueClient;

    @Override
    public FullScreenSearchFieldV2Response getCatalogueSearchDetailsForClient(final CatalogueSearchSourceConfig catalogueSearchSourceConfig,
                                                                              final String searchString,
                                                                              final int pageNumber,
                                                                              final int pageSize) {

        final var filterSelection = FilterSelection.builder()
                .filterName(catalogueSearchSourceConfig.getFilterPattern())
                .value(new ExactValue<>(String.format("*%s*", searchString.toUpperCase())))
                .build();

        final var sorterSelection = UnidirectionalSorterSelection.builder()
                .sorterName(catalogueSearchSourceConfig.getSortingPattern())
                .build();

        final var searchQuery = SearchQuery.builder()
                .filterSelection(filterSelection)
                .sorterSelection(sorterSelection)
                .build();

        final var unitSearchRequest = UnitSearchRequest.builder()
                .pageSize(pageSize)
                .pageNumber(pageNumber)
                .query(searchQuery)
                .build();

        final var catalogueSearchData = catalogueClient.getCatalogueSearchData(catalogueSearchSourceConfig.getTenant(),
                catalogueSearchSourceConfig.getCategory(), unitSearchRequest);

        if (catalogueSearchData.isSuccess() && Objects.nonNull(catalogueSearchData.getData())) {

            final var data = MapperUtils.convertValue(catalogueSearchData.getData(),
                    new TypeReference<List<CatalogueSearchDataItem>>() {
                    });

            final var valueList = data.stream()
                    .map(row -> transformToValueList((String) row.getData()
                            .get(catalogueSearchSourceConfig.getFilterOnColumnName()), row.getId()))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .toList();

            return FullScreenSearchFieldV2Response.builder()
                    .result(valueList)
                    .build();

        }
        return null;
    }

    @Override
    public String getCatalogueSearchDetails(final String tenant,
                                            final String category,
                                            final String searchId,
                                            final String columnName) {

        final var filterSelection = FilterSelection.builder()
                .filterName(FILTER_PATTERN_FOR_SEARCH_BY_PRIMARY_KEY)
                .value(new ExactValue<>(searchId))
                .build();

        final var sorterSelection = UnidirectionalSorterSelection.builder()
                .sorterName(SORT_PATTERN_FOR_SEARCH_BY_PRIMARY_KEY)
                .build();

        final var searchQuery = SearchQuery.builder()
                .filterSelection(filterSelection)
                .sorterSelection(sorterSelection)
                .build();

        final var unitSearchRequest = UnitSearchRequest.builder()
                .pageSize(10)
                .pageNumber(0)
                .query(searchQuery)
                .build();

        final var catalogueSearchData = catalogueClient.getCatalogueSearchData(tenant, category, unitSearchRequest);

        if (catalogueSearchData.isSuccess() && Objects.nonNull(catalogueSearchData.getData())) {

            final var data = MapperUtils.convertValue(catalogueSearchData.getData(),
                    new TypeReference<List<CatalogueSearchDataItem>>() {
                    });

            return data.stream()
                    .map(row -> getStringFromValueObject(row.getData()
                            .get(columnName)))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    private Optional<Value> transformToValueList(final String displayCodeName,
                                                 final String code) {

        if (!Strings.isNullOrEmpty(displayCodeName) && !Strings.isNullOrEmpty(code)) {

            final var value = Value.builder()
                    .order(0)
                    .code(code)
                    .displayCodeName(displayCodeName)
                    .build();

            return Optional.of(value);
        }

        return Optional.empty();
    }

    private Optional<String> getStringFromValueObject(final Object value) {

        if (Objects.nonNull(value) && !Strings.isNullOrEmpty((String) value)) {
            return Optional.of((String) value);
        }

        return Optional.empty();
    }
}
