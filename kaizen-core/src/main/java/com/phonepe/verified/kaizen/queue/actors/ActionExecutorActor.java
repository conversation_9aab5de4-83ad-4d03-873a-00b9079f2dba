package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.utils.Constants;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ActionExecutorActor extends BaseActor<ActionExecutionMessage> {

    private final ActionService actionService;

    @Inject
    protected ActionExecutorActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                  final ConnectionRegistry connectionRegistry,
                                  final ObjectMapper mapper,
                                  final RetryStrategyFactory retryStrategyFactory,
                                  final ExceptionHandlingFactory exceptionHandlingFactory,
                                  final ActionService actionService) {
        super(ActorType.ACTION_EXECUTOR, actorConfigMap.get(ActorType.ACTION_EXECUTOR), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, ActionExecutionMessage.class);
        this.actionService = actionService;
    }

    @Override
    protected boolean handleMessage(final ActionExecutionMessage actionExecutionMessage) {

        final var storedAction = actionService.validateAndGetAction(actionExecutionMessage.getActionId());
        final var auxiliaryStateMachineMap = actionExecutionMessage.getActionFailureErrorCode()
                .map(actionFailureErrorCode -> Map.<Object, Object>of(ActionFailureErrorCode.class,
                        actionFailureErrorCode))
                .orElse(Map.of());

        actionService.triggerEvent(actionExecutionMessage.getActionId(), storedAction.getActionType(),
                storedAction.getStateMachineVersion(), actionExecutionMessage.getEventToTrigger(),
                Constants.EMPTY_TRANSITION_CONTEXT, actionExecutionMessage.getUserDetails(), auxiliaryStateMachineMap);
        return true;
    }
}
