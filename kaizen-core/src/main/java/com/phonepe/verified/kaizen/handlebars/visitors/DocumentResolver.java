package com.phonepe.verified.kaizen.handlebars.visitors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.Document;
import com.phonepe.verified.kaizen.models.configs.documentsource.DocumentSourceTypeVisitor;
import com.phonepe.verified.kaizen.models.configs.documentsource.impl.HandleBarsDocumentSourceConfig;
import com.phonepe.verified.kaizen.models.configs.documentsource.impl.WorkflowHandleBarsDocumentSourceConfig;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.dropwizard.util.Strings;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.ws.rs.core.HttpHeaders;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class DocumentResolver implements DocumentSourceTypeVisitor<List<Document>, StoredWorkflowStep> {

    private final DocumentService documentService;
    private final HandleBarsService handleBarsService;
    private final WorkflowContextStore workflowContextStore;
    private final ActionMetadataService actionMetadataService;

    @Override
    public List<Document> visit(final WorkflowHandleBarsDocumentSourceConfig workflowHandleBarsDocumentSourceConfig,
                                final StoredWorkflowStep storedWorkflowStep) {
        final var actionMetadataList = resolveTemplateConfig(workflowHandleBarsDocumentSourceConfig,
                storedWorkflowStep);
        return actionMetadataList.stream()
                .map(this::fetchDocument)
                .toList();
    }

    private Document fetchDocument(final StoredDocumentUploadActionMetadata documentUploadActionMetadata) {

        try (final var response = documentService.download(documentUploadActionMetadata.getActionId(),
                documentUploadActionMetadata.getDocumentId())) {

            final var fileContent = (byte[]) response.getEntity();
            final var contentDispositionHeader = response.getHeaderString(HttpHeaders.CONTENT_DISPOSITION);
            final var fileName = getFileName(documentUploadActionMetadata, contentDispositionHeader);

            log.info("Successfully downloaded document for actionId - {} documentId - {} key - {}",
                    documentUploadActionMetadata.getActionId(), documentUploadActionMetadata.getDocumentId(),
                    documentUploadActionMetadata.getActionMetadataContextKey());

            return Document.builder()
                    .documentType(documentUploadActionMetadata.getDocumentType())
                    .documentLabel(documentUploadActionMetadata.getDocumentLabel())
                    .fileContent(fileContent)
                    .fileName(fileName)
                    .contentType(response.getHeaderString(HttpHeaders.CONTENT_TYPE))
                    .build();
        }
    }

    private List<StoredDocumentUploadActionMetadata> resolveTemplateConfig(final WorkflowHandleBarsDocumentSourceConfig workflowHandleBarsDocumentSourceConfig,
                                                                           final StoredWorkflowStep storedWorkflowStep) {
        final var workflowId = storedWorkflowStep.getWorkflowId();
        final var workflowContext = workflowContextStore.getWorkflowContext(workflowId);
        final var handleBarResolvedTemplate = handleBarsService.transform(
                MapperUtils.serializeToString(workflowHandleBarsDocumentSourceConfig), workflowContext);
        final var resolvedDocumentSourceConfig = MapperUtils.deserialize(handleBarResolvedTemplate,
                WorkflowHandleBarsDocumentSourceConfig.class);

        final var actionIds = resolvedDocumentSourceConfig.getDocumentActionConfigs()
                .stream()
                .map(HandleBarsDocumentSourceConfig::getActionId)
                .filter(actionId -> !Strings.isNullOrEmpty(actionId))
                .collect(Collectors.toSet());
        final var documentIdentifiers = resolvedDocumentSourceConfig.getDocumentActionConfigs()
                .stream()
                .flatMap(d -> d.getDocumentContextKeys()
                        .stream())
                .collect(Collectors.toSet());

        log.info("Document resolver: workflowId {}, actionIds {}, documentIdentifiers {}", workflowId, actionIds,
                documentIdentifiers);

        final var actionMetadataList = actionMetadataService.getActionMetadataList(actionIds);
        return actionMetadataList.stream()
                .filter(StoredDocumentUploadActionMetadata.class::isInstance)
                .map(StoredDocumentUploadActionMetadata.class::cast)
                .filter(storedDocumentUploadActionMetadata -> documentIdentifiers.contains(
                        storedDocumentUploadActionMetadata.getActionMetadataContextKey()))
                .toList();
    }

    private String getFileName(final StoredDocumentUploadActionMetadata documentUploadActionMetadata,
                               final String contentDispositionHeader) {
        final var headerSplit = Optional.ofNullable(contentDispositionHeader)
                .map(h -> Arrays.asList(h.split("filename=")))
                .orElse(List.of());
        if (headerSplit.size() > 1) {
            return headerSplit.get(1);
        }
        return String.join("_", documentUploadActionMetadata.getDocumentType()
                .name(), documentUploadActionMetadata.getDocumentLabel());
    }
}
