package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.drishti.models.responses.DrishtiResponse;
import com.phonepe.verified.drishti.models.responses.masking.MaskingResponse;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ProcessDocumentMaskingCallbackMessage extends BaseMessage {

    @NonNull
    private final String actionId;

    @NonNull
    private final String requestId;

    @NonNull
    private final DrishtiResponse<MaskingResponse> drishtiResponse;

    @Builder
    @Jacksonized
    public ProcessDocumentMaskingCallbackMessage(final RequestInfo requestInfo,
                                                 @NonNull final String actionId,
                                                 @NonNull final String requestId,
                                                 @NonNull final DrishtiResponse<MaskingResponse> drishtiResponse) {

        super(ActorMessageType.PROCESS_DOCUMENT_MASKING_CALLBACK, requestInfo);
        this.actionId = actionId;
        this.requestId = requestId;
        this.drishtiResponse = drishtiResponse;
    }
}
