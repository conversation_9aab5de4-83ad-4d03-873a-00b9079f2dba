package com.phonepe.verified.kaizen.authz.resolvers.override;

import com.google.inject.Inject;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.resolver.OverrideAccessResolver;
import java.util.Optional;
import javax.ws.rs.container.ContainerRequestContext;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@AllArgsConstructor(onConstructor = @__(@Inject))
public class KaizenAuthZNoOpOverrideAccessResolver implements OverrideAccessResolver {

    @Override
    public Optional<Boolean> isAuthorized(final ContainerRequestContext containerRequestContext,
                                          final AccessAllowed accessAllowed) {

        return Optional.empty();
    }
}
