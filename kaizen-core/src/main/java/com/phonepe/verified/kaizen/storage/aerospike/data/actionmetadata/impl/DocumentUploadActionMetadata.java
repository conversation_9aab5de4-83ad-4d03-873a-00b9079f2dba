package com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.ActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.ActionMetadataVisitor;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentUploadActionMetadata extends ActionMetadata {

    private List<DocumentTypeIdentifierAndLabel> documents;

    @Builder
    public DocumentUploadActionMetadata(final String actionId,
                                        final List<DocumentTypeIdentifierAndLabel> documents) {
        super(actionId, ActionMetadataType.DOCUMENT_UPLOAD);
        this.documents = documents;
    }

    public DocumentUploadActionMetadata() {
        super(ActionMetadataType.DOCUMENT_UPLOAD);
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }
}
