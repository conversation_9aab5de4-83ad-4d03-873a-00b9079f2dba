package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.ProfileScreenStatus;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import java.util.Comparator;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class IdentifyProfileScreenToResume implements ProfileScreenVisitor<Optional<ProfileScreenStatus>> {

    private final String workflowStepId;

    private final ActionService actionService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowContextStore workflowContextStore;

    @Override
    public Optional<ProfileScreenStatus> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        final var actionCompletionStatus = standardProfileScreenConfig.getStepActionConfig()
                .accept(new ProfileScreenCompletionActionConfigVisitor(workflowStepId, actionService, workflowService,
                        hopeLangService, workflowContextStore), null);

        return Optional.of(ProfileScreenStatus.builder()
                .profileScreenConfigToTrigger(standardProfileScreenConfig)
                .completionState(actionCompletionStatus.getCompletionState())
                .build());
    }

    @Override
    public Optional<ProfileScreenStatus> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        for (final var orderedProfileScreen : sortedOrderedProfileScreenList) {
            final var profileScreenStatus = orderedProfileScreen.getProfileScreenConfig()
                    .accept(this);

            if (profileScreenStatus.isPresent() && !CompletionState.SUCCESS_STATES.contains(profileScreenStatus.get()
                    .getCompletionState())) {
                return profileScreenStatus;
            }
        }

        return Optional.empty();
    }
}
