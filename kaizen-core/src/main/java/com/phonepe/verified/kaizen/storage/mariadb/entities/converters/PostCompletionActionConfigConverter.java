package com.phonepe.verified.kaizen.storage.mariadb.entities.converters;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.Objects;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class PostCompletionActionConfigConverter implements AttributeConverter<PostCompletionActionConfig, String> {

    @Override
    public String convertToDatabaseColumn(final PostCompletionActionConfig postCompletionActionConfig) {
        return Objects.isNull(postCompletionActionConfig)
               ? null
               : MapperUtils.serializeToString(postCompletionActionConfig);
    }

    @Override
    public PostCompletionActionConfig convertToEntityAttribute(final String text) {
        return Objects.isNull(text)
               ? null
               : MapperUtils.deserialize(text, PostCompletionActionConfig.class);
    }
}
