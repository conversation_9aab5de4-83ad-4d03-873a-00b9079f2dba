package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

public class GetActionMappingIdsForActionTypeVisitor implements ProfileScreenVisitor<List<String>> {

    private final ActionType actionType;

    public GetActionMappingIdsForActionTypeVisitor(final ActionType actionType) {
        this.actionType = actionType;
    }

    @Override
    public List<String> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        return standardProfileScreenConfig.getStepActionConfig()
                .accept(GetAllStandardStepActionConfigsVisitor.INSTANCE, null)
                .stream()
                .filter(stepActionConfig -> Objects.equals(stepActionConfig.getActionType(), actionType))
                .map(StandardStepActionConfig::getActionMappingId)
                .toList();
    }

    @Override
    public List<String> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        return sortedOrderedProfileScreenList.stream()
                .map(orderedProfileScreen -> orderedProfileScreen.getProfileScreenConfig()
                        .accept(new GetActionMappingIdsForActionTypeVisitor(actionType)))
                .flatMap(Collection::stream)
                .toList();
    }
}