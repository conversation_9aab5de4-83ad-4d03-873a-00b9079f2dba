package com.phonepe.verified.kaizen.queue;

import lombok.experimental.UtilityClass;

public enum ActorMessageType {

    ABORT_WORKFLOW,
    ABORT_WORKFLOW_V2,
    <PERSON><PERSON><PERSON><PERSON>LEDGE_SMS_CONSENT,
    ACTION_EXECUTION,
    CONFIRMATION_ACTION,
    D<PERSON><PERSON><PERSON>_WORKFLOW,
    DOCUMENT_MASKING,
    DOCUMENT,
    EVENT_INGESTION,
    HANDLE_TTL_CALLBACK,
    MOVE_TO_IN_PROGRESS_WORKFLOW_STEP,
    POST_COMPLETION_ACTION_CONFIG_PROCESSOR,
    PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL_ACTION,
    PROCESS_AUTO_RETRY_CLOCKWORK_ACTION,
    PROCESS_DOCUMENT_FETCH_CALLBACK,
    PROCESS_DOCUMENT_MASKING_CALLBACK,
    PROCESS_SELFIE_DOCUMENT_ID_SUBMIT,
    <PERSON>URGE_WORKFLOW,
    REVOLVER_CALLBACK,
    SCHEDULED_WORKFLOW_ABORT_CALLBACK,
    WORKFLOW_AUTO_ABORT_CALLBACK,
    WOR<PERSON><PERSON>OW_AUTO_SKIP_CALLBACK,
    SCHEDULED_WORKFLOW_STEP_ABORT_CALLBACK,
    SEARCH_CLIENT_DATA,
    SECTION_SUBMIT,
    SECTION_SUBMIT_V2,
    SKIP_WORKFLOW_STEP,
    STEP_ACTION,
    TRIGGER_WORKFLOW_STEP,
    VERIFY_OTP_HURDLE,
    WORKFLOW_CLIENT_CALLBACK,
    WORKFLOW_INIT,
    WORKFLOW_INIT_V2,
    WORKFLOW,
    WORKFLOW_STEP_COMPLETION,
    WORKFLOW_STEP,
    WORKFLOW_STEP_V2,
    DOCUMENT_PREFILL,
    ;

    @UtilityClass
    public static class Names {

        public static final String WORKFLOW_AUTO_ABORT_CALLBACK = "WORKFLOW_AUTO_ABORT_CALLBACK";
        public static final String WORKFLOW_AUTO_SKIP_CALLBACK = "WORKFLOW_AUTO_SKIP_CALLBACK";
        public static final String ABORT_WORKFLOW = "ABORT_WORKFLOW";
        public static final String ABORT_WORKFLOW_V2 = "ABORT_WORKFLOW_V2";
        public static final String ACKNOWLEDGE_SMS_CONSENT = "ACKNOWLEDGE_SMS_CONSENT";
        public static final String ACTION_EXECUTION = "ACTION_EXECUTION";
        public static final String CONFIRMATION_ACTION = "CONFIRMATION_ACTION";
        public static final String DISCARD_WORKFLOW = "DISCARD_WORKFLOW";
        public static final String DOCUMENT_MASKING = "DOCUMENT_MASKING";
        public static final String DOCUMENT = "DOCUMENT";
        public static final String EVENT_INGESTION = "EVENT_INGESTION";
        public static final String HANDLE_TTL_CALLBACK = "HANDLE_TTL_CALLBACK";
        public static final String MOVE_TO_IN_PROGRESS_WORKFLOW_STEP = "MOVE_TO_IN_PROGRESS_WORKFLOW_STEP";
        public static final String POST_COMPLETION_ACTION_CONFIG_PROCESSOR = "POST_COMPLETION_ACTION_CONFIG_PROCESSOR";
        public static final String PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL_ACTION = "PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL_ACTION";
        public static final String PROCESS_AUTO_RETRY_CLOCKWORK_ACTION = "PROCESS_AUTO_RETRY_CLOCKWORK_ACTION";
        public static final String PROCESS_DOCUMENT_MASKING_CALLBACK = "PROCESS_DOCUMENT_MASKING_CALLBACK";
        public static final String PROCESS_SELFIE_DOCUMENT_ID_SUBMIT = "PROCESS_SELFIE_DOCUMENT_ID_SUBMIT";
        public static final String PURGE_WORKFLOW = "PURGE_WORKFLOW";
        public static final String REVOLVER_CALLBACK = "REVOLVER_CALLBACK";
        public static final String SCHEDULED_WORKFLOW_ABORT_CALLBACK = "SCHEDULED_WORKFLOW_ABORT_CALLBACK";
        public static final String SCHEDULED_WORKFLOW_STEP_ABORT_CALLBACK = "SCHEDULED_WORKFLOW_STEP_ABORT_CALLBACK";
        public static final String SEARCH_CLIENT_DATA = "SEARCH_CLIENT_DATA";
        public static final String SECTION_SUBMIT = "SECTION_SUBMIT";
        public static final String SECTION_SUBMIT_V2 = "SECTION_SUBMIT_V2";
        public static final String SKIP_WORKFLOW_STEP = "SKIP_WORKFLOW_STEP";
        public static final String STEP_ACTION = "STEP_ACTION";
        public static final String TRIGGER_WORKFLOW_STEP = "TRIGGER_WORKFLOW_STEP";
        public static final String VERIFY_OTP_HURDLE = "VERIFY_OTP_HURDLE";
        public static final String WORKFLOW_CLIENT_CALLBACK = "WORKFLOW_CLIENT_CALLBACK";
        public static final String WORKFLOW_INIT = "WORKFLOW_INIT";
        public static final String WORKFLOW_INIT_V2 = "WORKFLOW_INIT_V2";
        public static final String WORKFLOW = "WORKFLOW";
        public static final String WORKFLOW_STEP_COMPLETION = "WORKFLOW_STEP_COMPLETION";
        public static final String WORKFLOW_STEP = "WORKFLOW_STEP";
        public static final String WORKFLOW_STEP_V2 = "WORKFLOW_STEP_V2";
        public static final String DOCUMENT_PREFILL = "DOCUMENT_PREFILL";
    }
}