package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.models.configs.callback.ClientCallbackConfig;
import com.phonepe.verified.kaizen.services.impl.ClientCallbackServiceImpl;
import java.util.Optional;

@Singleton
public class ClientCallbackCache extends Cache<String, Optional<ClientCallbackConfig>> {

    private final Provider<ClientCallbackServiceImpl> clientCallbackServiceProvider;

    @Inject
    public ClientCallbackCache(final CaffeineCacheConfig caffeineCacheConfig,
                               final MetricRegistry metricRegistry,
                               final Provider<ClientCallbackServiceImpl> clientCallbackServiceProvider) {
        super(CacheName.CLIENT_CALLBACK_CACHE, caffeineCacheConfig, metricRegistry);
        this.clientCallbackServiceProvider = clientCallbackServiceProvider;
    }

    @Override
    protected Optional<ClientCallbackConfig> build(final String profileId) {
        return clientCallbackServiceProvider.get()
                .getClientCallbackConfigFromDB(profileId);
    }
}
