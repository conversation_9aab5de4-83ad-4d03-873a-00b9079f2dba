package com.phonepe.verified.kaizen.handlebars;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.TextNode;
import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Helper;
import com.github.jknack.handlebars.Options;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.jayway.jsonpath.JsonPath;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.docstore.client.DocstoreClient;
import com.phonepe.shadow.data.fields.FieldData;
import com.phonepe.shadow.data.sections.PermissionDataForSectionInput;
import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.shadow.models.response.GenericFieldInput;
import com.phonepe.shadow.page.field.Field;
import com.phonepe.shadow.services.TemplateServiceV2;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.models.data.common.AlertPriority;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.CatalogueService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.PincodeAndStateMatchingService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowContext;
import com.phonepe.verified.kaizen.utils.AddressUtils;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.ConverterUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.dropwizard.util.Strings;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.StringUtils;
import org.luaj.vm2.ast.Str;

@Slf4j
@Singleton
@SuppressWarnings({"java:S1192", "java:S1874", "java:S5361"})
public class HandleBarsHelperRegistry {

    private static final String FIELD_TYPE = "fieldType";
    private static final String PERMISSION_TYPE = "permissionType";
    private static final String FIELD_INPUT_VALUE_DATA_KEY = "fieldInputValueDataKey";
    private static final String INDEX = "index";
    private static final String DELIMITER = "delimiter";

    @Getter
    private final Handlebars handlebars;

    private final DocstoreClient docstoreClient;

    private final OlympusIMClient olympusIMClient;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowContextStore workflowContextStore;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final Provider<CatalogueService> catalogueService;

    private final Provider<DocumentService> documentServiceProvider;

    private final Provider<HandleBarsService> handleBarsServiceProvider;

    private final PincodeAndStateMatchingService pincodeAndStateMatchingService;

    private final ProfileService profileService;

    private final TemplateServiceV2 templateServiceV2;

    @Inject
    public HandleBarsHelperRegistry(final Handlebars handlebars,
                                    final DocstoreClient docstoreClient,
                                    final OlympusIMClient olympusIMClient,
                                    final WorkflowService workflowService,
                                    final HopeLangService hopeLangService,
                                    final WorkflowContextStore workflowContextStore,
                                    final DataProvider<KaizenConfig> appConfigProvider,
                                    final Provider<CatalogueService> catalogueService,
                                    final Provider<DocumentService> documentServiceProvider,
                                    final Provider<HandleBarsService> handleBarsServiceProvider,
                                    final PincodeAndStateMatchingService pincodeAndStateMatchingService,
                                    final ProfileService profileService,
                                    final TemplateServiceV2 templateServiceV2) {

        this.handlebars = handlebars;
        this.docstoreClient = docstoreClient;
        this.olympusIMClient = olympusIMClient;
        this.workflowService = workflowService;
        this.hopeLangService = hopeLangService;
        this.catalogueService = catalogueService;
        this.appConfigProvider = appConfigProvider;
        this.workflowContextStore = workflowContextStore;
        this.documentServiceProvider = documentServiceProvider;
        this.handleBarsServiceProvider = handleBarsServiceProvider;
        this.pincodeAndStateMatchingService = pincodeAndStateMatchingService;
        this.profileService = profileService;
        this.templateServiceV2 = templateServiceV2;
        register();
    }

    // splits a string into exact nLines with n-1 lines have max characters as maxChars while nth line has remaining characters
    // ex1- 200 characters string, 55 maxChars, 3 nLines -> splits as 55,55,90 length string
    // ex2 - 65 characters string, 55 maxChars, 3 nLines -> splits as 55,10,0 length string.
    private static List<String> splitString(final String input,
                                            final int maxChars,
                                            final int nLines) {
        final List<String> lines = new ArrayList<>();

        int startIndex = 0;
        int endIndex = maxChars;

        while (startIndex < input.length()) {
            if (endIndex > input.length()) {
                endIndex = input.length();
            }

            final String line = input.substring(startIndex, endIndex);
            lines.add(line);

            startIndex = endIndex;
            endIndex += maxChars;
        }

        // Add empty values if the string cannot be split further
        while (lines.size() < nLines) {
            lines.add(StringUtils.EMPTY);
        }

        return lines;
    }

    private static boolean parseToBoolean(final Object obj) {

        if (Objects.isNull(obj)) {
            return false;
        }

        if (obj instanceof final String string) {
            return Boolean.parseBoolean(string);
        }

        if (obj instanceof final Boolean bool) {
            return bool;
        }

        return false;
    }

    public void register() {
        registerStringFromGenericFieldData();
        registerStringFromPermissionData();
        registerListFromGenericFieldData();
        registerNthElementFromList();
        registerAddressStringToAddressLine1();
        registerAddressStringToAddressLine2();
        registerGetCityFromPincodeDetails();
        registerGetStateFromPincodeDetails();
        registerBooleanFromGenericFieldData();
        registerBooleanFromString();
        registerNot();
        registerAnd();
        registerOr();
        registerStringEquals();
        registerLocalDateStringFromGenericFieldData();
        registerSelectedEntriesFromListWidget();
        registerGetDelimitedStringOfSelectedEntriesFromListWidget();
        registerDocumentsFromFieldDataList();
        registerStringReplace();
        registerSubstring();
        registerToUppercase();
        registerToTitleCase();
        registerSanitizeString();
        registerIsAlertActive();
        registryWrapWithDoubleBraces();
        registerHopeRuleEvaluator();
        registerResolveValueFromAllWorkflowsOfSameEntityAndProfile();
        registerLeftPadString();
        registerIsNull();
        registerIsStringNullOrEmpty();
        registerGetMilliSecondsSinceEpochFromIsoLocalDateString();
        registerGetStringFromLocalDateTime();
        registerGetBase64ImageFromDocstore();
        registerGetCurrentDateTime();
        registerDateTimeFormatter();
        registerSplitIntoNParts();
        registerFetchValueFromJsonObject();
        registerGetCatalogueSearchDetails();
        registerFormatPercentValues();
        registerGetCompressedBase64Image();
        registerGetBytesImageFromDocstore();
        registerGetStringFromGenericFieldInputValueObject();
        registerResolveWithInvalidatedActionsDataInContext();
        registerValidateString();
        registerGetUserNameFromOlympusId();
        registerNumberOfWordsInAString();
        registerTernary();
        registerGetDocumentMetaDataFromDocstore();
        registerGetTemplateIdFromAppData();
        registerCheckFieldOptionalStatus();
        registerDocumentsFromFieldDataListWithMetadata();
    }

    private void registerNumberOfWordsInAString() {

        handlebars.registerHelper("numberOfWordsInAString", (final String inputString, final Options options) -> {

            if (Strings.isNullOrEmpty(inputString)) {
                return "0";
            }

            final var trimmedString = inputString.trim();

            final var delimiter = (String) options.hash(DELIMITER);

            if (Objects.isNull(delimiter)) {
                return Integer.toString(trimmedString.split(" ").length);
            }

            return Integer.toString(trimmedString.split(delimiter).length);
        });
    }

    private void registerFetchValueFromJsonObject() {
        handlebars.registerHelper("fetchValueFromJsonObject", (final Object obj, final Options options) -> {
            final var jsonPath = (String) options.hash("jsonPath");

            try {
                return JsonPath.parse(MapperUtils.serializeToString(obj))
                        .read(jsonPath);
            } catch (final Exception e) {
                return "";
            }

        });
    }

    private void registerSplitIntoNParts() {
        handlebars.registerHelper("splitIntoNParts", (final String str, final Options options) -> {
            final var noOfLines = Integer.parseInt(options.hash("n"));
            final var maxCharsInALine = Integer.parseInt(options.hash("maxChars"));
            return splitString(str, maxCharsInALine, noOfLines);
        });
    }

    private void registerDateTimeFormatter() {
        handlebars.registerHelper("transformDateStringToFormattedString",
                (final String date, final Options options) -> {

                    final var type = (String) options.hash("type");

                    final DateTimeFormatter currentFormat = DateTimeFormatter.ofPattern(options.hash("currentFormat"));

                    final DateTimeFormatter formatRequired = DateTimeFormatter.ofPattern(
                            options.hash("formatRequired"));

                    if (type.equals("date")) {
                        final var localDate = LocalDate.parse(date, currentFormat);
                        return localDate.format(formatRequired);
                    }

                    final var localDateTime = LocalDateTime.parse(date, currentFormat);

                    return localDateTime.format(formatRequired);
                });
    }

    private void registerGetCurrentDateTime() {
        handlebars.registerHelper("getCurrentDateTime", (final String zone, final Options options) -> {
            final var zonedDateTime = ZonedDateTime.now(ZoneId.of(zone));
            return DateTimeFormatter.ofPattern("dd-MM-yyyy")
                    .format(zonedDateTime);
        });
    }

    private void registerGetBase64ImageFromDocstore() {
        handlebars.registerHelper("getBase64ImageFromDocstore",
                (final String documentId, final Options options) -> documentServiceProvider.get()
                        .getBase64EncodedImage(documentId));
    }

    private void registerGetBytesImageFromDocstore() {
        handlebars.registerHelper("getBytesImageFromDocstore",
                (final String documentId, final Options options) -> documentServiceProvider.get()
                        .getImagebytes(documentId));
    }

    private void registerGetCompressedBase64Image() {
        handlebars.registerHelper("getCompressedBase64Image", (final byte[] imageBytes, final Options options) -> {

            final var quality = Double.parseDouble(options.hash("quality"));

            if (quality < 0 || quality > 0.5) {
                throw new KaizenException(KaizenResponseCode.INVALID_QUALITY_FOR_COMPRESSION,
                        "Image Cannot be compressed. Check quality.", Map.of());
            }

            final var sizeLimitInBytes = Long.parseLong(options.hash("sizeLimitInBytes"));

            return ConverterUtils.compress(imageBytes, quality, sizeLimitInBytes);
        });
    }

    private void registerHopeRuleEvaluator() {
        handlebars.registerHelper("hopeRuleEvaluate", (final Object ctx, final Options options) -> {

            final String rule = options.hash("rule");
            final var hopeRule = rule.replaceAll("\\\\", "");

            return hopeLangService.evaluate(hopeRule, MapperUtils.convertToJsonNode(ctx));
        });
    }

    private void registerIsNull() {
        handlebars.registerHelper("isNull", (final Object object, final Options options) -> Objects.isNull(object));
    }

    private void registerListFromGenericFieldData() {

        handlebars.registerHelper("listFromGenericFieldData", (Helper<List<FieldData>>) (fieldDataList, options) -> {

            final String fieldType = options.hash(FIELD_TYPE);

            if (Objects.isNull(fieldDataList) || Objects.isNull(fieldType)) {
                return List.of();
            }

            return fieldDataList.stream()
                    .filter(fd -> fd.getType()
                            .equals(fieldType))
                    .findFirst()
                    .map(fd -> getList((GenericFieldInput) fd))
                    .orElseGet(List::of);
        });
    }

    private List<?> getList(final GenericFieldInput fieldData) {
        return (List<?>) fieldData.getValue();
    }

    private void registerNthElementFromList() {

        handlebars.registerHelper("nthElementFromList", (Helper<List<?>>) (list, options) -> {

            final int index = options.hash(INDEX);

            if (Objects.isNull(list) || list.isEmpty()) {
                return null;
            }

            return list.get(index);

        });
    }

    private void registerAddressStringToAddressLine1() {

        handlebars.registerHelper("addressStringToAddressLine1",
                (Helper<String>) (value, options) -> AddressUtils.splitAddress(value, true));
    }

    private void registerAddressStringToAddressLine2() {

        handlebars.registerHelper("addressStringToAddressLine2",
                (Helper<String>) (value, options) -> AddressUtils.splitAddress(value, false));
    }

    private void registerGetCityFromPincodeDetails() {

        handlebars.registerHelper("getCityFromPincodeDetails", (Helper<String>) (value, options) -> {

            if (options.params.length == 0) {
                return pincodeAndStateMatchingService.getCityFromPincodeDetails(value, Set.of())
                        .orElse(null);
            }

            final var pincodeSources = Arrays.stream(options.params)
                    .filter(String.class::isInstance)
                    .map(String.class::cast)
                    .collect(Collectors.toSet());

            return pincodeAndStateMatchingService.getCityFromPincodeDetails(value, pincodeSources)
                    .orElse(null);
        });
    }

    private void registerGetStateFromPincodeDetails() {

        handlebars.registerHelper("getStateFromPincodeDetails", (Helper<String>) (value, options) -> {

            if (options.params.length == 0) {
                return pincodeAndStateMatchingService.getStateFromPincodeDetails(value, Set.of())
                        .orElse(null);
            }

            final var pincodeSources = Arrays.stream(options.params)
                    .filter(String.class::isInstance)
                    .map(String.class::cast)
                    .collect(Collectors.toSet());

            return pincodeAndStateMatchingService.getStateFromPincodeDetails(value, pincodeSources)
                    .orElse(null);
        });
    }

    private void registerStringFromGenericFieldData() {

        handlebars.registerHelper("stringFromGenericFieldData", (Helper<List<FieldData>>) (fieldDataList, options) -> {

            final String fieldType = options.hash(FIELD_TYPE);

            if (Objects.isNull(fieldDataList) || Objects.isNull(fieldType)) {
                return null;
            }

            return fieldDataList.stream()
                    .filter(fd -> fd.getType()
                            .equals(fieldType))
                    .findFirst()
                    .map(fd -> getValue((GenericFieldInput) fd))
                    .orElse(null);
        });
    }

    private void registerStringFromPermissionData() {

        handlebars.registerHelper("stringFromPermissionData",
                (Helper<List<PermissionDataForSectionInput>>) (permissionDataList, options) -> {

                    final String permissionType = options.hash(PERMISSION_TYPE);

                    if (Objects.isNull(permissionDataList) || Objects.isNull(permissionType)) {
                        return null;
                    }

                    final var template = (String) options.hash("template");

                    final var permissionData = permissionDataList.stream()
                            .filter(pd -> pd.getPermissionType()
                                    .name()
                                    .equals(permissionType))
                            .findFirst()
                            .orElse(null);

                    return Objects.nonNull(permissionData) && Objects.nonNull(permissionData.getData())
                            ? handleBarsServiceProvider.get()
                            .transform(template, permissionData.getData())
                            : null;
                });
    }

    private String getValue(final GenericFieldInput fieldData) {
        return (String) fieldData.getValue();
    }

    private void registerBooleanFromGenericFieldData() {

        handlebars.registerHelper("booleanFromGenericFieldData", (Helper<List<FieldData>>) (fieldDataList, options) -> {

            final String fieldType = options.hash(FIELD_TYPE);

            if (Objects.isNull(fieldDataList) || Objects.isNull(fieldType)) {
                return Boolean.FALSE;
            }

            return fieldDataList.stream()
                    .filter(fd -> fd.getType()
                            .equals(fieldType))
                    .findFirst()
                    .map(fd -> getBooleanValue((GenericFieldInput) fd))
                    .orElse(Boolean.FALSE);
        });
    }

    private boolean getBooleanValue(final GenericFieldInput fieldData) {
        return Objects.nonNull(fieldData.getValue()) && (boolean) fieldData.getValue();
    }

    private void registerBooleanFromString() {

        handlebars.registerHelper("booleanFromString",
                (Helper<String>) (string, options) -> Boolean.parseBoolean(string));

    }

    private void registerNot() {

        handlebars.registerHelper("not", (object, options) -> !parseToBoolean(object));
    }

    private void registerAnd() {

        handlebars.registerHelper("and", (Helper<String>) (string, options) -> Arrays.stream(options.params)
                .allMatch(HandleBarsHelperRegistry::parseToBoolean));
    }

    private void registerOr() {

        handlebars.registerHelper("or", (Helper<String>) (string, options) -> Arrays.stream(options.params)
                .anyMatch(HandleBarsHelperRegistry::parseToBoolean));
    }

    private void registerLocalDateStringFromGenericFieldData() {

        handlebars.registerHelper("localDateStringFromGenericFieldData",
                (Helper<List<FieldData>>) (fieldDataList, options) -> {

                    final String fieldType = options.hash(FIELD_TYPE);

                    if (Objects.isNull(fieldDataList) || Objects.isNull(fieldType)) {
                        return null;
                    }

                    return fieldDataList.stream()
                            .filter(fd -> fd.getType()
                                    .equals(fieldType))
                            .findFirst()
                            .map(GenericFieldInput.class::cast)
                            .filter(fd -> Objects.nonNull(fd.getValue()))
                            .map(this::getLocalDateStringValue)
                            .orElse(null);
                });
    }

    private String getLocalDateStringValue(final GenericFieldInput fieldData) {

        if (fieldData.getValue() instanceof final Number number) {

            return Instant.ofEpochMilli(number.longValue())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
                    .format(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION,
                Map.of("localDate", fieldData.getValue(), "errorMessage", "Date should be epoch timestamp"));
    }

    private void registerStringEquals() {

        handlebars.registerHelper("stringEquals", (final Object value, final Options options) -> {
            if (Objects.nonNull(value) && !Strings.isNullOrEmpty(value.toString()) && value.toString()
                    .equals(options.param(0)
                            .toString())) {
                return "true";
            }
            return null;
        });
    }

    private void registerIsStringNullOrEmpty() {

        handlebars.registerHelper("isStringNullOrEmpty",
                (final String value, final Options options) -> Strings.isNullOrEmpty(value));
    }

    private void registerSelectedEntriesFromListWidget() {

        handlebars.registerHelper("selectedEntriesFromListWidget",
                (Helper<List<FieldData>>) (fieldDataList, options) -> {

                    final String fieldType = options.hash(FIELD_TYPE);

                    if (Objects.isNull(fieldDataList) || Objects.isNull(fieldType)) {
                        return "{}";
                    }

                    final var entries = fieldDataList.stream()
                            .filter(fd -> fd.getType()
                                    .equals(fieldType))
                            .findFirst()
                            .map(fd -> getList((GenericFieldInput) fd))
                            .orElseGet(List::of);

                    final var entryMap = entries.stream()
                            .collect(Collectors.toMap(Function.identity(), t -> Boolean.TRUE, (a, b) -> b));

                    return MapperUtils.serializeToString(entryMap);
                });
    }

    private void registerGetDelimitedStringOfSelectedEntriesFromListWidget() {

        handlebars.registerHelper("getDelimitedStringOfSelectedEntriesFromListWidget",
                (Helper<List<FieldData>>) (fieldDataList, options) -> {

                    final String fieldType = options.hash(FIELD_TYPE);

                    final String delimiter = StringUtils.defaultIfEmpty(options.hash(DELIMITER), ",");

                    if (Objects.isNull(fieldDataList) || Objects.isNull(fieldType)) {
                        return "";
                    }

                    return fieldDataList.stream()
                            .filter(fd -> fd.getType()
                                    .equals(fieldType))
                            .findFirst()
                            .map(fd -> getList((GenericFieldInput) fd))
                            .orElseGet(List::of)
                            .stream()
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .collect(Collectors.joining(delimiter));
                });
    }

    private void registerDocumentsFromFieldDataList() {

        handlebars.registerHelper("documentsFromFieldDataList", (Helper<List<FieldData>>) (fieldDataList, options) -> {

            if (Objects.isNull(fieldDataList) || options.params.length == 0) {
                return "[]";
            }

            final var documentTypeIdentifierAndLabels = Arrays.stream(options.params)
                    .filter(String.class::isInstance)
                    .map(String.class::cast)
                    .map(fieldType -> getDocumentTypeIdentifierAndLabel(fieldDataList, fieldType))
                    .filter(Objects::nonNull)
                    .toList();

            return MapperUtils.serializeToString(documentTypeIdentifierAndLabels);
        });
    }

    private void registerDocumentsFromFieldDataListWithMetadata() {

        handlebars.registerHelper("documentsFromFieldDataListWithMetadata", (Helper<List<FieldData>>) (fieldDataList, options) -> {

            if (Objects.isNull(fieldDataList) || options.params.length == 0) {
                return "[]";
            }
            final var context = (ShadowV2UiRequestContext) options.context.data("root");
            final var ckv = context.getComponentKitVersion();
            final var intent = context.getIntent();
            final var sectionInputData = context.getSectionInputData();
            final var workflowId = context.getWorkflowId();

            final var templateWorkflowType = BuildUtils.getTemplateWorkflowType(profileService.get(
                    workflowService.validateAndGetWorkflow(workflowId)
                            .getProfileId(), false));

            final var template = templateServiceV2.getTemplate(templateWorkflowType, (String) intent, (int) ckv);

            final var documentTypeIdentifierAndLabels = Arrays.stream(options.params)
                    .filter(String.class::isInstance)
                    .map(String.class::cast)
                    .map(fieldType -> {

                        final var documentTypeIdentifierAndLabel = getDocumentTypeIdentifierAndLabel(fieldDataList, fieldType);
                        if(documentTypeIdentifierAndLabel == null){
                            return null;
                        }

                        final var field = template.getSectionMappings().stream()
                                .flatMap(t -> t.getSection().getFieldGroups().stream())
                                .filter(t -> t.getId().equals(sectionInputData.getMappingId()))
                                .flatMap(t -> t.getFields().stream())
                                .filter(t -> t.getId().equals(fieldType))
                                .findAny();

                        final var isOptional = field.<Object>map(Field::getOptional)
                                .orElse(true);

                        documentTypeIdentifierAndLabel.setMetadata(Map.of("isOptional", isOptional.toString()));

                        return documentTypeIdentifierAndLabel;
                    })
                    .filter(Objects::nonNull)
                    .toList();

            return MapperUtils.serializeToString(documentTypeIdentifierAndLabels);
        });
    }

    private void registerStringReplace() {

        handlebars.registerHelper("stringReplace",
                (String value, Options options) -> value.replace(options.param(0), options.param(1)));
    }

    private void registryWrapWithDoubleBraces() {

        handlebars.registerHelper("wrapWithDoubleBraces", (String value, Options options) -> "{{" + value + "}}");
    }

    private void registerSubstring() {

        handlebars.registerHelper("substring",
                (String value, Options options) -> value.substring(options.param(0), options.param(1)));
    }

    private void registerToUppercase() {

        handlebars.registerHelper("toUppercase", (String value, Options options) -> value.toUpperCase());
    }

    private void registerToTitleCase() {

        handlebars.registerHelper("toTitleCase", (String value, Options options) -> WordUtils.capitalizeFully(value));
    }

    private void registerSanitizeString() {

        handlebars.registerHelper("sanitizeString", (final String value, final Options options) -> {

            if (Strings.isNullOrEmpty(value)) {
                return "";
            }

            final var sanitizedString = TextNode.valueOf(value)
                    .toString();

            return sanitizedString.substring(1, sanitizedString.length() - 1);
        });
    }

    private DocumentTypeIdentifierAndLabel getDocumentTypeIdentifierAndLabel(final List<FieldData> fieldDataList,
                                                                             final String fieldType) {

        return fieldDataList.stream()
                .filter(fd -> fd.getType()
                        .equals(fieldType))
                .findFirst()
                .map(fd -> getDocumentTypeIdentifierAndLabelFromGenericFieldData((GenericFieldInput) fd))
                .orElse(null);
    }

    private void registerIsAlertActive() {

        handlebars.registerHelper("isAlertActive", (final String alertType, final Options options) -> {

            final var alertGroupId = (String) options.hash("alertGroupId");

            final var alertsConfig = appConfigProvider.getData()
                    .getAlertsConfig();

            if (Objects.isNull(alertGroupId) || Objects.isNull(alertType) || Objects.isNull(alertsConfig)
                    || Objects.isNull(alertsConfig.getAlertGroups()) || Objects.isNull(
                    alertsConfig.getActiveAlertTypes()) || alertsConfig.getActiveAlertTypes()
                    .isEmpty()) {

                return Boolean.toString(false);
            }

            final var resultOptional = alertsConfig.getAlertGroups()
                    .getOrDefault(alertGroupId, Set.of())
                    .stream()
                    .sorted(Comparator.comparing(AlertPriority::getPriority))
                    .map(AlertPriority::getAlertType)
                    .filter(alertsConfig.getActiveAlertTypes()::contains)
                    .findFirst()
                    .filter(at -> at.equals(alertType));

            return Boolean.toString(resultOptional.isPresent());
        });
    }

    public void registerGetMilliSecondsSinceEpochFromIsoLocalDateString() {
        handlebars.registerHelper("getMilliSecondsSinceEpochFromIsoLocalDateString",
                (final String value, final Options options) -> {
                    if (Strings.isNullOrEmpty(value)) {
                        return null;
                    }

                    return LocalDate.parse(value, DateTimeFormatter.ISO_LOCAL_DATE)
                            .atStartOfDay(ZoneId.systemDefault())
                            .toInstant()
                            .toEpochMilli();
                });
    }

    private void registerResolveValueFromAllWorkflowsOfSameEntityAndProfile() {

        handlebars.registerHelper("resolveValueFromAllWorkflowsOfSameEntityAndProfile",
                (final String workflowId, final Options options) -> {

                    try {
                        final var allWorkflows = workflowService.getAllWorkflowsOfSameEntityAndProfileFromWorkflowId(
                                workflowId);

                        final var workflowContexts = allWorkflows.stream()
                                .map(storedWorkflow -> workflowContextStore.getWorkflowContext(
                                        storedWorkflow.getWorkflowId()))
                                .sorted(Comparator.comparing(WorkflowContext::getCreatedAt)
                                        .reversed())
                                .toList();

                        final var template = (String) options.hash("template");

                        return workflowContexts.stream()
                                .map(workflowContext -> handleBarsServiceProvider.get()
                                        .transform(template, workflowContext))
                                .filter(x -> !Strings.isNullOrEmpty(x))
                                .findFirst()
                                .orElse("");

                    } catch (final Exception e) {
                        log.error(
                                "resolveValueFromAllWorkflowsOfSameEntityAndProfile failed to fetch data for workflowId: {}",
                                workflowId, e);
                        return "";
                    }
                });
    }

    private DocumentTypeIdentifierAndLabel getDocumentTypeIdentifierAndLabelFromGenericFieldData(final GenericFieldInput genericFieldInput) {

        try {
            return MapperUtils.convertValue(genericFieldInput.getValue(), DocumentTypeIdentifierAndLabel.class);
        } catch (final Exception e) {
            log.error("Error while fetching documents", e);
            return null;
        }
    }

    private void registerLeftPadString() {

        handlebars.registerHelper("leftPadString", (final String input, final Options options) -> {
            final var targetLength = (Integer) options.param(0);
            final var padStr = (String) options.param(1);

            if (Strings.isNullOrEmpty(input) || input.length() >= targetLength) {
                return input;
            }
            return StringUtils.leftPad(input, targetLength, padStr);
        });
    }

    private void registerGetStringFromLocalDateTime() {

        handlebars.registerHelper("getStringFromLocalDateTime", (final Object value, final Options options) -> {

            if (Objects.isNull(value)) {
                return "";
            }

            final var outputDateFormat = (String) options.hash("type");

            final var format = (String) options.hash("format");

            if (Strings.isNullOrEmpty(format)) {

                return MapperUtils.deserialize(value.toString(), LocalDateTime.class)
                        .format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }

            if (outputDateFormat.equals("date")) {

                return MapperUtils.deserialize(value.toString(), LocalDate.class)
                        .format(DateTimeFormatter.ofPattern(format));
            }

            return MapperUtils.deserialize(value.toString(), LocalDateTime.class)
                    .format(DateTimeFormatter.ofPattern(format));
        });
    }

    private void registerGetCatalogueSearchDetails() {

        handlebars.registerHelper("getCatalogueSearchDetails", (final String entityId, final Options options) -> {

            final var tenant = (String) options.hash("tenant");
            final var category = (String) options.hash("category");
            final var searchId = (String) options.hash("searchId");
            final var columnName = (String) options.hash("columnName");

            return catalogueService.get()
                    .getCatalogueSearchDetails(tenant, category, searchId, columnName);
        });
    }

    private void registerFormatPercentValues() {

        handlebars.registerHelper("formatPercentValues", (final Object value, final Options options) -> {

            final var operand = String.valueOf(value);

            final var lessThanOne = (Boolean) options.hash("lessThanOne");

            final var precision = Integer.parseInt(options.hash("precision"));

            final var roundingMode = (String) options.hash("roundingMode");

            if (Objects.isNull(operand) || Objects.isNull(lessThanOne) || Objects.isNull(roundingMode)) {
                return null;
            }

            if (Boolean.TRUE.equals(lessThanOne)) {

                return BigDecimal.valueOf(Double.parseDouble(operand))
                        .multiply(BigDecimal.valueOf(100))
                        .setScale(precision, RoundingMode.valueOf(roundingMode));
            } else {

                return BigDecimal.valueOf(Double.parseDouble(operand))
                        .setScale(precision, RoundingMode.valueOf(roundingMode));
            }
        });
    }

    private void registerGetStringFromGenericFieldInputValueObject() {

        handlebars.registerHelper("getStringFromGenericFieldInputValueObject",
                (Helper<List<FieldData>>) (fieldDataList, options) -> {

                    final String fieldType = options.hash(FIELD_TYPE);

                    final String responseObjectKey = options.hash(FIELD_INPUT_VALUE_DATA_KEY);

                    if (Objects.isNull(fieldDataList) || Objects.isNull(fieldType) || Objects.isNull(
                            responseObjectKey)) {
                        return null;
                    }

                    return fieldDataList.stream()
                            .filter(fd -> fd.getType()
                                    .equals(fieldType))
                            .findFirst()
                            .map(fd -> getStringForGivenParamFromFieldDataValueObject((GenericFieldInput) fd,
                                    responseObjectKey))
                            .orElse(null);
                });
    }

    private void registerResolveWithInvalidatedActionsDataInContext() {

        handlebars.registerHelper("resolveWithInvalidatedActionsDataInContext",
                (final String workflowId, final Options options) -> {

                    final var workflowContext = workflowContextStore.getWorkflowContextWithInvalidatedActions(
                            workflowId);

                    final var template = (String) options.hash("template");

                    return handleBarsServiceProvider.get()
                            .transform(template, workflowContext);
                });
    }

    @SneakyThrows
    private String getStringForGivenParamFromFieldDataValueObject(final GenericFieldInput fieldData,
                                                                  final String responseObjectKey) {

        return MapperUtils.convertValue(fieldData.getValue(), new TypeReference<Map<String, String>>() {
                })
                .getOrDefault(responseObjectKey, "");
    }

    private void registerValidateString() {

        handlebars.registerHelper("validateString", (final String value, final Options options) -> {

            final var minLengthOfString = (String) options.hash("min_length");
            final var maxLengthOfString = (String) options.hash("max_length");
            final var regex = (String) options.hash("regex");

            if (Strings.isNullOrEmpty(value)) {
                return false;
            }

            if (!Strings.isNullOrEmpty(minLengthOfString) && value.length() <= Integer.parseInt(minLengthOfString)) {
                return false;
            }

            if (!Strings.isNullOrEmpty(maxLengthOfString) && value.length() >= Integer.parseInt(maxLengthOfString)) {
                return false;
            }

            return Strings.isNullOrEmpty(regex) || value.matches(regex);
        });
    }

    private void registerGetUserNameFromOlympusId() {

        handlebars.registerHelper("getUserNameFromOlympusId", (final String olympusId, final Options options) -> {

            if (!Strings.isNullOrEmpty(olympusId)) {

                // TODO: Cache the name to avoid excess Olympus calls
                try {
                    return olympusIMClient.getFeignApiClient()
                            .getUser(olympusId, olympusIMClient.getSystemAuthHeader())
                            .getName();
                } catch (final Exception e) {
                    return null;
                }
            }
            return null;
        });
    }

    private void registerTernary() {

        handlebars.registerHelper("ternary",
                (final Object condition, final Options options) -> (Objects.nonNull(condition)
                        && !Strings.isNullOrEmpty(String.valueOf(condition)) && Boolean.parseBoolean(
                        String.valueOf(condition)))
                        ? options.params[0]
                        : options.params[1]);
    }

    private void registerGetDocumentMetaDataFromDocstore() {

        handlebars.registerHelper("getDocumentMetaDataFromDocstore",
                (final String documentId, final Options options) -> {

                    final var param = (String) options.hash("metaKey");

                    if (Strings.isNullOrEmpty(documentId) || Strings.isNullOrEmpty(param)) {
                        return null;
                    }

                    try {
                        final var fileMeta = docstoreClient.getFileMetaByExternalRefId(appConfigProvider.getData()
                                .getDocstoreNamespace(), documentId);

                        return fileMeta.getMetas()
                                .getOrDefault(param, null);
                    } catch (final Exception exception) {

                        return null;
                    }
                });
    }

    private void registerGetTemplateIdFromAppData() {
        handlebars.registerHelper("getTemplateId", (Helper<String>) (workflowId, options) -> {

            final var ckv = options.hash("componentKitVersion");
            final var intent = options.hash("intent");

            final var templateWorkflowType = BuildUtils.getTemplateWorkflowType(profileService.get(
                    workflowService.validateAndGetWorkflow(workflowId)
                            .getProfileId(), false));

            return templateServiceV2.getTemplate(templateWorkflowType, (String) intent, (int) ckv)
                    .getTemplateId();
        });
    }

    private void registerCheckFieldOptionalStatus() {

        handlebars.registerHelper("checkFieldOptionalStatus", (Helper<String>) (fieldId, options) -> {

            final var screenId = options.hash("screenId");
            final var templateId = options.hash("templateId");

            final var template = templateServiceV2.getTemplate((String) templateId);

            final var field = template.getSectionMappings()
                    .stream()
                    .flatMap(t -> t.getSection()
                            .getFieldGroups()
                            .stream())
                    .filter(t -> t.getId()
                            .equals(screenId))
                    .flatMap(t -> t.getFields()
                            .stream())
                    .filter(t -> t.getId()
                            .equals(fieldId))
                    .findAny();

            return field.<Object>map(Field::getOptional)
                    .orElse(null);
        });
    }
}
