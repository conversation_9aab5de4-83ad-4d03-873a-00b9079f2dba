package com.phonepe.verified.kaizen.storage.aerospike.commands;

import com.aerospike.client.AerospikeClient;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeCommand;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeSet;
import com.phonepe.verified.kaizen.storage.aerospike.keys.RequestKey;

@Singleton
public class RespondedToRevolverAgainstRequestIdCommand extends AerospikeCommand<RequestKey, Boolean> {

    @Inject
    public RespondedToRevolverAgainstRequestIdCommand(final AerospikeClient aerospikeClient,
                                                      final AerospikeConfig aerospikeConfig) {
        super(aerospikeClient, aerospikeConfig, AerospikeSet.RESPONDED_TO_REVOLVER_AGAINST_REQUEST_ID, Boolean.class);
    }
}
