package com.phonepe.verified.kaizen.queue;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.verified.kaizen.utils.RequestInfoUtils;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Set;
import lombok.SneakyThrows;

public abstract class BaseActor<V extends BaseMessage> extends Actor<ActorType, V> {

    protected BaseActor(final ActorType actorType,
                        final ActorConfig actorConfig,
                        final ConnectionRegistry connectionRegistry,
                        final ObjectMapper mapper,
                        final RetryStrategyFactory retryStrategyFactory,
                        final ExceptionHandlingFactory exceptionHandlingFactory,
                        final Class<V> clazz) {

        super(actorType, actorConfig, connectionRegistry, mapper, retryStrategyFactory, exceptionHandlingFactory, clazz,
                Set.of(JsonProcessingException.class));
    }

    @Override
    @SneakyThrows
    protected boolean handle(final V message,
                             final MessageMetadata messageMetadata) {

        RequestInfoUtils.saveRequestInfoInThread(message.getRequestInfo());

        try {
            return handleMessage(message);
        } finally {
            RequestInfoUtils.clearRequestInfo();
        }
    }

    protected abstract boolean handleMessage(final V message);
}

