package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.OtpProviderConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.SentinelOtpProviderConfig;
import com.phonepe.verified.kaizen.queue.messages.VerifyOtpHurdleMessage;
import com.phonepe.verified.kaizen.services.OtpService;
import javax.inject.Singleton;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class OtpProviderConfigToVerifyOtpHurdleVisitor implements
        OtpProviderConfigVisitor<Void, VerifyOtpHurdleMessage> {

    private final OtpService otpService;

    @Override
    public Void visit(final SentinelOtpProviderConfig sentinelOtpProviderConfig,
                      final VerifyOtpHurdleMessage verifyOtpHurdleMessage) {

        otpService.verifyOtpHurdle(verifyOtpHurdleMessage.getOtpVerificationRequest(),
                verifyOtpHurdleMessage.getActionId(), verifyOtpHurdleMessage.getIntent(),
                verifyOtpHurdleMessage.getComponentKitVersion(), verifyOtpHurdleMessage.getRequestInfo());
        return null;
    }
}
