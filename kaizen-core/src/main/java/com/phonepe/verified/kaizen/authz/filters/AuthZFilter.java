package com.phonepe.verified.kaizen.authz.filters;

import com.google.inject.Injector;
import com.phonepe.gandalf.client.GandalfClient;
import com.phonepe.gandalf.core.jwt.JWTHelper;
import com.phonepe.gandalf.models.authz.PermissionAuthorizationOperator;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.requestinfo.models.headers.RequestInfoHeaders;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.configs.AuthZConfig;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.AuthNService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.Utils;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Priority;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jose4j.jwt.JwtClaims;
import org.jose4j.jwt.MalformedClaimException;

@AuthZ
@Slf4j
@Priority(5001)
public class AuthZFilter implements ContainerRequestFilter {

    private final AuthZConfig authZConfig;
    private final AuthNService authNService;
    private final OlympusIMClient olympusIMClient;
    @Context
    private ResourceInfo resourceInfo;

    public AuthZFilter(final KaizenConfig kaizenConfig,
                       final Injector injector) {

        this.authZConfig = kaizenConfig.getAuthZConfig();
        this.authNService = injector.getInstance(AuthNService.class);
        this.olympusIMClient = injector.getInstance(OlympusIMClient.class);
    }

    @Override
    @SneakyThrows
    public void filter(final ContainerRequestContext requestContext) {

        final var method = resourceInfo.getResourceMethod();

        if (method.isAnnotationPresent(AuthZ.class)) {

            final var authorizationHeaderValue = requestContext.getHeaderString(HttpHeaders.AUTHORIZATION);
            if (!StringUtils.isEmpty(authorizationHeaderValue)) {
                final var space = authorizationHeaderValue.indexOf(' ');
                if (space > 0) {
                    final var tokenType = authorizationHeaderValue.substring(0, space);
                    if ("O-Bearer".equalsIgnoreCase(tokenType)) {
                        handleOlympus(requestContext);
                        return;
                    } else if ("Bearer".equalsIgnoreCase(tokenType)) {
                        handleGandalfToken(requestContext, authorizationHeaderValue.substring(space + 1));
                        return;
                    }
                }
            }
            throw KaizenException.create(KaizenResponseCode.AUTH_Z_TOKEN_NOT_AVAILABLE, Map.of());
        }
    }

    private void handleOlympus(final ContainerRequestContext requestContext) {

        final var userPrincipal = (ServiceUserPrincipal) requestContext.getSecurityContext()
                .getUserPrincipal();

        if (Objects.isNull(userPrincipal)) {
            throw KaizenException.create(KaizenResponseCode.AUTH_TOKEN_NOT_AVAILABLE, Map.of());
        }

        requestContext.setProperty(ClientNamePropertyFilter.CLIENT_NAME_ATTRIBUTE, userPrincipal.getUserAuthDetails()
                .getUserDetails()
                .getUserId());

        final var userAuthDetails = userPrincipal.getUserAuthDetails();
        final var method = resourceInfo.getResourceMethod();
        final var authZAnnotation = method.getAnnotation(AuthZ.class);

        if (olympusIMClient.verifyPermission(userAuthDetails, authZAnnotation.olympusPermission())) {

            final var userDetails = UserDetails.builder()
                    .userId(userAuthDetails.getUserDetails()
                            .getUserId())
                    .token(userAuthDetails.getUserDetails()
                            .getToken())
                    .userType(userAuthDetails.getUserDetails()
                                      .getUserType() == com.phonepe.olympus.im.models.authn.UserType.HUMAN
                              ? com.phonepe.gandalf.models.authn.UserType.USER
                              : com.phonepe.gandalf.models.authn.UserType.SYSTEM)
                    .build();

            requestContext.setProperty(Constants.PV_AUTH_Z_CONTEXT, userDetails);
            return;
        }

        throw KaizenException.create(KaizenResponseCode.AUTH_Z_FORBIDDEN, Map.of());
    }

    @SneakyThrows
    private void handleGandalfToken(final ContainerRequestContext requestContext,
                                    final String authToken) {

        final var method = resourceInfo.getResourceMethod();
        final var effectiveToken = authNService.getEffectiveToken(authToken);
        final var jwtClaims = authNService.getJwtClaims(effectiveToken);

        validateExpiry(jwtClaims);

        final var authRole = requestContext.getHeaderString(RequestInfoHeaders.AUTHORIZED_FOR_ROLE);
        final var authId = requestContext.getHeaderString(RequestInfoHeaders.AUTHORIZED_FOR_ID);

        if (Constants.CONSUMER_ROLE.equals(authRole)) {

            requestContext.setProperty(ClientNamePropertyFilter.CLIENT_NAME_ATTRIBUTE, authId);
            requestContext.setProperty(Constants.PV_AUTH_Z_CONTEXT, Utils.buildHumanUserDetails(authId));
            return;
        }

        if (authZConfig.getRoleName()
                .equalsIgnoreCase(authRole)) {
            final var authName = requestContext.getHeaderString(RequestInfoHeaders.AUTHORIZED_FOR_NAME);

            requestContext.setProperty(ClientNamePropertyFilter.CLIENT_NAME_ATTRIBUTE, authId);
            requestContext.setProperty(Constants.PV_AUTH_Z_CONTEXT, Utils.buildHumanUserDetails(authId));
            requestContext.setProperty(Constants.PV_WORKFLOW_ID, authName);

            return;
        }

        if (JWTHelper.Keys.JWT_ISSUER.equalsIgnoreCase(jwtClaims.getIssuer())) {

            final var parsedJWT = GandalfClient.parse(effectiveToken);
            final var authZAnnotation = method.getAnnotation(AuthZ.class);

            if (GandalfClient.verifyPermissions(parsedJWT, new String[]{authZAnnotation.gandalfPermission()},
                    PermissionAuthorizationOperator.OR)) {

                final var userDetails = GandalfClient.toUserDetails(jwtClaims, parsedJWT);
                requestContext.setProperty(ClientNamePropertyFilter.CLIENT_NAME_ATTRIBUTE, userDetails.getUserId());
                requestContext.setProperty(Constants.PV_AUTH_Z_CONTEXT, userDetails);
                return;
            }
        }

        log.error("Ideally if the execution reaches here, the token is not a valid gandalf token. EffectiveToken : {}",
                effectiveToken);
        throw KaizenException.create(KaizenResponseCode.AUTH_Z_FORBIDDEN, Map.of());
    }

    private void validateExpiry(final JwtClaims jwtClaims) throws MalformedClaimException {

        final var expirationTime = jwtClaims.getExpirationTime()
                .getValue();

        final var currentTime = LocalDateTime.now(ZoneOffset.UTC)
                .toEpochSecond(ZoneOffset.UTC);

        if (expirationTime < currentTime) {
            throw KaizenException.create(KaizenResponseCode.AUTH_TOKEN_EXPIRED, Map.of());
        }
    }
}
