package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.platform.killswitch.common.RecommendedAction;
import com.phonepe.verified.kaizen.clients.models.killswitch.KillSwitchContextType;
import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class KillSwitchEvent extends BaseEvent {


    private final String id;

    private final String rule;

    private final String reason;

    private KillSwitchContextType contextType;

    private RecommendedAction recommendedAction;

    @Builder
    public KillSwitchEvent(final String intent,
                           final String entityId,
                           final String namespace,
                           final String workflowId,
                           @NotNull final String groupingKey,
                           final String organization,
                           final String workflowType,
                           final String workflowStepId,
                           final EntityType entityType,
                           final String workflowVersion,
                           final String screenMappingId,
                           final String actionMappingId,
                           final long componentKitVersion,
                           final String profileStepMappingId,
                           final ProfileType profileType,
                           final String addOnType,
                           final String id,
                           final String rule,
                           final String reason,
                           final KillSwitchContextType killSwitchContextType,
                           final RecommendedAction recommendedAction) {

        super(EventType.KILL_SWITCH, intent, entityId, namespace, workflowId, profileType, addOnType, groupingKey,
                organization, workflowType, workflowStepId, entityType, workflowVersion, screenMappingId,
                actionMappingId, componentKitVersion, profileStepMappingId);
        this.id = id;
        this.rule = rule;
        this.reason = reason;
        this.contextType = killSwitchContextType;
        this.recommendedAction = recommendedAction;
    }
}
