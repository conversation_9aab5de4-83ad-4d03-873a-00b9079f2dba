package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.shadow.models.response.TemplateInitResponse;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.AlwaysResumeGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.AlwaysSummaryViewGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.HopeRuleBasedSummaryView;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.SummaryViewBasedOnFlagGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.visitor.GetTemplateConfigVisitor;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.GenerateTemplateInitResponseGetTemplateConfigVisitor.GenerateTemplateInitResponseGetTemplateConfigVisitorMessage;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Singleton
@AllArgsConstructor(onConstructor = @__(@Inject))
public class GenerateTemplateInitResponseGetTemplateConfigVisitor implements
        GetTemplateConfigVisitor<TemplateInitResponse, GenerateTemplateInitResponseGetTemplateConfigVisitorMessage> {

    private final HopeLangService hopeLangService;
    private final WorkflowContextStore workflowContextStore;
    private WorkflowService workflowService;

    @Override
    public TemplateInitResponse visit(final AlwaysSummaryViewGetTemplateConfig alwaysSummaryViewGetTemplateConfig,
                                      final GenerateTemplateInitResponseGetTemplateConfigVisitorMessage data) {

        return workflowService.generateTemplateInitResponseForSummaryConfig(data.getWorkflowId(), data.getIntent(),
                data.getComponentKitVersion(), alwaysSummaryViewGetTemplateConfig.getSummaryViewScreenMappingId());
    }

    @Override
    public TemplateInitResponse visit(final SummaryViewBasedOnFlagGetTemplateConfig summaryViewBasedOnFlagGetTemplateConfig,
                                      final GenerateTemplateInitResponseGetTemplateConfigVisitorMessage data) {

        if (data.isShowSummaryView()) {

            return workflowService.generateTemplateInitResponseForSummaryConfig(data.getWorkflowId(), data.getIntent(),
                    data.getComponentKitVersion(),
                    summaryViewBasedOnFlagGetTemplateConfig.getSummaryViewScreenMappingId());

        } else {
            return workflowService.generateTemplateInitResponseForResumeConfig(data.getWorkflowId(), data.getIntent(),
                    data.getComponentKitVersion());
        }
    }

    @Override
    public TemplateInitResponse visit(final AlwaysResumeGetTemplateConfig alwaysResumeGetTemplateConfig,
                                      final GenerateTemplateInitResponseGetTemplateConfigVisitorMessage data) {
        return workflowService.generateTemplateInitResponseForResumeConfig(data.getWorkflowId(), data.getIntent(),
                data.getComponentKitVersion());
    }

    @Override
    public TemplateInitResponse visit(final HopeRuleBasedSummaryView hopeRuleBasedSummaryView,
                                      final GenerateTemplateInitResponseGetTemplateConfigVisitorMessage data) {

        final var workflowContext = workflowContextStore.getWorkflowContext(data.workflowId);

        final var workflowContextNode = MapperUtils.convertToJsonNode(workflowContext);

        final var matchResult = hopeLangService.evaluate(hopeRuleBasedSummaryView.getEvaluationRule(),
                workflowContextNode);

        if (matchResult) {

            return workflowService.generateTemplateInitResponseForSummaryConfig(data.getWorkflowId(), data.getIntent(),
                    data.getComponentKitVersion(), hopeRuleBasedSummaryView.getSummaryViewScreenMappingId());

        } else {

            return workflowService.generateTemplateInitResponseForResumeConfig(data.getWorkflowId(), data.getIntent(),
                    data.getComponentKitVersion());
        }
    }

    @Getter
    @Builder
    @AllArgsConstructor
    public static class GenerateTemplateInitResponseGetTemplateConfigVisitorMessage {

        private final String workflowId;
        private final String intent;
        private final long componentKitVersion;
        private final boolean showSummaryView;

    }
}
