package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.PostCompletionActionConfigProcessorMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowMessage;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.PostCompletionActionConfigExecutor;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class PostCompletionActionConfigProcessorActor extends BaseActor<PostCompletionActionConfigProcessorMessage> {

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final PostCompletionActionConfigExecutor postCompletionActionConfigExecutor;

    @Inject
    protected PostCompletionActionConfigProcessorActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                       final ConnectionRegistry connectionRegistry,
                                                       final ObjectMapper mapper,
                                                       final RetryStrategyFactory retryStrategyFactory,
                                                       final ExceptionHandlingFactory exceptionHandlingFactory,
                                                       final ProfileService profileService,
                                                       final WorkflowService workflowService,
                                                       final PostCompletionActionConfigExecutor postCompletionActionConfigExecutor) {
        super(ActorType.POST_COMPLETION_ACTION_CONFIG_PROCESSOR,
                actorConfigMap.get(ActorType.POST_COMPLETION_ACTION_CONFIG_PROCESSOR), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, PostCompletionActionConfigProcessorMessage.class);
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.postCompletionActionConfigExecutor = postCompletionActionConfigExecutor;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final PostCompletionActionConfigProcessorMessage postCompletionActionConfigProcessorMessage) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(
                postCompletionActionConfigProcessorMessage.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var callbackWorkflowMessage = WorkflowMessage.builder()
                .workflowId(postCompletionActionConfigProcessorMessage.getWorkflowId())
                .failureErrorCode(postCompletionActionConfigProcessorMessage.getFailureErrorCode())
                .build();

        profile.getPostCompletionActionConfig()
                .accept(postCompletionActionConfigExecutor, callbackWorkflowMessage);

        return true;
    }
}
