package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.SelfieDocumentIdSubmitRequest;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ProcessSelfieDocumentIdSubmitMessage extends BaseMessage {

    @NotEmpty
    private final String actionId;

    @NonNull
    private final String intent;

    @NonNull
    private final UserDetails userDetails;

    private final long componentKitVersion;

    @Valid
    @NotNull
    private final SelfieDocumentIdSubmitRequest selfieDocumentIdSubmitRequest;

    @Builder
    @Jacksonized
    public ProcessSelfieDocumentIdSubmitMessage(@NonNull final RequestInfo requestInfo,
                                                @NotEmpty final String actionId,
                                                @NonNull final String intent,
                                                @NonNull final UserDetails userDetails,
                                                final long componentKitVersion,
                                                @Valid @NotNull final SelfieDocumentIdSubmitRequest selfieDocumentIdSubmitRequest) {

        super(ActorMessageType.PROCESS_SELFIE_DOCUMENT_ID_SUBMIT, requestInfo);
        this.actionId = actionId;
        this.intent = intent;
        this.userDetails = userDetails;
        this.componentKitVersion = componentKitVersion;
        this.selfieDocumentIdSubmitRequest = selfieDocumentIdSubmitRequest;
    }
}
