package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.OtpHurdleV2ActionContext;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.OtpProviderConfigToTriggerOtpHurdleActionVisitor;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class TriggerOtpHurdleV2Actor extends BaseActor<StepActionMessage> {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final OtpProviderConfigToTriggerOtpHurdleActionVisitor otpProviderConfigToTriggerOtpHurdleActionVisitor;

    @Inject
    protected TriggerOtpHurdleV2Actor(final Map<ActorType, ActorConfig> actorConfigMap,
                                      final ConnectionRegistry connectionRegistry,
                                      final ObjectMapper mapper,
                                      final RetryStrategyFactory retryStrategyFactory,
                                      final ExceptionHandlingFactory exceptionHandlingFactory,
                                      final ActionService actionService,
                                      final ProfileService profileService,
                                      final WorkflowService workflowService,
                                      final WorkflowStepService workflowStepService,
                                      final OtpProviderConfigToTriggerOtpHurdleActionVisitor otpProviderConfigToTriggerOtpHurdleActionVisitor) {
        super(ActorType.TRIGGER_OTP_HURDLE_V2, actorConfigMap.get(ActorType.TRIGGER_OTP_HURDLE_V2), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, StepActionMessage.class);
        this.actionService = actionService;
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
        this.otpProviderConfigToTriggerOtpHurdleActionVisitor = otpProviderConfigToTriggerOtpHurdleActionVisitor;
    }

    @Override
    protected boolean handleMessage(final StepActionMessage stepActionMessage) {

        final var storedAction = actionService.validateAndGetAction(stepActionMessage.getActionId());

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var otpActionContext = (OtpHurdleV2ActionContext) actionService.extractStepActionContext(
                storedAction.getActionId(), profileStep.getProfileScreenConfig());

        otpActionContext.getOtpProviderConfig()
                .accept(otpProviderConfigToTriggerOtpHurdleActionVisitor, storedAction);

        return true;
    }
}
