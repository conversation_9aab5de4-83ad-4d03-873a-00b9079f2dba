package com.phonepe.verified.kaizen.hopelangfunctions;

import io.appform.hope.core.Value;
import io.appform.hope.core.functions.FunctionImplementation;
import io.appform.hope.core.functions.HopeFunction;
import io.appform.hope.core.utils.Converters;
import io.appform.hope.core.values.NumericValue;
import io.appform.hope.core.visitors.Evaluator.EvaluationContext;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import lombok.AllArgsConstructor;

@AllArgsConstructor
@FunctionImplementation("computeAgeFromDobString")
public class ComputeAgeFromDobString extends HopeFunction<NumericValue> {

    private final Value dateOfBirth; // YYYY-MM-DD format

    @Override
    public NumericValue apply(final EvaluationContext evaluationContext) {

        final var dobString = Converters.stringValue(evaluationContext, dateOfBirth, LocalDate.now()
                .format(DateTimeFormatter.ISO_LOCAL_DATE));

        final var dob = LocalDate.parse(dobString, DateTimeFormatter.ISO_LOCAL_DATE);

        final var age = Period.between(dob, LocalDate.now())
                .getYears();

        return new NumericValue(age);
    }
}
