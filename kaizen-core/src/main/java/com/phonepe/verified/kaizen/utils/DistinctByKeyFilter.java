package com.phonepe.verified.kaizen.utils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.function.Predicate;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DistinctByKeyFilter<T, K> implements Predicate<T> {

    private final Function<T, K> keyExtractor;
    private final ConcurrentMap<K, Boolean> seen = new ConcurrentHashMap<>();

    @Override
    public boolean test(final T object) {
        return seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }
}