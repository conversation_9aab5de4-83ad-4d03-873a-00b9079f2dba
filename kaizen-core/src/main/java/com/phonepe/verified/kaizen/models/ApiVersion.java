package com.phonepe.verified.kaizen.models;

import lombok.experimental.UtilityClass;

public enum ApiVersion {
    V1 {
        @Override
        public <T> T accept(final ApiVersion.ApiVersionVisitor<T> visitor) {
            return visitor.visitV1();
        }
    },
    V2 {
        @Override
        public <T> T accept(final ApiVersion.ApiVersionVisitor<T> visitor) {
            return visitor.visitV2();
        }
    };

    public abstract <T> T accept(ApiVersion.ApiVersionVisitor<T> visitor);

    public interface ApiVersionVisitor<T> {

        T visitV1();

        T visitV2();

    }

    @UtilityClass
    public static final class Names {

        public static final String V1 = "V1";
        public static final String V2 = "V2";
    }
}