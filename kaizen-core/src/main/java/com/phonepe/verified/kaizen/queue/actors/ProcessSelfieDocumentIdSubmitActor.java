package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.ProcessSelfieDocumentIdSubmitMessage;
import com.phonepe.verified.kaizen.services.SelfieHurdleService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ProcessSelfieDocumentIdSubmitActor extends BaseActor<ProcessSelfieDocumentIdSubmitMessage> {

    private final SelfieHurdleService selfieService;

    @Inject
    protected ProcessSelfieDocumentIdSubmitActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                 final ConnectionRegistry connectionRegistry,
                                                 final ObjectMapper mapper,
                                                 final RetryStrategyFactory retryStrategyFactory,
                                                 final ExceptionHandlingFactory exceptionHandlingFactory,
                                                 final SelfieHurdleService selfieService) {
        super(ActorType.PROCESS_SELFIE_DOCUMENT_ID_SUBMIT,
                actorConfigMap.get(ActorType.PROCESS_SELFIE_DOCUMENT_ID_SUBMIT), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, ProcessSelfieDocumentIdSubmitMessage.class);
        this.selfieService = selfieService;
    }

    @Override
    protected boolean handleMessage(final ProcessSelfieDocumentIdSubmitMessage message) {

        selfieService.submitSelfieDocumentId(message.getActionId(), message.getIntent(), message.getRequestInfo(),
                message.getComponentKitVersion(), message.getUserDetails(), message.getSelfieDocumentIdSubmitRequest());
        return true;
    }
}
