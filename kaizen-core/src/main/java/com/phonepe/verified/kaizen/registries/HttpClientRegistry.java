package com.phonepe.verified.kaizen.registries;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class HttpClientRegistry {

    private final Map<String, HttpExecutorBuilderFactory> httpExecutorBuilderFactoryServiceMap;

    @Inject
    public HttpClientRegistry(final RangerHubConfiguration rangerHubConfiguration,
                              final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                              final ObjectMapper mapper,
                              final MetricRegistry metricRegistry) {
        this.httpExecutorBuilderFactoryServiceMap = buildHttpClientRegistry(rangerHubConfiguration.getServices(),
                serviceEndpointProviderFactory, mapper, metricRegistry);
    }

    private Map<String, HttpExecutorBuilderFactory> buildHttpClientRegistry(final Set<HttpConfiguration> httpConfigurations,
                                                                            final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                                                                            final ObjectMapper mapper,
                                                                            final MetricRegistry metricRegistry) {

        return httpConfigurations.stream()
                .filter(httpConfiguration -> ClientIds.WHITELISTED_CLIENT_IDS_FOR_HTTP_CLIENT_REGISTRY.contains(
                        httpConfiguration.getClientId()))
                .collect(Collectors.toMap(HttpConfiguration::getClientId,
                        httpConfiguration -> HttpClientUtils.getHttpExecutorBuilderFactory(httpConfiguration,
                                serviceEndpointProviderFactory, mapper, metricRegistry)));
    }

    public HttpExecutorBuilderFactory getHttpExecutorBuilderFactoryForService(final String serviceName) {

        final var httpExecutorBuilderFactory = httpExecutorBuilderFactoryServiceMap.get(serviceName);

        if (Objects.isNull(httpExecutorBuilderFactory)) {
            throw KaizenException.create(KaizenResponseCode.HTTP_EXECUTOR_NOT_FOUND,
                    Map.of("serviceName", serviceName));
        }

        return httpExecutorBuilderFactory;
    }
}
