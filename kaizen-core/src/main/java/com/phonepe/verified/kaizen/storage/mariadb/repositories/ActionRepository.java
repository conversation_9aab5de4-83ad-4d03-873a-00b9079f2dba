package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class ActionRepository extends CrudRepository<StoredAction> {

    @Inject
    public ActionRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredAction.class), null);
    }

    public Optional<StoredAction> select(final String actionId) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredAction.class)
                .add(Restrictions.eq(Fields.actionId, actionId));
        return select(actionId, detachedCriteria).stream()
                .findFirst();
    }

    public List<StoredAction> selectFromAllShards(final Set<String> actionIds) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredAction.class)
                .add(Restrictions.in(Fields.actionId, actionIds));
        return scatterGather(detachedCriteria);
    }

    public List<StoredAction> select(final String workflowStepId,
                                     final String actionMappingId) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredAction.class)
                .add(Restrictions.eq(Fields.workflowStepId, workflowStepId))
                .add(Restrictions.eq(Fields.actionMappingId, actionMappingId));
        return select(workflowStepId, detachedCriteria);
    }

    public List<StoredAction> select(final String workflowStepId,
                                     final List<String> actionMappingIds) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredAction.class)
                .add(Restrictions.eq(Fields.workflowStepId, workflowStepId))
                .add(Restrictions.in(Fields.actionMappingId, actionMappingIds));
        return select(workflowStepId, detachedCriteria);
    }

    public List<StoredAction> select(final Set<String> workflowStepIds) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredAction.class)
                .add(Restrictions.in(Fields.workflowStepId, workflowStepIds));

        return select(workflowStepIds.iterator()
                .next(), detachedCriteria);
    }

    public List<StoredAction> select(final Set<String> workflowStepIds,
                                     final String actionMappingId) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredAction.class)
                .add(Restrictions.in(Fields.workflowStepId, workflowStepIds))
                .add(Restrictions.eq(Fields.actionMappingId, actionMappingId));

        return select(workflowStepIds.iterator()
                .next(), detachedCriteria);
    }
}
