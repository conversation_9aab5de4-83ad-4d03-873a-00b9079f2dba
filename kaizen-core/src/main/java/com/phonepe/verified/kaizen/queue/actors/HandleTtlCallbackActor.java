package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.HandleTtlCallbackMessage;
import com.phonepe.verified.kaizen.registries.StateMachineRegistry;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.States;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.transition.Transition;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class HandleTtlCallbackActor extends BaseActor<HandleTtlCallbackMessage> {

    private final ActionService actionService;

    private final Provider<StateMachineRegistry> stateMachineRegistry;

    @Inject
    protected HandleTtlCallbackActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                     final ConnectionRegistry connectionRegistry,
                                     final ObjectMapper mapper,
                                     final RetryStrategyFactory retryStrategyFactory,
                                     final ExceptionHandlingFactory exceptionHandlingFactory,
                                     final ActionService actionService,
                                     final Provider<StateMachineRegistry> stateMachineRegistry) {
        super(ActorType.HANDLE_TTL_CALLBACK, actorConfigMap.get(ActorType.HANDLE_TTL_CALLBACK), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, HandleTtlCallbackMessage.class);
        this.actionService = actionService;
        this.stateMachineRegistry = stateMachineRegistry;
    }

    @Override
    @SuppressWarnings("java:S3516")
    protected boolean handleMessage(final HandleTtlCallbackMessage handleTtlCallbackMessage) {

        final var actionId = handleTtlCallbackMessage.getActionId();

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var springStateMachine = stateMachineRegistry.get()
                .getSpringStateMachine(storedAction.getActionType(), storedAction.getStateMachineVersion());

        final var transitions = springStateMachine.getTransitions();

        final var requiredSourceStateOptional = transitions.stream()
                .filter(t -> States.PSEUDO_SUCCESS.equals(t.getTarget()
                        .getId()))
                .findFirst()
                .map(Transition::getSource)
                .map(State::getId);

        if (requiredSourceStateOptional.isEmpty()) {

            log.info("No transition to PSEUDO_SUCCESS found for actionId {}", actionId);
            return true;
        }

        if (!requiredSourceStateOptional.get()
                .equals(storedAction.getCurrentState())) {
            return true;
        }

        actionService.triggerEvent(actionId, storedAction.getActionType(), storedAction.getStateMachineVersion(),
                Events.MOVE_TO_PSEUDO_SUCCESS, Constants.EMPTY_TRANSITION_CONTEXT,
                handleTtlCallbackMessage.getUserDetails(), null);

        return true;
    }
}
