package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadataAudit;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredConsentActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredConsentActionMetadataAudit;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadataAudit;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredKeyValueMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredKeyValueMetadataAudit;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredOtpHurdleActionMetaData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredOtpHurdleActionMetaDataAudit;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataAuditRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import lombok.RequiredArgsConstructor;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class StoredActionMetadataToPurgedStoredActionMetadataVisitor extends
        ActionMetadataExceptionThrowingVisitor<Void, Void> {

    private final ActionMetadataRepository actionMetadataRepository;

    private final ActionMetadataAuditRepository actionMetadataAuditRepository;

    @Override
    public Void visit(final StoredKeyValueMetadata storedKeyValueMetadata,
                      final Void data) {

        actionMetadataRepository.update(storedKeyValueMetadata.getShardingKey(),
                DetachedCriteria.forClass(StoredKeyValueMetadata.class)
                        .add(Restrictions.eq(Fields.actionId, storedKeyValueMetadata.getActionId())),
                (final StoredActionMetadata actionMetadata) -> {
                    final StoredKeyValueMetadata storedActionKeyValueMetadata = (StoredKeyValueMetadata) actionMetadata;
                    storedActionKeyValueMetadata.setValue(Constants.PURGE_STRING);
                    return actionMetadata;
                });

        actionMetadataAuditRepository.updateAll(storedKeyValueMetadata.getShardingKey(), 0, Integer.MAX_VALUE,
                DetachedCriteria.forClass(StoredKeyValueMetadataAudit.class)
                        .add(Restrictions.eq(Fields.actionId, storedKeyValueMetadata.getActionId())),
                (final StoredActionMetadataAudit actionMetadata) -> {
                    final StoredKeyValueMetadataAudit storedKeyValueMetadataAudit = (StoredKeyValueMetadataAudit) actionMetadata;
                    storedKeyValueMetadataAudit.setValue(Constants.PURGE_STRING);
                    return storedKeyValueMetadataAudit;
                });

        return data;
    }

    @Override
    public Void visit(final StoredConsentActionMetadata storedConsentActionMetadata,
                      final Void data) {

        actionMetadataRepository.update(storedConsentActionMetadata.getShardingKey(),
                DetachedCriteria.forClass(StoredConsentActionMetadata.class)
                        .add(Restrictions.eq(Fields.actionId, storedConsentActionMetadata.getActionId())),
                (final StoredActionMetadata actionMetadata) -> {
                    final StoredConsentActionMetadata storeActionMetaData = (StoredConsentActionMetadata) actionMetadata;
                    storeActionMetaData.setConsentGranted(Constants.PURGE_STRING);
                    storeActionMetaData.setConsentDetailsUrl(Constants.PURGE_STRING);
                    storeActionMetaData.setLanguage(Constants.PURGE_STRING);
                    storeActionMetaData.setSourceType(Constants.PURGE_STRING);
                    storeActionMetaData.setSourceVersion(Constants.PURGE_STRING);
                    storeActionMetaData.setSourcePlatform(Constants.PURGE_STRING);
                    storeActionMetaData.setConsentGrantedBy(Constants.PURGE_STRING);
                    storeActionMetaData.setConsentType(Constants.PURGE_STRING);
                    return storeActionMetaData;
                });

        actionMetadataAuditRepository.updateAll(storedConsentActionMetadata.getShardingKey(), 0, Integer.MAX_VALUE,
                DetachedCriteria.forClass(StoredConsentActionMetadataAudit.class)
                        .add(Restrictions.eq(Fields.actionId, storedConsentActionMetadata.getActionId())),
                (final StoredActionMetadataAudit actionMetadata) -> {
                    final StoredConsentActionMetadataAudit storedConsentActionMetadataAudit = (StoredConsentActionMetadataAudit) actionMetadata;
                    storedConsentActionMetadataAudit.setConsentGranted(Constants.PURGE_STRING);
                    storedConsentActionMetadataAudit.setConsentDetailsUrl(Constants.PURGE_STRING);
                    storedConsentActionMetadataAudit.setLanguage(Constants.PURGE_STRING);
                    storedConsentActionMetadataAudit.setSourceType(Constants.PURGE_STRING);
                    storedConsentActionMetadataAudit.setSourceVersion(Constants.PURGE_STRING);
                    storedConsentActionMetadataAudit.setSourcePlatform(Constants.PURGE_STRING);
                    storedConsentActionMetadataAudit.setConsentGrantedBy(Constants.PURGE_STRING);
                    storedConsentActionMetadataAudit.setConsentType(Constants.PURGE_STRING);
                    return storedConsentActionMetadataAudit;
                });

        return data;
    }

    @Override
    public Void visit(final StoredOtpHurdleActionMetaData storedOTPHurdleActionMetaData,
                      final Void data) {

        actionMetadataRepository.update(storedOTPHurdleActionMetaData.getShardingKey(),
                DetachedCriteria.forClass(StoredOtpHurdleActionMetaData.class)
                        .add(Restrictions.eq(Fields.actionId, storedOTPHurdleActionMetaData.getActionId())),
                (final StoredActionMetadata actionMetadata) -> {
                    final StoredOtpHurdleActionMetaData storedActionMetadata = (StoredOtpHurdleActionMetaData) actionMetadata;
                    storedActionMetadata.setOtpReferenceId(Constants.PURGE_STRING);
                    storedActionMetadata.setOtp(Constants.PURGE_STRING);
                    return storedActionMetadata;
                });

        actionMetadataAuditRepository.updateAll(storedOTPHurdleActionMetaData.getShardingKey(), 0, Integer.MAX_VALUE,
                DetachedCriteria.forClass(StoredOtpHurdleActionMetaDataAudit.class)
                        .add(Restrictions.eq(Fields.actionId, storedOTPHurdleActionMetaData.getActionId())),
                (final StoredActionMetadataAudit actionMetadata) -> {
                    final StoredOtpHurdleActionMetaDataAudit storedOtpHurdleActionMetaDataAudit = (StoredOtpHurdleActionMetaDataAudit) actionMetadata;
                    storedOtpHurdleActionMetaDataAudit.setOtpReferenceId(Constants.PURGE_STRING);
                    storedOtpHurdleActionMetaDataAudit.setOtp(Constants.PURGE_STRING);
                    return storedOtpHurdleActionMetaDataAudit;
                });

        return data;
    }

    @Override
    public Void visit(final StoredDocumentUploadActionMetadata storedDocumentUploadActionMetadata,
                      final Void data) {

        actionMetadataRepository.update(storedDocumentUploadActionMetadata.getShardingKey(),
                DetachedCriteria.forClass(StoredDocumentUploadActionMetadata.class)
                        .add(Restrictions.eq(Fields.actionId, storedDocumentUploadActionMetadata.getActionId())),
                (final StoredActionMetadata actionMetadata) -> {
                    final StoredDocumentUploadActionMetadata storedActionMetadata = (StoredDocumentUploadActionMetadata) actionMetadata;
                    storedActionMetadata.setRequestId(Constants.PURGE_STRING);
                    storedActionMetadata.setDocumentId(Constants.PURGE_STRING);
                    storedActionMetadata.setDocumentLabel(Constants.PURGE_STRING);
                    return actionMetadata;
                });

        actionMetadataAuditRepository.updateAll(storedDocumentUploadActionMetadata.getShardingKey(), 0,
                Integer.MAX_VALUE, DetachedCriteria.forClass(StoredDocumentUploadActionMetadataAudit.class)
                        .add(Restrictions.eq(Fields.actionId, storedDocumentUploadActionMetadata.getActionId())),
                (final StoredActionMetadataAudit actionMetadata) -> {
                    final StoredDocumentUploadActionMetadataAudit storedDocumentUploadActionMetadataAudit = (StoredDocumentUploadActionMetadataAudit) actionMetadata;
                    storedDocumentUploadActionMetadataAudit.setRequestId(Constants.PURGE_STRING);
                    storedDocumentUploadActionMetadataAudit.setDocumentId(Constants.PURGE_STRING);
                    storedDocumentUploadActionMetadataAudit.setDocumentLabel(Constants.PURGE_STRING);
                    return storedDocumentUploadActionMetadataAudit;
                });

        return data;
    }
}
