package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.caches.impl.WorkflowContextWithInvalidatedActionsCache;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.aerospike.commands.WorkflowContextCommand;
import com.phonepe.verified.kaizen.storage.aerospike.commands.WorkflowContextLockCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.ActionContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowScreenContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowStepContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.WorkflowContextKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.utils.WorkflowContextStoreUtils;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class WorkflowContextStoreImpl implements WorkflowContextStore {

    private final ActionService actionService;
    private final ProfileService profileService;
    private final WorkflowService workflowService;
    private final WorkflowStepService workflowStepService;
    private final ActionMetadataService actionMetadataService;
    private final WorkflowContextCommand workflowContextCommand;
    private final WorkflowContextLockCommand workflowContextLockCommand;
    private final WorkflowContextWithInvalidatedActionsCache workflowContextWithInvalidatedActionsCache;

    @Override
    public WorkflowContext getWorkflowContext(final String workflowId) {

        return getWorkflowContext(workflowId, false);
    }

    @Override
    public WorkflowContext getWorkflowContextWithInvalidatedActions(final String workflowId) {

        return workflowContextWithInvalidatedActionsCache.get(workflowId);
    }

    @Override
    public WorkflowContext buildWorkflowContextWithInvalidatedActions(final String workflowId) {

        return getAndBuildWorkflowContext(workflowId, true);
    }

    private WorkflowContext getWorkflowContext(final String workflowId,
                                               final boolean lockAlreadyTaken) {

        final var workflowContext = workflowContextCommand.get(WorkflowContextKey.builder()
                .workflowId(workflowId)
                .build());

        if (Objects.nonNull(workflowContext)) {

            return workflowContext;
        }
        return buildAndCacheWorkflowContext(workflowId, lockAlreadyTaken);
    }

    private WorkflowContext buildAndCacheWorkflowContext(final String workflowId,
                                                         final boolean lockAlreadyTaken) {

        if (!lockAlreadyTaken) {
            acquireWorkflowContextLock(workflowId);
        }

        try {
            final var existingWorkflowContext = workflowContextCommand.get(WorkflowContextKey.builder()
                    .workflowId(workflowId)
                    .build());

            if (Objects.isNull(existingWorkflowContext)) {
                final var workflowContext = getAndBuildWorkflowContext(workflowId, false);

                workflowContextCommand.save(WorkflowContextKey.builder()
                        .workflowId(workflowId)
                        .build(), workflowContext);

                return workflowContext;
            }
            return existingWorkflowContext;
        } finally {

            if (!lockAlreadyTaken) {
                releaseLock(workflowId);
            }
        }
    }

    private WorkflowContext getAndBuildWorkflowContext(final String workflowId,
                                                       final boolean includeInvalidatedActions) {
        /*
        workflowContext ->  workflowStepMap (profileStepId, workflowStep)
            workflowStep -> WorkflowScreenContextMap (screenMappingId, WorkflowScreenContext)
                WorkflowScreenContext -> actionContextMap (ActionMappingId, actionContext)
                    actionContext -> actionMetadataMap (actionMetadataContextKey, StoredActionMetadata)
       */

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        final var workflowStepMap = getAndBuildWorkflowStepMap(storedWorkflow, includeInvalidatedActions);

        return WorkflowContextStoreUtils.buildWorkflowContext(storedWorkflow, workflowStepMap);
    }

    @NotNull
    private Map<String, WorkflowStepContext> getAndBuildWorkflowStepMap(final StoredWorkflow storedWorkflow,
                                                                        final boolean includeInvalidatedActions) {

        final var storedWorkflowSteps = workflowStepService.getWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var profileStepIdToProfileStepMappingIdMap = profile.getProfileSteps()
                .stream()
                .collect(Collectors.toMap(ProfileStep::getProfileStepId, ProfileStep::getProfileStepMappingId));

        // In final WF context, we require the latest workflow steps. Latest here is defined on the basis of creation time
        final var profileStepMappingIdToLatestWorkflowStepMap = storedWorkflowSteps.stream()
                .collect(Collectors.toMap(storedWorkflowStep -> profileStepIdToProfileStepMappingIdMap.get(
                                storedWorkflowStep.getProfileStepId()), Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparing(StoredWorkflowStep::getCreatedAt))));

        final var latestWorkflowStepIds = profileStepMappingIdToLatestWorkflowStepMap.values()
                .stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        // Now that we have the latest WF steps, fetch corresponding latest actions
        final var latestActionsForLatestWorkflowStepIds = actionService.getActions(latestWorkflowStepIds)
                .stream()
                .filter(storedAction -> includeInvalidatedActions
                        || CompletionState.INVALIDATED != storedAction.getCompletionState())
                .collect(Collectors.groupingBy(StoredAction::getActionMappingId,
                        Collectors.maxBy(Comparator.comparing(StoredAction::getCreatedAt))))
                .values()
                .stream()
                .flatMap(Optional::stream)
                .toList();

        final var workflowStepIdToStoredActionsMap = latestActionsForLatestWorkflowStepIds.stream()
                .collect(Collectors.groupingBy(StoredAction::getWorkflowStepId));

        final var storedActionIds = latestActionsForLatestWorkflowStepIds.stream()
                .map(StoredAction::getActionId)
                .collect(Collectors.toSet());

        final var storedActionMetadataList = actionMetadataService.getActionMetadataList(storedActionIds);

        final var actionIdToActionMetadataListMap = storedActionMetadataList.stream()
                .collect(Collectors.groupingBy(StoredActionMetadata::getActionId));

        return profileStepMappingIdToLatestWorkflowStepMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Entry::getKey,
                        entry -> buildWorkflowStepContext(entry.getValue(), workflowStepIdToStoredActionsMap,
                                actionIdToActionMetadataListMap)));
    }

    private WorkflowStepContext buildWorkflowStepContext(final StoredWorkflowStep storedWorkflowStep,
                                                         final Map<String, List<StoredAction>> workflowStepIdToStoredActionsMap,
                                                         final Map<String, List<StoredActionMetadata>> actionIdToActionMetadataListMap) {

        return WorkflowContextStoreUtils.buildWorkflowStepContextFromStoredWorkflowStep(storedWorkflowStep,
                getAndBuildWorkflowScreenContextMap(storedWorkflowStep,
                        workflowStepIdToStoredActionsMap.getOrDefault(storedWorkflowStep.getWorkflowStepId(),
                                List.of()), actionIdToActionMetadataListMap));
    }

    @NotNull
    private Map<String, WorkflowScreenContext> getAndBuildWorkflowScreenContextMap(final StoredWorkflowStep storedWorkflowStep,
                                                                                   final List<StoredAction> storedActionsForGivenWorkflowStep,
                                                                                   final Map<String, List<StoredActionMetadata>> actionIdToActionMetadataListMap) {

        final var screenMappingIdToActionsMap = storedActionsForGivenWorkflowStep.stream()
                .collect(Collectors.groupingBy(StoredAction::getScreenMappingId));

        return screenMappingIdToActionsMap.keySet()
                .stream()
                .collect(Collectors.toMap(Function.identity(), screenMappingId -> WorkflowScreenContext.builder()
                        .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                        .screenMappingId(screenMappingId)
                        .actionContextMap(buildActionContextMap(
                                screenMappingIdToActionsMap.getOrDefault(screenMappingId, List.of()),
                                actionIdToActionMetadataListMap))
                        .build(), (a, b) -> b));
    }

    @NotNull
    private Map<String, ActionContext> buildActionContextMap(final List<StoredAction> storedActionsForGivenScreen,
                                                             final Map<String, List<StoredActionMetadata>> actionIdToActionMetadataListMap) {

        return storedActionsForGivenScreen.stream()
                .collect(Collectors.toMap(StoredAction::getActionMappingId,
                        storedAction -> WorkflowContextStoreUtils.buildActionContextFromStoredAction(storedAction,
                                getActionMetadataMap(
                                        actionIdToActionMetadataListMap.getOrDefault(storedAction.getActionId(),
                                                List.of()))), (a, b) -> b));
    }

    @NotNull
    private Map<String, StoredActionMetadata> getActionMetadataMap(final List<StoredActionMetadata> actionMetadataListForGivenAction) {

        return actionMetadataListForGivenAction.stream()
                .collect(Collectors.toMap(StoredActionMetadata::getActionMetadataContextKey, Function.identity(),
                        (a, b) -> b));
    }

    @Override
    public void createAndPersistWorkflowContext(final StoredWorkflow storedWorkflow) {

        final var workflowContext = WorkflowContextStoreUtils.buildWorkflowContext(storedWorkflow, null);

        workflowContextCommand.save(WorkflowContextKey.builder()
                .workflowId(storedWorkflow.getWorkflowId())
                .build(), workflowContext);
    }

    @Override
    public void updateWorkflowContext(final String workflowId) {
        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        updateWorkflowContext(storedWorkflow);
    }

    @Override
    public void updateWorkflowContext(final StoredWorkflow storedWorkflow) {
        final var workflowId = storedWorkflow.getWorkflowId();

        final var workflowContextKey = WorkflowContextKey.builder()
                .workflowId(workflowId)
                .build();

        acquireWorkflowContextLock(workflowId);

        try {
            final var workflowStepContextMap = getWorkflowContext(workflowId, true).getWorkflowStepMap();
            final var workflowContext = WorkflowContextStoreUtils.buildWorkflowContext(storedWorkflow,
                    workflowStepContextMap);
            workflowContextCommand.save(workflowContextKey, workflowContext);
        } finally {

            releaseLock(workflowId);
        }
    }

    @Override
    public void addWorkflowStepContext(final StoredWorkflowStep storedWorkflowStep) {
        final var workflowId = storedWorkflowStep.getWorkflowId();

        final var workflowContextKey = WorkflowContextKey.builder()
                .workflowId(workflowId)
                .build();

        acquireWorkflowContextLock(workflowId);

        final var profileStepMappingId = profileService.getProfileStep(storedWorkflowStep.getProfileStepId())
                .getProfileStepMappingId();

        try {
            final var workflowContext = getWorkflowContext(workflowId, true);
            final var workflowStepContext = WorkflowContextStoreUtils.buildWorkflowStepContextFromStoredWorkflowStep(
                    storedWorkflowStep, null);
            workflowContext.getWorkflowStepMap()
                    .put(profileStepMappingId, workflowStepContext);
            workflowContextCommand.save(workflowContextKey, workflowContext);
        } finally {

            releaseLock(workflowId);
        }
    }

    @Override
    public void updateActionContextInWorkflowContext(final StoredAction storedAction) {
        updateActionContextInWorkflowContext(storedAction, this::defaultWorkflowContextUpdater);
    }

    private void defaultWorkflowContextUpdater(final WorkflowContext workflowContext,
                                               final String profileStepMappingId,
                                               final StoredAction storedAction,
                                               final List<StoredActionMetadata> storedActionMetadataList) {

        workflowContext.getWorkflowStepMap()
                .get(profileStepMappingId)
                .getWorkflowScreenContextMap()
                .get(storedAction.getScreenMappingId())
                .getActionContextMap()
                .put(storedAction.getActionMappingId(),
                        WorkflowContextStoreUtils.buildActionContextFromStoredAction(storedAction,
                                getActionMetadataMap(storedActionMetadataList)));
    }

    private void updateActionContextInWorkflowContext(final StoredAction storedAction,
                                                      final WorkflowContextUpdater workflowContextUpdater) {

        final var workflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());
        final var workflowId = workflowStep.getWorkflowId();

        final var profileStep = profileService.getProfileStep(workflowStep.getProfileStepId());
        final var profileStepMappingId = profileStep.getProfileStepMappingId();

        final var workflowContextKey = WorkflowContextKey.builder()
                .workflowId(workflowId)
                .build();

        acquireWorkflowContextLock(workflowId);

        try {
            final var workflowContext = getWorkflowContext(workflowId, true);

            final var storedActionMetadataList = actionMetadataService.getActionMetadataList(
                    storedAction.getActionId());

            workflowContextUpdater.updateWorkflowContext(workflowContext, profileStepMappingId, storedAction,
                    storedActionMetadataList);

            workflowContextCommand.save(workflowContextKey, workflowContext);

        } finally {

            releaseLock(workflowId);
        }
    }

    @Override
    public void updateActionContextInWorkflowContextIfPresent(final StoredAction storedAction) {
        updateActionContextInWorkflowContext(storedAction, this::streamSafeWorkflowContextUpdater);
    }

    private void streamSafeWorkflowContextUpdater(final WorkflowContext workflowContext,
                                                  final String profileStepMappingId,
                                                  final StoredAction storedAction,
                                                  final List<StoredActionMetadata> storedActionMetadataList) {

        Optional.ofNullable(workflowContext.getWorkflowStepMap()
                        .get(profileStepMappingId))
                .map(workflowStepContext -> workflowStepContext.getWorkflowScreenContextMap()
                        .get(storedAction.getScreenMappingId()))
                .map(WorkflowScreenContext::getActionContextMap)
                .ifPresent(actionContextMap -> actionContextMap.computeIfPresent(storedAction.getActionMappingId(),
                        (actionMappingId, actionContext) -> WorkflowContextStoreUtils.buildActionContextFromStoredAction(
                                storedAction, getActionMetadataMap(storedActionMetadataList))));
    }

    @Override
    public void deleteActionFromWorkflowContext(final StoredAction storedAction) {

        final var workflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var workflowId = workflowStep.getWorkflowId();

        final var profileStep = profileService.getProfileStep(workflowStep.getProfileStepId());

        final var profileStepMappingId = profileStep.getProfileStepMappingId();

        acquireWorkflowContextLock(workflowId);

        try {

            final var workflowContext = getWorkflowContext(workflowId, true);

            final var workflowStepContext = workflowContext.getWorkflowStepMap()
                    .get(profileStepMappingId);

            if (Objects.nonNull(workflowStepContext)) {

                final var workflowScreenContext = workflowStepContext.getWorkflowScreenContextMap()
                        .get(storedAction.getScreenMappingId());

                if (Objects.nonNull(workflowScreenContext)) {

                    workflowScreenContext.getActionContextMap()
                            .remove(storedAction.getActionMappingId());

                    if (workflowScreenContext.getActionContextMap()
                            .isEmpty()) {

                        workflowStepContext.getWorkflowScreenContextMap()
                                .remove(storedAction.getScreenMappingId());
                    }

                }
            }

            workflowContextCommand.save(WorkflowContextKey.builder()
                    .workflowId(workflowId)
                    .build(), workflowContext);
        } finally {

            releaseLock(workflowId);
        }

    }

    @Override
    public void addActionInWorkflowContext(final StoredAction storedAction) {
        final var workflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());
        final var workflowId = workflowStep.getWorkflowId();
        final var profileStepMappingId = profileService.getProfileStep(workflowStep.getProfileStepId())
                .getProfileStepMappingId();

        final var workflowContextKey = WorkflowContextKey.builder()
                .workflowId(workflowId)
                .build();

        acquireWorkflowContextLock(workflowId);

        try {
            final var workflowContext = getWorkflowContext(workflowId, true);
            final var updatedWorkflowContext = WorkflowContextStoreUtils.addActionContextInWorkflowContext(
                    workflowContext, storedAction, profileStepMappingId);
            workflowContextCommand.save(workflowContextKey, updatedWorkflowContext);
        } finally {

            releaseLock(workflowId);
        }
    }

    @Override
    public void updateWorkflowStepContext(final StoredWorkflowStep storedWorkflowStep) {
        final var workflowId = storedWorkflowStep.getWorkflowId();

        final var workflowContextKey = WorkflowContextKey.builder()
                .workflowId(workflowId)
                .build();

        acquireWorkflowContextLock(workflowId);

        final var profileStepMappingId = profileService.getProfileStep(storedWorkflowStep.getProfileStepId())
                .getProfileStepMappingId();
        try {
            final var workflowContext = getWorkflowContext(workflowId, true);
            final var workflowScreenContextMap = workflowContext.getWorkflowStepMap()
                    .get(profileStepMappingId)
                    .getWorkflowScreenContextMap();

            final var updatedWorkflowContextStep = WorkflowContextStoreUtils.buildWorkflowStepContextFromStoredWorkflowStep(
                    storedWorkflowStep, workflowScreenContextMap);

            workflowContext.getWorkflowStepMap()
                    .put(profileStepMappingId, updatedWorkflowContextStep);
            workflowContextCommand.save(workflowContextKey, workflowContext);
        } finally {

            releaseLock(workflowId);
        }
    }

    private void acquireWorkflowContextLock(final String workflowId) {
        //todo {PK} if lock acquisition fails after retries, whole action will be retried. Need to solve
        try {
            workflowContextLockCommand.strictSaveWithRetries(WorkflowContextKey.builder()
                    .workflowId(workflowId)
                    .build(), workflowId);
        } catch (final Exception e) {
            throw KaizenException.create(KaizenResponseCode.UNABLE_TO_ACQUIRE_WORKFLOW_CONTEXT_LOCK, e,
                    Map.of(StoredWorkflow.Fields.workflowId, workflowId));
        }
    }

    private void releaseLock(final String workflowId) {
        workflowContextLockCommand.delete(WorkflowContextKey.builder()
                .workflowId(workflowId)
                .build());
    }

    /**
     * This method is only for building the WorkflowContext using pre-collected entities
     * This is only used for Bulk Migration housekeeping
     */
    @Override
    public WorkflowContext buildWorkflowContextWithoutDbCall(final StoredWorkflow storedWorkflow,
                                                             final List<StoredWorkflowStep> storedWorkflowSteps,
                                                             final List<StoredAction> storedActions,
                                                             final List<StoredActionMetadata> storedActionMetadataList) {

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var profileStepIdToProfileStepMappingIdMap = profile.getProfileSteps()
                .stream()
                .collect(Collectors.toMap(ProfileStep::getProfileStepId, ProfileStep::getProfileStepMappingId));

        final var workflowStepIdToStoredActionsMap = storedActions.stream()
                .collect(Collectors.groupingBy(StoredAction::getWorkflowStepId));

        final var actionIdToActionMetadataListMap = storedActionMetadataList.stream()
                .collect(Collectors.groupingBy(StoredActionMetadata::getActionId));

        final var workflowStepMap = storedWorkflowSteps.stream()
                .collect(Collectors.toMap(storedWorkflowStep -> profileStepIdToProfileStepMappingIdMap.get(
                                storedWorkflowStep.getProfileStepId()),
                        storedWorkflowStep -> WorkflowContextStoreUtils.buildWorkflowStepContextFromStoredWorkflowStep(
                                storedWorkflowStep, getAndBuildWorkflowScreenContextMap(storedWorkflowStep,
                                        workflowStepIdToStoredActionsMap.getOrDefault(
                                                storedWorkflowStep.getWorkflowStepId(), List.of()),
                                        actionIdToActionMetadataListMap))));

        return WorkflowContextStoreUtils.buildWorkflowContext(storedWorkflow, workflowStepMap);
    }

    @FunctionalInterface
    private interface WorkflowContextUpdater {

        void updateWorkflowContext(final WorkflowContext workflowContext,
                                   final String profileStepMappingId,
                                   final StoredAction storedAction,
                                   final List<StoredActionMetadata> storedActionMetadataList);
    }
}
