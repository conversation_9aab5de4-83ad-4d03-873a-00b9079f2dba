package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ActionMetadataService {

    Optional<StoredActionMetadata> save(StoredActionMetadata storedActionMetadata);

    boolean saveAll(final String actionId,
                    final List<StoredActionMetadata> entities);

    List<StoredActionMetadata> getActionMetadataList(String actionId);

    List<StoredActionMetadata> getActionMetadataList(Set<String> actionIds);

    boolean isDocumentIdLinkedWithActionMetadata(String documentId);

    List<StoredActionMetadata> getActionMedataByActionIdAndActionMetaDataType(String actionId,
                                                                              ActionMetadataType actionMetadataType);

    void updateActionMetadataForPurge(String actionId);

}
