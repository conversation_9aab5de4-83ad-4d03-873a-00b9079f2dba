package com.phonepe.verified.kaizen.storage.mariadb.entities.profile;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.summary.config.SummaryViewConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;


@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue("1")
public class StoredAddOnProfile extends StoredProfile {

    @Serial
    private static final long serialVersionUID = 1964300645240956233L;

    @Column(name = "rule", columnDefinition = "text")
    private String rule;

    @Column(name = "upgrade_type", columnDefinition = "varchar(128) DEFAULT NULL")
    private String addOnType;

    @Column(name = "priority", columnDefinition = "TINYINT UNSIGNED DEFAULT NULL")
    private Integer priority;

    @Builder
    public StoredAddOnProfile(final long id,
                              @NotEmpty final String profileId,
                              @NotEmpty final String organization,
                              @NotEmpty final String namespace,
                              @NotEmpty final String type,
                              final String version,
                              final SummaryViewConfig summaryViewConfig,
                              final GetTemplateConfig getTemplateConfig,
                              final PostCompletionActionConfig postCompletionActionConfig,
                              @Nullable final List<PostWorkflowCreationActionConfig> postWorkflowCreationActionConfigs,
                              final String lastUpdatedBy,
                              final LocalDateTime createdAt,
                              final LocalDateTime lastUpdatedAt,
                              final String rule,
                              final String addOnType,
                              final Integer priority,
                              final String approvedBy) {
        super(id, profileId, organization, namespace, type, version, summaryViewConfig, getTemplateConfig,
                postCompletionActionConfig, postWorkflowCreationActionConfigs, ProfileType.ADD_ON, lastUpdatedBy,
                createdAt, lastUpdatedAt, approvedBy);
        this.rule = rule;
        this.addOnType = addOnType;
        this.priority = priority;
    }

    @Override
    public <T, J> T accept(final StoredProfileVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
