package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.OtpProviderType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class OtpGenerationEvent extends BaseEvent {

    private String errorCode;

    private String actionId;

    private ActionType actionType;

    private String currentState;

    private String currentEvent;

    private OtpProviderType otpProviderType;

    @Builder
    public OtpGenerationEvent(final EventType eventType,
                              final String actionId,
                              final String intent,
                              final String entityId,
                              final String namespace,
                              final String workflowId,
                              @NonNull final String groupingKey,
                              final String currentState,
                              final String currentEvent,
                              final OtpProviderType otpProviderType,
                              final String organization,
                              final String workflowType,
                              final String workflowStepId,
                              final ActionType actionType,
                              final EntityType entityType,
                              final String workflowVersion,
                              final String screenMappingId,
                              final String actionMappingId,
                              final long componentKitVersion,
                              final String profileStepMappingId,
                              final ProfileType profileType,
                              final String addOnType,
                              final String errorCode) {
        super(eventType, intent, entityId, namespace, workflowId, profileType, addOnType, groupingKey, organization,
                workflowType, workflowStepId, entityType, workflowVersion, screenMappingId, actionMappingId,
                componentKitVersion, profileStepMappingId);
        this.errorCode = errorCode;
        this.actionId = actionId;
        this.currentEvent = currentEvent;
        this.currentState = currentState;
        this.actionType = actionType;
        this.otpProviderType = otpProviderType;
    }
}

