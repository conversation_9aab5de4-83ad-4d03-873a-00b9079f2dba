package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.ProfileScreenCompletionStatus;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import java.util.Comparator;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class EvaluateProfileScreenCompletionAndTriggerNextActionVisitor implements
        ProfileScreenVisitor<ProfileScreenCompletionStatus> {

    private final StoredAction storedAction;

    private final ActionService actionService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowContextStore workflowContextStore;

    @Override
    public ProfileScreenCompletionStatus visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        final var completionStatusOfAction = standardProfileScreenConfig.getStepActionConfig()
                .accept(new ProfileScreenCompletionActionConfigVisitor(storedAction.getWorkflowStepId(), actionService,
                        workflowService, hopeLangService, workflowContextStore), null);

        if (storedAction.getScreenMappingId()
                .equals(standardProfileScreenConfig.getScreenMappingId())) {

            if (CompletionState.NON_STARTED_STATES.contains(completionStatusOfAction.getCompletionState())) {

                actionService.identifyNextActionAndTrigger(storedAction, standardProfileScreenConfig);
            }

            return ProfileScreenCompletionStatus.builder()
                    .mappingIdFound(true)
                    .completionState(completionStatusOfAction.getCompletionState())
                    .build();
        }

        return ProfileScreenCompletionStatus.builder()
                .mappingIdFound(false)
                .completionState(completionStatusOfAction.getCompletionState())
                .build();
    }

    @Override
    public ProfileScreenCompletionStatus visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        var isPseudoSuccessFound = false;

        for (final var orderedProfileScreen : sortedOrderedProfileScreenList) {

            final var profileScreenCompletionStatus = orderedProfileScreen.getProfileScreenConfig()
                    .accept(this);

            isPseudoSuccessFound = CompletionState.PSEUDO_SUCCESS == profileScreenCompletionStatus.getCompletionState();

            if (profileScreenCompletionStatus.isMappingIdFound()) {

                if (isPseudoSuccessFound && CompletionState.SUCCESS_STATES.contains(
                        profileScreenCompletionStatus.getCompletionState())) {

                    return profileScreenCompletionStatus.withCompletionState(CompletionState.PSEUDO_SUCCESS);
                }

                return profileScreenCompletionStatus;
            }
        }

        return ProfileScreenCompletionStatus.builder()
                .mappingIdFound(false)
                .completionState(isPseudoSuccessFound
                                 ? CompletionState.PSEUDO_SUCCESS
                                 : null)
                .build();
    }
}
