package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.drishti.models.responses.DrishtiAsyncResponse;
import com.phonepe.verified.drishti.models.responses.DrishtiResponse;
import com.phonepe.verified.drishti.models.responses.extraction.ExtractionResponse;
import com.phonepe.verified.drishti.models.responses.facematch.FaceMatchResponse;
import com.phonepe.verified.drishti.models.responses.liveness.LivenessCheckResponse;
import com.phonepe.verified.drishti.models.responses.masking.MaskingResponse;
import com.phonepe.verified.kaizen.models.Document;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import java.util.List;

public interface DrishtiService {

    DrishtiAsyncResponse livenessCheck(final String documentId,
                                       final StoredAction storedAction);

    DrishtiResponse<LivenessCheckResponse> getLivenessCheckDrishtiResponse(final String requestId);

    DrishtiAsyncResponse faceMatch(final String selfieDocumentId,
                                   final String referenceDocumentId,
                                   final StoredAction storedAction);

    DrishtiResponse<FaceMatchResponse> getFaceMatchDrishtiResponse(final String requestId);

    DrishtiAsyncResponse submitOcrRequest(final DocumentType documentType,
                                          List<Document> documentList,
                                          final StoredAction storedAction);

    DrishtiResponse<ExtractionResponse> getDocumentOcrResponse(final String requestId);

    DrishtiAsyncResponse maskDocument(final StoredAction storedAction,
                                      final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel);

    public DrishtiResponse<MaskingResponse> fetchMaskedDocumentStatus(final String requestId);

    public byte[] downloadDocument(final String documentId);

    DrishtiAsyncResponse faceMatchWithImages(final String selfieDocument,
                                             final String referenceDocument,
                                             final StoredAction storedAction);
}
