package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.shadow.models.request.SearchFieldRequest;
import com.phonepe.shadow.page.field.FullScreenSearchFieldV2Response;
import com.phonepe.verified.kaizen.models.data.search.SearchSourceTypeVisitor;
import com.phonepe.verified.kaizen.models.data.search.catalogue.CatalogueSearchSourceConfig;
import com.phonepe.verified.kaizen.services.CatalogueService;

@Singleton
public class SearchSourceTypeDataVisitor implements
        SearchSourceTypeVisitor<FullScreenSearchFieldV2Response, SearchFieldRequest> {

    private final CatalogueService catalogueService;

    @Inject
    public SearchSourceTypeDataVisitor(final CatalogueService catalogueService) {

        this.catalogueService = catalogueService;
    }

    @Override
    public FullScreenSearchFieldV2Response visit(final CatalogueSearchSourceConfig catalogueSearchSourceConfig,
                                                 final SearchFieldRequest searchFieldRequest) {

        return catalogueService.getCatalogueSearchDetailsForClient(catalogueSearchSourceConfig,
                searchFieldRequest.getSearchString(), searchFieldRequest.getPageNumber(),
                searchFieldRequest.getPageSize());
    }
}
