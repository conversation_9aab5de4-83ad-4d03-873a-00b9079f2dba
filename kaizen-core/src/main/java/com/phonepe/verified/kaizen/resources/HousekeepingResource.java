package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowContext;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/housekeeping")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "HouseKeeping", description = "HouseKeeping related APIs")
public class HousekeepingResource {

    private final ActionService actionService;

    private final HopeLangService hopeLangService;

    private final HandleBarsService handleBarsService;

    private final WorkflowStepService workflowStepService;

    private final WorkflowContextStore workflowContextStore;

    @GET
    @Path("/workflow/context/{workflowId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @RolesAllowed(OlympusPermissionNames.HOUSEKEEPING_GET_WORKFLOW_CONTEXT)
    @Operation(summary = "Get Workflow Context for given workflow ID", hidden = true)
    public WorkflowContext getDetails(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                      @NotEmpty @PathParam("workflowId") final String workflowId) {

        return workflowContextStore.getWorkflowContext(workflowId);
    }

    @GET
    @Path("/workflow/fetch/step/{workflowStepId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Get WorkflowID for given workflow step ID")
    @RolesAllowed(OlympusPermissionNames.HOUSEKEEPING_GET_WORKFLOW_CONTEXT)
    public String getWorkflowIdFromWorkflowStep(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                @NotEmpty @PathParam("workflowStepId") final String workflowStepId) {

        return workflowStepService.validateAndGetWorkflowStep(workflowStepId)
                .getWorkflowId();
    }

    @GET
    @Path("/workflow/fetch/action/{actionId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Get Workflow ID for given action ID")
    @RolesAllowed(OlympusPermissionNames.HOUSEKEEPING_GET_WORKFLOW_CONTEXT)
    public String getWorkflowIdFromActionId(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                            @NotEmpty @PathParam("actionId") final String actionId) {

        return workflowStepService.validateAndGetWorkflowStep(actionService.validateAndGetAction(actionId)
                        .getWorkflowStepId())
                .getWorkflowId();
    }

    @GET
    @Path("/workflow/step/fetch/{actionId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Get Workflow Step Id for given action step ID")
    @RolesAllowed(OlympusPermissionNames.HOUSEKEEPING_GET_WORKFLOW_CONTEXT)
    public String getWorkflowStepIdFromActionId(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                @NotEmpty @PathParam("actionId") final String actionId) {

        return actionService.validateAndGetAction(actionId)
                .getWorkflowStepId();
    }

    @POST
    @Path("/hope/lang/execute/{rule}")
    @Operation(summary = "Hope Lang rule execute")
    public boolean execute(final WorkflowContext workflowContext,
                           @NotEmpty @PathParam("rule") final String rule) {

        return hopeLangService.evaluate(rule, MapperUtils.convertToJsonNode(workflowContext));
    }

    @POST
    @Produces(MediaType.TEXT_PLAIN)
    @Path("/handlebars/execute/{rule}")
    @Operation(summary = "Handlebars execute")
    public String executeHandlebars(@NotNull final Object data,
                                    @NotEmpty @PathParam("rule") final String rule) {

        return handleBarsService.transform(rule, MapperUtils.convertToJsonNode(data));
    }
}
