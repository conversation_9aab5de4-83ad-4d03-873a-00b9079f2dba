package com.phonepe.verified.kaizen.services.impl;

import com.phonepe.platform.docstore.client.DocstoreClient;
import com.phonepe.platform.docstore.model.exceptions.DocstoreException;
import com.phonepe.platform.docstore.model.request.documents.upload.FileUploadRequest;
import com.phonepe.platform.docstore.model.request.documents.upload.context.Models.TTLConfig;
import com.phonepe.platform.docstore.model.request.documents.upload.context.Models.TTLPriority;
import com.phonepe.platform.docstore.model.request.documents.upload.context.external.CreateOnlyExternalRefContext;
import com.phonepe.platform.docstore.model.request.documents.upload.context.external.ReplaceAllExternalRefContext;
import com.phonepe.platform.docstore.model.request.documents.upload.context.fileupload.InternalFileUploadContext;
import com.phonepe.platform.http.v2.executor.exception.HttpException;
import com.phonepe.shadow.page.field.impl.MediaUploadWidgetField;
import com.phonepe.shadow.services.TemplateServiceV2;
import com.phonepe.shadow.storage.field.FieldType;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.DocumentIdentifier;
import com.phonepe.verified.kaizen.models.data.DocumentIdentifierAndLabel;
import com.phonepe.verified.kaizen.models.data.DocumentMetaDetails;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.FetchDocumentsResponse;
import com.phonepe.verified.kaizen.models.data.MimeType;
import com.phonepe.verified.kaizen.models.data.contexts.DocumentUploadActionTransitionContext;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.CipherUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.IdUtils;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;
import javax.validation.constraints.NotNull;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.Response;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.tika.Tika;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;

@Slf4j
@Singleton
@SuppressWarnings("java:S4790")
public class DocumentServiceImpl implements DocumentService {

    public static final int MAX_IMAGE_SIZE_IN_BYTES = 4 * 1024 * 1024; // 4 MB

    public static final int MAX_VIDEO_SIZE_IN_BYTES = 5 * 1024 * 1024; // 5 MB

    public static final int MAX_PDF_SIZE_IN_BYTES = 4 * 1024 * 1024; // 4 MB

    public static final int MAX_JSON_SIZE_IN_BYTES = 1024 * 1024; // 1MB

    public static final String IMAGE_MIMETYPE_PREFIX = "image/";

    public static final String VIDEO_MIMETYPE_PREFIX = "video/";

    public static final String PDF_MIMETYPE = "application/pdf";

    public static final String JSON_MIMETYPE = "application/json";

    public static final String MIME_TYPE = "MIME_TYPE";

    public static final InternalFileUploadContext INTERNAL_FILE_UPLOAD_CONTEXT = new InternalFileUploadContext();

    private final Tika tika;

    private final String docstoreNamespace;

    private final ProfileService profileService;

    private final DocstoreClient docstoreClient;

    private final WorkflowService workflowService;

    private final TemplateServiceV2 templateService;

    private final ToIntFunction<String> shardingFunction;

    private final Map<Integer, byte[]> aesCipherKeyConfig;

    private final ActionMetadataService actionMetadataService;

    private final Map.Entry<Integer, byte[]> latestAesCipherKey;

    @Inject
    public DocumentServiceImpl(final Tika tika,
                               final ProfileService profileService,
                               final DocstoreClient docstoreClient,
                               final WorkflowService workflowService,
                               final TemplateServiceV2 templateService,
                               final ToIntFunction<String> shardingFunction,
                               final ActionMetadataService actionMetadataService,
                               @NotNull final KaizenConfig kaizenConfig,
                               @Named(value = "aesCipherKeyConfig") final Map<Integer, byte[]> aesCipherKeyConfig,
                               @Named(value = "latestAesCipherKey") final Entry<Integer, byte[]> latestAesCipherKey) {

        this.tika = tika;
        this.profileService = profileService;
        this.docstoreClient = docstoreClient;
        this.workflowService = workflowService;
        this.templateService = templateService;
        this.shardingFunction = shardingFunction;
        this.aesCipherKeyConfig = aesCipherKeyConfig;
        this.latestAesCipherKey = latestAesCipherKey;
        this.actionMetadataService = actionMetadataService;
        this.docstoreNamespace = kaizenConfig.getDocstoreNamespace();
    }

    @Override
    @SneakyThrows
    public DocumentIdentifier uploadWithNewExternalReferenceId(final FormDataContentDisposition fileMetaData,
                                                               final InputStream fileInputStream,
                                                               final String userId,
                                                               final String workflowId,
                                                               final String actionMappingId,
                                                               final DocumentType documentType,
                                                               final String documentLabel) {

        return uploadWithNewExternalReferenceId(fileMetaData, fileInputStream, null, userId, workflowId,
                actionMappingId, documentType, documentLabel, -1);
    }

    @Override
    @SneakyThrows
    public DocumentIdentifier uploadWithNewExternalReferenceId(final FormDataContentDisposition fileMetaData,
                                                               final InputStream fileInputStream,
                                                               final String password,
                                                               final String userId,
                                                               final String workflowId,
                                                               final String actionMappingId,
                                                               final DocumentType documentType,
                                                               final String documentLabel) {

        return uploadWithNewExternalReferenceId(fileMetaData, fileInputStream, password, userId, workflowId,
                actionMappingId, documentType, documentLabel, -1);
    }

    @Override
    @SneakyThrows
    public DocumentIdentifier uploadWithNewExternalReferenceId(final FormDataContentDisposition fileMetaData,
                                                               final InputStream fileInputStream,
                                                               final String userId,
                                                               final String workflowId,
                                                               final String actionMappingId,
                                                               final DocumentType documentType,
                                                               final String documentLabel,
                                                               final long timeToLiveInSec) {

        return uploadWithNewExternalReferenceId(fileMetaData, fileInputStream, null, userId, workflowId,
                actionMappingId, documentType, documentLabel, timeToLiveInSec);
    }

    @Override
    @SneakyThrows
    public DocumentIdentifier uploadWithNewExternalReferenceId(final FormDataContentDisposition fileMetaData,
                                                               final InputStream fileInputStream,
                                                               final String password,
                                                               final String userId,
                                                               final String workflowId,
                                                               final String actionMappingId,
                                                               final DocumentType documentType,
                                                               final String documentLabel,
                                                               final long timeToLiveInSec) {

        //todo: FRA check for file upload limits

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);
        validateStandardStepActionConfigPresentForGivenActionMappingId(profile, actionMappingId);

        final var documentId = IdUtils.createIdInSameShard("D", storedWorkflow.getEntityId(), shardingFunction);

        return getDocumentIdAfterUploadToDocstore(fileMetaData, fileInputStream, password, userId, workflowId,
                actionMappingId, documentType, documentLabel, documentId, true, timeToLiveInSec);
    }

    @SneakyThrows
    private DocumentIdentifier getDocumentIdAfterUploadToDocstore(final FormDataContentDisposition fileMetaData,
                                                                  final InputStream fileInputStream,
                                                                  final String password,
                                                                  final String userId,
                                                                  final String workflowId,
                                                                  final String actionMappingId,
                                                                  final DocumentType documentType,
                                                                  final String documentLabel,
                                                                  final String documentId,
                                                                  final boolean isNewDocumentUpload,
                                                                  final long timeToLiveInSec) {

        final byte[] fileContent = getFileContent(fileInputStream);

        final var mimeType = tika.detect(new ByteArrayInputStream(fileContent), fileMetaData.getFileName());

        extractAndValidateMimeType(fileContent, mimeType);

        return uploadFileToDocStore(fileContent, password, userId, workflowId, actionMappingId, documentType,
                documentLabel, mimeType, fileMetaData.getFileName(), documentId, isNewDocumentUpload, timeToLiveInSec);
    }

    @Override
    @SneakyThrows
    public DocumentIdentifier uploadWithNewExternalReferenceId(final FormDataContentDisposition fileMetaData,
                                                               final InputStream fileInputStream,
                                                               final String password,
                                                               final String userId,
                                                               final String workflowId,
                                                               final String actionMappingId,
                                                               final DocumentType documentType,
                                                               final String documentLabel,
                                                               final String intent,
                                                               final long componentKitVersion) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);
        validateStandardStepActionConfigPresentForGivenActionMappingId(profile, actionMappingId);

        final var profileConfig = profileService.getStandardProfileScreenConfigForGivenActionMappingId(profile,
                actionMappingId);

        final var screenMappingID = profileConfig.getScreenMappingId();

        final var templateWorkflowType = BuildUtils.getTemplateWorkflowType(profile);

        final var template = templateService.getTemplate(templateWorkflowType, intent, componentKitVersion);

        final var documentFromTemplate = template.getSectionMappings()
                .stream()
                .filter(templateSectionMapping -> screenMappingID.equals(templateSectionMapping.getMappingId()))
                .flatMap(templateSectionMapping -> templateSectionMapping.getSection()
                        .getFieldGroups()
                        .stream())
                .flatMap(fieldGroup -> fieldGroup.getFields()
                        .stream())
                .filter(field -> FieldType.MEDIA_UPLOAD_WIDGET.equals(field.getType()))
                .map(MediaUploadWidgetField.class::cast)
                .flatMap(m -> m.getDocumentTypes()
                        .stream())
                .filter(document -> documentType.name()
                        .equals(((com.phonepe.shadow.page.field.Document) document).getDocumentType()))
                .findFirst();

        if (documentFromTemplate.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.DOCUMENT_TYPE_CONFIG_NOT_FOUND, Map.of());
        }

        final var templateDocument = (com.phonepe.shadow.page.field.Document) documentFromTemplate.get();

        final var documentMimeTypeSizeLimitMap = templateDocument.getDocumentMimeTypeSizeLimitMap();

        if (Objects.isNull(documentMimeTypeSizeLimitMap)) {
            throw KaizenException.create(KaizenResponseCode.DOCUMENT_TYPE_SIZE_LIMIT_CONFIG_NOT_FOUND, Map.of());
        }

        final var fileContent = fileInputStream.readAllBytes();
        fileInputStream.close();

        final var mimeType = tika.detect(new ByteArrayInputStream(fileContent), fileMetaData.getFileName());

        final var maxDataSizeOfDocument = documentMimeTypeSizeLimitMap.get(mimeType);

        if (Objects.isNull(maxDataSizeOfDocument)) {
            throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_FILE_TYPE, Map.of(MIME_TYPE, mimeType));
        }

        if (fileContent.length > maxDataSizeOfDocument.toBytes()) {
            throw KaizenException.create(KaizenResponseCode.FILE_SIZE_LIMIT_EXCEEDED,
                    Map.of("FILE_SIZE", fileContent.length));
        }

        final var documentId = IdUtils.createIdInSameShard("D", storedWorkflow.getEntityId(), shardingFunction);

        return uploadFileToDocStore(fileContent, password, userId, storedWorkflow.getWorkflowId(), actionMappingId,
                documentType, documentLabel, mimeType, fileMetaData.getFileName(), documentId, true, -1);
    }

    public DocumentIdentifier uploadFileToDocStore(final byte[] fileContent,
                                                   final String password,
                                                   final String userId,
                                                   final String workflowId,
                                                   final String actionMappingId,
                                                   final DocumentType documentType,
                                                   final String documentLabel,
                                                   final String mimeType,
                                                   final String fileName,
                                                   final String documentId,
                                                   final boolean isNewDocumentUpload,
                                                   final long timeToLiveInSec) {

        final var encryptedFileContent = CipherUtils.aesEncrypt(fileContent, password, mimeType, latestAesCipherKey);

        final var externalReferenceContext = isNewDocumentUpload
                                             ? CreateOnlyExternalRefContext.builder()
                                                     .externalRefId(documentId)
                                                     .build()
                                             : ReplaceAllExternalRefContext.builder()
                                                     .externalRefId(documentId)
                                                     .build();

        final var ttlConfig = timeToLiveInSec > 0
                              ? TTLConfig.builder()
                                      .priority(TTLPriority.HIGH)
                                      .timeToLiveInSec(timeToLiveInSec)
                                      .build()
                              : null;

        final var fileUploadRequest = FileUploadRequest.builder()
                .namespace(docstoreNamespace)
                .fileUploadContext(INTERNAL_FILE_UPLOAD_CONTEXT)
                .tags(Set.of(documentType.name(), documentLabel))
                .meta(Map.ofEntries(Map.entry(HttpHeaders.CONTENT_TYPE, mimeType),
                        Map.entry(StoredWorkflow.Fields.workflowId, workflowId), Map.entry(Constants.USER_ID, userId),
                        Map.entry(Constants.ORIGINAL_FILE_NAME, fileName),
                        Map.entry(Constants.FILE_SIZE, FileUtils.byteCountToDisplaySize(fileContent.length)),
                        Map.entry(Fields.actionMappingId, actionMappingId)))
                .docCreationTime(System.currentTimeMillis())
                .md5Checksum(DigestUtils.md5Hex(encryptedFileContent))
                .customRespHeaders(Map.of(HttpHeaders.CONTENT_TYPE, mimeType))
                .externalContext(externalReferenceContext)
                .ttlConfig(ttlConfig)
                .build();

        try {
            final var docStoreUploadResponse = docstoreClient.uploadFileV2(fileName, encryptedFileContent, mimeType,
                    fileUploadRequest, docstoreNamespace);

            if (!docStoreUploadResponse.isSuccess()) {
                throw KaizenException.create(KaizenResponseCode.DOCSTORE_FILE_UPLOAD_ERROR,
                        Map.ofEntries(Map.entry(StoredWorkflow.Fields.workflowId, workflowId),
                                Map.entry(StoredDocumentUploadActionMetadata.Fields.documentType, documentType),
                                Map.entry(StoredDocumentUploadActionMetadata.Fields.documentLabel, documentLabel)));
            }
        } catch (final DocstoreException docstoreException) {
            throw KaizenException.create(KaizenResponseCode.DOCSTORE_FILE_UPLOAD_ERROR, docstoreException,
                    docstoreException.getContext());
        } catch (final HttpException httpException) {
            throw KaizenException.propagate(KaizenResponseCode.DOCSTORE_FILE_UPLOAD_ERROR, httpException);
        }

        return DocumentIdentifier.builder()
                .documentId(documentId)
                .documentMetaDetails(DocumentMetaDetails.builder()
                        .fileName(fileName)
                        .size(FileUtils.byteCountToDisplaySize(fileContent.length))
                        .mimeType(MimeType.fromString(mimeType))
                        .build())
                .build();
    }

    private void validateStandardStepActionConfigPresentForGivenActionMappingId(final Profile profile,
                                                                                final String actionMappingId) {

        final var documentUploadStepActionConfig = getStandardStepActionConfigForGivenActionMappingId(profile,
                actionMappingId);

        if (!ActionType.DOCUMENT_UPLOAD_ACTION_TYPES.contains(documentUploadStepActionConfig.getActionType())) {
            throw KaizenException.create(KaizenResponseCode.INVALID_ACTION_MAPPING_ID,
                    Map.of(Fields.actionMappingId, actionMappingId));
        }
    }

    @Override
    public StandardStepActionConfig getStandardStepActionConfigForGivenActionMappingId(final Profile profile,
                                                                                       final String actionMappingId) {

        final var standardProfileScreenConfigs = profileService.getAllStandardProfileScreenConfigs(profile);

        final var allStandardStepActionConfig = ActionService.getAllStandardStepActionConfig(
                standardProfileScreenConfigs);

        return ActionService.filterActionMappingId(allStandardStepActionConfig, actionMappingId);
    }

    @Override
    @SneakyThrows
    public DocumentIdentifier uploadWithExistingExternalReferenceId(final FormDataContentDisposition fileMetaData,
                                                                    final InputStream fileInputStream,
                                                                    final String userId,
                                                                    final String workflowId,
                                                                    final String actionMappingId,
                                                                    final DocumentType documentType,
                                                                    final String documentLabel,
                                                                    final String existingDocumentId) {

        return getDocumentIdAfterUploadToDocstore(fileMetaData, fileInputStream, null, userId, workflowId,
                actionMappingId, documentType, documentLabel, existingDocumentId, false, -1);
    }

    @Override
    public void validateAllDocumentsPresentOnDocStore(final DocumentUploadActionTransitionContext documentActionContext) {

        final var missingDocumentIds = documentActionContext.getDocuments()
                .stream()
                .flatMap(documentTypeIdentifierAndLabel -> documentTypeIdentifierAndLabel.getDocuments()
                        .stream())
                .map(DocumentIdentifierAndLabel::getDocumentId)
                .filter(documentId -> !isDocumentPresentOnDocStore(documentId))
                .toList();

        if (!missingDocumentIds.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.DOCUMENT_NOT_FOUND,
                    Map.of("documentIds", missingDocumentIds));
        }
    }

    private void extractAndValidateMimeType(final byte[] fileContent,
                                            final String mimeType) {

        if (!mimeType.startsWith(IMAGE_MIMETYPE_PREFIX) && !mimeType.startsWith(VIDEO_MIMETYPE_PREFIX)
                && !PDF_MIMETYPE.equals(mimeType) && !JSON_MIMETYPE.equals(mimeType)) {
            throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_FILE_TYPE, Map.of(MIME_TYPE, mimeType));
        }

        if ((mimeType.startsWith(IMAGE_MIMETYPE_PREFIX) && fileContent.length > MAX_IMAGE_SIZE_IN_BYTES) || (
                mimeType.startsWith(VIDEO_MIMETYPE_PREFIX) && fileContent.length > MAX_VIDEO_SIZE_IN_BYTES) || (
                mimeType.equals(PDF_MIMETYPE) && fileContent.length > MAX_PDF_SIZE_IN_BYTES) || (
                mimeType.equals(JSON_MIMETYPE) && fileContent.length > MAX_JSON_SIZE_IN_BYTES)) {
            throw KaizenException.create(KaizenResponseCode.FILE_SIZE_LIMIT_EXCEEDED,
                    Map.of("FILE_SIZE", fileContent.length, MIME_TYPE, mimeType));
        }
    }

    @Override
    public Response download(final String actionId,
                             final String documentId) {

        //todo{Ravi}: Add validations for access control checks on file download

        final var actionMetadataList = actionMetadataService.getActionMetadataList(actionId);
        validateDocumentIdPresentInActionMetadata(actionMetadataList, actionId, documentId);

        try {
            final var file = docstoreClient.getFileByExternalRefId(docstoreNamespace, documentId);

            final var decryptedFileContent = CipherUtils.aesDecrypt(file.getFileContent(), aesCipherKeyConfig);

            return Response.ok(decryptedFileContent)
                    .header(HttpHeaders.CONTENT_DISPOSITION, file.getHeaders()
                            .get(HttpHeaders.CONTENT_DISPOSITION))
                    .header(HttpHeaders.CONTENT_TYPE, file.getHeaders()
                            .get(HttpHeaders.CONTENT_TYPE))
                    .build();

        } catch (final DocstoreException docstoreException) {
            throw KaizenException.create(KaizenResponseCode.DOCSTORE_FILE_DOWNLOAD_ERROR, docstoreException,
                    docstoreException.getContext());
        } catch (final HttpException httpException) {
            throw KaizenException.propagate(KaizenResponseCode.DOCSTORE_FILE_DOWNLOAD_ERROR, httpException);
        }
    }

    @Override
    public String getBase64EncodedImage(final String documentId) {

        try {
            final var file = docstoreClient.getFileByExternalRefId(docstoreNamespace, documentId);

            final var decryptedFileContent = CipherUtils.aesDecrypt(file.getFileContent(), aesCipherKeyConfig);

            return Base64.getEncoder()
                    .encodeToString(decryptedFileContent);

        } catch (final DocstoreException docstoreException) {
            throw KaizenException.create(KaizenResponseCode.DOCSTORE_FILE_DOWNLOAD_ERROR, docstoreException,
                    docstoreException.getContext());
        } catch (final HttpException httpException) {
            throw KaizenException.propagate(KaizenResponseCode.DOCSTORE_FILE_DOWNLOAD_ERROR, httpException);
        }
    }

    @Override
    public byte[] getImagebytes(final String documentId) {

        try {
            final var file = docstoreClient.getFileByExternalRefId(docstoreNamespace, documentId);

            return CipherUtils.aesDecrypt(file.getFileContent(), aesCipherKeyConfig);
        } catch (final DocstoreException docstoreException) {
            throw KaizenException.create(KaizenResponseCode.DOCSTORE_FILE_DOWNLOAD_ERROR, docstoreException,
                    docstoreException.getContext());
        } catch (final HttpException httpException) {
            throw KaizenException.propagate(KaizenResponseCode.DOCSTORE_FILE_DOWNLOAD_ERROR, httpException);
        }
    }

    @Override
    public FetchDocumentsResponse fetchDocuments(final String actionId,
                                                 final DocumentType documentType) {

        //todo{Ravi}: Add validations for access control checks on file download

        final var actionMetadataList = actionMetadataService.getActionMetadataList(Set.of(actionId));

        final var documentIds = actionMetadataList.stream()
                .filter(StoredDocumentUploadActionMetadata.class::isInstance)
                .map(StoredDocumentUploadActionMetadata.class::cast)
                .filter(sdm -> documentType == sdm.getDocumentType())
                .map(StoredDocumentUploadActionMetadata::getDocumentId)
                .collect(Collectors.toSet());

        return FetchDocumentsResponse.newInstance(documentIds);
    }

    private void validateDocumentIdPresentInActionMetadata(final List<StoredActionMetadata> actionMetadataList,
                                                           final String actionId,
                                                           final String documentId) {

        final var documentIdPresent = actionMetadataList.stream()
                .filter(StoredDocumentUploadActionMetadata.class::isInstance)
                .map(StoredDocumentUploadActionMetadata.class::cast)
                .anyMatch(storedDocumentUploadActionMetadata -> documentId.equals(
                        storedDocumentUploadActionMetadata.getDocumentId()));

        if (!documentIdPresent) {
            throw KaizenException.create(KaizenResponseCode.INVALID_DOCUMENT_DOWNLOAD_REQUEST,
                    Map.ofEntries(Map.entry(Fields.actionId, actionId),
                            Map.entry(StoredDocumentUploadActionMetadata.Fields.documentId, documentId)));
        }
    }

    @Override
    public void delete(final String documentId,
                       final String userId) {

        // This operation is only triggered from device to delete the image which might have been selected by mistake from user
        // If due to some error App makes delete the API call for a documentId which is already linked with Action
        // We cannot delete the document, but we can let the App allow user to select another image
        // Hence we are just returning without performing any activity in such scenario
        // ====
        // Example: User completed upload on the document upload section moved ahead and went back to upload another image
        // In such scenario as image was already uploaded by user for given section, we need to keep the audit
        // Hence we can't delete the image but by returning successful response we can allow user to upload another image
        if (actionMetadataService.isDocumentIdLinkedWithActionMetadata(documentId)) {
            return;
        }

        try {

            final var fileMeta = docstoreClient.getFileMetaByExternalRefId(docstoreNamespace, documentId);

            if (!fileMeta.getMetas()
                    .get(Constants.USER_ID)
                    .equals(userId)) {
                return;
            }

            docstoreClient.deleteFile(fileMeta.getId());
            docstoreClient.deleteExternalRefId(docstoreNamespace, documentId);

        } catch (final HttpException httpException) {
            throw KaizenException.propagate(KaizenResponseCode.DOCSTORE_FILE_DELETE_ERROR, httpException);
        }
    }

    @Override
    public boolean isDocumentPresentOnDocStore(final String documentId) {

        try {
            final var fileMeta = docstoreClient.getFileMetaByExternalRefId(docstoreNamespace, documentId);
            return Objects.nonNull(fileMeta.getId());
        } catch (final Exception e) {
            log.error("Error occurred while validating if document is present", e);
            return false;
        }
    }

    private byte[] getFileContent(final InputStream fileInputStream) throws IOException {

        final var fileContent = fileInputStream.readAllBytes();
        fileInputStream.close();
        return fileContent;
    }
}
