package com.phonepe.verified.kaizen.configs;

import com.phonepe.verified.kaizen.models.data.common.MailboxApiName;
import io.dropwizard.util.Duration;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MailboxConfig {

    @NotNull
    private CallbackConfig defaultCallbackConfig;

    @NotEmpty
    private Map<MailboxApiName, CallbackConfig> callbackConfigs;

    public CallbackConfig getCallbackConfig(final MailboxApiName apiName) {
        return callbackConfigs.getOrDefault(apiName, this.defaultCallbackConfig);
    }

    public Duration getPollingTime(final MailboxApiName apiName) {
        return callbackConfigs.getOrDefault(apiName, this.defaultCallbackConfig)
                .getPollingTime();
    }

    public Duration getPollingFrequency(final MailboxApiName apiName) {
        return callbackConfigs.getOrDefault(apiName, this.defaultCallbackConfig)
                .getPollingFrequency();
    }
}
