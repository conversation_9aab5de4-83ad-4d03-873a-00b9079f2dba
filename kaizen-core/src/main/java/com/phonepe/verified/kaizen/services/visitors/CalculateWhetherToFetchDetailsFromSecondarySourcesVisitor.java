package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.requests.details.FetchDetailsFromSecondarySources.FetchDetailsFromSecondarySourcesVisitor;

public class CalculateWhetherToFetchDetailsFromSecondarySourcesVisitor implements
        FetchDetailsFromSecondarySourcesVisitor<Boolean, Boolean> {

    public static final CalculateWhetherToFetchDetailsFromSecondarySourcesVisitor INSTANCE = new CalculateWhetherToFetchDetailsFromSecondarySourcesVisitor();

    @Override
    public Boolean visitNever(final Boolean pvDataAbsent) {
        return false;
    }

    @Override
    public Boolean visitWhenPvDetailsAbsent(final Boolean pvDataAbsent) {
        return pvDataAbsent;
    }

    @Override
    public Boolean visitAlways(final Boolean pvDataAbsent) {
        return true;
    }
}
