package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl.AnyStepInSuccessWorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl.HopeRuleEvaluationWorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.services.SchedulingService;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoSkipCallbackScheduler.WorkflowAutoSkipCallbackSchedulerData;
import com.phonepe.verified.kaizen.utils.Constants;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class WorkflowAutoSkipCallbackScheduler implements
        WorkflowAutoSkipConfigVisitor<Boolean, WorkflowAutoSkipCallbackSchedulerData> {

    private final SchedulingService schedulingService;

    @Override
    public Boolean visit(final AnyStepInSuccessWorkflowAutoSkipConfig config,
                         final WorkflowAutoSkipCallbackSchedulerData data) {

        return scheduleClockworkCallback(config, data);
    }

    @Override
    public Boolean visit(final HopeRuleEvaluationWorkflowAutoSkipConfig config,
                         final WorkflowAutoSkipCallbackSchedulerData data) {

        return scheduleClockworkCallback(config, data);
    }

    private boolean scheduleClockworkCallback(final WorkflowAutoSkipConfig configPayload,
                                              final WorkflowAutoSkipCallbackSchedulerData data) {

        return schedulingService.scheduleClockworkCallback(configPayload,
                Constants.AUTO_SKIP_CALLBACK_PATH_FORMAT.formatted(data.workflowId()), configPayload.getSkipAfter(),
                data.workflowId(), configPayload.getType()
                        .name());
    }

    @Builder
    @Jacksonized
    public record WorkflowAutoSkipCallbackSchedulerData(String workflowId) {

    }
}
