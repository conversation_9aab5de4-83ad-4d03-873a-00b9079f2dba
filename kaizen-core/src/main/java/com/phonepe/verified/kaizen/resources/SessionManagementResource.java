package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.models.requests.session.CreateClientSessionRequest;
import com.phonepe.verified.kaizen.models.requests.session.EndClientSessionRequest;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.UserDetailsUtils;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/session")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Session Management", description = "Session Management related APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class SessionManagementResource {

    private static final String RESOURCE_SESSION_MANAGEMENT = "SESSION_MANAGEMENT";

    private static final String OPERATION_SESSION_MANAGEMENT_CREATE = "CREATE";
    private static final String OPERATION_SESSION_MANAGEMENT_END = "END";


    private final SessionManagementService sessionManagementService;

    private final AuthZService authZService;

    @POST
    @SneakyThrows
    @Operation(summary = "Create or override a session")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void createSession(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                              @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                              @Valid @NotNull final CreateClientSessionRequest createClientSessionRequest) {

        authZService.authorizeOperationForTenant(
                authZService.getTenantFromWorkflow(createClientSessionRequest.getWorkflowId()),
                RESOURCE_SESSION_MANAGEMENT, OPERATION_SESSION_MANAGEMENT_CREATE, serviceUserPrincipal);

        sessionManagementService.createSession(createClientSessionRequest);
    }

    @DELETE
    @SneakyThrows
    @Operation(summary = "End a session")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void endSession(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                           @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                           @Valid @NotNull final EndClientSessionRequest endClientSessionRequest) {

        authZService.authorizeOperationForTenant(
                authZService.getTenantFromWorkflow(endClientSessionRequest.getWorkflowId()),
                RESOURCE_SESSION_MANAGEMENT, OPERATION_SESSION_MANAGEMENT_END, serviceUserPrincipal);

        sessionManagementService.endSession(requestInfo.getRequestId(),
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                        .getUserId(), endClientSessionRequest);
    }
}