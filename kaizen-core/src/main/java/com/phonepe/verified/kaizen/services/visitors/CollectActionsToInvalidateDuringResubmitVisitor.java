package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class CollectActionsToInvalidateDuringResubmitVisitor implements ProfileScreenVisitor<List<String>> {

    private final boolean currentScreenFound;

    private final StandardProfileScreenConfig currentProfileScreenConfig;

    public CollectActionsToInvalidateDuringResubmitVisitor(final StandardProfileScreenConfig currentProfileScreenConfig) {
        this.currentScreenFound = false;
        this.currentProfileScreenConfig = currentProfileScreenConfig;
    }

    @Override
    public @NotNull List<String> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        if (currentScreenFound || standardProfileScreenConfig.getScreenMappingId()
                .equals(currentProfileScreenConfig.getScreenMappingId())) {

            return standardProfileScreenConfig.getStepActionConfig()
                    .accept(GetAllStandardStepActionConfigsVisitor.INSTANCE, null)
                    .stream()
                    .map(StandardStepActionConfig::getActionMappingId)
                    .toList();
        }

        return List.of();
    }

    @Override
    public @NotNull List<String> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        final var actionMappingIdsToInvalidate = new ArrayList<String>();

        for (final var orderedProfileScreen : sortedOrderedProfileScreenList) {

            final var profileScreenConfig = orderedProfileScreen.getProfileScreenConfig();

            final var effectiveScreenFound = currentScreenFound || !actionMappingIdsToInvalidate.isEmpty();

            final var storedActionListOfCurrentScreen = profileScreenConfig.accept(
                    new CollectActionsToInvalidateDuringResubmitVisitor(effectiveScreenFound,
                            currentProfileScreenConfig));

            actionMappingIdsToInvalidate.addAll(storedActionListOfCurrentScreen);
        }

        return actionMappingIdsToInvalidate;
    }
}
