package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.configs.dependency.DependencyConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.dependency.HopeRuleBasedDependencyConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.OrderedActionMappingIdHopeRule;
import com.phonepe.verified.kaizen.models.configs.dependency.StandardDependencyConfig;
import com.phonepe.verified.kaizen.services.HopeLangService;
import java.util.Comparator;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class GetDependentActionMappingIdDependencyConfigVisitor implements DependencyConfigVisitor<String, JsonNode> {

    private final HopeLangService hopeLangService;

    @Override
    public String visit(final StandardDependencyConfig standardDependencyConfig,
                        final JsonNode workflowContextJsonNode) {

        return standardDependencyConfig.getActionMappingId();
    }

    @Override
    public String visit(final HopeRuleBasedDependencyConfig hopeRuleBasedDependencyConfig,
                        final JsonNode workflowContextJsonNode) {

        return hopeRuleBasedDependencyConfig.getOrderedActionMappingIdHopeRules()
                .stream()
                .sorted(Comparator.comparing(OrderedActionMappingIdHopeRule::getOrder))
                .filter(orderedActionMappingIdHopeRule -> hopeLangService.evaluate(
                        orderedActionMappingIdHopeRule.getEvaluationRule(), workflowContextJsonNode))
                .findFirst()
                .map(OrderedActionMappingIdHopeRule::getActionMappingId)
                .orElse(hopeRuleBasedDependencyConfig.getDefaultActionMappingId());
    }
}
