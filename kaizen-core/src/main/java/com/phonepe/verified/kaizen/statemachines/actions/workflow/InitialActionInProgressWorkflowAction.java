package com.phonepe.verified.kaizen.statemachines.actions.workflow;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class InitialActionInProgressWorkflowAction extends UpdateWorkflowBaseAction {

    private final WorkflowStepService workflowStepService;

    @Inject
    public InitialActionInProgressWorkflowAction(final ProfileService profileService,
                                                 final WorkflowService workflowService,
                                                 final WorkflowRepository workflowRepository,
                                                 final EventIngestionCommand eventIngestionCommand,
                                                 final Provider<WorkflowContextStore> workflowContextStore,
                                                 final WorkflowStepService workflowStepService) {
        super(profileService, workflowService, workflowRepository, eventIngestionCommand, workflowContextStore);
        this.workflowStepService = workflowStepService;
    }

    @Override
    protected void transition(final StoredWorkflow storedWorkflow,
                              final StateContext<TransitionState, TransitionEvent> stateContext) {

        workflowStepService.createAndTriggerInitialActionWorkflowStep(storedWorkflow);
    }
}
