package com.phonepe.verified.kaizen.utils;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import io.appform.functionmetrics.MonitoredFunction;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ConverterUtils {

    @MonitoredFunction
    public String compress(final byte[] imageBytes,
                           final double quality,
                           final long sizeLimitBytes) {

        // Get the size of the decoded byte array
        final var sizeInBytes = imageBytes.length;

        if (sizeInBytes < sizeLimitBytes) {
            return java.util.Base64.getEncoder()
                    .encodeToString(imageBytes);
        }
        var initialQuality = quality;  // Initial compression quality

        // Convert base64 image string to BufferedImage
        final var image = decodeBytesToImage(imageBytes);

        // Iterate while the compressed size is above the limit
        while (true) {
            final var compressedImage = compressImage(image, initialQuality);
            final var imageSize = compressedImage.length;

            if (imageSize <= sizeLimitBytes) {
                return java.util.Base64.getEncoder()
                        .encodeToString(compressedImage);
            }

            // Adjust quality and compress again
            initialQuality -= 0.05;
            if (initialQuality < 0.1) {
                return java.util.Base64.getEncoder()
                        .encodeToString(compressedImage);
            }
        }
    }

    private BufferedImage decodeBytesToImage(final byte[] imageBytes) {
        try {
            final var bis = new ByteArrayInputStream(imageBytes);
            return ImageIO.read(bis);
        } catch (IOException e) {
            throw new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, "Decoding base64 to image failed.",
                    Map.of());
        }
    }

    public byte[] compressImage(final BufferedImage image,
                                final double quality) {
        try {
            final var bos = new ByteArrayOutputStream();
            final var writer = ImageIO.getImageWritersByFormatName("jpeg")
                    .next();
            final var param = writer.getDefaultWriteParam();
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality((float) quality);

            ImageIO.write(image, "jpeg", bos);
            return bos.toByteArray();
        } catch (IOException e) {
            throw new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, "Compression of image failed.",
                    Map.of());
        }
    }
}
