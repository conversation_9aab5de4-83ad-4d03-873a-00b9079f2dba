package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.platform.atlas.model.common.Place;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.common.ForensicFields;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.GeolocationService;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.commands.UiRequestContextCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.KeyValuePairsActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.aerospike.keys.UiRequestContextKey;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class PersistForensicsActor extends BaseActor<StepActionMessage> {

    private final ActionService actionService;

    private final GeolocationService geolocationService;

    private final UiRequestContextCommand uiRequestContextCommand;

    private final ActionMetadataStoreCommand actionMetadataStoreCommand;

    private final Provider<ActionExecutorActor> actionExecutorActorProvider;

    @Inject
    protected PersistForensicsActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                    final ConnectionRegistry connectionRegistry,
                                    final ObjectMapper mapper,
                                    final RetryStrategyFactory retryStrategyFactory,
                                    final ExceptionHandlingFactory exceptionHandlingFactory,
                                    final ActionService actionService,
                                    final GeolocationService geolocationService,
                                    final UiRequestContextCommand uiRequestContextCommand,
                                    final ActionMetadataStoreCommand actionMetadataStoreCommand,
                                    final Provider<ActionExecutorActor> actionExecutorActorProvider) {
        super(ActorType.PERSIST_FORENSICS, actorConfigMap.get(ActorType.PERSIST_FORENSICS), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, StepActionMessage.class);
        this.actionService = actionService;
        this.geolocationService = geolocationService;
        this.uiRequestContextCommand = uiRequestContextCommand;
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
        this.actionExecutorActorProvider = actionExecutorActorProvider;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final StepActionMessage actionMessage) {
        final var actionId = actionMessage.getActionId();

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var uiRequestContext = uiRequestContextCommand.get(UiRequestContextKey.builder()
                .workflowStepId(storedAction.getWorkflowStepId())
                .build());
        final var requestInfo = uiRequestContext.getRequestInfo();

        final Optional<Place> placeDetails = geolocationService.fetchGeolocationDetails(requestInfo.getIp());

        if (placeDetails.isEmpty()) {
            actionExecutorActorProvider.get()
                    .publish(ActionExecutionMessage.builder()
                            .actionId(actionId)
                            .eventToTrigger(Events.FAIL_FORENSICS)
                            .userDetails(Constants.PVCORE_SYSTEM_USER)
                            .actionFailureErrorCode(ActionFailureErrorCode.PERSIST_FORENSICS_FAILED)
                            .build());
            return true;
        }

        final var forensicsKeyValuePairs = Map.ofEntries(Map.entry(ForensicFields.IP, requestInfo.getIp()),
                Map.entry(ForensicFields.LATITUDE, String.valueOf(placeDetails.map(Place::getLatitude)
                        .orElse(0.0))), Map.entry(ForensicFields.LONGITUDE, String.valueOf(
                        placeDetails.map(Place::getLongitude)
                                .orElse(0.0))), Map.entry(ForensicFields.CITY, placeDetails.map(Place::getCity)
                        .orElse(StringUtils.EMPTY)), Map.entry(ForensicFields.STATE, placeDetails.map(Place::getState)
                        .orElse(StringUtils.EMPTY)), Map.entry(ForensicFields.COUNTRY,
                        placeDetails.map(Place::getCountry)
                                .orElse(StringUtils.EMPTY)), Map.entry(ForensicFields.POSTAL_CODE,
                        placeDetails.map(Place::getPincode)
                                .orElse(StringUtils.EMPTY)));

        final var actionMetadata = KeyValuePairsActionMetadata.builder()
                .actionId(storedAction.getActionId())
                .keyValuePairs(forensicsKeyValuePairs)
                .build();

        actionMetadataStoreCommand.save(ActionMetadataStoreKey.builder()
                .actionId(storedAction.getActionId())
                .build(), actionMetadata);

        actionExecutorActorProvider.get()
                .publish(ActionExecutionMessage.builder()
                        .actionId(storedAction.getActionId())
                        .eventToTrigger(Events.PERSIST_FORENSICS_DATA)
                        .userDetails(Constants.PVCORE_SYSTEM_USER)
                        .build());
        return true;
    }

}
