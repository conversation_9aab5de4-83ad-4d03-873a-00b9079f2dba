package com.phonepe.verified.kaizen.registries;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.keys.StateMachineRegistryKey;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.statemachines.BaseStateMachine;
import com.phonepe.verified.kaizen.storage.aerospike.commands.TransitionLockCommand;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredStateMachineTransition;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredStateMachineTransition.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.StateMachineRepository;
import com.phonepe.verified.kaizen.utils.StateMachineUtils;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.builders.StateMachineModelConfigurer;

@Slf4j
@Singleton
public class StateMachineRegistry {

    private final SpringActionRegistry springActionRegistry;
    private final TransitionLockCommand transitionLockCommand;
    private final StateMachineRepository stateMachineRepository;
    private final Map<StateMachineRegistryKey, BaseStateMachine<String, String>> stateMachineRegistryStore;
    private final Map<StateMachineRegistryKey, StateMachine<String, String>> springStateMachineRegistryStore;

    @Inject
    public StateMachineRegistry(final SpringActionRegistry springActionRegistry,
                                final TransitionLockCommand transitionLockCommand,
                                final StateMachineRepository stateMachineRepository) {
        this.springActionRegistry = springActionRegistry;
        this.stateMachineRepository = stateMachineRepository;
        this.transitionLockCommand = transitionLockCommand;
        this.stateMachineRegistryStore = configureStateMachineRegistry();
        this.springStateMachineRegistryStore = configureSpringStateMachineRegistry(stateMachineRegistryStore);
        logStateMachineRegistry();
    }

    private Map<StateMachineRegistryKey, BaseStateMachine<String, String>> configureStateMachineRegistry() {

        final var storedStateMachineTransitions = stateMachineRepository.selectAll();

        final var stateMachinesTransitionsMap = storedStateMachineTransitions.stream()
                .collect(Collectors.groupingBy(storedStateMachineTransition -> StateMachineRegistryKey.newInstance(
                        storedStateMachineTransition.getActionType(), storedStateMachineTransition.getVersion())));

        return stateMachinesTransitionsMap.entrySet()
                .stream()
                .filter(e -> isValidStateMachine(e.getValue()))
                .map(e -> buildStateMachine(e.getKey(), e.getValue()))
                .collect(Collectors.toMap(BaseStateMachine::getStateMachineRegistryKey, Function.identity()));
    }

    private Map<StateMachineRegistryKey, StateMachine<String, String>> configureSpringStateMachineRegistry(final Map<StateMachineRegistryKey, BaseStateMachine<String, String>> baseStateMachineMap) {

        return baseStateMachineMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Entry::getKey, bsm -> bsm.getValue()
                        .internalApiGetStateMachine()));
    }

    private boolean isValidStateMachine(final List<StoredStateMachineTransition> stateMachineTransitions) {
        return StateMachineUtils.validateStateMachineTransitions(stateMachineTransitions)
                && stateMachineTransitions.stream()
                .map(StoredStateMachineTransition::getActionKey)
                .noneMatch(springActionRegistry::doesNotExist);
    }

    private BaseStateMachine<String, String> buildStateMachine(final StateMachineRegistryKey stateMachineRegistryKey,
                                                               final List<StoredStateMachineTransition> stateMachineTransitions) {

        final var allStates = StateMachineUtils.getAllStates(stateMachineTransitions);
        final var initialState = StateMachineUtils.getInitialState(stateMachineTransitions);
        final var terminalStates = StateMachineUtils.getTerminalStates(stateMachineTransitions);

        final var baseStateMachine = new BaseStateMachine<String, String>(transitionLockCommand) {

            @Override
            @SneakyThrows
            protected void configure(final StateMachineModelConfigurer<String, String> model) {
                model.withModel()
                        .factory(new StateMachineRegistryModelFactory(springActionRegistry, allStates, initialState,
                                terminalStates, stateMachineTransitions));
            }

            @Override
            public StateMachineRegistryKey getStateMachineRegistryKey() {
                return stateMachineRegistryKey;
            }

        };
        baseStateMachine.start();

        return baseStateMachine;
    }

    public BaseStateMachine<String, String> getBaseStateMachine(final ActionType actionType,
                                                                final String version) {

        final var baseStateMachine = stateMachineRegistryStore.get(
                StateMachineRegistryKey.newInstance(actionType, version));
        if (Objects.isNull(baseStateMachine)) {
            throw KaizenException.create(KaizenResponseCode.STATE_MACHINE_NOT_FOUND,
                    Map.of(Fields.actionType, actionType, Fields.version, version));
        }
        return baseStateMachine;
    }

    public StateMachine<String, String> getSpringStateMachine(final ActionType actionType,
                                                              final String version) {

        final var springStateMachine = springStateMachineRegistryStore.get(
                StateMachineRegistryKey.newInstance(actionType, version));
        if (Objects.isNull(springStateMachine)) {
            throw KaizenException.create(KaizenResponseCode.STATE_MACHINE_NOT_FOUND,
                    Map.of(Fields.actionType, actionType, Fields.version, version));
        }
        return springStateMachine;
    }

    public Set<StateMachineRegistryKey> getAllStateMachineRegistryKeysFromStore() {
        return stateMachineRegistryStore.keySet();
    }

    private void logStateMachineRegistry() {

        final var builder = new StringBuilder("Registered State Machines:").append(System.lineSeparator())
                .append(System.lineSeparator());

        stateMachineRegistryStore.forEach((k, v) -> builder.append("\t")
                .append("(")
                .append(k)
                .append(")")
                .append(System.lineSeparator()));

        log.info(builder.toString());
    }
}
