package com.phonepe.verified.kaizen.guice;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.AbstractModule;
import com.phonepe.data.provider.rosey.bundle.RoseyConfigProviderBundle;
import com.phonepe.olympus.im.client.OlympusIMBundle;
import com.phonepe.platform.aerospike.bundle.AerospikeBundle;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.shadow.ShadowModule;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import io.appform.dropwizard.actors.RabbitmqActorBundle;
import io.appform.dropwizard.sharding.DBShardingBundleBase;
import io.dropwizard.Configuration;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class KaizenModule<T extends Configuration & KaizenConfig> extends AbstractModule {

    private final ObjectMapper objectMapper;

    private final MetricRegistry metricRegistry;

    private final AerospikeBundle<T> aerospikeBundle;

    private final OlympusIMBundle<T> olympusIMBundle;

    private final DBShardingBundleBase<T> dbShardingBundle;

    private final RabbitmqActorBundle<T> rabbitmqActorBundle;

    private final HttpDiscoveryBundle<T> httpDiscoveryBundle;

    private final RoseyConfigProviderBundle<T> roseyConfigProviderBundle;

    @Override
    protected void configure() {
        install(new AerospikeModule(aerospikeBundle));
        install(new BindingModule());
        install(new ConfigModule(httpDiscoveryBundle));
        install(new CoreModule<>(roseyConfigProviderBundle, objectMapper, metricRegistry));
        install(new DbModule<>(dbShardingBundle));
        install(new OlympusModule<>(olympusIMBundle));
        install(new OncallOpsModule());
        install(new RabbitmqModule(rabbitmqActorBundle));
        install(new ServiceDiscoveryModule<>(httpDiscoveryBundle));
        install(new StateMachineModule());
        install(new ShadowModule<>(dbShardingBundle) {
        });
    }
}
