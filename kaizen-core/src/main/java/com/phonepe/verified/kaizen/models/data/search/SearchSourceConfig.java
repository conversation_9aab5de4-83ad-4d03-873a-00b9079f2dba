package com.phonepe.verified.kaizen.models.data.search;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.data.search.SearchSourceType.Names;
import com.phonepe.verified.kaizen.models.data.search.catalogue.CatalogueSearchSourceConfig;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.CATALOGUE, value = CatalogueSearchSourceConfig.class)})
public abstract class SearchSourceConfig {

    @NotNull
    private SearchSourceType type;

    public abstract <T, U> T accept(SearchSourceTypeVisitor<T, U> visitor,
                                    U data);
}
