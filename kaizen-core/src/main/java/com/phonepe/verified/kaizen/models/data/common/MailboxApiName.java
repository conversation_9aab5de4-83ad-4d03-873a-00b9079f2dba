package com.phonepe.verified.kaizen.models.data.common;

import lombok.experimental.UtilityClass;

public enum MailboxApiName {

    SECTION_SUBMIT,
    VERIFY_OTP,
    CONFIRM_ACTION,
    SELFIE_DOCUMENT_ID_SUBMIT,
    WORKFLOW_STEP_SKIP,
    <PERSON><PERSON><PERSON><PERSON>LEDGE_SMS_CONSENT,
    SEARCH_CLIENT_DATA,
    VALIDATE_NEFT_ENABLED_IFSC,
    DOCUMENT_PREFILL,
    REVERSE_PENNY_DROP_STATUS_CHECK;

    @UtilityClass
    public static class Names {

        public static final String SECTION_SUBMIT = "SECTION_SUBMIT";
        public static final String REDIRECTION_VALIDATION = "REDIRECTION_VALIDATION";
        public static final String GET_TEMPLATE = "GET_TEMPLATE";
        public static final String VERIFY_OTP = "VERIFY_OTP";
        public static final String VERIFY_AADHAAR_OTP = "VERIFY_AADHAAR_OTP";
        public static final String PROCESS_OCR = "PROCESS_OCR";
        public static final String CONFIRM_ACTION = "CONFIRM_ACTION";
        public static final String SELFIE_DOCUMENT_ID_SUBMIT = "SELFIE_DOCUMENT_ID_SUBMIT";
        public static final String WORKFLOW_STEP_SKIP = "WORKFLOW_STEP_SKIP";
        public static final String SEARCH_CLIENT_DATA = "SEARCH_CLIENT_DATA";
        public static final String VALIDATE_CCD_ORCH_PINCODE = "VALIDATE_CCD_ORCH_PINCODE";
        public static final String ACKNOWLEDGE_SMS_CONSENT = "ACKNOWLEDGE_SMS_CONSENT";
        public static final String VALIDATE_NEFT_ENABLED_IFSC = "VALIDATE_NEFT_ENABLED_IFSC";
        public static final String DOCUMENT_PREFILL = "DOCUMENT_PREFILL";
        public static final String REVERSE_PENNY_DROP_STATUS_CHECK = "REVERSE_PENNY_DROP_STATUS_CHECK";
    }
}
