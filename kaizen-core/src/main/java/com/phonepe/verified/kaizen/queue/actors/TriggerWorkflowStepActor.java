package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.TriggerWorkflowStepMessage;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class TriggerWorkflowStepActor extends BaseActor<TriggerWorkflowStepMessage> {

    private final WorkflowStepService workflowStepService;

    @Inject
    protected TriggerWorkflowStepActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                       final ConnectionRegistry connectionRegistry,
                                       final ObjectMapper mapper,
                                       final RetryStrategyFactory retryStrategyFactory,
                                       final ExceptionHandlingFactory exceptionHandlingFactory,
                                       final WorkflowStepService workflowStepService) {
        super(ActorType.TRIGGER_WORKFLOW_STEP, actorConfigMap.get(ActorType.TRIGGER_WORKFLOW_STEP), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, TriggerWorkflowStepMessage.class);
        this.workflowStepService = workflowStepService;
    }

    @Override
    protected boolean handleMessage(final TriggerWorkflowStepMessage message) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(message.getWorkflowStepId());

        final var screenMappingId = message.getShadowV2SectionSubmitRequest()
                .getSectionInputData()
                .getMappingId();

        workflowStepService.triggerWorkflowStep(storedWorkflowStep, screenMappingId, message.getUserDetails());
        return true;
    }
}
