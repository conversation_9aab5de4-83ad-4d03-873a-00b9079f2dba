package com.phonepe.verified.kaizen.storage.aerospike.commands;

import com.aerospike.client.IAerospikeClient;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import com.phonepe.verified.kaizen.models.responses.details.EntityDetailsResponse;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeCommand;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeSet;
import com.phonepe.verified.kaizen.storage.aerospike.keys.EntityDetailsKey;
import javax.inject.Inject;

@Singleton
public class EntityDetailsCommand extends AerospikeCommand<EntityDetailsKey, EntityDetailsResponse> {

    @Inject
    public EntityDetailsCommand(final IAerospikeClient aerospikeClient,
                                final AerospikeConfig aerospikeConfig) {
        super(aerospikeClient, aerospikeConfig, AerospikeSet.ENTITY_DETAILS, EntityDetailsResponse.class);
    }
}
