package com.phonepe.verified.kaizen.storage.mariadb.entities.converters;

import com.fasterxml.jackson.core.type.TypeReference;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.dropwizard.util.Strings;
import java.util.List;
import java.util.Objects;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class PostWorkflowCreationActionConfigConverter implements
        AttributeConverter<List<PostWorkflowCreationActionConfig>, String> {

    @Override
    public String convertToDatabaseColumn(final List<PostWorkflowCreationActionConfig> data) {
        return Objects.isNull(data)
               ? null
               : MapperUtils.serializeToString(data);
    }

    @Override
    public List<PostWorkflowCreationActionConfig> convertToEntityAttribute(final String dbData) {
        return Strings.isNullOrEmpty(dbData)
               ? null
               : MapperUtils.deserialize(dbData, new TypeReference<>() {
               });
    }
}
