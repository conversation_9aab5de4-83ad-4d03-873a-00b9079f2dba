package com.phonepe.verified.kaizen.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMBundle;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import io.dropwizard.Configuration;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class OlympusModule<T extends Configuration & KaizenConfig> extends AbstractModule {

    private final OlympusIMBundle<T> olympusIMBundle;

    @Provides
    @Singleton
    public OlympusIMClient provideOlympusIMClient() {
        return olympusIMBundle.getOlympusIMClient();
    }
}