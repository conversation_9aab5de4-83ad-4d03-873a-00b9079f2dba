package com.phonepe.verified.kaizen.caches.key;

import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
public class PrimaryProfileCacheKey extends ProfileCacheKey {

    @Builder
    public PrimaryProfileCacheKey(final String organization,
                                  final String namespace,
                                  final String type,
                                  final String version) {
        super(organization, namespace, type, version, ProfileType.PRIMARY);
    }
}
