package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.SearchClientDataMessage;
import com.phonepe.verified.kaizen.services.SearchService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ProcessSearchClientDataActor extends BaseActor<SearchClientDataMessage> {

    private final SearchService searchService;

    @Inject
    protected ProcessSearchClientDataActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                           final ConnectionRegistry connectionRegistry,
                                           final ObjectMapper mapper,
                                           final RetryStrategyFactory retryStrategyFactory,
                                           final ExceptionHandlingFactory exceptionHandlingFactory,
                                           final SearchService searchService) {
        super(ActorType.PROCESS_SEARCH_CLIENT_DATA, actorConfigMap.get(ActorType.PROCESS_SEARCH_CLIENT_DATA),
                connectionRegistry, mapper, retryStrategyFactory, exceptionHandlingFactory,
                SearchClientDataMessage.class);
        this.searchService = searchService;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final SearchClientDataMessage searchClientDataMessage) {

        searchService.fetchSearchDetails(searchClientDataMessage.getWorkflowId(), searchClientDataMessage.getFieldId(),
                searchClientDataMessage.getIntent(), searchClientDataMessage.getComponentKitVersion(),
                searchClientDataMessage.getRequestInfo(), searchClientDataMessage.getSearchFieldRequest());
        return true;
    }
}
