package com.phonepe.verified.kaizen.services;

import com.phonepe.shadow.page.field.FullScreenSearchFieldV2Response;
import com.phonepe.verified.kaizen.models.data.search.catalogue.CatalogueSearchSourceConfig;

public interface CatalogueService {

    FullScreenSearchFieldV2Response getCatalogueSearchDetailsForClient(CatalogueSearchSourceConfig catalogueSearchSourceConfig,
                                                                       String searchString,
                                                                       int pageNumber,
                                                                       int pageSize);

    String getCatalogueSearchDetails(String tenant,
                                     String category,
                                     String searchId,
                                     String columnName);
}
