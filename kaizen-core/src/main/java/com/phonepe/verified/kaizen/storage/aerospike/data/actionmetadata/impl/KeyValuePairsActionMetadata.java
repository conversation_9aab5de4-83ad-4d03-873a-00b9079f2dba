package com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.ActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.ActionMetadataVisitor;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class KeyValuePairsActionMetadata extends ActionMetadata {

    private Map<String, String> keyValuePairs;

    @Builder
    public KeyValuePairsActionMetadata(final String actionId,
                                       final Map<String, String> keyValuePairs) {
        super(actionId, ActionMetadataType.KEY_VALUE);
        this.keyValuePairs = keyValuePairs;
    }

    public KeyValuePairsActionMetadata() {
        super(ActionMetadataType.KEY_VALUE);
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }
}
