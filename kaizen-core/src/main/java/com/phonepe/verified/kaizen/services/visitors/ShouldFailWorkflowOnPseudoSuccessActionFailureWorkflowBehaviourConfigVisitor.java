package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.AlwaysFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.DoNotFailWorkflowBasedOnActionFailureErrorCodePseudoSuccessActionFailureWorkflowBehaviourConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.DoNotFailWorkflowPseudoSuccessActionFailureWorkflowBehaviourConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.PseudoSuccessActionFailureWorkflowBehaviourConfigType;
import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.PseudoSuccessActionFailureWorkflowBehaviourConfigVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ShouldFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfigVisitor implements
        PseudoSuccessActionFailureWorkflowBehaviourConfigVisitor<PseudoSuccessActionFailureWorkflowBehaviourConfigType, StoredAction> {

    public static final ShouldFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfigVisitor INSTANCE = new ShouldFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfigVisitor();

    @Override
    public PseudoSuccessActionFailureWorkflowBehaviourConfigType visit(final DoNotFailWorkflowBasedOnActionFailureErrorCodePseudoSuccessActionFailureWorkflowBehaviourConfig config,
                                                                       final StoredAction storedAction) {

        if (config.getActionFailureErrorCodes()
                .contains(storedAction.getFailureErrorCode())) {
            return PseudoSuccessActionFailureWorkflowBehaviourConfigType.DO_NOT_FAIL_WORKFLOW_BASED_ON_ACTION_FAILURE_ERROR_CODE;
        } else {
            return PseudoSuccessActionFailureWorkflowBehaviourConfigType.ALWAYS_FAIL_WORKFLOW;
        }
    }

    @Override
    public PseudoSuccessActionFailureWorkflowBehaviourConfigType visit(final AlwaysFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfig config,
                                                                       final StoredAction storedAction) {

        return PseudoSuccessActionFailureWorkflowBehaviourConfigType.ALWAYS_FAIL_WORKFLOW;
    }

    @Override
    public PseudoSuccessActionFailureWorkflowBehaviourConfigType visit(final DoNotFailWorkflowPseudoSuccessActionFailureWorkflowBehaviourConfig doNotFailWorkflowPseudoSuccessActionFailureWorkflowBehaviourConfig,
                                                                       final StoredAction data) {

        return PseudoSuccessActionFailureWorkflowBehaviourConfigType.DO_NOT_FAIL_WORKFLOW;
    }
}
