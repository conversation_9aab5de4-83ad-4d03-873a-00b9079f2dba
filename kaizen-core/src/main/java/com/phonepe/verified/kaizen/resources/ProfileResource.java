package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.keys.ProfileKey;
import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.profiles.ProfileChange;
import com.phonepe.verified.kaizen.models.responses.profiles.ProfileChangeMetadata;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.MakerCheckerService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.FeatureEnvironmentUtils;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.UserDetailsUtils;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/profile")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Profile", description = "Profile Related APIs")
public class ProfileResource {

    private static final String RESOURCE_PROFILE = "PROFILE";
    private static final String OPERATION_PROFILE_CREATE = "CREATE";
    private static final String OPERATION_PROFILE_UPDATE = "UPDATE";
    private static final String OPERATION_PROFILE_REVIEW = "REVIEW";
    private static final String OPERATION_PROFILE_GET = "GET";

    private final AuthZService authZService;

    private final ProfileService profileService;

    private final MakerCheckerService<ProfileChange, ProfileChangeMetadata, Profile> profileMakerCheckerService;

    @POST
    @Path("/direct")
    @Operation(summary = "Create new profile for defining new workflow (Only for Feature Environment")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public Profile createDirect(@Valid @NotNull final Profile profile,
                                @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenant(profile.getOrganization(), profile.getNamespace()), RESOURCE_PROFILE,
                OPERATION_PROFILE_CREATE, serviceUserPrincipal);

        if (!FeatureEnvironmentUtils.isFeatureEnvApplicable()) {
            throw KaizenException.create(KaizenResponseCode.INVALID_REQUEST, Map.of());
        }

        return profileService.create(profile, UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                .getUserId(), UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                .getUserId());
    }

    @PUT
    @Path("/direct")
    @Operation(summary = "Update profile and profile steps (Only for Feature Environment)")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public Profile updateDirect(@Valid @NotNull final Profile profile,
                                @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenant(profile.getOrganization(), profile.getNamespace()), RESOURCE_PROFILE,
                OPERATION_PROFILE_UPDATE, serviceUserPrincipal);

        if (!FeatureEnvironmentUtils.isFeatureEnvApplicable()) {
            throw KaizenException.create(KaizenResponseCode.INVALID_REQUEST, Map.of());
        }

        return profileService.update(profile, UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                .getUserId(), UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                .getUserId());
    }

    @POST
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/change/{requestId}/approve")
    @Operation(summary = "Approve profile change request")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ProfileChange approve(@PathParam("requestId") @NotEmpty final String requestId,
                                 @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                 @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        final var profileChange = profileMakerCheckerService.getStagedChange(requestId);

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenant(profileChange.getProposedProfile()
                .getOrganization(), profileChange.getProposedProfile()
                .getNamespace()), RESOURCE_PROFILE, OPERATION_PROFILE_REVIEW, serviceUserPrincipal);

        return profileMakerCheckerService.approve(requestId,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                        .getUserId());
    }

    @POST
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Create new profile for defining new workflow")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ProfileChange create(@Valid @NotNull final Profile profile,
                                @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenant(profile.getOrganization(), profile.getNamespace()), RESOURCE_PROFILE,
                OPERATION_PROFILE_CREATE, serviceUserPrincipal);

        return profileMakerCheckerService.stage(profile, UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                .getUserId(), null);
    }

    @GET
    @Path("/{profileId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Get profile for a given profile id")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public Profile get(@PathParam("profileId") @NotEmpty final String profileId,
                       @QueryParam("fetchProfileSteps") @DefaultValue("false") final boolean fetchProfileSteps,
                       @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                       @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenantFromProfile(profileId),
                RESOURCE_PROFILE, OPERATION_PROFILE_GET, serviceUserPrincipal);

        return profileService.get(profileId, fetchProfileSteps);
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @RolesAllowed(OlympusPermissionNames.GET_ALL_PROFILE)
    @Operation(summary = "Get list of all profiles")
    public List<Profile> getAll(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                @QueryParam("fetchProfileSteps") @DefaultValue("false") final boolean fetchProfileSteps) {

        return profileService.getAll(fetchProfileSteps);
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Path("/change/metadata")
    @Operation(summary = "get all profile changes requested")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public List<ProfileChangeMetadata> getAllProfileChangeMetadatas(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                                    @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        return profileMakerCheckerService.getAllChangeMetadata();
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Path("/change")
    @Operation(summary = "get all profile changes requested")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public List<ProfileChange> getAllProfileChanges(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                    @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        return profileMakerCheckerService.getAllChanges();
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Path("/{organization}/{namespace}/{type}/{version}")
    @Operation(summary = "Get profile for a given namespace, type and version")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public PrimaryProfile getPrimaryProfile(@PathParam("organization") @NotEmpty final String organization,
                                            @PathParam("namespace") @NotEmpty final String namespace,
                                            @PathParam("type") @NotEmpty final String type,
                                            @PathParam("version") @NotEmpty final String version,
                                            @QueryParam("fetchProfileSteps") @DefaultValue("false") final boolean fetchProfileSteps,
                                            @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                            @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenant(organization, namespace),
                RESOURCE_PROFILE, OPERATION_PROFILE_GET, serviceUserPrincipal);

        return profileService.getPrimaryProfile(
                BuildUtils.toPrimaryProfileCacheKey(organization, namespace, type, version), fetchProfileSteps);
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Path("/change/{requestId}")
    @Operation(summary = "Fetch profile change request")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ProfileChange getProfileChange(@PathParam("requestId") @NotEmpty final String requestId,
                                          @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                          @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        final var profileChange = profileMakerCheckerService.getStagedChange(requestId);

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenant(profileChange.getProposedProfile()
                .getOrganization(), profileChange.getProposedProfile()
                .getNamespace()), RESOURCE_PROFILE, OPERATION_PROFILE_GET, serviceUserPrincipal);

        return profileChange;
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Path("/change/metadata/{requestId}")
    @Operation(summary = "Fetch profile change request")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ProfileChangeMetadata getProfileChangeMetadata(@PathParam("requestId") @NotEmpty final String requestId,
                                                          @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                          @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        final var profileChangeMetadata = profileMakerCheckerService.getChangeMetadata(requestId);

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenant(
                profileChangeMetadata.getProfileIdentifier()
                        .getOrganization(), profileChangeMetadata.getProfileIdentifier()
                        .getNamespace()), RESOURCE_PROFILE, OPERATION_PROFILE_GET, serviceUserPrincipal);

        return profileChangeMetadata;
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Path("addon/{organization}/{namespace}/{type}/{version}/{addOnType}")
    @Operation(summary = "Get add-on profile for a given organization, namespace, type, version and addOnType")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public AddOnProfile getAddOnProfile(@PathParam("organization") @NotEmpty final String organization,
                                        @PathParam("namespace") @NotEmpty final String namespace,
                                        @PathParam("type") @NotEmpty final String type,
                                        @PathParam("version") @NotEmpty final String version,
                                        @PathParam("addOnType") @NotEmpty final String addOnType,
                                        @QueryParam("fetchProfileSteps") @DefaultValue("false") final boolean fetchProfileSteps,
                                        @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                        @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenant(authZService.getTenant(organization, namespace), RESOURCE_PROFILE,
                OPERATION_PROFILE_GET, serviceUserPrincipal);

        return profileService.getAddOnProfile(BuildUtils.toAddOnProfileCacheKey(organization, namespace, type, version),
                addOnType, fetchProfileSteps);
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Path("addon/{organization}/{namespace}/{type}/{version}")
    @Operation(summary = "Get all add-on profiles for a given organization, namespace, type and version")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public List<AddOnProfile> getAddOnProfiles(@PathParam("organization") @NotEmpty final String organization,
                                               @PathParam("namespace") @NotEmpty final String namespace,
                                               @PathParam("type") @NotEmpty final String type,
                                               @PathParam("version") @NotEmpty final String version,
                                               @QueryParam("fetchProfileSteps") @DefaultValue("false") final boolean fetchProfileSteps,
                                               @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                               @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenant(authZService.getTenant(organization, namespace), RESOURCE_PROFILE,
                OPERATION_PROFILE_GET, serviceUserPrincipal);

        return profileService.getAddOnProfiles(
                BuildUtils.toAddOnProfileCacheKey(organization, namespace, type, version), fetchProfileSteps);
    }

    @GET
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Path("addon/{organization}/{namespace}/{type}/{version}/{entityType}/{entityId}")
    @Operation(summary = "Get valid add-on profile for a given entity, namespace, type and version")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public AddOnProfile getValidAddOnProfile(@PathParam("organization") @NotEmpty final String organization,
                                             @PathParam("namespace") @NotEmpty final String namespace,
                                             @PathParam("type") @NotEmpty final String type,
                                             @PathParam("version") @NotEmpty final String version,
                                             @PathParam("entityType") @Valid @NotNull final EntityType entityType,
                                             @PathParam("entityId") @NotEmpty final String entityId,
                                             @QueryParam("fetchProfileSteps") @DefaultValue("false") final boolean fetchProfileSteps,
                                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                             @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenant(authZService.getTenant(organization, namespace), RESOURCE_PROFILE,
                OPERATION_PROFILE_GET, serviceUserPrincipal);

        return profileService.getValidAddOnProfileForEntity(entityId, entityType, ProfileKey.builder()
                        .namespace(namespace)
                        .organization(organization)
                        .type(type)
                        .version(version)
                        .build(), fetchProfileSteps)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ADDON_PROFILE_NOT_SUPPORTED, Map.of()));
    }

    @POST
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/change/{requestId}/reject")
    @Operation(summary = "Reject profile change request")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ProfileChange reject(@PathParam("requestId") @NotEmpty final String requestId,
                                @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        final var profileChange = profileMakerCheckerService.getStagedChange(requestId);
        final var userId = UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                .getUserId();

        if (!profileChange.getProfileChangeMetadata()
                .getRequestedBy()
                .equals(userId)) {
            authZService.authorizeOperationForTenantOrDefault(authZService.getTenant(profileChange.getProposedProfile()
                    .getOrganization(), profileChange.getProposedProfile()
                    .getNamespace()), RESOURCE_PROFILE, OPERATION_PROFILE_REVIEW, serviceUserPrincipal);
        }

        return profileMakerCheckerService.reject(requestId, userId);
    }

    @PUT
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Update profile and profile steps")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public ProfileChange update(@Valid @NotNull final Profile profile,
                                @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenant(profile.getOrganization(), profile.getNamespace()), RESOURCE_PROFILE,
                OPERATION_PROFILE_UPDATE, serviceUserPrincipal);

        return profileMakerCheckerService.stage(profile, UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                .getUserId(), profile.getProfileId());
    }

}
