package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowInitRequest;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WorkflowInitMessage extends BaseMessage {

    @NotNull
    private final WorkflowInitRequest workflowInitRequest;

    @NotNull
    private final UserDetails userDetails;

    @NotEmpty
    private final String requestId;

    @NotEmpty
    private final String workflowId;

    @Builder
    @Jacksonized
    public WorkflowInitMessage(final RequestInfo requestInfo,
                               @NotNull final WorkflowInitRequest workflowInitRequest,
                               @NotNull final UserDetails userDetails,
                               @NotEmpty final String requestId,
                               @NotEmpty final String workflowId) {

        super(ActorMessageType.WORKFLOW_INIT, requestInfo);
        this.workflowInitRequest = workflowInitRequest;
        this.userDetails = userDetails;
        this.requestId = requestId;
        this.workflowId = workflowId;
    }
}

