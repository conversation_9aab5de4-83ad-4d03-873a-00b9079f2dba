package com.phonepe.verified.kaizen.statemachines.actions.triggerevent;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.TriggerEventActionContext;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "createTriggerEventAction")
public class CreateTriggerEventAction extends CreateEntryBaseAction {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final ActionExecutorActor actionExecutorActor;

    @Inject
    public CreateTriggerEventAction(final ActionService actionService,
                                    final WorkflowService workflowService,
                                    final ClockworkClient clockworkClient,
                                    final ActionRepository actionRepository,
                                    final WorkflowStepService workflowStepService,
                                    final DataProvider<KaizenConfig> appConfigDataProvider,
                                    final Provider<WorkflowContextStore> workflowContextStore,
                                    final Provider<EventIngestionActor> eventIngestionActorProvider,
                                    final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                    final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor,
                                    final ProfileService profileService,
                                    final ActionExecutorActor actionExecutorActor) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.actionService = actionService;
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
        this.actionExecutorActor = actionExecutorActor;
    }

    @Override
    @SneakyThrows
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var stepActionContext = stateContext.getExtendedState()
                .get(StepActionContext.class, TriggerEventActionContext.class);

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        final var profileStepOfEventToTriggerAction = profileService.getProfileStep(storedWorkflow.getProfileId(),
                stepActionContext.getProfileStepMappingId());

        final var storedWorkflowStepOfEventToTriggerAction = workflowStepService.getValidWorkflowStep(
                        storedWorkflow.getWorkflowId(), profileStepOfEventToTriggerAction.getProfileStepId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.WORKFLOW_STEP_NOT_FOUND,
                        Map.of(StoredWorkflowStep.Fields.workflowId, storedWorkflow.getWorkflowId(),
                                Fields.profileStepId, profileStepOfEventToTriggerAction.getProfileStepId())));

        final var storedActionOptional = actionService.getLatestAction(
                storedWorkflowStepOfEventToTriggerAction.getWorkflowStepId(), stepActionContext.getActionMappingId());

        final var storedActionOfEventToTrigger = storedActionOptional.orElseThrow(
                () -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Fields.workflowStepId, storedWorkflowStepOfEventToTriggerAction.getWorkflowStepId(),
                                StoredAction.Fields.actionMappingId, stepActionContext.getActionMappingId())));

        actionExecutorActor.publish(ActionExecutionMessage.builder()
                .actionId(storedActionOfEventToTrigger.getActionId())
                .eventToTrigger(stepActionContext.getEventToTrigger())
                .userDetails(Constants.PVCORE_SYSTEM_USER)
                .build());
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        actionExecutorActor.publish(ActionExecutionMessage.builder()
                .actionId(storedAction.getActionId())
                .eventToTrigger(Events.SUCCESS)
                .userDetails(Constants.PVCORE_SYSTEM_USER)
                .build());
    }
}
