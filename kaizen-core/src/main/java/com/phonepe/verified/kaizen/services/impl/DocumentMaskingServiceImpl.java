package com.phonepe.verified.kaizen.services.impl;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.phonepe.verified.drishti.models.commons.documents.DocumentMaskingIdentifier;
import com.phonepe.verified.drishti.models.responses.DrishtiAsyncResponse;
import com.phonepe.verified.drishti.models.responses.DrishtiResponse;
import com.phonepe.verified.drishti.models.responses.Status;
import com.phonepe.verified.drishti.models.responses.masking.MaskingResponse;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.DocumentIdentifierAndLabel;
import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DocumentMaskingService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.DrishtiService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.DrishtiDocumentTypeToMaskingResponseVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.springframework.data.util.StreamUtils;

@Slf4j
@Singleton
@SuppressWarnings("java:S4790")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class DocumentMaskingServiceImpl implements DocumentMaskingService {

    private static final Set<String> ACTION_FAILURE_STATES = Set.of("FAILED");
    private static final Predicate<DrishtiResponse<MaskingResponse>> DOCUMENT_MASKING_IN_PROGRESS_STATE_PREDICATE = maskingResponseDrishtiResponse -> Status.IN_PROGRESS.equals(
            maskingResponseDrishtiResponse.getStatus());

    private final Retryer<DrishtiResponse<MaskingResponse>> documentMaskingStatusFetchRetryer = RetryerBuilder.<DrishtiResponse<MaskingResponse>>newBuilder()
            .retryIfException()
            .retryIfResult(DOCUMENT_MASKING_IN_PROGRESS_STATE_PREDICATE::test)
            .withStopStrategy(StopStrategies.stopAfterDelay(10L, TimeUnit.SECONDS))
            .withWaitStrategy(WaitStrategies.fixedWait(1L, TimeUnit.SECONDS))
            .build();

    private final ActionService actionService;
    private final DrishtiService drishtiService;
    private final DocumentService documentService;
    private final WorkflowService workflowService;
    private final WorkflowStepService workflowStepService;

    @Override
    @SneakyThrows
    public boolean mask(final String actionId,
                        final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel) {

        final var storedAction = actionService.validateAndGetAction(actionId);
        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());
        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var drishtiAsyncResponse = drishtiService.maskDocument(storedAction, documentTypeIdentifierAndLabel);
        final var maskingResponseDrishtiResponse = retryDocumentMaskingStatusFetch(drishtiAsyncResponse);

        if (Objects.isNull(maskingResponseDrishtiResponse)) {

            //Incase we are not able to get drishti response in given time, trigger failure transition and delete all the documents
            triggerFailureEventAndDeleteFromDocstore(storedAction, documentTypeIdentifierAndLabel,
                    ActionFailureErrorCode.DOCUMENT_MASKING_FAILED);

        } else if (maskingResponseDrishtiResponse.getStatus() != Status.SUCCEED || Objects.isNull(
                maskingResponseDrishtiResponse.getData())) {

            //Incase we are getting non success drishti response, trigger failure transition and delete all the documents
            triggerFailureEventAndDeleteFromDocstore(storedAction, documentTypeIdentifierAndLabel,
                    ActionFailureErrorCode.DOCUMENT_MASKING_FAILED_IMPROPER_IMAGE);
        } else {

            checkMaskingResponseAndUpdateMaskedDocumentOnDocStore(storedAction, storedWorkflow,
                    documentTypeIdentifierAndLabel, maskingResponseDrishtiResponse, drishtiAsyncResponse);
        }

        return true;
    }

    private DrishtiResponse<MaskingResponse> retryDocumentMaskingStatusFetch(final DrishtiAsyncResponse drishtiAsyncResponse) {

        try {
            Callable<DrishtiResponse<MaskingResponse>> callable = () -> drishtiService.fetchMaskedDocumentStatus(
                    drishtiAsyncResponse.getRequestId());

            return documentMaskingStatusFetchRetryer.call(callable);

        } catch (Exception exception) {
            return null;
        }
    }

    private void checkMaskingResponseAndUpdateMaskedDocumentOnDocStore(final StoredAction storedAction,
                                                                       final StoredWorkflow storedWorkflow,
                                                                       final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel,
                                                                       final DrishtiResponse<MaskingResponse> maskingResponseDrishtiResponse,
                                                                       final DrishtiAsyncResponse drishtiAsyncResponse) {

        final var maskingResponseData = maskingResponseDrishtiResponse.getData();

        final var documentMaskingIdentifierList = maskingResponseData.accept(
                DrishtiDocumentTypeToMaskingResponseVisitor.INSTANCE, null);

        final var idNumberNotFoundInMaskedDocument = documentMaskingIdentifierList.stream()
                .anyMatch(documentMaskingIdentifier -> Boolean.FALSE.equals(
                        documentMaskingIdentifier.getIdNumberFound()));

        if (idNumberNotFoundInMaskedDocument) {
            // Incase one/more id number is not masked properly, trigger failure transition and delete all the documents
            triggerFailureEventAndDeleteFromDocstore(storedAction, documentTypeIdentifierAndLabel,
                    ActionFailureErrorCode.DOCUMENT_MASKING_FAILED_ID_NOT_VISIBLE);

            return;
        }

        final var booleanList = StreamUtils.zip(documentTypeIdentifierAndLabel.getDocuments()
                                .stream(), documentMaskingIdentifierList.stream(),
                        (documentIdentifierAndLabel, documentMaskingIdentifier) -> updateMaskedDocumentOnDocstore(
                                documentIdentifierAndLabel, documentMaskingIdentifier, documentTypeIdentifierAndLabel,
                                storedAction, storedWorkflow))
                .toList();

        if (!booleanList.contains(Boolean.FALSE)) {

            triggerSuccessEvent(storedAction, drishtiAsyncResponse);
        } else {

            //Incase for one/more document there was an error in updating masked document on docstore, trigger failure transition and delete all the documents
            triggerFailureEventAndDeleteFromDocstore(storedAction, documentTypeIdentifierAndLabel,
                    ActionFailureErrorCode.DOCUMENT_MASKING_FAILED);
        }
    }

    private boolean updateMaskedDocumentOnDocstore(final DocumentIdentifierAndLabel documentIdentifierAndLabel,
                                                   final DocumentMaskingIdentifier documentMaskingIdentifier,
                                                   final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel,
                                                   final StoredAction storedAction,
                                                   final StoredWorkflow storedWorkflow) {

        try {
            final var fileContent = drishtiService.downloadDocument(documentMaskingIdentifier.getDocumentId());

            documentService.delete(documentIdentifierAndLabel.getDocumentId(), storedWorkflow.getEntityId());

            documentService.uploadWithExistingExternalReferenceId(FormDataContentDisposition.name(
                                    documentTypeIdentifierAndLabel.getDocumentType()
                                            .name())
                            .fileName("image.jpg")
                            .build(), new ByteArrayInputStream(fileContent), storedWorkflow.getEntityId(),
                    storedWorkflow.getWorkflowId(), storedAction.getActionMappingId(),
                    documentTypeIdentifierAndLabel.getDocumentType(), documentIdentifierAndLabel.getDocumentLabel(),
                    documentIdentifierAndLabel.getDocumentId());
        } catch (Exception e) {

            return false;
        }

        return true;
    }

    private void triggerSuccessEvent(final StoredAction storedAction,
                                     final DrishtiAsyncResponse drishtiAsyncResponse) {

        final var auxiliaryStateMachineTransitionMap = new HashMap<>();

        auxiliaryStateMachineTransitionMap.put(DrishtiAsyncResponse.class, drishtiAsyncResponse);

        actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                storedAction.getStateMachineVersion(), Events.DOCUMENT_UPLOAD_SUCCEEDED,
                Constants.EMPTY_TRANSITION_CONTEXT, Constants.PVCORE_SYSTEM_USER, auxiliaryStateMachineTransitionMap);
    }

    private void triggerFailureEventAndDeleteFromDocstore(final StoredAction storedAction,
                                                          final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel,
                                                          final ActionFailureErrorCode actionFailureErrorCode) {

        //If the State machine is already in Failed state, we will just try to delete the documents
        if (!ACTION_FAILURE_STATES.contains(storedAction.getCurrentState())) {

            final var auxiliaryStateMachineTransitionMap = new HashMap<>();

            auxiliaryStateMachineTransitionMap.put(ActionFailureErrorCode.class, actionFailureErrorCode);

            actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                    storedAction.getStateMachineVersion(), Events.DOCUMENT_UPLOAD_FAILED,
                    Constants.EMPTY_TRANSITION_CONTEXT, Constants.PVCORE_SYSTEM_USER,
                    auxiliaryStateMachineTransitionMap);
        }

        //Delete all the documents from docstore, as masking operation failed
        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        documentTypeIdentifierAndLabel.getDocuments()
                .forEach(each -> documentService.delete(each.getDocumentId(), storedWorkflow.getEntityId()));
    }
}
