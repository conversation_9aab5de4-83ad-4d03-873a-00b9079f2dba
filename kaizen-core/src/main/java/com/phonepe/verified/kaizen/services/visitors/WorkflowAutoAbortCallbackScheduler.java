package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.abort.impl.NoValidStepSubmissionPresentWorkflowAutoAbortConfig;
import com.phonepe.verified.kaizen.services.SchedulingService;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoAbortCallbackScheduler.WorkflowAutoAbortCallbackSchedulerData;
import com.phonepe.verified.kaizen.utils.Constants;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class WorkflowAutoAbortCallbackScheduler implements
        WorkflowAutoAbortConfigVisitor<Boolean, WorkflowAutoAbortCallbackSchedulerData> {

    private final SchedulingService schedulingService;

    @Override
    public Boolean visit(final NoValidStepSubmissionPresentWorkflowAutoAbortConfig config,
                         final WorkflowAutoAbortCallbackSchedulerData data) {

        return schedulingService.scheduleClockworkCallback(config,
                Constants.AUTO_ABORT_CALLBACK_PATH_FORMAT.formatted(data.workflowId()), config.getAbortAfter(),
                data.workflowId(), config.getAbortReason());
    }

    @Builder
    @Jacksonized
    public record WorkflowAutoAbortCallbackSchedulerData(String workflowId) {

    }
}
