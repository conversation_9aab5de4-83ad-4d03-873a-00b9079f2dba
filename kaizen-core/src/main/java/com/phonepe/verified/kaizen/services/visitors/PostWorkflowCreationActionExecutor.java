package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.impl.ScheduleWorkflowAutoAbortActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.impl.ScheduleWorkflowAutoSkipActionConfig;
import com.phonepe.verified.kaizen.services.visitors.PostWorkflowCreationActionExecutor.PostWorkflowCreationActionExecutorData;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoAbortCallbackScheduler.WorkflowAutoAbortCallbackSchedulerData;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoSkipCallbackScheduler.WorkflowAutoSkipCallbackSchedulerData;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class PostWorkflowCreationActionExecutor implements
        PostWorkflowCreationActionConfigVisitor<Boolean, PostWorkflowCreationActionExecutorData> {

    private final WorkflowAutoAbortCallbackScheduler workflowAutoAbortCallbackScheduler;

    private final WorkflowAutoSkipCallbackScheduler workflowAutoSkipCallbackScheduler;

    @Override
    public Boolean visit(final ScheduleWorkflowAutoAbortActionConfig config,
                         final PostWorkflowCreationActionExecutorData data) {

        return config.getWorkflowAutoAbortConfig()
                .accept(workflowAutoAbortCallbackScheduler, WorkflowAutoAbortCallbackSchedulerData.builder()
                        .workflowId(data.workflowId())
                        .build());
    }

    @Override
    public Boolean visit(final ScheduleWorkflowAutoSkipActionConfig config,
                         final PostWorkflowCreationActionExecutorData data) {

        return config.getWorkflowAutoSkipConfig()
                .accept(workflowAutoSkipCallbackScheduler, WorkflowAutoSkipCallbackSchedulerData.builder()
                        .workflowId(data.workflowId())
                        .build());
    }

    @Builder
    @Jacksonized
    public record PostWorkflowCreationActionExecutorData(String workflowId) {

    }
}
