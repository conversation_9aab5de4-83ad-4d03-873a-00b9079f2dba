package com.phonepe.verified.kaizen.guice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.docstore.client.DocstoreClient;
import com.phonepe.platform.docstore.client.impl.HttpDocstoreClient;
import com.phonepe.platform.http.v2.client.ClientFactory;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.platform.http.v2.discovery.utils.DiscoveryUtils;
import com.phonepe.platform.killswitch.client.KillswitchClient;
import com.phonepe.platform.sentinel.SentinelClient;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import io.dropwizard.setup.Environment;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import javax.inject.Named;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class ConfigModule extends AbstractModule {

    private final HttpDiscoveryBundle<? extends KaizenConfig> httpDiscoveryBundle;

    @Provides
    @Singleton
    public EventIngestorClient provideEventIngestionClient(final KaizenConfig config,
                                                           final Environment environment,
                                                           final OlympusIMClient olympusIMClient) {
        try {
            return new EventIngestorClient(config.getEventIngestorClientConfig(),
                    httpDiscoveryBundle.getEndpointProviderFactory(), environment.getObjectMapper(),
                    environment.metrics(), olympusIMClient::getSystemAuthHeader);
        } catch (final Exception e) {
            log.error("Error occurred while providing event ingestor client", e);
            throw KaizenException.create(KaizenResponseCode.INTERNAL_SERVER_ERROR, e, Map.of());
        }
    }

    @Provides
    @Singleton
    @SneakyThrows
    public SentinelClient provideSentinelClient(final KaizenConfig config,
                                                final Environment environment,
                                                final ObjectMapper mapper,
                                                final OlympusIMClient olympusIMClient) {

        final var sentinalHttpConfiguration = DiscoveryUtils.getConfiguration(config.getRangerHubConfiguration(),
                        ClientIds.SENTINEL)
                .orElse(null);

        return new SentinelClient(mapper, ClientFactory.newHttpClientBuilder()
                .withMetricRegistry(environment.metrics())
                .withConfiguration(sentinalHttpConfiguration)
                .build(), () -> httpDiscoveryBundle.getEndpointProviderFactory()
                .provider(ClientIds.SENTINEL), olympusIMClient::getSystemAuthHeader);
    }

    @Provides
    @Singleton
    @SneakyThrows
    public DocstoreClient provideDocstoreClient(final HttpClientRegistry httpClientRegistry,
                                                final ObjectMapper mapper,
                                                final OlympusIMClient olympusIMClient) {

        return new HttpDocstoreClient(mapper, olympusIMClient::getSystemAuthHeader, true,
                httpClientRegistry.getHttpExecutorBuilderFactoryForService(ClientIds.DOCSTORE));
    }

    @Provides
    @Singleton
    public CaffeineCacheConfig caffeineCacheConfig(final KaizenConfig kaizenConfig) {
        return kaizenConfig.getCaffeineCacheConfig();
    }

    @Provides
    @Singleton
    @Named(value = "aesCipherKeyConfig")
    public Map<Integer, byte[]> provideAesCipherKeyConfig(final KaizenConfig kaizenConfig) {

        return kaizenConfig.getAesCipherKeyConfig()
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                        .getBytes(StandardCharsets.UTF_8)));
    }

    @Provides
    @Singleton
    @Named(value = "latestAesCipherKey")
    public Map.Entry<Integer, byte[]> provideLatestAesCipherKey(@Named(value = "aesCipherKeyConfig") final Map<Integer, byte[]> aesCipherKeyConfig) {

        return Collections.max(aesCipherKeyConfig.entrySet(), Map.Entry.comparingByKey());
    }

    @Provides
    @Singleton
    public RangerHubConfiguration getRangerHubConfiguration(final KaizenConfig kaizenConfig) {
        return kaizenConfig.getRangerHubConfiguration();
    }

    @Singleton
    @Provides
    public KillswitchClient provideKillswitchClient(final KaizenConfig kaizenConfig,
                                                    final ObjectMapper mapper,
                                                    final Environment environment) {

        final var killswitchHttpConfig = DiscoveryUtils.getConfiguration(kaizenConfig.getRangerHubConfiguration(),
                        ClientIds.KILLSWITCH)
                .orElseThrow(() -> new IllegalArgumentException("Killswitch HttpConfig missing."));

        return KillswitchClient.builder()
                .projectId(kaizenConfig.getKillswitchConfig()
                        .getProjectId())
                .endpointProviderSupplier(() -> httpDiscoveryBundle.getEndpointProviderFactory()
                        .provider(killswitchHttpConfig))
                .mapper(mapper)
                .killswitchesConfig(killswitchHttpConfig)
                .skipCache(false)
                .metricRegistry(environment.metrics())
                .build();
    }
}
