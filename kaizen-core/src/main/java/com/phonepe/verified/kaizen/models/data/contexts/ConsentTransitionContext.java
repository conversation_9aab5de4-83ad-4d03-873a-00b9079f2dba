package com.phonepe.verified.kaizen.models.data.contexts;

import com.phonepe.verified.kaizen.models.data.contexts.visitor.TransitionContextTypeVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ConsentTransitionContext extends TransitionContext {

    private String language;

    public ConsentTransitionContext() {
        super(ContextType.CONSENT_TRANSITION_CONTEXT);
    }

    @Builder
    public ConsentTransitionContext(final String language) {
        this();
        this.language = language;
    }

    @Override
    public <T, J> T accept(final TransitionContextTypeVisitor<T, J> transitionContextTypeVisitor,
                           final J data) {
        return transitionContextTypeVisitor.visit(this, data);
    }
}
