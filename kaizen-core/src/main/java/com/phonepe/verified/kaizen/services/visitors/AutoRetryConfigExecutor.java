package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.models.configs.retry.autoretry.AutoRetryConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.retry.autoretry.CountLimitedAutoRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.autoretry.CountLimitedWithFixedWaitAutoRetryConfig;
import com.phonepe.verified.kaizen.services.visitors.AutoRetryConfigExecutor.AutoRetryConfigExecutorData;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class AutoRetryConfigExecutor implements AutoRetryConfigVisitor<Boolean, AutoRetryConfigExecutorData> {

    @Override
    public Boolean visit(final CountLimitedAutoRetryConfig countLimitedAutoRetryConfig,
                         final AutoRetryConfigExecutorData autoRetryConfigExecutorData) {

        return countLimitedAutoRetryConfig.getRetryCount() >= autoRetryConfigExecutorData.getFailedActionCount();
    }

    @Override
    public Boolean visit(final CountLimitedWithFixedWaitAutoRetryConfig countLimitedWithFixedWaitAutoRetryConfig,
                         final AutoRetryConfigExecutorData autoRetryConfigExecutorData) {

        return countLimitedWithFixedWaitAutoRetryConfig.getRetryCount()
                >= autoRetryConfigExecutorData.getFailedActionCount();
    }

    @Data
    @Builder
    public static class AutoRetryConfigExecutorData {

        private final int failedActionCount;
    }

}
