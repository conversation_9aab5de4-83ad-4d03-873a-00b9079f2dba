package com.phonepe.verified.kaizen.statemachines.actions;

import com.google.inject.Provider;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import org.springframework.statemachine.StateContext;

public abstract class AbortStateBaseAction extends CompletedStateBaseAction {

    private final Provider<WorkflowContextStore> workflowContextStore;

    protected AbortStateBaseAction(final ActionService actionService,
                                   final ActionRepository actionRepository,
                                   final AutoRetryActionService autoRetryActionService,
                                   final Provider<AutoRetryActionActor> autoRetryActionActorProvider,
                                   final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider,
                                   final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider,
                                   final Provider<WorkflowContextStore> workflowContextStore,
                                   final Provider<EventIngestionActor> eventIngestionActorProvider) {

        super(actionService, false, actionRepository, autoRetryActionService, eventIngestionActorProvider,
                autoRetryActionActorProvider, handleActionCompletionActorProvider,
                handlePseudoSuccessActionCompletionActorProvider);
        this.workflowContextStore = workflowContextStore;
    }

    @Override
    protected CompletionState getCompletionState() {
        return CompletionState.ABORTED;
    }

    @Override
    protected ActionFailureErrorCode getFailureErrorCode(final StateContext<String, String> stateContext,
                                                         final StoredAction storedAction) {
        return storedAction.getFailureErrorCode();
    }

    @Override
    protected void updateActionInWorkflowContextStore(final StoredAction storedAction) {
        workflowContextStore.get()
                .updateActionContextInWorkflowContextIfPresent(storedAction);
    }
}
