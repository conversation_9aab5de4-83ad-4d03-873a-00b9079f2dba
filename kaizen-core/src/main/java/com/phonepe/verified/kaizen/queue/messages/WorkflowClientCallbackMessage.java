package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.annotation.Nullable;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WorkflowClientCallbackMessage extends BaseMessage {

    @NonNull
    private final String workflowId;

    @Nullable
    private final ActionFailureErrorCode failureErrorCode;

    @Nullable
    private final String requestId;

    @Nullable
    private final String accessToken;

    @Builder
    @Jacksonized
    public WorkflowClientCallbackMessage(final RequestInfo requestInfo,
                                         @NonNull final String workflowId,
                                         @Nullable final ActionFailureErrorCode failureErrorCode,
                                         @Nullable final String requestId,
                                         @Nullable final String accessToken) {

        super(ActorMessageType.WORKFLOW_CLIENT_CALLBACK, requestInfo);
        this.workflowId = workflowId;
        this.failureErrorCode = failureErrorCode;
        this.requestId = requestId;
        this.accessToken = accessToken;
    }
}
