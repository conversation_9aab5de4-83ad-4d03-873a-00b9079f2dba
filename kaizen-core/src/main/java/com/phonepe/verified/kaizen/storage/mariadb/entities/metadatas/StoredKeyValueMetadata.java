package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.services.ActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.KEY_VALUE)
public class StoredKeyValueMetadata extends StoredActionMetadata {

    private static final long serialVersionUID = 8815578266011682188L;

    @Column(name = "name", columnDefinition = "varchar(128)")
    private String key;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String value;

    @Builder
    public StoredKeyValueMetadata(@NonNull final String actionId,
                                  final LocalDateTime createdAt,
                                  final LocalDateTime lastUpdatedAt,
                                  final String key,
                                  final String value) {
        super(BuildUtils.primaryKey(), actionId, ActionMetadataType.KEY_VALUE, createdAt, lastUpdatedAt);
        this.key = key;
        this.value = value;
    }

    @JsonIgnore
    @Transient
    public static int maxLengthSupportedValue() {
        return 128;
    }

    @Override
    public String getActionMetadataContextKey() {
        return String.join(":", Names.KEY_VALUE, this.key);
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }

}
