package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType.EventTypeVisitor;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.services.visitors.data.ActionCompletionEventIngestionVisitorData;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class EventIngestionVisitor implements EventTypeVisitor<Void, EventIngestionMessage> {

    private final Profile profile;

    private final ProfileStep profileStep;

    private final StoredAction storedAction;

    private final StoredWorkflow storedWorkflow;

    private final StoredWorkflowStep storedWorkflowStep;

    private final EventIngestionCommand eventIngestionCommand;

    private final ShadowV2UiRequestContext shadowV2UiRequestContext;

    private final ActionCompletionEventIngestionVisitor actionCompletionEventIngestionVisitor;

    @Override
    public Void visitActionInit(final EventIngestionMessage message) {

        eventIngestionCommand.actionInitEvent(storedAction, storedWorkflowStep, storedWorkflow, profile, profileStep,
                shadowV2UiRequestContext, message);
        return null;
    }

    @Override
    public Void visitActionCompletion(final EventIngestionMessage message) {
        actionCompletionEventIngestionVisitor.publishEvent(ActionCompletionEventIngestionVisitorData.builder()
                .profile(profile)
                .profileStep(profileStep)
                .storedAction(storedAction)
                .storedWorkflow(storedWorkflow)
                .eventIngestionMessage(message)
                .storedWorkflowStep(storedWorkflowStep)
                .shadowV2UiRequestContext(shadowV2UiRequestContext)
                .build());

        return null;
    }

    @Override
    public Void visitActionUpdate(final EventIngestionMessage message) {
        eventIngestionCommand.actionUpdateEvent(storedAction, storedWorkflowStep, storedWorkflow, profile, profileStep,
                shadowV2UiRequestContext, message);
        return null;
    }

    @Override
    public Void visitWorkflowTransition(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitKillSwitch(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitActionInvalidated(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitSectionSubmitCompletion(final EventIngestionMessage message) {

        eventIngestionCommand.sectionSubmitEvent(storedWorkflow, profile, profileStep, storedAction,
                shadowV2UiRequestContext, message.getEventType());

        return null;
    }

    @Override
    public Void visitDocumentUploadActionCompletion(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitCallbackSentToClient(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitCallbackReceivedFromDrishti(final EventIngestionMessage message) {
        eventIngestionCommand.callbackReceivedFromDrishti(storedAction, storedWorkflowStep, storedWorkflow, profile,
                profileStep, shadowV2UiRequestContext, message);
        return null;
    }

    @Override
    public Void visitOtpVerificationFailed(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitOtpVerificationSucceed(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitOtpGenerationSucceed(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitOtpGenerationFailed(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitGetTemplate(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitWorkflowStepTransition(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitSectionSubmitReceived(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitRevolverCallback(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitClockworkCallbackScheduled(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitAbortWorkflowScheduleCreatedOnClockwork(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitWorkflowAbortTriggeredOnScheduledCallback(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitWorkflowAbortNotTriggeredOnScheduledCallback(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitEntityDetailsFetch(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitAsyncWorkflowInit(final EventIngestionMessage message) {
        return null;
    }

    @Override
    public Void visitTagCalculation(final EventIngestionMessage message) {
        return null;
    }
}
