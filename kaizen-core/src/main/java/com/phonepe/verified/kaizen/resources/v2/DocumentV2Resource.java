package com.phonepe.verified.kaizen.resources.v2;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.authz.annotations.WorkflowId;
import com.phonepe.verified.kaizen.models.MultiPartFileRequest;
import com.phonepe.verified.kaizen.models.data.DocumentIdentifier;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.queue.actors.DeleteDocStoreFileActor;
import com.phonepe.verified.kaizen.queue.messages.DocumentMessage;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Encoding;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.InputStream;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Slf4j
@Singleton
@Path("/v2/document")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Document V2", description = "Document related V2 APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class DocumentV2Resource {

    private final AuthZService authZService;

    private final DocumentService documentService;

    private final WorkflowService workflowService;

    private final DeleteDocStoreFileActor deleteDocStoreFileActor;

    private final SessionManagementService sessionManagementService;

    @POST
    @AuthZ
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/upload/{actionMappingId}/{documentType}/{documentLabel}")
    @Operation(summary = "Upload file from PhonePe Verified SDK", requestBody = @RequestBody(content = @Content(mediaType = "multipart/form-data", schema = @Schema(implementation = MultiPartFileRequest.class), encoding = @Encoding(name = "file", contentType = "image/png, image/jpeg"))))
    public DocumentIdentifier upload(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                     @Parameter(hidden = true) @FormDataParam("file") final FormDataContentDisposition fileMetaData,
                                     @NotNull @FormDataParam("file") final InputStream fileInputStream,
                                     @FormDataParam("password") final String password,
                                     @NotEmpty @PathParam("actionMappingId") final String actionMappingId,
                                     @NotNull @PathParam("documentType") final DocumentType documentType,
                                     @NotEmpty @PathParam("documentLabel") final String documentLabel,
                                     @Parameter(hidden = true) @WorkflowId final String workflowId,
                                     @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                     @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        return documentService.uploadWithNewExternalReferenceId(fileMetaData, fileInputStream, password,
                userDetails.getUserId(), storedWorkflow.getWorkflowId(), actionMappingId, documentType, documentLabel);
    }

    @POST
    @AuthZ
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/upload/{actionMappingId}/{documentType}/{documentLabel}/{intent}/{componentKitVersion}")
    @Operation(summary = "Upload file from PhonePe Verified SDK", requestBody = @RequestBody(content = @Content(mediaType = "multipart/form-data", schema = @Schema(implementation = MultiPartFileRequest.class), encoding = @Encoding(name = "file", contentType = "image/png, image/jpeg, application/pdf"))))
    public DocumentIdentifier upload(@Parameter(hidden = true) @FormDataParam("file") final FormDataContentDisposition fileMetaData,
                                     @NotNull @FormDataParam("file") final InputStream fileInputStream,
                                     @FormDataParam("password") final String password,
                                     @Parameter(hidden = true) @WorkflowId final String workflowId,
                                     @NotEmpty @PathParam("actionMappingId") final String actionMappingId,
                                     @NotNull @PathParam("documentType") final DocumentType documentType,
                                     @NotEmpty @PathParam("documentLabel") final String documentLabel,
                                     @NotEmpty @PathParam("intent") final String intent,
                                     @PathParam("componentKitVersion") final long componentKitVersion,
                                     @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                     @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                     @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken) {

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        return documentService.uploadWithNewExternalReferenceId(fileMetaData, fileInputStream, password,
                userDetails.getUserId(), workflowId, actionMappingId, documentType, documentLabel, intent,
                componentKitVersion);
    }

    @GET
    @AuthZ
    @Path("/download/{actionId}/{documentId}")
    @Operation(summary = "Download file for given Document Id")
    public Response download(@Parameter(hidden = true) @WorkflowId final String workflowId,
                             @NotEmpty @PathParam("actionId") final String actionId,
                             @NotEmpty @PathParam("documentId") final String documentId,
                             @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                             @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken) {

        authZService.authorizeActionIdBelongsToWorkflowId(actionId, workflowId);

        return documentService.download(actionId, documentId);
    }

    @POST
    @AuthZ
    @SneakyThrows
    @Path("/delete")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Delete file from PhonePe Verified SDK for given Document Id")
    public void delete(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                       @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                       @Valid @NotNull final DocumentIdentifier documentIdentifier) {

        deleteDocStoreFileActor.publish(DocumentMessage.builder()
                .documentId(documentIdentifier.getDocumentId())
                .userId(userDetails.getUserId())
                .build());
    }
}