package com.phonepe.verified.kaizen.statemachines.actions.selfiehurdle;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.SelfieHurdleService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.UpdateStateBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "triggerSelfieHurdleAction")
public class TriggerSelfieHurdleAction extends UpdateStateBaseAction {

    private final SelfieHurdleService selfieHurdleService;

    @Inject
    public TriggerSelfieHurdleAction(final ActionService actionService,
                                     final ActionRepository actionRepository,
                                     final Provider<WorkflowContextStore> workflowContextStore,
                                     final Provider<EventIngestionActor> eventIngestionActorProvider,
                                     final SelfieHurdleService selfieHurdleService) {
        super(actionService, actionRepository, workflowContextStore, eventIngestionActorProvider);
        this.selfieHurdleService = selfieHurdleService;
    }

    @Override
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        selfieHurdleService.triggerSelfieHurdle(storedAction.getActionId());
    }
}
