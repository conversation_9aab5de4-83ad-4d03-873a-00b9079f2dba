package com.phonepe.verified.kaizen.services.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.Title;
import com.phonepe.shadow.models.response.TemplateInitResponse;
import com.phonepe.shadow.models.response.v2.TemplateInitV2Response;
import com.phonepe.shadow.services.TemplateServiceV2;
import com.phonepe.shadow.template.Template;
import com.phonepe.verified.kaizen.caches.impl.ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache;
import com.phonepe.verified.kaizen.clients.internal.CallbackClient;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.WorkflowInitRequestContext;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl.TerminalActionShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.AlwaysResumeGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.impl.ShadowV2UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.WorkflowInitAsyncDetails;
import com.phonepe.verified.kaizen.models.data.keys.ProfileIdSectionMappingIdKey;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileCriteria;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.requests.v2.ShadowV2SectionSubmitV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.AbortWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.FilterAllWorkflowIdsRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.LatestWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.PurgeWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowAddOnRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowInitRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.FilterAllWorkflowIdsV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.LatestWorkflowRequestV2;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.ResumeWorkflowV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowAddOnV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowInitV2Request;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.WorkflowClientCallback;
import com.phonepe.verified.kaizen.models.responses.workflow.AccessTokenResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.ActionFailureResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.LatestWorkflowResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowAddOnResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowFilterResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowInitResponse;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.actors.WorkflowInitActor;
import com.phonepe.verified.kaizen.queue.actors.WorkflowInitV2Actor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowClientCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowInitMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowInitV2Message;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AuthNService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.KillSwitchService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.BuildShadowV2ActionVisitor;
import com.phonepe.verified.kaizen.services.visitors.CallbackWorkflowMessageBuilderProfileVisitor;
import com.phonepe.verified.kaizen.services.visitors.CallbackWorkflowMessageBuilderProfileVisitor.CallbackWorkflowMessageBuilderProfileVisitorData;
import com.phonepe.verified.kaizen.services.visitors.ExpressionEvaluator;
import com.phonepe.verified.kaizen.services.visitors.GenerateTemplateInitResponseGetTemplateConfigVisitor;
import com.phonepe.verified.kaizen.services.visitors.GenerateTemplateInitResponseGetTemplateConfigVisitor.GenerateTemplateInitResponseGetTemplateConfigVisitorMessage;
import com.phonepe.verified.kaizen.services.visitors.GetFirstStandardProfileScreenConfig;
import com.phonepe.verified.kaizen.services.visitors.IdentifyProfileScreenToResume;
import com.phonepe.verified.kaizen.services.visitors.PostWorkflowCreationActionExecutor;
import com.phonepe.verified.kaizen.services.visitors.PostWorkflowCreationActionExecutor.PostWorkflowCreationActionExecutorData;
import com.phonepe.verified.kaizen.services.visitors.StandardProfileScreenConfigFromProfileStepVisitor;
import com.phonepe.verified.kaizen.services.visitors.WorkflowStepServiceTransitionStateBaseVisitor;
import com.phonepe.verified.kaizen.statemachines.WorkflowStateMachine;
import com.phonepe.verified.kaizen.storage.aerospike.commands.WorkflowInitAsyncDataForRequestIdCommand;
import com.phonepe.verified.kaizen.storage.aerospike.commands.WorkflowInitAsyncDataForWorkflowIdCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.RequestKey;
import com.phonepe.verified.kaizen.storage.aerospike.keys.TransitionLockKey;
import com.phonepe.verified.kaizen.storage.aerospike.keys.WorkflowContextKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredProfileStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredProfile;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.IdUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.Utils;
import com.phonepe.verified.kaizen.utils.WorkflowUtils;
import io.appform.functionmetrics.MonitoredFunction;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class WorkflowServiceImpl implements WorkflowService {

    public static final String INTENT = "intent";
    public static final String INTENT_JSONPATH = String.format("$.%s", INTENT);
    public static final String COMPONENT_KIT_VERSION = "componentKitVersion";
    public static final String COMPONENT_KIT_VERSION_JSONPATH = String.format("$.%s", COMPONENT_KIT_VERSION);
    private static final TerminalActionShadowV2ResponseConfig TERMINAL_ACTION_SHADOW_V2_RESPONSE_CONFIG = TerminalActionShadowV2ResponseConfig.builder()
            .build();
    private static final Set<TransitionState> NON_COMPLETED_STATES = Set.of(TransitionState.CREATED,
            TransitionState.IN_PROGRESS, TransitionState.PSEUDO_SUCCESS);
    private static final String REQUEST_ID = "requestId";
    private final AuthNService authNService;

    private final ActionService actionService;

    private final CallbackClient callbackClient;

    private final ProfileService profileService;

    private final HopeLangService hopeLangService;

    private final TemplateServiceV2 templateService;

    private final HandleBarsService handleBarsService;

    private final KillSwitchService killSwitchService;

    private final WorkflowRepository workflowRepository;

    private final ExpressionEvaluator expressionEvaluator;

    private final WorkflowStepService workflowStepService;

    private final WorkflowContextStore workflowContextStore;

    private final WorkflowStateMachine workflowStateMachine;

    private final EventIngestionCommand eventIngestionCommand;

    private final DynamicUiResponseService dynamicUiResponseService;

    private final WorkflowInitActor workflowInitActor;

    private final WorkflowInitV2Actor workflowInitV2Actor;

    private final WorkflowInitAsyncDataForWorkflowIdCommand workflowInitAsyncDataForWorkflowIdCommand;

    private final WorkflowInitAsyncDataForRequestIdCommand workflowInitAsyncDataForRequestIdCommand;

    private final GetFirstStandardProfileScreenConfig getFirstStandardProfileScreenConfig;

    private final ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache profileIdSectionMappingIdCache;

    private final GenerateTemplateInitResponseGetTemplateConfigVisitor generateTemplateInitResponseGetTemplateConfigVisitor;

    private final PostWorkflowCreationActionExecutor postWorkflowCreationActionExecutor;

    @Override
    public WorkflowInitResponse init(final WorkflowInitRequest workflowInitRequest,
                                     final UserDetails userDetails) {

        final var workflowId = IdUtils.createIdInSameShard("W", workflowInitRequest.getEntityId(),
                workflowRepository::getShardId);

        final var primaryProfile = profileService.getPrimaryProfile(
                BuildUtils.toPrimaryProfileCacheKey(workflowInitRequest.getProfileKey()), true);

        killSwitchService.evaluateWorkflowInitKillSwitch(workflowInitRequest, primaryProfile, workflowId);

        final var initialActionProfileStep = fetchInitialActionProfileStep(primaryProfile);

        if (initialActionProfileStep.isPresent()) {

            throw KaizenException.create(KaizenResponseCode.INITIAL_ACTION_PROFILE_STEP_NOT_SUPPORTED_IN_SYNC,
                    Map.of(Fields.profileId, primaryProfile.getProfileId()));
        }

        createWorkflowEntry(workflowId, BuildUtils.buildContext(workflowInitRequest, primaryProfile.getProfileId()),
                userDetails);

        validateAndTriggerWorkflowEvent(workflowId, workflowInitRequest.getEntityId(),
                TransitionEvent.MOVE_TO_IN_PROGRESS);

        return WorkflowInitResponse.builder()
                .workflowId(workflowId)
                .build();
    }

    @Override
    public WorkflowAddOnResponse initAddOn(final WorkflowAddOnRequest workflowAddOnRequest,
                                           final UserDetails userDetails) {

        final var workflowId = IdUtils.createIdInSameShard("W", workflowAddOnRequest.getEntityId(),
                workflowRepository::getShardId);

        final var addOnProfileOptional = profileService.getValidAddOnProfileForEntity(
                workflowAddOnRequest.getEntityId(), workflowAddOnRequest.getEntityType(),
                workflowAddOnRequest.getProfileKey(), false);

        if (addOnProfileOptional.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                    Map.of("Profile key", workflowAddOnRequest.getProfileKey()));
        }

        final var addOnProfile = addOnProfileOptional.get();

        killSwitchService.evaluateWorkflowAddOnInitKillSwitch(workflowAddOnRequest, addOnProfile, workflowId);

        createWorkflowEntry(workflowId, BuildUtils.buildContext(workflowAddOnRequest, addOnProfile.getProfileId()),
                userDetails);

        handlePostWorkflowCreationActionConfigs(addOnProfile, workflowId);

        validateAndTriggerWorkflowEvent(workflowId, workflowAddOnRequest.getEntityId(),
                TransitionEvent.MOVE_TO_IN_PROGRESS);

        return WorkflowAddOnResponse.builder()
                .workflowId(workflowId)
                .build();
    }

    @Override
    public AccessTokenResponse initV2(final WorkflowInitV2Request workflowInitV2Request,
                                      final RequestInfo requestInfo,
                                      final UserDetails userDetails) {

        final var workflowId = IdUtils.createIdInSameShard("W", workflowInitV2Request.getEntityId(),
                workflowRepository::getShardId);

        final Profile primaryProfile = profileService.getPrimaryProfile(
                BuildUtils.toPrimaryProfileCacheKey(workflowInitV2Request), true);

        final var initialActionProfileStep = fetchInitialActionProfileStep(primaryProfile);

        if (initialActionProfileStep.isPresent()) {

            throw KaizenException.create(KaizenResponseCode.INITIAL_ACTION_PROFILE_STEP_NOT_SUPPORTED_IN_SYNC,
                    Map.of(Fields.profileId, primaryProfile.getProfileId()));
        }

        killSwitchService.evaluateWorkflowInitV2KillSwitch(workflowInitV2Request, primaryProfile, workflowId);

        createWorkflowEntry(workflowId, BuildUtils.buildContext(workflowInitV2Request, primaryProfile.getProfileId()),
                userDetails);

        validateAndTriggerWorkflowEvent(workflowId, workflowInitV2Request.getEntityId(),
                TransitionEvent.MOVE_TO_IN_PROGRESS);

        final var userReferenceId = workflowInitV2Request.getUserReferenceId();

        final var accessToken = authNService.createAndGetAccessToken(workflowId, userReferenceId);

        return AccessTokenResponse.builder()
                .workflowId(workflowId)
                .accessToken(accessToken)
                .build();
    }

    private void handlePostWorkflowCreationActionConfigs(final Profile primaryProfile,
                                                         final String workflowId) {

        // Be very, very careful with your actions in sync flow. Plan for failures and latency accordingly.
        // Be very careful with these actions in async flow. Failures can still stop execution.
        // While latency may not be a concern, race conditions might be.
        Optional.ofNullable(primaryProfile.getPostWorkflowCreationActionConfigs())
                .orElse(List.of())
                .forEach(postWorkflowCreationActionConfig -> postWorkflowCreationActionConfig.accept(
                        postWorkflowCreationActionExecutor, PostWorkflowCreationActionExecutorData.builder()
                                .workflowId(workflowId)
                                .build()));
    }

    @Override
    @SneakyThrows
    public String initAsync(final WorkflowInitRequest workflowInitRequest,
                            final UserDetails userDetails) {

        // Request id which clients can use to know status of async init call
        final var requestId = UUID.randomUUID()
                .toString();

        final var workflowId = IdUtils.createIdInSameShard("W", workflowInitRequest.getEntityId(),
                workflowRepository::getShardId);

        final var primaryProfile = profileService.getPrimaryProfile(
                BuildUtils.toPrimaryProfileCacheKey(workflowInitRequest.getProfileKey()), false);

        killSwitchService.evaluateWorkflowInitKillSwitch(workflowInitRequest, primaryProfile, workflowId);

        workflowInitActor.publish(WorkflowInitMessage.builder()
                .workflowInitRequest(workflowInitRequest)
                .userDetails(userDetails)
                .requestId(requestId)
                .workflowId(workflowId)
                .build());

        persistAsyncInitDetailsInAerospike(requestId, workflowId, null);

        return requestId;
    }

    @Override
    @SneakyThrows
    public String initAsyncV2(final WorkflowInitV2Request workflowInitV2Request,
                              final UserDetails userDetails) {

        final var requestId = UUID.randomUUID()
                .toString();

        final var workflowId = IdUtils.createIdInSameShard("W", workflowInitV2Request.getEntityId(),
                workflowRepository::getShardId);

        final var primaryProfile = profileService.getPrimaryProfile(
                BuildUtils.toPrimaryProfileCacheKey(workflowInitV2Request), false);

        killSwitchService.evaluateWorkflowInitV2KillSwitch(workflowInitV2Request, primaryProfile, workflowId);

        workflowInitV2Actor.publish(WorkflowInitV2Message.builder()
                .workflowInitV2Request(workflowInitV2Request)
                .userDetails(userDetails)
                .requestId(requestId)
                .workflowId(workflowId)
                .build());

        persistAsyncInitDetailsInAerospike(requestId, workflowId, workflowInitV2Request.getUserReferenceId());

        return requestId;
    }

    private void persistAsyncInitDetailsInAerospike(final String requestId,
                                                    final String workflowId,
                                                    final String userReferenceId) {

        final var verificationInitAsyncDetails = WorkflowInitAsyncDetails.builder()
                .workflowId(workflowId)
                .requestId(requestId)
                .userReferenceId(userReferenceId)
                .build();

        workflowInitAsyncDataForWorkflowIdCommand.save(WorkflowContextKey.builder()
                .workflowId(workflowId)
                .build(), verificationInitAsyncDetails);

        workflowInitAsyncDataForRequestIdCommand.save(RequestKey.builder()
                .requestId(requestId)
                .build(), verificationInitAsyncDetails);
    }

    @Override
    @SneakyThrows
    public void processInitAsyncRequest(final WorkflowInitRequest request,
                                        final String requestId,
                                        final String workflowId,
                                        final UserDetails userDetails) {

        final var primaryProfile = profileService.getPrimaryProfile(
                BuildUtils.toPrimaryProfileCacheKey(request.getProfileKey()), true);

        createWorkflowEntry(workflowId, BuildUtils.buildContext(request, primaryProfile.getProfileId()), userDetails);

        final var initialActionProfileStep = primaryProfile.getProfileSteps()
                .stream()
                .filter(ps -> Constants.INITIAL_ACTION_STEP.equals(ps.getProfileStepMappingId()))
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(initialActionProfileStep)) {

            validateAndTriggerWorkflowEvent(workflowId, request.getEntityId(), TransitionEvent.TRIGGER_INITIAL_ACTION);

        } else {

            validateAndTriggerWorkflowEvent(workflowId, request.getEntityId(), TransitionEvent.READY);

            callbackClient.sendCallback(WorkflowClientCallbackMessage.builder()
                    .workflowId(workflowId)
                    .requestId(requestId)
                    .build());

            validateAndTriggerWorkflowEvent(workflowId, request.getEntityId(), TransitionEvent.MOVE_TO_IN_PROGRESS);
        }

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        eventIngestionCommand.workflowInitAsyncEvent(storedWorkflow, primaryProfile, requestId);
    }

    @Override
    @SneakyThrows
    public void processInitAsyncV2Request(final WorkflowInitV2Request workflowInitV2Request,
                                          final String requestId,
                                          final String workflowId,
                                          final UserDetails userDetails) {

        final var primaryProfile = profileService.getPrimaryProfile(
                BuildUtils.toPrimaryProfileCacheKey(workflowInitV2Request.getProfileKey()), true);

        createWorkflowEntry(workflowId, BuildUtils.buildContext(workflowInitV2Request, primaryProfile.getProfileId()),
                userDetails);

        final var initialActionProfileStep = fetchInitialActionProfileStep(primaryProfile);

        if (initialActionProfileStep.isPresent()) {

            validateAndTriggerWorkflowEvent(workflowId, workflowInitV2Request.getEntityId(),
                    TransitionEvent.TRIGGER_INITIAL_ACTION);
        } else {

            validateAndTriggerWorkflowEvent(workflowId, workflowInitV2Request.getEntityId(), TransitionEvent.READY);

            final var userReferenceId = workflowInitV2Request.getUserReferenceId();

            final var accessToken = authNService.createAndGetAccessToken(workflowId, userReferenceId);

            callbackClient.sendCallback(WorkflowClientCallbackMessage.builder()
                    .workflowId(workflowId)
                    .requestId(requestId)
                    .accessToken(accessToken)
                    .build());

            validateAndTriggerWorkflowEvent(workflowId, workflowInitV2Request.getEntityId(),
                    TransitionEvent.MOVE_TO_IN_PROGRESS);
        }

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        eventIngestionCommand.workflowInitAsyncEvent(storedWorkflow, primaryProfile, requestId);
    }

    private Optional<ProfileStep> fetchInitialActionProfileStep(final Profile profile) {

        return profile.getProfileSteps()
                .stream()
                .filter(ps -> Constants.INITIAL_ACTION_STEP.equals(ps.getProfileStepMappingId()))
                .findFirst();
    }

    @Override
    public WorkflowClientCallback checkInitWorkflowStatus(final String requestId) {

        final var verificationInitAsyncDetails = workflowInitAsyncDataForRequestIdCommand.get(RequestKey.builder()
                .requestId(requestId)
                .build());

        if (Objects.isNull(verificationInitAsyncDetails)) {

            throw KaizenException.create(KaizenResponseCode.REQUEST_ID_EXPIRED, Map.of(REQUEST_ID, requestId));
        }

        final var storedWorkflow = getStoredWorkflowForAsyncRequest(requestId,
                verificationInitAsyncDetails.getWorkflowId());

        if (TransitionState.WORKFLOW_NOT_READY.contains(storedWorkflow.getCurrentState())) {

            throw KaizenException.create(KaizenResponseCode.WORKFLOW_NOT_READY, Map.of(REQUEST_ID, requestId));
        }

        final var userReferenceId = verificationInitAsyncDetails.getUserReferenceId();

        final var accessToken = Objects.isNull(userReferenceId)
                                ? null
                                : authNService.createAndGetAccessToken(storedWorkflow.getWorkflowId(), userReferenceId);

        final var actionFailureResponseOptional = fetchWorkflowActionFailure(storedWorkflow);

        final var callbackWorkflowMessageBuilderProfileVisitorData = CallbackWorkflowMessageBuilderProfileVisitorData.builder()
                .storedWorkflow(storedWorkflow)
                .failureErrorCode(actionFailureResponseOptional.map(ActionFailureResponse::getActionFailureErrorCode)
                        .orElse(null))
                .accessToken(accessToken)
                .requestId(requestId)
                .build();

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        return profile.accept(CallbackWorkflowMessageBuilderProfileVisitor.INSTANCE,
                callbackWorkflowMessageBuilderProfileVisitorData);
    }

    private StoredWorkflow getStoredWorkflowForAsyncRequest(final String requestId,
                                                            final String workflowId) {
        try {

            return validateAndGetWorkflow(workflowId);

        } catch (final Exception exception) {

            log.error("Exception encountered while fetching workflow: {} for async init request id: {} ", workflowId,
                    requestId, exception);

            throw KaizenException.create(KaizenResponseCode.WORKFLOW_NOT_READY, Map.of(REQUEST_ID, requestId));
        }
    }

    @Override
    @SneakyThrows
    public void handleInitialActionWorkflowStepCompletion(final StoredWorkflowStep storedWorkflowStep,
                                                          final ActionFailureErrorCode actionFailureErrorCode) {

        final var storedWorkflow = validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var notStartedExecutableProfileSteps = getNotStartedExecutableProfileSteps(storedWorkflowStep);

        if (TransitionState.SUCCESS_STATES.contains(storedWorkflowStep.getCurrentState())
                && notStartedExecutableProfileSteps.isEmpty()) {

            validateAndTriggerWorkflowEvent(storedWorkflow.getWorkflowId(), storedWorkflow.getEntityId(),
                    storedWorkflowStep.getCurrentEvent());

            return;
        }

        if (!TransitionState.READY.equals(storedWorkflow.getCurrentState())) {

            handleInitialActionWorkflowTransitionFromWorkflowStep(storedWorkflowStep, storedWorkflow);
        }

        final var verificationInitAsyncDetails = workflowInitAsyncDataForWorkflowIdCommand.get(
                WorkflowContextKey.builder()
                        .workflowId(storedWorkflow.getWorkflowId())
                        .build());

        if (Objects.isNull(verificationInitAsyncDetails)) {

            validateAndTriggerWorkflowEvent(storedWorkflow.getWorkflowId(), storedWorkflow.getEntityId(),
                    TransitionEvent.FAILURE);

            log.error("For workflow: {}, requestId was not found / expired, failing the workflow",
                    storedWorkflow.getWorkflowId());
            return;
        }

        final var requestId = verificationInitAsyncDetails.getRequestId();

        final var userReferenceId = verificationInitAsyncDetails.getUserReferenceId();

        final var accessToken = Objects.isNull(userReferenceId)
                                ? null
                                : authNService.createAndGetAccessToken(storedWorkflow.getWorkflowId(), userReferenceId);

        callbackClient.sendCallback(WorkflowClientCallbackMessage.builder()
                .workflowId(storedWorkflow.getWorkflowId())
                .failureErrorCode(actionFailureErrorCode)
                .requestId(requestId)
                .accessToken(accessToken)
                .build());

        if (TransitionState.SUCCESS_STATES.contains(storedWorkflowStep.getCurrentState())) {

            validateAndTriggerWorkflowEvent(storedWorkflow.getWorkflowId(), storedWorkflow.getEntityId(),
                    TransitionEvent.MOVE_TO_IN_PROGRESS);
        }
    }

    private List<ProfileStep> getNotStartedExecutableProfileSteps(final StoredWorkflowStep storedWorkflowStep) {

        final var storedWorkflow = validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflowStep.getWorkflowId());

        final var workflowContextNode = MapperUtils.convertToJsonNode(workflowContext);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var profileSteps = profile.getProfileSteps();

        final var workflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        final var workflowStepMap = workflowSteps.stream()
                .collect(Collectors.toMap(StoredWorkflowStep::getProfileStepId, Function.identity()));

        return profileSteps.stream()
                .filter(ps -> !workflowStepMap.containsKey(ps.getProfileStepId()))
                .filter(ps -> hopeLangService.evaluate(ps.getExecutionRule(), workflowContextNode))
                .toList();
    }

    private void handleInitialActionWorkflowTransitionFromWorkflowStep(final StoredWorkflowStep storedWorkflowStep,
                                                                       final StoredWorkflow storedWorkflow) {

        if (TransitionState.SUCCESS_STATES.contains(storedWorkflowStep.getCurrentState())) {

            validateAndTriggerWorkflowEvent(storedWorkflow.getWorkflowId(), storedWorkflow.getEntityId(),
                    TransitionEvent.READY);

        } else if (storedWorkflowStep.getCurrentState() == TransitionState.FAILURE) {

            validateAndTriggerWorkflowEvent(storedWorkflow.getWorkflowId(), storedWorkflow.getEntityId(),
                    TransitionEvent.FAILURE);

        } else {

            throw KaizenException.create(KaizenResponseCode.INVALID_STATE,
                    Map.of(StoredWorkflowStep.Fields.workflowStepId, storedWorkflowStep.getWorkflowStepId(),
                            Fields.currentState, storedWorkflowStep.getCurrentState()));
        }
    }

    @Override
    public AccessTokenResponse addOnV2(final WorkflowAddOnV2Request workflowAddOnV2Request,
                                       final RequestInfo requestInfo,
                                       final UserDetails userDetails) {

        final var workflowId = IdUtils.createIdInSameShard("W", workflowAddOnV2Request.getEntityId(),
                workflowRepository::getShardId);

        final var addOnProfileOptional = profileService.getValidAddOnProfileForEntity(
                workflowAddOnV2Request.getEntityId(), workflowAddOnV2Request.getEntityType(),
                workflowAddOnV2Request.getProfileKey(), false);

        if (addOnProfileOptional.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                    Map.of("Profile key", workflowAddOnV2Request.getProfileKey()));
        }

        createWorkflowEntry(workflowId, BuildUtils.buildContext(workflowAddOnV2Request, addOnProfileOptional.get()
                .getProfileId()), userDetails);

        validateAndTriggerWorkflowEvent(workflowId, workflowAddOnV2Request.getEntityId(),
                TransitionEvent.MOVE_TO_IN_PROGRESS);

        final var userReferenceId = workflowAddOnV2Request.getUserReferenceId();

        final var accessToken = authNService.createAndGetAccessToken(workflowId, userReferenceId);

        return AccessTokenResponse.builder()
                .workflowId(workflowId)
                .accessToken(accessToken)
                .build();
    }

    private void validateAndTriggerWorkflowEvent(final String workflowId,
                                                 final String entityId,
                                                 final TransitionEvent transitionEvent) {

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(Fields.entityId, entityId);
        stateMachineTransitionContext.put(Fields.workflowId, workflowId);
        stateMachineTransitionContext.put(UserDetails.class, Constants.PVCORE_SYSTEM_USER);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(workflowId)
                .build());

        workflowStateMachine.sendEvent(storedWorkflow.getCurrentState(), transitionEvent,
                stateMachineTransitionContext);
    }

    @Override
    public void sectionSubmit(final RequestInfo requestInfo,
                              final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                              final UserDetails userDetails) {

        final var storedWorkflow = validateAndGetWorkflow(shadowV2SectionSubmitRequest.getWorkflowId());

        processSectionSubmit(ApiVersion.V1, requestInfo, userDetails, storedWorkflow, shadowV2SectionSubmitRequest);
    }

    private void processSectionSubmit(final ApiVersion apiVersion,
                                      final RequestInfo requestInfo,
                                      final UserDetails userDetails,
                                      final StoredWorkflow storedWorkflow,
                                      final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest) {

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        eventIngestionCommand.sectionSubmitReceivedEvent(storedWorkflow, profile,
                shadowV2SectionSubmitRequest.getIntent(), shadowV2SectionSubmitRequest.getComponentKitVersion(),
                shadowV2SectionSubmitRequest.getSectionInputData(), userDetails, requestInfo);

        final var uiRequestContext = BuildUtils.buildShadowV2UiRequestContext(storedWorkflow.getEntityId(),
                shadowV2SectionSubmitRequest.getIntent(), apiVersion, requestInfo,
                shadowV2SectionSubmitRequest.getComponentKitVersion(),
                shadowV2SectionSubmitRequest.getSectionInputData(), storedWorkflow.getWorkflowId());

        if (!TransitionState.PENDING_STATES.contains(storedWorkflow.getCurrentState())) {

            final var uiResponseConfigMap = Map.<TemplateType, UiResponseConfig>of(TemplateType.SHADOW_V2,
                    ShadowV2UiResponseConfig.builder()
                            .shadowV2ResponseConfig(TerminalActionShadowV2ResponseConfig.builder()
                                    .build())
                            .build());

            dynamicUiResponseService.sendResponseToUi(uiRequestContext, uiResponseConfigMap,
                    storedWorkflow.getWorkflowId());

            return;
        }

        final var profileStepStandardProfileScreen = profileIdSectionMappingIdCache.get(
                ProfileIdSectionMappingIdKey.builder()
                        .profileId(storedWorkflow.getProfileId())
                        .sectionMappingId(shadowV2SectionSubmitRequest.getSectionInputData()
                                .getMappingId())
                        .build());

        final var profileStep = profileStepStandardProfileScreen.getProfileStep();

        /*
         * Invalidate all screens and steps from the screen for which section submit call is received
         * as older inputs are not valid.
         *
         * 1. Invalidate workflow steps after current step is performed here.
         * 2. Invalidation of workflow screens including current screen and next screens of current workflow step
         * is invalidated when workflow step transitions to in progress state.
         */
        workflowStepService.invalidateWorkflowStepsAfterCurrentStep(storedWorkflow.getWorkflowId(),
                profileStep.getProfileStepId(), userDetails);

        final var storedWorkflowStepOptional = workflowStepService.getValidWorkflowStep(storedWorkflow.getWorkflowId(),
                profileStep.getProfileStepId());

        final var standardProfileScreenConfig = profileStepStandardProfileScreen.getStandardProfileScreenConfig();

        if (userHasSkippedWorkflowStep(shadowV2SectionSubmitRequest, standardProfileScreenConfig)) {

            final var effectiveWorkflowStep = storedWorkflowStepOptional.orElseGet(
                    () -> workflowStepService.createAndGetWorkflowStep(storedWorkflow.getWorkflowId(),
                            shadowV2SectionSubmitRequest.getUserId(), shadowV2SectionSubmitRequest, profileStep,
                            userDetails));

            workflowStepService.saveUiRequestContext(effectiveWorkflowStep.getWorkflowStepId(), uiRequestContext);

            handleSkipWorkflowStep(effectiveWorkflowStep, standardProfileScreenConfig, userDetails,
                    standardProfileScreenConfig.getSkipWorkflowStepConfig()
                            .isInvalidateAllActions());

        } else if (userHasSkippedWorkflow(shadowV2SectionSubmitRequest, standardProfileScreenConfig)) {

            final var effectiveWorkflowStep = storedWorkflowStepOptional.orElseGet(
                    () -> workflowStepService.createAndGetWorkflowStep(storedWorkflow.getWorkflowId(),
                            shadowV2SectionSubmitRequest.getUserId(), shadowV2SectionSubmitRequest, profileStep,
                            userDetails));

            workflowStepService.saveUiRequestContext(effectiveWorkflowStep.getWorkflowStepId(), uiRequestContext);

            handleSkipWorkflow(storedWorkflow, effectiveWorkflowStep, standardProfileScreenConfig, userDetails,
                    standardProfileScreenConfig.getSkipWorkflowConfig()
                            .isInvalidateAllActions());

        } else {

            if (storedWorkflowStepOptional.isEmpty()) {

                createWorkflowStep(shadowV2SectionSubmitRequest, userDetails, storedWorkflow.getWorkflowId(),
                        profileStep, uiRequestContext);
            } else {

                processWorkflowStep(shadowV2SectionSubmitRequest, userDetails, storedWorkflowStepOptional.get(),
                        uiRequestContext);
            }
        }
    }

    @Override
    public void sectionSubmitV2(final String workflowId,
                                final RequestInfo requestInfo,
                                final UserDetails userDetails,
                                final ShadowV2SectionSubmitV2Request shadowV2SectionSubmitV2Request) {

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        final var shadowV2SectionSubmitRequest = BuildUtils.buildShadowV2SectionSubmitRequest(
                storedWorkflow.getWorkflowId(), storedWorkflow.getEntityId(), shadowV2SectionSubmitV2Request);

        processSectionSubmit(ApiVersion.V2, requestInfo, userDetails, storedWorkflow, shadowV2SectionSubmitRequest);
    }

    private boolean userHasSkippedWorkflow(final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                           final StandardProfileScreenConfig standardProfileScreenConfig) {

        final var skipWorkflowConfig = standardProfileScreenConfig.getSkipWorkflowConfig();

        if (Objects.nonNull(skipWorkflowConfig)) {

            final var actualFieldValue = handleBarsService.transform(
                    skipWorkflowConfig.getFieldValueHandlebarsTranslator(), shadowV2SectionSubmitRequest);

            return skipWorkflowConfig.getExpectedFieldValue()
                    .equals(actualFieldValue);
        }

        return false;
    }

    private boolean userHasSkippedWorkflowStep(final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                               final StandardProfileScreenConfig standardProfileScreenConfig) {

        final var skipWorkflowStepConfig = standardProfileScreenConfig.getSkipWorkflowStepConfig();

        if (Objects.nonNull(skipWorkflowStepConfig)) {

            final var actualFieldValue = handleBarsService.transform(
                    skipWorkflowStepConfig.getFieldValueHandlebarsTranslator(), shadowV2SectionSubmitRequest);

            return skipWorkflowStepConfig.getExpectedFieldValue()
                    .equals(actualFieldValue);
        }

        return false;
    }

    private void createWorkflowStep(final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                    final UserDetails userDetails,
                                    final String workflowId,
                                    final ProfileStep profileStep,
                                    final UiRequestContext uiRequestContext) {

        // todo{Ankit}:: Validate executionRule of a workflowStep
        final var workflowStepId = IdUtils.createIdInSameShard("WS", workflowId, workflowRepository::getShardId);

        workflowStepService.saveUiRequestContext(workflowStepId, uiRequestContext);

        workflowStepService.createWorkflowStep(workflowId, shadowV2SectionSubmitRequest.getUserId(), workflowStepId,
                shadowV2SectionSubmitRequest, profileStep, userDetails, true);
    }

    private void processWorkflowStep(final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                     final UserDetails userDetails,
                                     final StoredWorkflowStep storedWorkflowStep,
                                     final UiRequestContext uiRequestContext) {

        workflowStepService.saveUiRequestContext(storedWorkflowStep.getWorkflowStepId(), uiRequestContext);

        if (storedWorkflowStep.getCurrentState() == TransitionState.FAILURE
                && workflowStepService.isWorkflowStepRetryAvailable(storedWorkflowStep.getWorkflowStepId(),
                storedWorkflowStep.getProfileStepId())) {

            workflowStepService.triggerMoveToInProgressEvent(storedWorkflowStep.getWorkflowStepId(),
                    shadowV2SectionSubmitRequest, userDetails);

        } else if (storedWorkflowStep.getCurrentState() == TransitionState.IN_PROGRESS) {

            final var screenMappingId = shadowV2SectionSubmitRequest.getSectionInputData()
                    .getMappingId();

            workflowStepService.triggerWorkflowStep(storedWorkflowStep, screenMappingId, userDetails);

        } else if (TransitionState.SUCCESS_STATES.contains(storedWorkflowStep.getCurrentState())) {

            workflowStepService.triggerResubmitEvent(storedWorkflowStep.getWorkflowStepId(),
                    shadowV2SectionSubmitRequest, userDetails);
        } else {

            // todo{Ankit}:: Handle based on success or failure
            throw KaizenException.create(KaizenResponseCode.WORKFLOW_STEP_ALREADY_COMPLETED, Map.ofEntries(
                    Map.entry(StoredWorkflowStep.Fields.workflowStepId, storedWorkflowStep.getWorkflowStepId()),
                    Map.entry(Fields.currentState, storedWorkflowStep.getCurrentState())));
        }
    }

    @Override
    @SneakyThrows
    @MonitoredFunction
    public TemplateInitV2Response getTemplateV2(final String workflowId,
                                                final String intent,
                                                final long componentKitVersion,
                                                final boolean showSummaryView) {

        final var templateInitResponse = getTemplate(workflowId, intent, componentKitVersion, showSummaryView);

        return BuildUtils.getTemplateInitV2Response(templateInitResponse);
    }

    @Override
    @SneakyThrows
    @MonitoredFunction
    public TemplateInitResponse getTemplate(final String workflowId,
                                            final String intent,
                                            final long componentKitVersion,
                                            final boolean showSummaryView) {

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        killSwitchService.evaluateWorkflowResumeKillSwitch(profile, storedWorkflow);

        final var getTemplateConfig = Objects.isNull(profile.getGetTemplateConfig())
                                      ? AlwaysResumeGetTemplateConfig.INSTANCE
                                      : profile.getGetTemplateConfig();

        return getTemplateConfig.accept(generateTemplateInitResponseGetTemplateConfigVisitor,
                GenerateTemplateInitResponseGetTemplateConfigVisitorMessage.builder()
                        .intent(intent)
                        .componentKitVersion(componentKitVersion)
                        .workflowId(workflowId)
                        .showSummaryView(showSummaryView)
                        .build());
    }

    @Override
    @SneakyThrows
    public TemplateInitResponse generateTemplateInitResponseForResumeConfig(final String workflowId,
                                                                            final String intent,
                                                                            final long componentKitVersion) {

        final var workflowContext = workflowContextStore.getWorkflowContext(workflowId);

        final var workflowContextNode = (ObjectNode) MapperUtils.convertToJsonNode(workflowContext);

        workflowContextNode.put(INTENT, intent);
        workflowContextNode.put(COMPONENT_KIT_VERSION, componentKitVersion);

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        if (!TransitionState.PENDING_STATES.contains(storedWorkflow.getCurrentState())) {

            return generateTemplateInitResponse(workflowId, intent, componentKitVersion, profile, (String) null,
                    workflowContextNode, TERMINAL_ACTION_SHADOW_V2_RESPONSE_CONFIG);
        }

        final var storedWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        final var pendingWorkflowStepOptional = findPendingWorkflowStep(storedWorkflowSteps);

        eventIngestionCommand.getTemplateEvent(profile, storedWorkflow, intent, componentKitVersion);

        if (pendingWorkflowStepOptional.isPresent()) {

            return handleGetTemplatePendingWorkflowStepExists(profile, pendingWorkflowStepOptional.get(), workflowId,
                    intent, componentKitVersion, workflowContextNode);

        } else {

            return handleGetTemplatePendingWorkflowStepDoesNotExists(profile, storedWorkflowSteps, workflowId, intent,
                    componentKitVersion, workflowContextNode);
        }
    }

    @Override
    @SneakyThrows
    public TemplateInitResponse generateTemplateInitResponseForSummaryConfig(final String workflowId,
                                                                             final String intent,
                                                                             final long componentKitVersion,
                                                                             final String summaryViewScreenMappingId) {

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        final var workflowContext = workflowContextStore.getWorkflowContext(workflowId);

        final var workflowContextNode = (ObjectNode) MapperUtils.convertToJsonNode(workflowContext);

        workflowContextNode.put(INTENT, intent);
        workflowContextNode.put(COMPONENT_KIT_VERSION, componentKitVersion);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        return generateTemplateInitResponse(workflowId, intent, componentKitVersion, profile,
                summaryViewScreenMappingId, workflowContextNode,
                Constants.MOVE_TO_NEXT_SECTION_SHADOW_V2_RESPONSE_CONFIG);

    }


    private TemplateInitResponse handleGetTemplatePendingWorkflowStepExists(final Profile profile,
                                                                            final StoredWorkflowStep pendingStoredWorkflowStep,
                                                                            final String workflowId,
                                                                            final String intent,
                                                                            final long componentKitVersion,
                                                                            final JsonNode workflowContextNode) {

        final var workflowStepId = pendingStoredWorkflowStep.getWorkflowStepId();
        final var profileStepId = pendingStoredWorkflowStep.getProfileStepId();

        final var pendingProfileStep = profileService.getProfileStep(profileStepId);

        final var profileScreenStatus = pendingProfileStep.getProfileScreenConfig()
                .accept(new IdentifyProfileScreenToResume(workflowStepId, actionService, this, hopeLangService,
                        workflowContextStore))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                        Map.of(StoredWorkflowStep.Fields.workflowStepId, workflowStepId)));

        final var nextProfileScreenConfig = profileScreenStatus.getProfileScreenConfigToTrigger();

        return generateTemplateInitResponse(workflowId, intent, componentKitVersion, profile, nextProfileScreenConfig,
                workflowContextNode, Constants.MOVE_TO_NEXT_SECTION_SHADOW_V2_RESPONSE_CONFIG);
    }

    private TemplateInitResponse handleGetTemplatePendingWorkflowStepDoesNotExists(final Profile profile,
                                                                                   final List<StoredWorkflowStep> storedWorkflowSteps,
                                                                                   final String workflowId,
                                                                                   final String intent,
                                                                                   final long componentKitVersion,
                                                                                   final JsonNode workflowContextNode) {

        if (!storedWorkflowSteps.isEmpty()) {

            final var optionalTemplateInitResponseApiResponse = validateAndHandleIfLastExecutedActionHasFailedAndRetryable(
                    profile, storedWorkflowSteps, workflowId, intent, componentKitVersion, workflowContextNode);

            if (optionalTemplateInitResponseApiResponse.isPresent()) {
                return optionalTemplateInitResponseApiResponse.get();
            }
        }

        return validateAndHandleIfNotStartedExecutableProfileStepExistsOrReturnTerminalAction(profile, workflowId,
                intent, componentKitVersion, workflowContextNode);
    }

    private Optional<TemplateInitResponse> validateAndHandleIfLastExecutedActionHasFailedAndRetryable(final Profile profile,
                                                                                                      final List<StoredWorkflowStep> storedWorkflowSteps,
                                                                                                      final String workflowId,
                                                                                                      final String intent,
                                                                                                      final long componentKitVersion,
                                                                                                      final JsonNode workflowContextNode) {

        final var workflowStepIds = storedWorkflowSteps.stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        final var latestStoredActionOptional = actionService.getActions(workflowStepIds)
                .stream()
                .max(Comparator.comparing(StoredAction::getCreatedAt));

        if (latestStoredActionOptional.isPresent() && CompletionState.FAILURE == latestStoredActionOptional.get()
                .getCompletionState()) {

            final var latestStoredAction = latestStoredActionOptional.get();

            final var latestActionStoredWorkflowStep = workflowStepService.validateAndGetWorkflowStep(
                    latestStoredAction.getWorkflowStepId());

            final var latestActionProfileStep = profileService.getProfileStep(
                    latestActionStoredWorkflowStep.getProfileStepId());

            final var latestActionStandardProfileScreenConfig = latestActionProfileStep.getProfileScreenConfig()
                    .accept(new StandardProfileScreenConfigFromProfileStepVisitor(
                            latestStoredAction.getScreenMappingId()))
                    .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                            Map.of(StoredWorkflowStep.Fields.workflowStepId, latestStoredAction.getWorkflowStepId(),
                                    StoredAction.Fields.screenMappingId, latestStoredAction.getScreenMappingId())));

            final var isRetryAvailable = workflowStepService.isRetryAvailable(latestStoredAction,
                    latestActionStandardProfileScreenConfig);

            if (isRetryAvailable) {

                return Optional.of(generateTemplateInitResponse(workflowId, intent, componentKitVersion, profile,
                        latestActionStandardProfileScreenConfig, workflowContextNode,
                        Constants.MOVE_TO_NEXT_SECTION_SHADOW_V2_RESPONSE_CONFIG));
            }
        }

        return Optional.empty();
    }

    private TemplateInitResponse validateAndHandleIfNotStartedExecutableProfileStepExistsOrReturnTerminalAction(final Profile profile,
                                                                                                                final String workflowId,
                                                                                                                final String intent,
                                                                                                                final long componentKitVersion,
                                                                                                                final JsonNode workflowContextNode) {

        final var notStartedExecutableProfileSteps = getNotStartedExecutableProfileSteps(workflowId,
                workflowContextNode);

        if (!notStartedExecutableProfileSteps.isEmpty()) {

            final var nextProfileScreenConfig = notStartedExecutableProfileSteps.iterator()
                    .next()
                    .getProfileScreenConfig()
                    .accept(getFirstStandardProfileScreenConfig)
                    .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND, Map.of()));

            return generateTemplateInitResponse(workflowId, intent, componentKitVersion, profile,
                    nextProfileScreenConfig, workflowContextNode,
                    Constants.MOVE_TO_NEXT_SECTION_SHADOW_V2_RESPONSE_CONFIG);

        } else {

            // todo:: Identify last action executed, fetch profile screen from it and return success
            //        or failure response based on the status of the screen
            return generateTemplateInitResponse(workflowId, intent, componentKitVersion, profile, (String) null,
                    workflowContextNode, TERMINAL_ACTION_SHADOW_V2_RESPONSE_CONFIG);
        }
    }


    @SneakyThrows
    private TemplateInitResponse generateTemplateInitResponse(final String workflowId,
                                                              final String intent,
                                                              final long componentKitVersion,
                                                              final Profile profile,
                                                              final StandardProfileScreenConfig nextProfileScreenConfig,
                                                              final JsonNode workflowContext,
                                                              final ShadowV2ResponseConfig shadowV2ResponseConfig) {

        final var screenMappingId = Objects.nonNull(nextProfileScreenConfig)
                                    ? nextProfileScreenConfig.getScreenMappingId()
                                    : null;

        return generateTemplateInitResponse(workflowId, intent, componentKitVersion, profile, screenMappingId,
                workflowContext, shadowV2ResponseConfig);
    }

    @SneakyThrows
    private TemplateInitResponse generateTemplateInitResponse(final String workflowId,
                                                              final String intent,
                                                              final long componentKitVersion,
                                                              final Profile profile,
                                                              final String screenMappingId,
                                                              final JsonNode workflowContext,
                                                              final ShadowV2ResponseConfig shadowV2ResponseConfig) {

        final var templateWorkflowType = BuildUtils.getTemplateWorkflowType(profile);

        final var template = templateService.getTemplate(templateWorkflowType, intent, componentKitVersion);

        final var clonedTemplate = MapperUtils.clone(template, Template.class);

        if (Objects.nonNull(screenMappingId)) {

            final var templateSectionMapping = clonedTemplate.getSectionMappings()
                    .stream()
                    .filter(tsm -> tsm.getMappingId()
                            .equals(screenMappingId))
                    .findFirst()
                    .orElseThrow(
                            () -> KaizenException.create(KaizenResponseCode.SECTION_MAPPING_ID_NOT_FOUND_IN_TEMPLATE,
                                    Map.of()));

            final var bottomButton = templateSectionMapping.getBottomButton();

            if (Objects.nonNull(bottomButton)) {
                final var titleString = MapperUtils.serializeToString(bottomButton.getTitle());

                final var transformedTitleString = handleBarsService.transform(titleString, workflowContext);

                templateSectionMapping.getBottomButton()
                        .setTitle(MapperUtils.deserialize(transformedTitleString, Title.class));
            }
        }

        final var nextAction = shadowV2ResponseConfig.accept(
                new BuildShadowV2ActionVisitor(template, workflowContext, screenMappingId, null, hopeLangService,
                        handleBarsService, false, expressionEvaluator), true);

        return TemplateInitResponse.builder()
                .workflowId(workflowId)
                .updatedTemplate(true)
                .template(clonedTemplate)
                .action(nextAction)
                .build();
    }

    private void handleSkipWorkflow(final StoredWorkflow storedWorkflow,
                                    final StoredWorkflowStep storedWorkflowStep,
                                    final StandardProfileScreenConfig standardProfileScreenConfig,
                                    final UserDetails userDetails,
                                    final boolean invalidateAllActions) {

        workflowStepService.skipWorkflowStep(storedWorkflowStep, standardProfileScreenConfig, userDetails,
                ActionType.SKIP_WORKFLOW, TransitionEvent.SKIP_WORKFLOW, invalidateAllActions);

        triggerWorkflowCompletionEvent(TransitionEvent.SKIP_WORKFLOW, storedWorkflow, storedWorkflowStep,
                standardProfileScreenConfig, null);

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedWorkflowStep.getWorkflowStepId());

        dynamicUiResponseService.sendSkipWorkflowResponseToUi(uiRequestContext, standardProfileScreenConfig,
                storedWorkflow.getWorkflowId());
    }

    private void handleSkipWorkflowStep(final StoredWorkflowStep storedWorkflowStep,
                                        final StandardProfileScreenConfig standardProfileScreenConfig,
                                        final UserDetails userDetails,
                                        final boolean invalidateAllActions) {

        workflowStepService.skipWorkflowStep(storedWorkflowStep, standardProfileScreenConfig, userDetails,
                ActionType.SKIP_WORKFLOW_STEP, TransitionEvent.SKIP_WORKFLOW_STEP, invalidateAllActions);
    }

    @Override
    public void autoSkipWorkflow(final StoredWorkflow workflowToAutoSkip,
                                 final UserDetails userDetails,
                                 final List<StoredWorkflowStep> workflowStepsToAutoSkip) {

        if (TransitionState.AUTO_SKIPPED.equals(workflowToAutoSkip.getCurrentState())) {
            return;
        }

        // No error handling on purpose, let upstream catch
        workflowStepsToAutoSkip.forEach(
                storedWorkflowStep -> workflowStepService.autoSkipWorkflowStep(storedWorkflowStep, userDetails, true));

        triggerWorkflowCompletionEvent(TransitionEvent.AUTO_SKIP_WORKFLOW, workflowToAutoSkip, null, null, null);
    }

    @Override
    public void abort(final AbortWorkflowRequest abortWorkflowRequest,
                      final UserDetails userDetails) {

        final var workflowId = abortWorkflowRequest.getWorkflowId();

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        WorkflowUtils.validateEntityAgainstWorkflow(storedWorkflow, abortWorkflowRequest.getEntityId(),
                abortWorkflowRequest.getEntityType());

        workflowStepService.abortWorkflowSteps(workflowId, userDetails);

        if (!TransitionState.FILTERED_STATES_FOR_CANCEL.contains(storedWorkflow.getCurrentState())) {
            triggerWorkflowCompletionEvent(TransitionEvent.ABORT, storedWorkflow, null, null, null);
        }
    }

    @Override
    public void abort(final List<String> workflowIds,
                      final boolean forceAbortTerminalStates,
                      final UserDetails userDetails) {

        // Currently short circuits for unknown wfid
        // May want to consider logging the exception and continuing

        workflowIds.stream()
                .map(workflowId -> Map.entry(workflowId, validateAndGetWorkflow(workflowId)))
                .filter(entry -> determineIfWorkflowStateCanBeAborted(entry.getValue(), forceAbortTerminalStates))
                .forEach(entry -> {
                    workflowStepService.abortWorkflowSteps(entry.getKey(), userDetails);
                    triggerWorkflowCompletionEvent(TransitionEvent.ABORT, entry.getValue(), null, null, null);
                });
    }

    private boolean determineIfWorkflowStateCanBeAborted(final StoredWorkflow storedWorkflow,
                                                         final boolean forceAbortSuccessStates) {
        return TransitionState.PENDING_STATES.contains(storedWorkflow.getCurrentState()) || (
                TransitionState.SUCCESS_STATES.contains(storedWorkflow.getCurrentState()) && forceAbortSuccessStates);
    }

    @Override
    public void discard(final String workflowId,
                        final UserDetails userDetails) {

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        workflowStepService.discardWorkflowSteps(workflowId, userDetails);

        if (!TransitionState.FILTERED_STATES_FOR_CANCEL.contains(storedWorkflow.getCurrentState())) {
            triggerWorkflowCompletionEvent(TransitionEvent.DISCARD, storedWorkflow, null, null, null);
        }
    }

    @Override
    public AccessTokenResponse resumeV2(final ResumeWorkflowV2Request resumeWorkflowV2Request,
                                        final RequestInfo requestInfo) {

        validateAndGetWorkflow(resumeWorkflowV2Request.getWorkflowId());

        final String accessToken = authNService.createAndGetAccessToken(resumeWorkflowV2Request.getWorkflowId(),
                resumeWorkflowV2Request.getUserReferenceId());

        return AccessTokenResponse.builder()
                .workflowId(resumeWorkflowV2Request.getWorkflowId())
                .accessToken(accessToken)
                .build();
    }

    @Override
    public StoredWorkflow validateAndGetWorkflow(final String workflowId) {
        return workflowRepository.select(workflowId)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.WORKFLOW_NOT_FOUND,
                        Map.of(Constants.MESSAGE, "Unable to find workflow for given inputs", Fields.workflowId,
                                workflowId)));
    }

    @Override
    public StoredWorkflow validateAndGetStoredWorkflowFromWorkflowStepId(final String workflowStepId) {
        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(workflowStepId);

        return validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());
    }

    @Override
    public void invalidateWorkflowFromProfileStepMappingId(final String workflowId,
                                                           final String profileStepMappingId,
                                                           final UserDetails userDetails) {

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        final var profileStep = profileService.getProfileStep(storedWorkflow.getProfileId(), profileStepMappingId);

        if (TransitionState.WORKFLOW_STATES_FROM_WHICH_WORKFLOW_STEPS_CAN_BE_INVALIDATED.contains(
                storedWorkflow.getCurrentState())) {

            workflowStepService.invalidateCurrentAndLaterWorkflowSteps(storedWorkflow.getWorkflowId(),
                    profileStep.getProfileStepId(), userDetails);

            if (storedWorkflow.getCurrentState() != TransitionState.IN_PROGRESS) {
                triggerWorkflowCompletionEvent(TransitionEvent.MOVE_TO_IN_PROGRESS, storedWorkflow, null);
            }
        } else {
            throw KaizenException.create(KaizenResponseCode.INVALIDATION_OF_WORKFLOW_STEP_NOT_ALLOWED,
                    Map.of(Fields.workflowId, workflowId, StoredProfileStep.Fields.profileStepMappingId,
                            profileStepMappingId));
        }
    }

    @Override
    public List<StoredWorkflow> getAllWorkflowsFromEntityId(final EntityType entityType,
                                                            final String entityId,
                                                            final String organization,
                                                            final String namespace) {

        final var profilesIds = profileService.get(organization, namespace, false)
                .stream()
                .map(Profile::getProfileId)
                .collect(Collectors.toSet());

        return workflowRepository.select(entityId, entityType, profilesIds);
    }

    @Override
    public List<StoredWorkflow> getAllWorkflowsFromEntityId(final EntityType entityType,
                                                            final String entityId,
                                                            final String organization,
                                                            final String namespace,
                                                            final String type) {

        final var profilesIds = profileService.get(organization, namespace, type, false)
                .stream()
                .map(Profile::getProfileId)
                .collect(Collectors.toSet());

        return workflowRepository.select(entityId, entityType, profilesIds);
    }

    @Override
    public List<StoredWorkflow> getStoredWorkflowFromAllShards(final Set<String> workflowStepIds) {

        final var workflowIds = workflowStepService.getWorkflowStepsFromAllShards(workflowStepIds)
                .stream()
                .map(StoredWorkflowStep::getWorkflowId)
                .collect(Collectors.toSet());
        return workflowRepository.selectFromAllShards(workflowIds);
    }

    /**
     * currentProfileScreenConfig and completedActionId should only be used for selecting dynamic screen
     */
    @Override
    @SneakyThrows
    public void handleWorkflowStepCompletion(final String completedWorkflowStepId,
                                             final String completedActionId,
                                             final StandardProfileScreenConfig currentProfileScreenConfig,
                                             final boolean isRetryable) {

        final var completedStoredWorkflowStep = workflowStepService.validateAndGetWorkflowStep(completedWorkflowStepId);

        final var workflowContext = workflowContextStore.getWorkflowContext(
                completedStoredWorkflowStep.getWorkflowId());

        final var storedWorkflow = validateAndGetWorkflow(completedStoredWorkflowStep.getWorkflowId());

        if (!NON_COMPLETED_STATES.contains(storedWorkflow.getCurrentState())) {
            return;
        }

        final var workflowContextNode = (ObjectNode) MapperUtils.convertToJsonNode(workflowContext);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var profileSteps = profile.getProfileSteps();

        final var workflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        final var workflowStepMap = workflowSteps.stream()
                .collect(Collectors.toMap(StoredWorkflowStep::getProfileStepId, Function.identity()));

        final var notStartedProfileSteps = profileSteps.stream()
                .filter(ps -> !workflowStepMap.containsKey(ps.getProfileStepId()))
                .toList();

        final var doesEvaluationRuleContainIntentOrCkv = notStartedProfileSteps.stream()
                .anyMatch(ps -> ps.getExecutionRule()
                        .contains(INTENT_JSONPATH) || ps.getExecutionRule()
                        .contains(COMPONENT_KIT_VERSION_JSONPATH));

        if (doesEvaluationRuleContainIntentOrCkv) {

            final var uiRequestContext = (ShadowV2UiRequestContext) workflowStepService.getUiRequestContext(
                    completedWorkflowStepId);

            if (Objects.isNull(uiRequestContext)) {
                throw KaizenException.create(KaizenResponseCode.UI_REQUEST_CONTEXT_NOT_FOUND,
                        Map.of("workflowId", workflowContext.getWorkflowId(), "completedActionId", completedActionId));
            }

            workflowContextNode.put(INTENT, uiRequestContext.getIntent());
            workflowContextNode.put(COMPONENT_KIT_VERSION, uiRequestContext.getComponentKitVersion());
        }

        final var notStartedExecutableProfileSteps = notStartedProfileSteps.stream()
                .filter(ps -> hopeLangService.evaluate(ps.getExecutionRule(), workflowContextNode))
                .toList();

        if (notStartedExecutableProfileSteps.isEmpty()) {

            evaluateEmptyNonStartedProfileSteps(storedWorkflow, completedStoredWorkflowStep, workflowSteps,
                    completedActionId, currentProfileScreenConfig, isRetryable);
        } else {

            evaluateNonStartedProfileSteps(completedStoredWorkflowStep, notStartedExecutableProfileSteps,
                    currentProfileScreenConfig, completedActionId, isRetryable);
        }
    }

    @Override
    public List<StoredWorkflow> getAllWorkflows(final LatestWorkflowRequest latestWorkflowRequest) {

        final var profileCriteria = List.of(ProfileCriteria.builder()
                .organization(latestWorkflowRequest.getOrganization())
                .namespace(latestWorkflowRequest.getNamespace())
                .type(latestWorkflowRequest.getType())
                .build());

        return fetchStoredWorkflowsAgainstProfileCriteria(profileCriteria, latestWorkflowRequest.getEntityId(),
                latestWorkflowRequest.getEntityType());
    }

    @Override
    public List<StoredWorkflow> getAllWorkflows(final LatestWorkflowRequestV2 latestWorkflowRequest) {

        return fetchStoredWorkflowsAgainstProfileCriteria(latestWorkflowRequest.getProfileCriteria(),
                latestWorkflowRequest.getEntityId(), latestWorkflowRequest.getEntityType());
    }

    private List<StoredWorkflow> fetchStoredWorkflowsAgainstProfileCriteria(final List<ProfileCriteria> profileCriteria,
                                                                            final String entityId,
                                                                            final EntityType entityType) {

        final var profiles = profileService.get(profileCriteria, false);

        if (Objects.isNull(profiles) || profiles.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                    Map.of("profileCriteria", MapperUtils.serializeToString(profileCriteria)));
        }

        final var profileIds = profiles.stream()
                .map(Profile::getProfileId)
                .collect(Collectors.toSet());

        return workflowRepository.select(entityId, entityType, profileIds);
    }

    @Override
    public LatestWorkflowResponse getLatestWorkflow(final LatestWorkflowRequest latestWorkflowRequest,
                                                    final Optional<TransitionState> transitionState) {

        final var workflows = getAllWorkflows(latestWorkflowRequest);

        final var profiles = profileService.get(latestWorkflowRequest.getOrganization(),
                latestWorkflowRequest.getNamespace(), latestWorkflowRequest.getType(), true);

        final var profileIdProfileMap = profiles.stream()
                .collect(Collectors.toMap(Profile::getProfileId, Function.identity()));

        if (Objects.isNull(workflows) || workflows.isEmpty()) {
            // Returning 204 if workflows not found
            return null;
        }

        final var latestStoredWorkflow = workflows.stream()
                .filter(storedWorkflow -> {
                    if (transitionState.isPresent()) {
                        return transitionState.get()
                                .equals(storedWorkflow.getCurrentState());
                    }
                    return true;
                })
                .max(Comparator.comparing(StoredWorkflow::getCreatedAt)
                        .thenComparing(StoredWorkflow::getLastUpdatedAt))
                .orElse(null);

        if (Objects.isNull(latestStoredWorkflow)) {
            return null;
        }

        final var latestProfile = profileIdProfileMap.get(latestStoredWorkflow.getProfileId());

        final var actionFailureResponseOptional = fetchWorkflowActionFailure(latestStoredWorkflow);

        return LatestWorkflowResponse.builder()
                .entityId(latestStoredWorkflow.getEntityId())
                .entityType(latestStoredWorkflow.getEntityType())
                .organization(latestProfile.getOrganization())
                .namespace(latestProfile.getNamespace())
                .workflowType(latestProfile.getType())
                .workflowVersion(latestProfile.getVersion())
                .workflowId(latestStoredWorkflow.getWorkflowId())
                .workflowState(State.valueOf(latestStoredWorkflow.getCurrentState()
                        .name()))
                .failureErrorCode(actionFailureResponseOptional.map(ActionFailureResponse::getActionFailureErrorCode)
                        .orElse(null))
                .failureReason(actionFailureResponseOptional.map(ActionFailureResponse::getReason)
                        .orElse(null))
                .workflowTag(latestStoredWorkflow.getTag())
                .createdAt(latestStoredWorkflow.getCreatedAt())
                .lastUpdatedAt(latestStoredWorkflow.getLastUpdatedAt())
                .build();
    }

    @Override
    public LatestWorkflowResponse getLatestWorkflow(final LatestWorkflowRequest latestWorkflowRequest) {

        return getLatestWorkflow(latestWorkflowRequest, Optional.empty());
    }

    @Override
    public List<StoredWorkflow> getAllWorkflowsOfSameEntityAndProfileFromWorkflowId(final String workflowId) {

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var storedWorkflows = workflowRepository.select(storedWorkflow.getEntityId(),
                storedWorkflow.getEntityType(), Set.of(profile.getProfileId()));

        if (Objects.isNull(storedWorkflows) || storedWorkflows.isEmpty()) {
            return List.of();
        }

        return storedWorkflows;
    }

    private void evaluateEmptyNonStartedProfileSteps(final StoredWorkflow storedWorkflow,
                                                     final StoredWorkflowStep completedStoredWorkflowStep,
                                                     final List<StoredWorkflowStep> storedWorkflowStepList,
                                                     final String completedActionId,
                                                     final StandardProfileScreenConfig currentProfileScreenConfig,
                                                     final boolean isRetryable) {

        completedStoredWorkflowStep.getCurrentState()
                .accept(new WorkflowStepServiceTransitionStateBaseVisitor() {

                    @Override
                    public Void visitSuccess(final StoredWorkflowStep data) {

                        final var storedWorkflowStep = findPendingWorkflowStep(storedWorkflowStepList);

                        if (storedWorkflowStep.isEmpty()) {

                            final var pseudoSuccessWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                                            storedWorkflow.getWorkflowId())
                                    .stream()
                                    .filter(sws -> TransitionState.PSEUDO_SUCCESS.equals(sws.getCurrentState()))
                                    .toList();

                            final var transitionEvent = pseudoSuccessWorkflowSteps.isEmpty()
                                                        ? TransitionEvent.SUCCESS
                                                        : TransitionEvent.PSEUDO_SUCCESS;

                            triggerWorkflowCompletionEvent(transitionEvent, storedWorkflow, completedStoredWorkflowStep,
                                    currentProfileScreenConfig, completedActionId);
                        }
                        return null;
                    }

                    @Override
                    public Void visitPseudoSuccess(final StoredWorkflowStep data) {

                        final var storedWorkflowStep = findPendingWorkflowStep(storedWorkflowStepList);

                        if (storedWorkflowStep.isEmpty()) {
                            triggerWorkflowCompletionEvent(TransitionEvent.PSEUDO_SUCCESS, storedWorkflow,
                                    completedStoredWorkflowStep, currentProfileScreenConfig, completedActionId);
                        }
                        return null;
                    }

                    @Override
                    public Void visitSkipped(final StoredWorkflowStep data) {

                        return visitSuccess(data);
                    }

                    @Override
                    public Void visitAutoSkipped(final StoredWorkflowStep data) {

                        return visitSuccess(data);
                    }

                    @Override
                    public Void visitFailure(final StoredWorkflowStep data) {

                        if (isRetryable) {

                            final var uiRequestContext = workflowStepService.getUiRequestContext(
                                    completedStoredWorkflowStep.getWorkflowStepId());

                            dynamicUiResponseService.sendFailureResponseToUi(completedActionId, uiRequestContext,
                                    currentProfileScreenConfig, null, false);
                            // show current screen (left button) only in assumeWorkflowStepFailed
                            // response with current screen (only left mapping id will be there as retry)
                        } else {
                            triggerWorkflowCompletionEvent(TransitionEvent.FAILURE, storedWorkflow,
                                    completedStoredWorkflowStep, currentProfileScreenConfig, completedActionId);
                        }
                        return null;
                    }
                }, completedStoredWorkflowStep);
    }

    private void evaluateNonStartedProfileSteps(final StoredWorkflowStep completedStoredWorkflowStep,
                                                final List<ProfileStep> notStartedExecutableProfileSteps,
                                                final StandardProfileScreenConfig currentProfileScreenConfig,
                                                final String completedActionId,
                                                final boolean isRetryable) {

        final var nextProfileStepToTrigger = notStartedExecutableProfileSteps.iterator()
                .next();

        final var uiRequestContext = workflowStepService.getUiRequestContext(
                completedStoredWorkflowStep.getWorkflowStepId());

        completedStoredWorkflowStep.getCurrentState()
                .accept(new WorkflowStepServiceTransitionStateBaseVisitor() {

                    @Override
                    public Void visitSuccess(final StoredWorkflowStep data) {

                        final var nextProfileScreenConfig = nextProfileStepToTrigger.getProfileScreenConfig()
                                .accept(getFirstStandardProfileScreenConfig)
                                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                                        Map.of()));

                        dynamicUiResponseService.sendSuccessResponseToUi(completedActionId, uiRequestContext,
                                currentProfileScreenConfig, nextProfileScreenConfig);

                        return null;
                    }

                    @Override
                    public Void visitPseudoSuccess(final StoredWorkflowStep data) {

                        return visitSuccess(data);
                    }

                    @Override
                    public Void visitSkipped(final StoredWorkflowStep data) {

                        final var nextProfileScreenConfig = nextProfileStepToTrigger.getProfileScreenConfig()
                                .accept(getFirstStandardProfileScreenConfig)
                                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                                        Map.of()));

                        dynamicUiResponseService.sendSkipWorkflowStepResponseToUi(completedActionId, uiRequestContext,
                                currentProfileScreenConfig, nextProfileScreenConfig);

                        return null;
                    }

                    @Override
                    public Void visitAutoSkipped(final StoredWorkflowStep data) {

                        // WF Steps would be in auto skipped when workflow itself is in AUTO_SKIPPED, hence treating it as success.
                        return visitSuccess(data);
                    }

                    @Override
                    public Void visitFailure(final StoredWorkflowStep data) {

                        final var nextProfileScreenConfig = nextProfileStepToTrigger.getProfileScreenConfig()
                                .accept(getFirstStandardProfileScreenConfig)
                                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                                        Map.of()));

                        dynamicUiResponseService.sendFailureResponseToUi(completedActionId, uiRequestContext,
                                currentProfileScreenConfig, nextProfileScreenConfig, !isRetryable);

                        return null;
                    }
                }, completedStoredWorkflowStep);

    }

    private void triggerWorkflowCompletionEvent(final TransitionEvent transitionEvent,
                                                final StoredWorkflow storedWorkflow,
                                                final StoredWorkflowStep storedWorkflowStep,
                                                final StandardProfileScreenConfig standardProfileScreenConfig,
                                                final String actionId) {

        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(StoredWorkflowStep.Fields.workflowId, storedWorkflow.getWorkflowId());
        stateMachineTransitionContext.put(UserDetails.class, Constants.PVCORE_SYSTEM_USER);
        if (Objects.nonNull(storedWorkflowStep)) {
            stateMachineTransitionContext.put(StoredWorkflowStep.Fields.workflowStepId,
                    storedWorkflowStep.getWorkflowStepId());
        }
        if (Objects.nonNull(standardProfileScreenConfig)) {
            stateMachineTransitionContext.put(StandardProfileScreenConfig.class, standardProfileScreenConfig);
        }
        if (Objects.nonNull(actionId)) {
            stateMachineTransitionContext.put(StoredAction.Fields.actionId, actionId);
        }
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(storedWorkflow.getWorkflowId())
                .build());

        workflowStateMachine.sendEvent(storedWorkflow.getCurrentState(), transitionEvent,
                stateMachineTransitionContext);
    }

    private void triggerMoveBackToPseudoSuccessEvent(final TransitionEvent transitionEvent,
                                                     final StoredWorkflow storedWorkflow,
                                                     final String workflowStepId,
                                                     final String actionId) {

        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(StoredWorkflowStep.Fields.workflowId, storedWorkflow.getWorkflowId());
        stateMachineTransitionContext.put(UserDetails.class, Constants.PVCORE_SYSTEM_USER);
        stateMachineTransitionContext.put(StoredWorkflowStep.Fields.workflowStepId, workflowStepId);
        stateMachineTransitionContext.put(StoredAction.Fields.actionId, actionId);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(storedWorkflow.getWorkflowId())
                .build());

        workflowStateMachine.sendEvent(storedWorkflow.getCurrentState(), transitionEvent,
                stateMachineTransitionContext);

    }

    @Override
    public void triggerWorkflowCompletionEvent(final TransitionEvent transitionEvent,
                                               final StoredWorkflow storedWorkflow,
                                               final StoredWorkflowStep storedWorkflowStep) {

        triggerWorkflowCompletionEvent(transitionEvent, storedWorkflow, storedWorkflowStep, null, null);
    }

    @Override
    public void triggerWorkflowCompletionEvent(final TransitionEvent transitionEvent,
                                               final StoredWorkflow storedWorkflow,
                                               final StoredWorkflowStep storedWorkflowStep,
                                               final String actionId) {

        triggerWorkflowCompletionEvent(transitionEvent, storedWorkflow, storedWorkflowStep, null, actionId);
    }

    @Override
    public void moveBackToPseudoSuccessOnManualRetryEvent(final String workflowId,
                                                          final String workflowStepId,
                                                          final String actionId) {
        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        triggerMoveBackToPseudoSuccessEvent(TransitionEvent.MANUAL_RETRY, storedWorkflow, workflowStepId, actionId);
        workflowStepService.moveToPseudoSuccessAndRetryAction(workflowStepId, actionId);

    }

    @Override
    public Optional<ActionFailureResponse> fetchWorkflowActionFailure(final StoredWorkflow storedWorkflow) {

        if (TransitionState.NON_SUCCESS_COMPLETED_STATES.contains(storedWorkflow.getCurrentState())) {

            final var workflowStepIds = workflowStepService.getWorkflowStepsFromWorkflowId(
                            storedWorkflow.getWorkflowId())
                    .stream()
                    .map(StoredWorkflowStep::getWorkflowStepId)
                    .collect(Collectors.toSet());

            final var actions = actionService.getActions(workflowStepIds);

            return actions.stream()
                    .filter(storedAction -> CompletionState.NON_SUCCESS_COMPLETED_STATES.contains(
                            storedAction.getCompletionState()))
                    .max(Comparator.comparing(StoredAction::getLastUpdatedAt))
                    .flatMap(actionService::getFailureDetailsFromFailedAction);

        }

        return Optional.empty();
    }


    @Override
    @SuppressWarnings("java:S1168")
    public Set<String> getAllWorkflowIds(final LatestWorkflowRequest workflowRequest) {

        final var profiles = profileService.get(workflowRequest.getOrganization(), workflowRequest.getNamespace(),
                workflowRequest.getType(), true);

        if (Objects.isNull(profiles) || profiles.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                    Map.ofEntries(Map.entry(StoredProfile.Fields.organization, workflowRequest.getOrganization()),
                            Map.entry(StoredProfile.Fields.namespace, workflowRequest.getNamespace()),
                            Map.entry(StoredProfile.Fields.type, workflowRequest.getType())));
        }

        final var profileIdProfileMap = profiles.stream()
                .collect(Collectors.toMap(Profile::getProfileId, Function.identity()));

        final var workflows = workflowRepository.select(workflowRequest.getEntityId(), workflowRequest.getEntityType(),
                profileIdProfileMap.keySet());

        if (Objects.isNull(workflows) || workflows.isEmpty()) {
            // Returning 204 if workflows not found
            return null;
        }

        return workflows.stream()
                .map(StoredWorkflow::getWorkflowId)
                .collect(Collectors.toSet());
    }

    @Override
    public WorkflowFilterResponse filterAllWorkflowIds(final FilterAllWorkflowIdsRequest filterAllWorkflowIdsRequest) {

        return filterAllWorkflowIds(filterAllWorkflowIdsRequest.getOrganization(),
                filterAllWorkflowIdsRequest.getNamespace(), filterAllWorkflowIdsRequest.getType(),
                filterAllWorkflowIdsRequest.getFromTime(), filterAllWorkflowIdsRequest.getToTime(),
                List.of(filterAllWorkflowIdsRequest.getState()));
    }

    @Override
    public WorkflowFilterResponse filterAllWorkflowIds(final FilterAllWorkflowIdsV2Request filterAllWorkflowIdsRequest) {

        return filterAllWorkflowIds(filterAllWorkflowIdsRequest.getOrganization(),
                filterAllWorkflowIdsRequest.getNamespace(), filterAllWorkflowIdsRequest.getType(),
                filterAllWorkflowIdsRequest.getFromTime(), filterAllWorkflowIdsRequest.getToTime(),
                filterAllWorkflowIdsRequest.getWorkflowStates());
    }

    private WorkflowFilterResponse filterAllWorkflowIds(final String organization,
                                                        final String namespace,
                                                        final String type,
                                                        final LocalDateTime fromTime,
                                                        final LocalDateTime toTime,
                                                        final List<State> workflowStatesToFilter) {
        final var profiles = profileService.get(organization, namespace, type, false);

        if (Utils.isNullOrEmpty(profiles)) {
            throw KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                    Map.ofEntries(Map.entry(StoredProfile.Fields.organization, organization),
                            Map.entry(StoredProfile.Fields.namespace, namespace),
                            Map.entry(StoredProfile.Fields.type, type)));
        }

        final var profileIds = profiles.stream()
                .map(Profile::getProfileId)
                .collect(Collectors.toSet());

        final var workflows = workflowRepository.select(fromTime, toTime, workflowStatesToFilter, profileIds);

        if (Utils.isNullOrEmpty(workflows)) {
            log.info("No workflow found with filter parameters {} {} {} {} {} {}", organization, namespace, type,
                    fromTime, toTime, workflowStatesToFilter);
            return WorkflowFilterResponse.builder()
                    .workflowInfoList(List.of())
                    .build();
        }

        return WorkflowFilterResponse.builder()
                .workflowInfoList(workflows.stream()
                        .map(Utils::convertToWorkflowInfo)
                        .toList())
                .build();
    }

    private void createWorkflowEntry(final String workflowId,
                                     final WorkflowInitRequestContext workflowInitRequestContext,
                                     final UserDetails userDetails) {

        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(WorkflowInitRequestContext.class, workflowInitRequestContext);
        stateMachineTransitionContext.put(Fields.entityId, workflowInitRequestContext.getEntityId());
        stateMachineTransitionContext.put(Fields.workflowId, workflowId);
        stateMachineTransitionContext.put(UserDetails.class, userDetails);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(workflowInitRequestContext.getEntityId())
                .build());

        workflowStateMachine.sendEvent(TransitionState.CREATED, TransitionEvent.CREATE_ENTRY,
                stateMachineTransitionContext);
    }

    private Optional<StoredWorkflowStep> findPendingWorkflowStep(final List<StoredWorkflowStep> storedWorkflowStepList) {

        return storedWorkflowStepList.stream()
                .filter(ws -> TransitionState.PENDING_STATES.contains(ws.getCurrentState()))
                .findFirst();
    }

    @Override
    public void purge(final PurgeWorkflowRequest purgeWorkflowRequest,
                      final UserDetails userDetails) {
        // Fetch workflowSteps
        // For each workflowStep, fetch actions
        // For each action, fetch action details
        // Update each action detail data using visitor
        // Update action status to invalidated
        // Update workflow status to purged

        final var workflowId = purgeWorkflowRequest.getWorkflowId();

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        WorkflowUtils.validateEntityAgainstWorkflow(storedWorkflow, purgeWorkflowRequest.getEntityId(),
                purgeWorkflowRequest.getEntityType());

        //if already purged we will return as there are no operations to be performed on this workflow.
        if (storedWorkflow.getCurrentState() == TransitionState.PURGED) {
            return;
        }

        triggerWorkflowCompletionEvent(TransitionEvent.PURGE, storedWorkflow, null, null, null);
    }

    private List<ProfileStep> getNotStartedExecutableProfileSteps(final String workflowId,
                                                                  final JsonNode workflowContextNode) {

        final var storedWorkflow = validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var profileSteps = profile.getProfileSteps();

        final var storedWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(workflowId);

        final var profileStepIdToStoredWorkflowMap = storedWorkflowSteps.stream()
                .collect(Collectors.toMap(StoredWorkflowStep::getProfileStepId, Function.identity()));

        return profileSteps.stream()
                .filter(ps -> !profileStepIdToStoredWorkflowMap.containsKey(ps.getProfileStepId()))
                .filter(ps -> hopeLangService.evaluate(ps.getExecutionRule(), workflowContextNode))
                .toList();
    }
}
