package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.clients.internal.CallbackClient;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowClientCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowMessage;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ClientWorkflowCallbackActor extends BaseActor<WorkflowMessage> {

    private final CallbackClient callbackClient;

    @Inject
    protected ClientWorkflowCallbackActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                          final ConnectionRegistry connectionRegistry,
                                          final ObjectMapper mapper,
                                          final RetryStrategyFactory retryStrategyFactory,
                                          final ExceptionHandlingFactory exceptionHandlingFactory,
                                          final CallbackClient callbackClient) {
        super(ActorType.CLIENT_WORKFLOW_CALLBACK, actorConfigMap.get(ActorType.CLIENT_WORKFLOW_CALLBACK),
                connectionRegistry, mapper, retryStrategyFactory, exceptionHandlingFactory, WorkflowMessage.class);
        this.callbackClient = callbackClient;
    }

    @Override
    protected boolean handleMessage(final WorkflowMessage message) {

        callbackClient.sendCallback(WorkflowClientCallbackMessage.builder()
                .workflowId(message.getWorkflowId())
                .failureErrorCode(message.getFailureErrorCode())
                .build());
        return true;
    }
}
