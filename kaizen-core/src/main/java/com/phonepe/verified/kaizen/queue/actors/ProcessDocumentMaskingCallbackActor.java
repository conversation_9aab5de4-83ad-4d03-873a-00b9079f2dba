package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.drishti.models.responses.Status;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.foxtrot.events.subevents.CallbackReceivedFromClientSubEventType;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.queue.messages.ProcessDocumentMaskingCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.eventingestion.CallbackReceivedFromDrishtiMessage;
import com.phonepe.verified.kaizen.services.DrishtiService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ProcessDocumentMaskingCallbackActor extends BaseActor<ProcessDocumentMaskingCallbackMessage> {

    private final DrishtiService drishtiService;

    private final EventIngestionActor eventIngestionActor;

    @Inject
    protected ProcessDocumentMaskingCallbackActor(final ObjectMapper mapper,
                                                  final ConnectionRegistry connectionRegistry,
                                                  final Map<ActorType, ActorConfig> actorConfigMap,
                                                  final RetryStrategyFactory retryStrategyFactory,
                                                  final ExceptionHandlingFactory exceptionHandlingFactory,
                                                  final DrishtiService drishtiService,
                                                  final EventIngestionActor eventIngestionActor) {

        super(ActorType.PROCESS_DOCUMENT_MASKING_DRISHTI_CALLBACK,
                actorConfigMap.get(ActorType.PROCESS_DOCUMENT_MASKING_DRISHTI_CALLBACK), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, ProcessDocumentMaskingCallbackMessage.class);
        this.drishtiService = drishtiService;
        this.eventIngestionActor = eventIngestionActor;
    }

    @Override
    @SneakyThrows
    public boolean handleMessage(final ProcessDocumentMaskingCallbackMessage processDocumentMaskingCallbackMessage) {

        final var requestId = processDocumentMaskingCallbackMessage.getRequestId();

        final var actionId = processDocumentMaskingCallbackMessage.getActionId();

        final var drishtiResponse = drishtiService.fetchMaskedDocumentStatus(requestId);

        log.info(String.format("Callback message received from Drishti for the request-id: %s and action-id: %s",
                requestId, actionId));

        eventIngestionActor.publish(EventIngestionMessage.builder()
                .actionId(actionId)
                .eventType(EventType.CALLBACK_RECEIVED_FROM_DRISHTI)
                .callbackReceivedFromDrishtiMessage(CallbackReceivedFromDrishtiMessage.builder()
                        .success(drishtiResponse.getStatus()
                                .equals(Status.SUCCEED))
                        .callbackReceivedFromClientSubEventType(
                                CallbackReceivedFromClientSubEventType.DOCUMENT_MASKING_CALLBACK_RECEIVED_FROM_DRISHTI_EVENT)
                        .build())
                .build());

        return true;
    }
}
