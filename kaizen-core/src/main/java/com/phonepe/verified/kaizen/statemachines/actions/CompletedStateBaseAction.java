package com.phonepe.verified.kaizen.statemachines.actions;

import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.queue.messages.eventingestion.ActionUpdateEventIngestionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@AllArgsConstructor
public abstract class CompletedStateBaseAction extends BaseTransitionAction<String, String> {

    private final ActionService actionService;

    private final boolean shouldTriggerHandshake;

    private final ActionRepository actionRepository;

    private final AutoRetryActionService autoRetryActionService;

    private final Provider<EventIngestionActor> eventIngestionActorProvider;

    private final Provider<AutoRetryActionActor> autoRetryActionActorProvider;

    private final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider;

    private final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider;

    @Override
    @SneakyThrows
    protected void performTransition(final StateContext<String, String> stateContext) {

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        final var actionId = stateContext.getExtendedState()
                .get(Fields.actionId, String.class);

        Objects.requireNonNull(userDetails);
        Objects.requireNonNull(actionId);

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var previousCompletionState = storedAction.getCompletionState();

        final var previousState = storedAction.getCurrentState();

        final var previousEvent = storedAction.getCurrentEvent();

        preTransition(storedAction, stateContext);

        storedAction.setCurrentState(stateContext.getTarget()
                .getId());
        storedAction.setCurrentEvent(stateContext.getEvent());
        storedAction.setCompletionState(getCompletionState());
        storedAction.setCompleted(true);
        storedAction.setFailureErrorCode(getFailureErrorCode(stateContext, storedAction));
        storedAction.setLastUpdatedBy(userDetails.getUserId());
        storedAction.setLastUpdaterType(UpdaterType.valueOf(userDetails.getUserType()
                .name()));

        actionRepository.save(storedAction, savedAction -> {

            transition(storedAction, stateContext);

            updateActionInWorkflowContextStore(storedAction);

            return savedAction;
        });

        final var eventType = getEventTypeForTransition();

        eventIngestionActorProvider.get()
                .publish(EventIngestionMessage.builder()
                        .actionId(actionId)
                        .eventType(eventType)
                        .actionUpdateEventIngestionMessage(ActionUpdateEventIngestionMessage.builder()
                                .previousState(previousState)
                                .previousEvent(previousEvent)
                                .build())
                        .build());

        if (CompletionState.FAILURE == storedAction.getCompletionState()
                && autoRetryActionService.checkIfAutoRetryAvailable(actionId)) {
            autoRetryActionActorProvider.get()
                    .publish(StepActionMessage.builder()
                            .actionId(actionId)
                            .build());
            return;
        }

        // todo:: We need to think about edge-case --> RMQ down

        if (shouldTriggerHandshake && CompletionState.PSEUDO_SUCCESS != previousCompletionState) {
            publishActionCompletion(actionId);
        }

        if (CompletionState.PSEUDO_SUCCESS == previousCompletionState
                && CompletionState.PSEUDO_SUCCESS != storedAction.getCompletionState()) {
            publishPseudoSuccessActionCompletion(actionId);
        }

        postTransition(storedAction);
    }

    @SneakyThrows
    private void publishActionCompletion(final String actionId) {

        handleActionCompletionActorProvider.get()
                .publish(StepActionMessage.builder()
                        .actionId(actionId)
                        .build());
    }

    @SneakyThrows
    private void publishPseudoSuccessActionCompletion(final String actionId) {

        handlePseudoSuccessActionCompletionActorProvider.get()
                .publish(StepActionMessage.builder()
                        .actionId(actionId)
                        .build());
    }

    private EventType getEventTypeForTransition() {
        return EventType.ACTION_COMPLETION;
    }

    protected abstract CompletionState getCompletionState();

    protected abstract ActionFailureErrorCode getFailureErrorCode(final StateContext<String, String> stateContext,
                                                                  final StoredAction storedAction);

    protected abstract void updateActionInWorkflowContextStore(final StoredAction storedAction);

    protected void preTransition(final StoredAction storedAction,
                                 final StateContext<String, String> stateContext) {
        // NOOP
    }

    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {
        // NOOP
    }

    protected void postTransition(final StoredAction storedAction) {
        // NOOP
    }
}
