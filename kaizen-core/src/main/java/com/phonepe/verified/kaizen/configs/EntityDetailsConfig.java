package com.phonepe.verified.kaizen.configs;

import com.phonepe.verified.kaizen.models.requests.profiles.ProfileCriteria;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class EntityDetailsConfig {

    @Valid
    private Map<String, Map<String, Map<String, @NotNull @NotEmpty List<String>>>> organizationNamespaceProfileCriteriaMap = new HashMap<>();

    public List<ProfileCriteria> getEntityDetailsProfileCriteriaList(final String organization,
                                                                     final String namespace,
                                                                     final String profileType) {
        return organizationNamespaceProfileCriteriaMap.getOrDefault(organization, Map.of())
                .getOrDefault(namespace, Map.of())
                .getOrDefault(profileType, List.of())
                .stream()
                .map(type -> ProfileCriteria.builder()
                        .namespace(namespace)
                        .organization(organization)
                        .type(type)
                        .build())
                .toList();
    }


}
