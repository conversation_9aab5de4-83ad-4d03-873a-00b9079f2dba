package com.phonepe.verified.kaizen.statemachines.actions.consent;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.ConsentActionContext;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigForGivenAction;
import com.phonepe.verified.kaizen.services.visitors.StandardProfileScreenConfigFromProfileStepVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.SuccessStateBaseAction;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.commands.UiRequestContextCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.ConsentActionMetaData;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.aerospike.keys.UiRequestContextKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredConsentActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "completeConsentAction")
public class CompleteConsentAction extends SuccessStateBaseAction {

    public static final String CONSENT_GRANTED = "consentGranted";

    public static final String CONSENT_PRIVACY_POLICY_URL = "consentPrivacyPolicyUrl";

    private final ProfileService profileService;

    private final HandleBarsService handleBarsService;

    private final WorkflowStepService workflowStepService;

    private final UiRequestContextCommand uiRequestContextCommand;

    private final ActionMetadataRepository actionMetadataRepository;

    private final ActionMetadataStoreCommand actionMetadataStoreCommand;

    @Inject
    public CompleteConsentAction(final ActionService actionService,
                                 final ActionRepository actionRepository,
                                 final AutoRetryActionService autoRetryActionService,
                                 final Provider<AutoRetryActionActor> autoRetryActionActorProvider,
                                 final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider,
                                 final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider,
                                 final ProfileService profileService,
                                 final HandleBarsService handleBarsService,
                                 final WorkflowStepService workflowStepService,
                                 final ActionMetadataRepository actionMetadataRepository,
                                 final Provider<WorkflowContextStore> workflowContextStore,
                                 final Provider<EventIngestionActor> eventIngestionActorProvider,
                                 final UiRequestContextCommand uiRequestContextCommand,
                                 final ActionMetadataStoreCommand actionMetadataStoreCommand) {
        super(actionService, actionRepository, autoRetryActionService, autoRetryActionActorProvider,
                handleActionCompletionActorProvider, handlePseudoSuccessActionCompletionActorProvider,
                workflowContextStore, eventIngestionActorProvider);
        this.profileService = profileService;
        this.handleBarsService = handleBarsService;
        this.workflowStepService = workflowStepService;
        this.actionMetadataRepository = actionMetadataRepository;
        this.uiRequestContextCommand = uiRequestContextCommand;
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
    }

    @Override
    public void transition(final StoredAction storedAction,
                           final StateContext<String, String> stateContext) {

        final var uiRequestContext = uiRequestContextCommand.get(UiRequestContextKey.builder()
                .workflowStepId(storedAction.getWorkflowStepId())
                .build());

        final var requestInfo = uiRequestContext.getRequestInfo();

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var profileScreenConfig = profileStep.getProfileScreenConfig();

        final var standardProfileScreenConfig = profileScreenConfig.accept(
                        new StandardProfileScreenConfigFromProfileStepVisitor(storedAction.getScreenMappingId()))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                        Map.of(Fields.screenMappingId, storedAction.getScreenMappingId())));

        final var standardStepActionConfig = standardProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Fields.actionMappingId, storedAction.getActionMappingId())));

        final var consentActionContext = (ConsentActionContext) standardStepActionConfig.getStepActionContext();

        final var actionMetadata = (ConsentActionMetaData) actionMetadataStoreCommand.get(
                ActionMetadataStoreKey.builder()
                        .actionId(storedAction.getActionId())
                        .build());

        final var shadowV2UiRequestContext = (ShadowV2UiRequestContext) workflowStepService.getUiRequestContext(
                storedAction.getWorkflowStepId());

        consentActionContext.getConsentActionDataList()
                .stream()
                .map(consentActionData -> StoredConsentActionMetadata.builder()
                        .actionId(storedAction.getActionId())
                        .consentDetailsUrl(handleBarsService.transform(consentActionData.getConsentDetailsUrl(),
                                shadowV2UiRequestContext))
                        .consentType(consentActionData.getConsentType())
                        .consentGranted(Boolean.toString(true))
                        .sourceType(requestInfo.getSourceType())
                        .sourceVersion(requestInfo.getSourceVersion())
                        .sourcePlatform(requestInfo.getSourcePlatform())
                        .consentGrantedBy(requestInfo.getAuthorizeName())
                        .language((Objects.nonNull(actionMetadata))
                                  ? actionMetadata.getLanguage()
                                  : null)
                        .build())
                .forEach(actionMetadataRepository::save);
    }

}
