package com.phonepe.verified.kaizen.storage.mariadb.entities.converters;

import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.Objects;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class GetTemplateConfigConverter implements AttributeConverter<GetTemplateConfig, String> {

    @Override
    public String convertToDatabaseColumn(final GetTemplateConfig getTemplateConfig) {
        return Objects.isNull(getTemplateConfig)
               ? null
               : MapperUtils.serializeToString(getTemplateConfig);
    }

    @Override
    public GetTemplateConfig convertToEntityAttribute(final String text) {
        return Objects.isNull(text)
               ? null
               : MapperUtils.deserialize(text, GetTemplateConfig.class);
    }

}
