package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.platform.clockwork.model.SchedulingResponse;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.services.SchedulingService;
import com.phonepe.verified.kaizen.utils.ClockworkUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.dropwizard.util.Duration;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ClockworkSchedulingServiceImpl implements SchedulingService {

    // Need to replace all calls to Clockwork Client with this
    private final DataProvider<KaizenConfig> appConfigDataProvider;

    private final ClockworkClient clockworkClient;

    private final EventIngestionCommand eventIngestionCommand;

    @Override
    public boolean scheduleClockworkCallback(final Object payload,
                                             final String callbackPath,
                                             final Duration duration,
                                             final String workflowId,
                                             final String actionMappingId) {

        final var schedulingRequest = ClockworkUtils.getActionMessageSchedulingRequest(
                MapperUtils.serializeToString(payload), appConfigDataProvider.getData()
                        .getBaseUrl(), callbackPath, ClockworkUtils.getSchedulingDelayDate(duration));

        try {
            final var clockworkSchedulingResponse = clockworkClient.schedule(schedulingRequest, Constants.APP_NAME);

            if (!clockworkSchedulingResponse.isSuccess()) {

                log.error("Received error code {} from Clockwork while scheduling callback",
                        clockworkSchedulingResponse.getErrorCode());

                final var jobId = Optional.ofNullable(clockworkSchedulingResponse.getData())
                        .map(SchedulingResponse::getJobId)
                        .orElse(null);

                eventIngestionCommand.ingestClockworkRequestScheduled(jobId, callbackPath, duration, false,
                        clockworkSchedulingResponse.getErrorCode()
                                .name(), workflowId, actionMappingId);

                return false;
            } else {

                eventIngestionCommand.ingestClockworkRequestScheduled(clockworkSchedulingResponse.getData()
                        .getJobId(), callbackPath, duration, true, null, workflowId, actionMappingId);

                return true;
            }
        } catch (final Exception e) {

            log.error("Scheduling Clockwork Callback for Workflow {} failed!", workflowId);
            return false;
        }
    }
}
