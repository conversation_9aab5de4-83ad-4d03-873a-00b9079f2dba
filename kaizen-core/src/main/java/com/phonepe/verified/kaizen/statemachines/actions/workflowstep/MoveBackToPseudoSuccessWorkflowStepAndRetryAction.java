package com.phonepe.verified.kaizen.statemachines.actions.workflowstep;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class MoveBackToPseudoSuccessWorkflowStepAndRetryAction extends UpdateWorkflowStepBaseAction {

    private final ActionService actionService;

    @Inject
    public MoveBackToPseudoSuccessWorkflowStepAndRetryAction(final ActionService actionService,
                                                             final ProfileService profileService,
                                                             final WorkflowService workflowService,
                                                             final WorkflowStepService workflowStepService,
                                                             final EventIngestionCommand eventIngestionCommand,
                                                             final WorkflowStepRepository workflowStepRepository,
                                                             final Provider<WorkflowContextStore> workflowContextStore) {
        super(profileService, workflowService, workflowStepService, eventIngestionCommand, workflowStepRepository,
                workflowContextStore);
        this.actionService = actionService;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final TransitionState previousState,
                                  final StoredWorkflowStep storedWorkflow,
                                  final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var workflowStepId = stateContext.getExtendedState()
                .get(Fields.workflowStepId, String.class);

        final var actionId = stateContext.getExtendedState()
                .get(StoredAction.Fields.actionId, String.class);

        Objects.requireNonNull(workflowStepId);
        Objects.requireNonNull(actionId);

        actionService.triggerActionsViaManualRetry(workflowStepId, actionId);
    }
}
