package com.phonepe.verified.kaizen.services.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.DependencyConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.PseudoSuccessActionFailureWorkflowBehaviourConfigType;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.CompletionState.CompletionStateVisitor;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DependencyManagementService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.CompletionStateBaseVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigForGivenAction;
import com.phonepe.verified.kaizen.services.visitors.ProfileStepCompletionStatusVisitor;
import com.phonepe.verified.kaizen.services.visitors.ShouldFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfigVisitor;
import com.phonepe.verified.kaizen.services.visitors.StandardProfileScreenConfigFromProfileStepVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import io.dropwizard.util.Strings;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class DependencyManagementServiceImpl implements DependencyManagementService {

    private final ActionService actionService;

    private final HandleBarsService handleBarsService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowStepService workflowStepService;

    private final WorkflowContextStore workflowContextStore;

    private final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor;


    @Override
    @SuppressWarnings("java:S3776")
    public void handlePseudoSuccessActionCompletion(final String actionId) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        if (TransitionState.PSEUDO_SUCCESS == storedWorkflowStep.getCurrentState()) {

            identifyWorkflowStepCompletionStateAndTriggerEvent(storedWorkflowStep, actionId);
        }

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflow.getWorkflowId());

        final var workflowContextJsonNode = MapperUtils.convertToJsonNode(workflowContext);

        final var storedWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        final var allStoredWorkflowStepIds = storedWorkflowSteps.stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        final var actionMappingIdStoredActionMap = actionService.getActionMappingIdStoredActionMap(
                allStoredWorkflowStepIds);

        // Identify the root node of the dependency tree
        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var allStandardProfileScreenConfigs = profileService.getAllStandardProfileScreenConfigs(profile);

        final var allStandardStepActionConfig = ActionService.getAllStandardStepActionConfig(
                allStandardProfileScreenConfigs);

        final var rootNodeStepActionConfig = getDependencyRootNodeStepActionConfig(allStandardStepActionConfig,
                storedAction.getActionMappingId(), workflowContextJsonNode);

        // Run BFS on the dependency tree defined by the root node
        final var queue = new LinkedList<StandardStepActionConfig>();

        queue.push(rootNodeStepActionConfig);

        while (!queue.isEmpty()) {

            final var currentStepActionConfig = queue.pop();

            final var currentActionOptional = actionMappingIdStoredActionMap.getOrDefault(
                    currentStepActionConfig.getActionMappingId(), Optional.empty());

            if (currentActionOptional.isEmpty()) {
                continue;
            }

            final var currentAction = currentActionOptional.get();

            final var exitFlag = currentAction.getCompletionState()
                    .accept(new CompletionStateVisitor<Boolean, Void>() {

                        @Override
                        public Boolean visitSuccess(final Void data) {

                            return addChildNodesToQueue();
                        }

                        @Override
                        public Boolean visitFailure(final Void data) {

                            return addChildNodesToQueue();
                        }

                        /**
                         * Fetch Parent Action
                         * if current Action is anybody's fallback
                         * * (X = Action For Which Fallback Is Current Action)
                         * * if X succeeded, then perform transition EXECUTION_NOT_REQUIRED
                         * * else if X failed and parent is same as X, then perform transition
                         * TRIGGER_FALLBACK
                         * * else if X failed and parent is different from X, then evaluate
                         * based on parent action
                         * * else, evaluate based on parent action
                         * else,
                         * evaluate based on parent action
                         */
                        @Override
                        public Boolean visitPseudoSuccess(final Void data) {

                            final var dependentActionMappingId = currentStepActionConfig.getDependencyConfig()
                                    .accept(getDependentActionMappingIdDependencyConfigVisitor,
                                            workflowContextJsonNode);

                            final var parentAction = actionMappingIdStoredActionMap.getOrDefault(
                                            dependentActionMappingId, Optional.empty())
                                    .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                                            Map.of(Fields.actionMappingId, dependentActionMappingId)));

                            // Here we're evaluating the first action that meets fallback criteria for current action
                            final var actionConfigOfActionForWhichFallbackIsCurrentActionOptional = allStandardStepActionConfig.stream()
                                    .filter(standardStepActionConfig -> isActionValidForFallback(currentAction,
                                            standardStepActionConfig, workflowContextJsonNode))
                                    .findFirst();

                            // Assumption:: Current action is fallback of one and only one action
                            if (actionConfigOfActionForWhichFallbackIsCurrentActionOptional.isPresent()) {

                                final var actionConfigOfActionForWhichFallbackIsCurrentAction = actionConfigOfActionForWhichFallbackIsCurrentActionOptional.get();

                                if (actionMappingIdStoredActionMap.getOrDefault(
                                                actionConfigOfActionForWhichFallbackIsCurrentAction.getActionMappingId(),
                                                Optional.empty())
                                        .map(StoredAction::getCompletionState)
                                        .filter(CompletionState.SUCCESS::equals)
                                        .isPresent()) {

                                    actionService.triggerEvent(currentAction.getActionId(),
                                            currentAction.getActionType(), currentAction.getStateMachineVersion(),
                                            Events.EXECUTION_NOT_REQUIRED, Constants.EMPTY_TRANSITION_CONTEXT,
                                            Constants.PVCORE_SYSTEM_USER, null);

                                    return true;

                                } else if (actionMappingIdStoredActionMap.getOrDefault(
                                                actionConfigOfActionForWhichFallbackIsCurrentAction.getActionMappingId(),
                                                Optional.empty())
                                        .map(StoredAction::getCompletionState)
                                        .filter(CompletionState.FAILURE::equals)
                                        .isPresent() && parentAction.getActionMappingId()
                                        .equals(actionConfigOfActionForWhichFallbackIsCurrentAction.getActionMappingId())) {

                                    actionService.triggerEvent(currentAction.getActionId(),
                                            currentAction.getActionType(), currentAction.getStateMachineVersion(),
                                            Events.TRIGGER_FALLBACK, Constants.EMPTY_TRANSITION_CONTEXT,
                                            Constants.PVCORE_SYSTEM_USER, null);

                                    return true;
                                }
                            }

                            final var auxiliaryStateMachineTransitionMap = new HashMap<>();

                            final var transitionEvent = parentAction.getCompletionState()
                                    .accept(new CompletionStateBaseVisitor<String, Void>() {

                                        @Override
                                        public String visitSuccess(final Void data) {

                                            if (actionConfigOfActionForWhichFallbackIsCurrentActionOptional.isPresent()) {
                                                return Events.TRIGGER_FALLBACK;
                                            }
                                            return Events.PARENT_PSEUDO_ACTION_SUCCEED;
                                        }

                                        @Override
                                        public String visitFailure(final Void data) {

                                            auxiliaryStateMachineTransitionMap.put(ActionFailureErrorCode.class,
                                                    ActionFailureErrorCode.DEPENDENT_ACTION_FAILED);

                                            return Events.PARENT_PSEUDO_ACTION_FAILED;
                                        }
                                    }, null);

                            actionService.triggerEvent(currentAction.getActionId(), currentAction.getActionType(),
                                    currentAction.getStateMachineVersion(), transitionEvent,
                                    Constants.EMPTY_TRANSITION_CONTEXT, Constants.PVCORE_SYSTEM_USER,
                                    auxiliaryStateMachineTransitionMap);
                            return true;
                        }

                        // Because the action under consideration is in non-considerable state for performing
                        // BFS on its child nodes. Just returning false to continue BFS with other nodes
                        @Override
                        public Boolean visitInProgress(final Void data) {
                            return false;
                        }

                        @Override
                        public Boolean visitNotStarted(final Void data) {
                            return false;
                        }

                        @Override
                        public Boolean visitInvalidated(final Void data) {
                            return false;
                        }

                        @Override
                        public Boolean visitSkipped(final Void data) {
                            return false;
                        }

                        @Override
                        public Boolean visitAborted(final Void data) {
                            return false;
                        }

                        @Override
                        public Boolean visitDiscarded(final Void data) {
                            return false;
                        }

                        private Boolean addChildNodesToQueue() {

                            allStandardStepActionConfig.stream()
                                    .filter(standardStepActionConfig -> isDependentOnActionMappingId(
                                            standardStepActionConfig, currentStepActionConfig.getActionMappingId(),
                                            workflowContextJsonNode))
                                    .forEach(queue::push);
                            return false;

                        }
                    }, null);

            if (Boolean.TRUE.equals(exitFlag)) {
                return;
            }
        }
    }

    private boolean isActionValidForFallback(final StoredAction currentAction,
                                             final StandardStepActionConfig actionToEvaluateForFallback,
                                             final JsonNode workflowContextJsonNode) {

        return Optional.ofNullable(actionToEvaluateForFallback.getDependencyConfig())
                .map(DependencyConfig::getFallbackConfig)
                .map(fallbackConfig -> {
                    final var fallbackActionMappingId = handleBarsService.transform(
                            fallbackConfig.getFallbackActionMappingId(), workflowContextJsonNode);

                    if (currentAction.getActionMappingId()
                            .equals(fallbackActionMappingId)) {
                        return hopeLangService.evaluate(fallbackConfig.getFallbackRule(), workflowContextJsonNode);
                    }

                    return false;
                })
                .orElse(false);
    }

    @Override
    public void handlePseudoSuccessWorkflowStepCompletion(final String workflowStepId,
                                                          final String actionId) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(workflowStepId);

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        if (TransitionState.FAILURE == storedWorkflowStep.getCurrentState()
                && TransitionState.STATES_FROM_WHICH_FAILURE_CAN_BE_TRIGGERED.contains(storedWorkflow.getCurrentState())
                && isFallbackActionFailedOrNotPresent(storedWorkflowStep) &&
                shouldFailWorkflowBasedOnPseudoSuccessActionFailureWorkflowBehaviourConfig(storedWorkflowStep, actionId)
                        == PseudoSuccessActionFailureWorkflowBehaviourConfigType.ALWAYS_FAIL_WORKFLOW) {

            workflowService.triggerWorkflowCompletionEvent(TransitionEvent.FAILURE, storedWorkflow, storedWorkflowStep,
                    actionId);
            return;
        }

        if (TransitionState.PSEUDO_SUCCESS != storedWorkflow.getCurrentState()) {
            return;
        }

        final var allWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        final var isPseudoSuccessPresentInWorkflowSteps = allWorkflowSteps.stream()
                .anyMatch(sws -> TransitionState.PSEUDO_SUCCESS == sws.getCurrentState());

        if (isPseudoSuccessPresentInWorkflowSteps) {
            return;
        }

        /**
         * When the current workflow step has failed
         * 1. Other workflow step is in PSEUDO_SUCCESS state, then do nothing
         * 2. No other step is in PSEUDO_SUCCESS and based on output of shouldFailWorkflowBasedOnPseudoSuccessActionFailureWorkflowBehaviourConfig
         *    a. Workflow cannot be failed, then move the workflow to IN_PROGRESS state
         *    b. Workflow can be failed then move the workflow to FAILED state
         */
        if (TransitionState.FAILURE == storedWorkflowStep.getCurrentState() && Objects.nonNull(actionId) &&
                shouldFailWorkflowBasedOnPseudoSuccessActionFailureWorkflowBehaviourConfig(storedWorkflowStep, actionId)
                        == PseudoSuccessActionFailureWorkflowBehaviourConfigType.DO_NOT_FAIL_WORKFLOW_BASED_ON_ACTION_FAILURE_ERROR_CODE) {

            workflowService.triggerWorkflowCompletionEvent(TransitionEvent.MOVE_TO_IN_PROGRESS, storedWorkflow,
                    storedWorkflowStep);
            return;
        }

        workflowService.triggerWorkflowCompletionEvent(TransitionEvent.SUCCESS, storedWorkflow, storedWorkflowStep);
    }

    private boolean isFallbackActionFailedOrNotPresent(final StoredWorkflowStep storedWorkflowStep) {

        final var actions = actionService.getActions(Set.of(storedWorkflowStep.getWorkflowStepId()));

        final var latestFailedActionOptional = actions.stream()
                .filter(action -> CompletionState.FAILURE == action.getCompletionState())
                .max(Comparator.comparing(StoredAction::getLastUpdatedAt));

        if (latestFailedActionOptional.isEmpty()) {
            return true;
        }

        final var standardStepActionConfig = getStandardStepActionConfigOfAction(storedWorkflowStep,
                latestFailedActionOptional.get());

        if (Objects.isNull(standardStepActionConfig.getDependencyConfig()) || Objects.isNull(
                standardStepActionConfig.getDependencyConfig()
                        .getFallbackConfig())) {
            return true;
        }

        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflowStep.getWorkflowId());

        final var workflowContextJsonNode = MapperUtils.convertToJsonNode(workflowContext);

        final var fallbackActionMappingId = handleBarsService.transform(standardStepActionConfig.getDependencyConfig()
                .getFallbackConfig()
                .getFallbackActionMappingId(), workflowContextJsonNode);

        if (Strings.isNullOrEmpty(fallbackActionMappingId)) {
            return true;
        }

        final var allWorkflowStepIds = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                        storedWorkflowStep.getWorkflowId())
                .stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        return actionService.getActions(allWorkflowStepIds)
                .stream()
                .filter(action -> fallbackActionMappingId.equals(action.getActionMappingId()))
                .filter(action -> hopeLangService.evaluate(standardStepActionConfig.getDependencyConfig()
                        .getFallbackConfig()
                        .getFallbackRule(), workflowContextJsonNode))
                .max(Comparator.comparing(StoredAction::getCreatedAt))
                .map(storedAction -> CompletionState.FAILURE.equals(storedAction.getCompletionState()))
                .orElse(true);
    }

    private PseudoSuccessActionFailureWorkflowBehaviourConfigType shouldFailWorkflowBasedOnPseudoSuccessActionFailureWorkflowBehaviourConfig(final StoredWorkflowStep storedWorkflowStep,
                                                                                                                                             final String failedActionId) {

        final var latestFailedStoredAction = actionService.validateAndGetAction(failedActionId);

        final var standardStepActionConfig = getStandardStepActionConfigOfAction(storedWorkflowStep,
                latestFailedStoredAction);

        return standardStepActionConfig.getPseudoSuccessActionFailureWorkflowBehaviourConfig()
                .accept(ShouldFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfigVisitor.INSTANCE,
                        latestFailedStoredAction);
    }

    private StandardStepActionConfig getStandardStepActionConfigOfAction(final StoredWorkflowStep storedWorkflowStep,
                                                                         final StoredAction storedAction) {

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var profileScreenConfig = profileStep.getProfileScreenConfig();

        final var standardProfileScreenConfig = profileScreenConfig.accept(
                        new StandardProfileScreenConfigFromProfileStepVisitor(storedAction.getScreenMappingId()))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                        Map.of(Fields.screenMappingId, storedAction.getScreenMappingId())));

        return standardProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Fields.actionMappingId, storedAction.getActionMappingId())));
    }

    private void identifyWorkflowStepCompletionStateAndTriggerEvent(final StoredWorkflowStep storedWorkflowStep,
                                                                    final String actionId) {

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var completionStatusOfWorkflowStep = profileStep.getProfileScreenConfig()
                .accept(new ProfileStepCompletionStatusVisitor(storedWorkflowStep.getWorkflowStepId(), actionService,
                        workflowService, hopeLangService, workflowContextStore));

        if (CompletionState.SUCCESS == completionStatusOfWorkflowStep) {

            workflowStepService.triggerEvent(storedWorkflowStep, TransitionEvent.SUCCESS);

        } else if (CompletionState.FAILURE == completionStatusOfWorkflowStep) {

            workflowStepService.triggerEvent(storedWorkflowStep, TransitionEvent.FAILURE, actionId);
        }
    }

    private boolean isDependentOnActionMappingId(final StandardStepActionConfig standardStepActionConfig,
                                                 final String actionMappingId,
                                                 final JsonNode workflowContextJsonNode) {

        return Objects.nonNull(standardStepActionConfig.getDependencyConfig()) && actionMappingId.equals(
                standardStepActionConfig.getDependencyConfig()
                        .accept(getDependentActionMappingIdDependencyConfigVisitor, workflowContextJsonNode));
    }

    private StandardStepActionConfig getDependencyRootNodeStepActionConfig(final List<StandardStepActionConfig> allStandardStepActionConfigs,
                                                                           final String actionMappingId,
                                                                           final JsonNode workflowContextJsonNode) {

        var currentStepActionConfig = ActionService.filterActionMappingId(allStandardStepActionConfigs,
                actionMappingId);

        while (Objects.nonNull(currentStepActionConfig.getDependencyConfig())) {

            currentStepActionConfig = ActionService.filterActionMappingId(allStandardStepActionConfigs,
                    currentStepActionConfig.getDependencyConfig()
                            .accept(getDependentActionMappingIdDependencyConfigVisitor, workflowContextJsonNode));

        }

        return currentStepActionConfig;
    }
}
