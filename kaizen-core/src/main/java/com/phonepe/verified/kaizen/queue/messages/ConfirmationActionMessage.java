package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ConfirmationActionMessage extends BaseMessage {

    @NonNull
    private final String intent;

    @NonNull
    private final String actionId;

    private final long componentKitVersion;

    @NonNull
    private final UserDetails userDetails;

    @Builder
    @Jacksonized
    public ConfirmationActionMessage(@NonNull final RequestInfo requestInfo,
                                     @NonNull final String intent,
                                     @NonNull final String actionId,
                                     final long componentKitVersion,
                                     @NonNull final UserDetails userDetails) {

        super(ActorMessageType.CONFIRMATION_ACTION, requestInfo);
        this.intent = intent;
        this.actionId = actionId;
        this.componentKitVersion = componentKitVersion;
        this.userDetails = userDetails;
    }
}
