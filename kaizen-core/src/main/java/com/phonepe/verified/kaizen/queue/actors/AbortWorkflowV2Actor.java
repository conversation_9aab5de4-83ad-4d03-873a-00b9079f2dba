package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.AbortWorkflowV2Message;
import com.phonepe.verified.kaizen.services.WorkflowService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class AbortWorkflowV2Actor extends BaseActor<AbortWorkflowV2Message> {

    private final WorkflowService workflowService;

    @Inject
    protected AbortWorkflowV2Actor(final Map<ActorType, ActorConfig> actorConfigMap,
                                   final ConnectionRegistry connectionRegistry,
                                   final ObjectMapper mapper,
                                   final RetryStrategyFactory retryStrategyFactory,
                                   final ExceptionHandlingFactory exceptionHandlingFactory,
                                   final WorkflowService workflowService) {

        super(ActorType.ABORT_WORKFLOW_V2, actorConfigMap.get(ActorType.ABORT_WORKFLOW_V2), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, AbortWorkflowV2Message.class);

        this.workflowService = workflowService;
    }

    @Override
    protected boolean handleMessage(final AbortWorkflowV2Message abortWorkflowMessage) {

        workflowService.abort(abortWorkflowMessage.getWorkflowIdsToAbort(),
                abortWorkflowMessage.isForceAbortTerminalStates(), abortWorkflowMessage.getCallerUserDetails());
        return true;
    }

}
