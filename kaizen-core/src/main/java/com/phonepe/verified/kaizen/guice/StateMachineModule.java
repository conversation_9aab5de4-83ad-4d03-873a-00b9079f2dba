package com.phonepe.verified.kaizen.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.statemachines.WorkflowStateMachine;
import com.phonepe.verified.kaizen.statemachines.WorkflowStepStateMachine;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.CompleteWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.CreateWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.InitialActionInProgressWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.PurgeWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.UpdateWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.CompleteWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.CreateWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.InitialActionCompleteWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.InitialActionInProgressWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.MoveBackToPseudoSuccessWorkflowStepAndRetryAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.MoveToInProgressWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.PurgeWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.UpdateWorkflowStepAction;
import com.phonepe.verified.kaizen.storage.aerospike.commands.TransitionLockCommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class StateMachineModule extends AbstractModule {

    @Provides
    @Singleton
    public WorkflowStateMachine providesWorkflowStateMachine(final TransitionLockCommand transitionLockCommand,
                                                             final CreateWorkflowAction createWorkflowAction,
                                                             final UpdateWorkflowAction updateWorkflowAction,
                                                             final CompleteWorkflowAction completeWorkflowAction,
                                                             final InitialActionInProgressWorkflowAction initialActionInProgressWorkflowAction,
                                                             final PurgeWorkflowAction purgeWorkflowAction) {

        final var workflowStateMachine = new WorkflowStateMachine(transitionLockCommand, createWorkflowAction,
                updateWorkflowAction, completeWorkflowAction, initialActionInProgressWorkflowAction,
                purgeWorkflowAction);

        workflowStateMachine.start();

        return workflowStateMachine;
    }

    @Provides
    @Singleton
    public WorkflowStepStateMachine providesWorkflowStepStateMachine(final TransitionLockCommand transitionLockCommand,
                                                                     final UpdateWorkflowStepAction updateWorkflowStepAction,
                                                                     final CreateWorkflowStepAction createWorkflowStepAction,
                                                                     final CompleteWorkflowStepAction completeWorkflowStepAction,
                                                                     final MoveToInProgressWorkflowStepAction moveToInprogressWorkflowStepAction,
                                                                     final InitialActionCompleteWorkflowStepAction initialActionCompleteWorkflowStepAction,
                                                                     final InitialActionInProgressWorkflowStepAction initialActionInProgressWorkflowStepAction,
                                                                     final MoveBackToPseudoSuccessWorkflowStepAndRetryAction moveBackToPseudoSuccessWorkflowStepAndRetryAction,
                                                                     final PurgeWorkflowStepAction purgeWorkflowStepAction) {

        final var workflowStepStateMachine = new WorkflowStepStateMachine(transitionLockCommand,
                updateWorkflowStepAction, createWorkflowStepAction, completeWorkflowStepAction,
                moveToInprogressWorkflowStepAction, initialActionCompleteWorkflowStepAction,
                initialActionInProgressWorkflowStepAction, moveBackToPseudoSuccessWorkflowStepAndRetryAction,
                purgeWorkflowStepAction);

        workflowStepStateMachine.start();

        return workflowStepStateMachine;
    }
}
