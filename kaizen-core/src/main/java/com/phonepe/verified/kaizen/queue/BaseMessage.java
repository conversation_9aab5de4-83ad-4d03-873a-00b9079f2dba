package com.phonepe.verified.kaizen.queue;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType.Names;
import com.phonepe.verified.kaizen.queue.messages.AbortWorkflowMessage;
import com.phonepe.verified.kaizen.queue.messages.AbortWorkflowV2Message;
import com.phonepe.verified.kaizen.queue.messages.AcknowledgeSmsConsentMessage;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.queue.messages.ConfirmationActionMessage;
import com.phonepe.verified.kaizen.queue.messages.DiscardWorkflowMessage;
import com.phonepe.verified.kaizen.queue.messages.DocumentMaskingMessage;
import com.phonepe.verified.kaizen.queue.messages.DocumentMessage;
import com.phonepe.verified.kaizen.queue.messages.DocumentPrefillMessage;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.queue.messages.HandleTtlCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.MoveToInProgressWorkflowStepMessage;
import com.phonepe.verified.kaizen.queue.messages.PersistKeyValuePairsFromApiCallActionMessage;
import com.phonepe.verified.kaizen.queue.messages.PostCompletionActionConfigProcessorMessage;
import com.phonepe.verified.kaizen.queue.messages.ProcessAutoRetryClockworkActionMessage;
import com.phonepe.verified.kaizen.queue.messages.ProcessDocumentMaskingCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.ProcessSelfieDocumentIdSubmitMessage;
import com.phonepe.verified.kaizen.queue.messages.PurgeWorkflowMessage;
import com.phonepe.verified.kaizen.queue.messages.RevolverCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.ScheduledWorkflowAbortCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.ScheduledWorkflowStepAbortCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.SearchClientDataMessage;
import com.phonepe.verified.kaizen.queue.messages.SectionSubmitMessage;
import com.phonepe.verified.kaizen.queue.messages.SectionSubmitV2Message;
import com.phonepe.verified.kaizen.queue.messages.SkipWorkflowStepMessage;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.queue.messages.TriggerWorkflowStepMessage;
import com.phonepe.verified.kaizen.queue.messages.VerifyOtpHurdleMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowAutoAbortCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowAutoSkipCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowClientCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowInitMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowInitV2Message;
import com.phonepe.verified.kaizen.queue.messages.WorkflowMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepCompletionMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepV2Message;
import com.phonepe.verified.kaizen.utils.RequestInfoUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(name = Names.ABORT_WORKFLOW, value = AbortWorkflowMessage.class),
        @JsonSubTypes.Type(name = Names.ABORT_WORKFLOW_V2, value = AbortWorkflowV2Message.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_AUTO_ABORT_CALLBACK, value = WorkflowAutoAbortCallbackMessage.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_AUTO_SKIP_CALLBACK, value = WorkflowAutoSkipCallbackMessage.class),
        @JsonSubTypes.Type(name = Names.ACKNOWLEDGE_SMS_CONSENT, value = AcknowledgeSmsConsentMessage.class),
        @JsonSubTypes.Type(name = Names.ACTION_EXECUTION, value = ActionExecutionMessage.class),
        @JsonSubTypes.Type(name = Names.CONFIRMATION_ACTION, value = ConfirmationActionMessage.class),
        @JsonSubTypes.Type(name = Names.DISCARD_WORKFLOW, value = DiscardWorkflowMessage.class),
        @JsonSubTypes.Type(name = Names.DOCUMENT_MASKING, value = DocumentMaskingMessage.class),
        @JsonSubTypes.Type(name = Names.DOCUMENT, value = DocumentMessage.class),
        @JsonSubTypes.Type(name = Names.EVENT_INGESTION, value = EventIngestionMessage.class),
        @JsonSubTypes.Type(name = Names.HANDLE_TTL_CALLBACK, value = HandleTtlCallbackMessage.class),
        @JsonSubTypes.Type(name = Names.MOVE_TO_IN_PROGRESS_WORKFLOW_STEP, value = MoveToInProgressWorkflowStepMessage.class),
        @JsonSubTypes.Type(name = Names.PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL_ACTION, value = PersistKeyValuePairsFromApiCallActionMessage.class),
        @JsonSubTypes.Type(name = Names.POST_COMPLETION_ACTION_CONFIG_PROCESSOR, value = PostCompletionActionConfigProcessorMessage.class),
        @JsonSubTypes.Type(name = Names.PROCESS_AUTO_RETRY_CLOCKWORK_ACTION, value = ProcessAutoRetryClockworkActionMessage.class),
        @JsonSubTypes.Type(name = Names.PROCESS_DOCUMENT_MASKING_CALLBACK, value = ProcessDocumentMaskingCallbackMessage.class),
        @JsonSubTypes.Type(name = Names.PROCESS_SELFIE_DOCUMENT_ID_SUBMIT, value = ProcessSelfieDocumentIdSubmitMessage.class),
        @JsonSubTypes.Type(name = Names.PURGE_WORKFLOW, value = PurgeWorkflowMessage.class),
        @JsonSubTypes.Type(name = Names.REVOLVER_CALLBACK, value = RevolverCallbackMessage.class),
        @JsonSubTypes.Type(name = Names.SCHEDULED_WORKFLOW_ABORT_CALLBACK, value = ScheduledWorkflowAbortCallbackMessage.class),
        @JsonSubTypes.Type(name = Names.SCHEDULED_WORKFLOW_STEP_ABORT_CALLBACK, value = ScheduledWorkflowStepAbortCallbackMessage.class),
        @JsonSubTypes.Type(name = Names.SEARCH_CLIENT_DATA, value = SearchClientDataMessage.class),
        @JsonSubTypes.Type(name = Names.SECTION_SUBMIT, value = SectionSubmitMessage.class),
        @JsonSubTypes.Type(name = Names.SECTION_SUBMIT_V2, value = SectionSubmitV2Message.class),
        @JsonSubTypes.Type(name = Names.SKIP_WORKFLOW_STEP, value = SkipWorkflowStepMessage.class),
        @JsonSubTypes.Type(name = Names.STEP_ACTION, value = StepActionMessage.class),
        @JsonSubTypes.Type(name = Names.TRIGGER_WORKFLOW_STEP, value = TriggerWorkflowStepMessage.class),
        @JsonSubTypes.Type(name = Names.VERIFY_OTP_HURDLE, value = VerifyOtpHurdleMessage.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_CLIENT_CALLBACK, value = WorkflowClientCallbackMessage.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_INIT, value = WorkflowInitMessage.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_INIT_V2, value = WorkflowInitV2Message.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW, value = WorkflowMessage.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_COMPLETION, value = WorkflowStepCompletionMessage.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP, value = WorkflowStepMessage.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_V2, value = WorkflowStepV2Message.class),
        @JsonSubTypes.Type(name = Names.DOCUMENT_PREFILL, value = DocumentPrefillMessage.class)})
public abstract class BaseMessage {

    private final ActorMessageType type;

    private final RequestInfo requestInfo;

    protected BaseMessage(final ActorMessageType actorMessageType,
                          final RequestInfo requestInfo) {

        type = actorMessageType;
        this.requestInfo = RequestInfoUtils.getRequestInfo()
                .orElse(requestInfo);
    }
}