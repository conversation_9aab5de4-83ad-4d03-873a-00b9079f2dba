package com.phonepe.verified.kaizen.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import io.appform.dropwizard.sharding.DBShardingBundleBase;
import io.dropwizard.Configuration;
import java.util.function.ToIntFunction;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DbModule<T extends Configuration & KaizenConfig> extends AbstractModule {

    private final DBShardingBundleBase<T> dbShardingBundle;

    @Override
    protected void configure() {
        bind(new TypeLiteral<DBShardingBundleBase<? extends KaizenConfig>>() {
        }).toInstance(dbShardingBundle);
    }

    @Provides
    @Singleton
    public ToIntFunction<String> shardingFunction(final WorkflowRepository workflowRepository) {
        return workflowRepository::getShardId;
    }
}
