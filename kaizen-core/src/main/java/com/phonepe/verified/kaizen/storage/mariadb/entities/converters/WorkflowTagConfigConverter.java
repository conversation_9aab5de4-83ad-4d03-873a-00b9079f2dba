package com.phonepe.verified.kaizen.storage.mariadb.entities.converters;

import com.phonepe.verified.kaizen.models.configs.tag.WorkflowTagConfig;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.Objects;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class WorkflowTagConfigConverter implements AttributeConverter<WorkflowTagConfig, String> {

    @Override
    public String convertToDatabaseColumn(final WorkflowTagConfig workflowTagConfig) {
        return Objects.isNull(workflowTagConfig)
               ? null
               : MapperUtils.serializeToString(workflowTagConfig);
    }

    @Override
    public WorkflowTagConfig convertToEntityAttribute(final String text) {
        return Objects.isNull(text)
               ? null
               : MapperUtils.deserialize(text, WorkflowTagConfig.class);
    }
}
