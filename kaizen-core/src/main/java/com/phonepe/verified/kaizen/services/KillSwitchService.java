package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowAddOnRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowInitRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowInitV2Request;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;

public interface KillSwitchService {


    void evaluateWorkflowInitKillSwitch(final WorkflowInitRequest workflowInitRequest,
                                        final Profile profile,
                                        final String workflowId);

    void evaluateWorkflowInitV2KillSwitch(final WorkflowInitV2Request workflowInitV2Request,
                                          final Profile profile,
                                          final String workflowId);

    void evaluateWorkflowAddOnInitKillSwitch(final WorkflowAddOnRequest workflowAddOnRequest,
                                             final AddOnProfile addOnProfile,
                                             final String workflowId);

    void evaluateWorkflowResumeKillSwitch(final Profile profile,
                                          final StoredWorkflow storedWorkflow);
}
