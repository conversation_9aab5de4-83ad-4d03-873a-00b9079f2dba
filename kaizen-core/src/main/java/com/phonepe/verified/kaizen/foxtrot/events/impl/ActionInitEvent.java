package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class ActionInitEvent extends BaseEvent {

    private String actionId;

    private ActionType actionType;

    private String stateMachineVersion;

    private Boolean isPseudoSuccess;

    private String neededActionMappingId;

    @Builder
    public ActionInitEvent(final EventType eventType,
                           final String intent,
                           final String entityId,
                           final String namespace,
                           final String workflowId,
                           @NonNull final String groupingKey,
                           final String organization,
                           final String workflowType,
                           final String workflowStepId,
                           final EntityType entityType,
                           final String workflowVersion,
                           final String screenMappingId,
                           final String actionMappingId,
                           final long componentKitVersion,
                           final String profileStepMappingId,
                           final String actionId,
                           final ActionType actionType,
                           final String stateMachineVersion,
                           final Boolean isPseudoSuccess,
                           final ProfileType profileType,
                           final String addOnType,
                           final String neededActionMappingId) {
        super(eventType, intent, entityId, namespace, workflowId, profileType, addOnType, groupingKey, organization,
                workflowType, workflowStepId, entityType, workflowVersion, screenMappingId, actionMappingId,
                componentKitVersion, profileStepMappingId);
        this.actionId = actionId;
        this.actionType = actionType;
        this.stateMachineVersion = stateMachineVersion;
        this.isPseudoSuccess = isPseudoSuccess;
        this.neededActionMappingId = neededActionMappingId;
    }
}
