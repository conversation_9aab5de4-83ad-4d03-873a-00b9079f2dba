package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowAudit;
import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class WorkflowAuditRepository extends CrudRepository<StoredWorkflowAudit> {

    @Inject
    public WorkflowAuditRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredWorkflowAudit.class), null);
    }
}
