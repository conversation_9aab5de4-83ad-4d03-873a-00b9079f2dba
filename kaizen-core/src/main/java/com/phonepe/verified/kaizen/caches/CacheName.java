package com.phonepe.verified.kaizen.caches;

import java.util.List;

public enum CacheName {

    PROFILE_CACHE {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitProfileCache();
        }
    },
    PROFILE_STEP_CACHE {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitProfileStepCache();
        }
    },
    PROFILE_ID_PROFILE_STEPS_CACHE {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitProfileIdProfileStepsCache();
        }
    },
    PROFILE_ID_SECTION_MAPPING_ID_TO_PROFILE_STEP_STANDARD_PROFILE_SCREEN_CACHE {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache();
        }
    },
    SESSION_MANAGEMENT_CONFIG_CACHE {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitSessionManagementConfigCache();
        }
    },
    CLIENT_CALLBACK_CACHE {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitClientCallbackCache();
        }
    },
    PRIMARY_PROFILE_CACHE {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitPrimaryProfileCache();
        }
    },
    ADD_ON_PROFILE_CACHE {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitAddOnProfileCache();
        }
    },
    WORKFLOW_CONTEXT_WITH_INVALIDATED_ACTIONS {
        @Override
        public <T> T visit(final CacheVisitor<T> visitor) {
            return visitor.visitWorkflowContextWithInvalidatedActions();
        }
    };

    public static final List<CacheName> PROFILE_RELATED_CACHES = List.of(CacheName.PROFILE_CACHE,
            CacheName.PROFILE_STEP_CACHE, CacheName.PROFILE_ID_PROFILE_STEPS_CACHE,
            CacheName.PROFILE_ID_SECTION_MAPPING_ID_TO_PROFILE_STEP_STANDARD_PROFILE_SCREEN_CACHE,
            CacheName.PRIMARY_PROFILE_CACHE, CacheName.ADD_ON_PROFILE_CACHE);

    public abstract <T> T visit(CacheVisitor<T> visitor);

    public interface CacheVisitor<T> {

        T visitProfileCache();

        T visitProfileStepCache();

        T visitProfileIdProfileStepsCache();

        T visitProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache();

        T visitSessionManagementConfigCache();

        T visitClientCallbackCache();

        T visitPrimaryProfileCache();

        T visitAddOnProfileCache();

        T visitWorkflowContextWithInvalidatedActions();
    }
}
