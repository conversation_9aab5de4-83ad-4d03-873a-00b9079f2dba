package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.services.ActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.io.Serial;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.WAIT_FOR_CONDITION)
public class StoredWaitForConditionMetadata extends StoredActionMetadata {

    @Serial
    private static final long serialVersionUID = 8815578266011682188L;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String value;

    @Builder
    public StoredWaitForConditionMetadata(@NonNull final String actionId,
                                          final LocalDateTime createdAt,
                                          final LocalDateTime lastUpdatedAt,
                                          final String value) {
        super(BuildUtils.primaryKey(), actionId, ActionMetadataType.WAIT_FOR_CONDITION, createdAt, lastUpdatedAt);
        this.value = value;
    }


    @Override
    public String getActionMetadataContextKey() {
        return Names.WAIT_FOR_CONDITION;
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }
}
