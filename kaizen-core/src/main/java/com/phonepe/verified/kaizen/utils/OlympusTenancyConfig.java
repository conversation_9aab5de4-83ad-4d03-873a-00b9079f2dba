package com.phonepe.verified.kaizen.utils;

import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class OlympusTenancyConfig {

    @NotNull
    private final Map<@NotEmpty String, Map<String, OlympusTenant>> organizationNamespaceToOlympusTenantMapping;

    @NotNull
    private final OlympusTenant defaultOlympusTenant;
}
