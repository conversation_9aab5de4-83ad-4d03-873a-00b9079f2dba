package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.states.TransitionState.TransitionStateVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class WorkflowStepServiceTransitionStateBaseVisitor implements
        TransitionStateVisitor<Void, StoredWorkflowStep> {

    private static final String UNEXPECTED_WORKFLOW_STEP_STATE_LOG = "Workflow Step is having unexpected state: {}, workflowId: {}, workflowStepId: {}";

    @Override
    public Void visitCreated(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitInProgress(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitSuccess(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitFailure(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitAborted(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitDiscarded(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitSkipped(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitPseudoSuccess(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitInitialActionInProgress(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitReady(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitInvalidated(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    @Override
    public Void visitPurged(final StoredWorkflowStep data) {
        return logWarningAndThrowException(data);
    }

    private Void logWarningAndThrowException(final StoredWorkflowStep storedWorkflowStep) {

        log.warn(UNEXPECTED_WORKFLOW_STEP_STATE_LOG, storedWorkflowStep.getCurrentState(),
                storedWorkflowStep.getWorkflowId(), storedWorkflowStep.getWorkflowStepId());

        throw KaizenException.create(KaizenResponseCode.UNEXPECTED_WORKFLOW_STEP_STATE,
                Map.of(StoredWorkflowStep.Fields.workflowId, storedWorkflowStep.getWorkflowId(),
                        StoredWorkflowStep.Fields.workflowStepId, storedWorkflowStep.getWorkflowStepId(),
                        StoredWorkflowStep.Fields.currentState, storedWorkflowStep.getCurrentState()));
    }
}
