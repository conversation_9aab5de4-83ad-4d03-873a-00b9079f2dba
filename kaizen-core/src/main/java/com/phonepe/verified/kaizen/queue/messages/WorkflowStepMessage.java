package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WorkflowStepMessage extends BaseMessage {

    @NonNull
    private final String workflowStepId;

    @Builder
    @Jacksonized
    public WorkflowStepMessage(final RequestInfo requestInfo,
                               @NonNull final String workflowStepId) {

        super(ActorMessageType.WORKFLOW_STEP, requestInfo);
        this.workflowStepId = workflowStepId;
    }
}
