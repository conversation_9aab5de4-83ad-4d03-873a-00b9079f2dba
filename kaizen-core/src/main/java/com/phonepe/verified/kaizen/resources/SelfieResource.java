package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.data.common.MailboxApiName;
import com.phonepe.verified.kaizen.models.requests.SelfieDocumentIdSubmitRequest;
import com.phonepe.verified.kaizen.queue.actors.ProcessSelfieDocumentIdSubmitActor;
import com.phonepe.verified.kaizen.queue.messages.ProcessSelfieDocumentIdSubmitMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/selfie")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Selfie", description = "Selfie related APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class SelfieResource {

    private final AuthZService authZService;

    private final ActionService actionService;

    private final WorkflowService workflowService;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final SessionManagementService sessionManagementService;

    private final ProcessSelfieDocumentIdSubmitActor processSelfieDocumentIdSubmitActor;


    @POST
    @Authorize
    @PermitAll
    @SneakyThrows
    @Path("/submit/{actionId}/{intent}/{componentKitVersion}")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Submit selfie documentId from PhonePe Verified SDK")
    @AuthZ(gandalfPermission = "uploadDocument", olympusPermission = OlympusPermissionNames.UPLOAD_DOCUMENT)
    public Response submit(@NotEmpty @PathParam("intent") final String intent,
                           @NotEmpty @PathParam("actionId") final String actionId,
                           @PathParam("componentKitVersion") final long componentKitVersion,
                           @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                           @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                           @Valid @NotNull final SelfieDocumentIdSubmitRequest selfieDocumentIdSubmitRequest,
                           @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken) {

        authZService.authorizeUserWithActionId(actionId, requestInfo, userDetails);

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        sessionManagementService.validateSessionAndThrowException(storedWorkflow, requestInfo, userDetails,
                sessionToken);

        processSelfieDocumentIdSubmitActor.publish(ProcessSelfieDocumentIdSubmitMessage.builder()
                .intent(intent)
                .actionId(actionId)
                .requestInfo(requestInfo)
                .userDetails(userDetails)
                .componentKitVersion(componentKitVersion)
                .selfieDocumentIdSubmitRequest(selfieDocumentIdSubmitRequest)
                .build());

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.SELFIE_DOCUMENT_ID_SUBMIT)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.SELFIE_DOCUMENT_ID_SUBMIT)
                        .toMilliseconds())
                .build();
    }
}
