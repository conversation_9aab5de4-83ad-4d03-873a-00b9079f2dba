package com.phonepe.verified.kaizen.models.primer;

import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateTokenV2Request {

    @NonNull
    private String id;

    @NonNull
    private String userId;

    @NonNull
    private String tokenId;

    @NonNull
    private String name;

    @NonNull
    private String role;

    @NonNull
    private String deviceId;

    @Valid
    private TokenExpiry tokenExpiry;

    @Valid
    private TokenTtl tokenTtl;

}