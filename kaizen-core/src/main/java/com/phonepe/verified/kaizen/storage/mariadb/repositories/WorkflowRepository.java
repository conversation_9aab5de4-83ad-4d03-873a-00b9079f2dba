package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow.Fields;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class WorkflowRepository extends CrudRepository<StoredWorkflow> {

    @Inject
    public WorkflowRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredWorkflow.class), null);
    }

    public Optional<StoredWorkflow> select(final String workflowId) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflow.class)
                .add(Restrictions.eq(Fields.workflowId, workflowId));

        return select(workflowId, detachedCriteria).stream()
                .findFirst();
    }

    public Optional<StoredWorkflow> select(final String workflowId,
                                           final String entityId) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflow.class)
                .add(Restrictions.eq(Fields.workflowId, workflowId))
                .add(Restrictions.eq(Fields.entityId, entityId));

        return select(workflowId, detachedCriteria).stream()
                .findFirst();
    }

    public List<StoredWorkflow> select(final String entityId,
                                       final EntityType entityType,
                                       final Set<String> profileIds) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflow.class)
                .add(Restrictions.eq(Fields.entityId, entityId))
                .add(Restrictions.eq(Fields.entityType, entityType))
                .add(Restrictions.in(Fields.profileId, profileIds));

        return select(entityId, detachedCriteria);
    }

    public List<StoredWorkflow> select(final LocalDateTime fromLastUpdatedAt,
                                       final LocalDateTime toLastUpdatedAt,
                                       final List<State> currentState,
                                       final Set<String> profileIds) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflow.class)
                .add(Restrictions.between(Fields.lastUpdatedAt, fromLastUpdatedAt, toLastUpdatedAt))
                .add(Restrictions.in(Fields.currentState, currentState))
                .add(Restrictions.in(Fields.profileId, profileIds));

        return scatterGather(detachedCriteria);
    }

    public List<StoredWorkflow> select(final String entityId,
                                       final EntityType entityType) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflow.class)
                .add(Restrictions.eq(Fields.entityId, entityId))
                .add(Restrictions.eq(Fields.entityType, entityType));

        return select(entityId, detachedCriteria);
    }

    public List<StoredWorkflow> selectFromAllShards(final Set<String> workflowIds) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflow.class)
                .add(Restrictions.in(Fields.workflowId, workflowIds));

        return scatterGather(detachedCriteria);
    }
}