package com.phonepe.verified.kaizen.storage.aerospike.keys;

import com.phonepe.verified.kaizen.storage.aerospike.AerospikeKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessTokenKey implements AerospikeKey {

    @NonNull
    private String accessToken;

    @Override
    public String getKey() {
        return accessToken;
    }
}