package com.phonepe.verified.kaizen.models.data.common;

import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StandardStepActionConfigCompletionTimestamp {

    private StandardStepActionConfig standardStepActionConfig;

    private LocalDateTime actionCompletionTimestamp;

    private CompletionState completionStateOfAction;

}
