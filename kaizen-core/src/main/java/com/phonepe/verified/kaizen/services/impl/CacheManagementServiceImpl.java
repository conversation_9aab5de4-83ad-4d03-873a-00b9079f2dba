package com.phonepe.verified.kaizen.services.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.CacheManagementService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.appform.ranger.common.server.ShardInfo;
import io.appform.ranger.core.model.ServiceNode;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class CacheManagementServiceImpl implements CacheManagementService {

    private final Provider<KaizenConfig> appConfigProvider;

    private final OlympusIMClient olympusIMClient;

    private final OkHttpClient okHttpClient;

    @Override
    public void invalidateCacheOnAllNodes(final List<CacheName> cacheNames) {

        final var serviceNodes = getAllServiceNodes(appConfigProvider.get()
                .getBaseUrl());

        cacheNames.forEach(cacheName -> serviceNodes.forEach(node -> invalidateCacheOnNode(cacheName, node)));
    }

    private List<ServiceNode<ShardInfo>> getAllServiceNodes(final String baseURL) {

        final var request = new Request.Builder().url(baseURL + "/instances")
                .addHeader(Constants.Headers.AUTHORIZATION, olympusIMClient.getSystemAuthHeader())
                .build();

        try (final var response = okHttpClient.newCall(request)
                .execute()) {
            if (response.isSuccessful()) {
                return MapperUtils.deserialize(response.body()
                        .string(), new TypeReference<>() {
                });
            } else {
                log.error("Error while getting instances for {}, got response {}", baseURL, response.body());
                throw new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, Map.of());
            }
        } catch (final IOException ex) {
            log.error("Error while getting instances for {}", baseURL, ex);
            throw new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, Map.of());
        }
    }

    private void invalidateCacheOnNode(final CacheName cacheName,
                                       final ServiceNode<ShardInfo> node) {

        final String invalidateCacheURL = String.format("http://%s:%s/v1/cache/invalidate/%s", node.getHost(),
                node.getPort(), cacheName);
        final var invalidationRequest = new Request.Builder().url(invalidateCacheURL)
                .addHeader(Constants.Headers.AUTHORIZATION, olympusIMClient.getSystemAuthHeader())
                .build();

        try (final var response = okHttpClient.newCall(invalidationRequest)
                .execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to invalidate cache {} for host {}, got response {}", cacheName, node.getHost(),
                        response.body());
                throw new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, Map.of());
            }
        } catch (final IOException ex) {
            log.error("Error while invalidating cache {} for host {}", cacheName, node.getHost(), ex);
            throw new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, Map.of());
        }
    }
}
