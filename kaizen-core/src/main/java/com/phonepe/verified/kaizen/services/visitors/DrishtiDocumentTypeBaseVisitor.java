package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.drishti.models.responses.extraction.ExtractionResponseVisitor;
import com.phonepe.verified.drishti.models.responses.extraction.impl.AadhaarExtractionResponse;
import com.phonepe.verified.drishti.models.responses.extraction.impl.DrivingLicenseExtractionResponse;
import com.phonepe.verified.drishti.models.responses.extraction.impl.PanExtractionResponse;
import com.phonepe.verified.drishti.models.responses.extraction.impl.PassportExtractionResponse;
import com.phonepe.verified.drishti.models.responses.extraction.impl.VehicleRegistrationCertificateExtractionResponse;
import com.phonepe.verified.drishti.models.responses.extraction.impl.VoterExtractionResponse;
import com.phonepe.verified.kaizen.utils.Utils;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DrishtiDocumentTypeBaseVisitor implements ExtractionResponseVisitor<Map<String, String>, Void> {

    public static final DrishtiDocumentTypeBaseVisitor INSTANCE = new DrishtiDocumentTypeBaseVisitor();

    @Override
    public Map<String, String> visitPan(final PanExtractionResponse panExtractionResponse,
                                        final Void data) {

        return Stream.of(Utils.convertToMapEntry("PAN_DOCUMENT_NUMBER", panExtractionResponse.getDocumentNumber()),
                        Utils.convertToMapEntry("PAN_DOCUMENT_NAME", panExtractionResponse.getName()),
                        Utils.convertToMapEntry("PAN_DOCUMENT_FATHER_NAME", panExtractionResponse.getFatherName()),
                        Utils.convertToMapEntryString("PAN_DOCUMENT_DATE_OF_BIRTH", panExtractionResponse.getDateOfBirth()),
                        Utils.convertToMapEntryString("PAN_DOCUMENT_DATE_OF_ISSUE", panExtractionResponse.getDateOfIssue()))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
    }

    @Override
    public Map<String, String> visitDrivingLicense(final DrivingLicenseExtractionResponse drivingLicenseExtractionResponse,
                                                   final Void data) {

        return Stream.of(
                        Utils.convertToMapEntry("DRIVING_LICENSE_NUMBER", drivingLicenseExtractionResponse.getDocumentNumber()),
                        Utils.convertToMapEntry("DRIVING_LICENSE_NAME", drivingLicenseExtractionResponse.getName()),
                        Utils.convertToMapEntry("DRIVING_LICENSE_FATHER_NAME",
                                drivingLicenseExtractionResponse.getFatherName()),
                        Utils.convertToMapEntry("DRIVING_LICENSE_ADDRESS", drivingLicenseExtractionResponse.getAddress()),
                        Utils.convertToMapEntry("DRIVING_LICENSE_POSTAL_CODE",
                                drivingLicenseExtractionResponse.getPostalCode()),
                        Utils.convertToMapEntry("DRIVING_LICENSE_STATE", drivingLicenseExtractionResponse.getState()),
                        Utils.convertToMapEntry("DRIVING_LICENSE_DISTRICT", drivingLicenseExtractionResponse.getDistrict()),
                        Utils.convertToMapEntry("DRIVING_LICENSE_STREET_ADDRESS",
                                drivingLicenseExtractionResponse.getStreetAddress()),
                        Utils.convertToMapEntryString("DRIVING_LICENSE_DATE_OF_BIRTH",
                                drivingLicenseExtractionResponse.getDateOfBirth()),
                        Utils.convertToMapEntryString("DRIVING_LICENSE_DATE_OF_ISSUE",
                                drivingLicenseExtractionResponse.getLatestDateOfIssue()),
                        Utils.convertToMapEntryString("DRIVING_LICENSE_DATE_OF_EXPIRY",
                                drivingLicenseExtractionResponse.getDateOfExpiry()))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
    }

    @Override
    public Map<String, String> visitVoter(final VoterExtractionResponse voterExtractionResponse,
                                          final Void data) {

        return Stream.of(Utils.convertToMapEntry("VOTER_ID_NUMBER", voterExtractionResponse.getDocumentNumber()),
                        Utils.convertToMapEntry("VOTER_ID_NAME", voterExtractionResponse.getName()),
                        Utils.convertToMapEntry("VOTER_ID_FATHER_NAME", voterExtractionResponse.getFatherName()),
                        Utils.convertToMapEntry("VOTER_ID_ADDRESS", voterExtractionResponse.getAddress()),
                        Utils.convertToMapEntry("VOTER_ID_POSTAL_CODE", voterExtractionResponse.getPostalCode()),
                        Utils.convertToMapEntry("VOTER_ID_STATE", voterExtractionResponse.getState()),
                        Utils.convertToMapEntry("VOTER_ID_DISTRICT", voterExtractionResponse.getDistrict()),
                        Utils.convertToMapEntry("VOTER_ID_STREET_ADDRESS", voterExtractionResponse.getStreetAddress()),
                        Utils.convertToMapEntryString("VOTER_ID_DATE_OF_BIRTH", voterExtractionResponse.getDateOfBirth()),
                        Utils.convertToMapEntry("VOTER_ID_YEAR_OF_BIRTH", voterExtractionResponse.getYearOfBirth()),
                        Utils.convertToMapEntryString("VOTER_ID_GENDER", voterExtractionResponse.getGender()))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
    }

    @Override
    public Map<String, String> visitAadhaar(final AadhaarExtractionResponse aadhaarExtractionResponse,
                                            final Void data) {

        return Stream.of(Utils.convertToMapEntry("AADHAAR_NUMBER", aadhaarExtractionResponse.getDocumentNumber()),
                        Utils.convertToMapEntry("AADHAAR_NAME", aadhaarExtractionResponse.getName()),
                        Utils.convertToMapEntry("AADHAAR_FATHER_NAME", aadhaarExtractionResponse.getFatherName()),
                        Utils.convertToMapEntry("AADHAAR_ADDRESS", aadhaarExtractionResponse.getAddress()),
                        Utils.convertToMapEntry("AADHAAR_POSTAL_CODE", aadhaarExtractionResponse.getPostalCode()),
                        Utils.convertToMapEntry("AADHAAR_STATE", aadhaarExtractionResponse.getState()),
                        Utils.convertToMapEntry("AADHAAR_DISTRICT", aadhaarExtractionResponse.getDistrict()),
                        Utils.convertToMapEntry("AADHAAR_STREET_ADDRESS", aadhaarExtractionResponse.getStreetAddress()),
                        Utils.convertToMapEntry("AADHAAR_HOUSE_NUMBER", aadhaarExtractionResponse.getHouseNumber()),
                        Utils.convertToMapEntry("AADHAAR_YEAR_OF_BIRTH", aadhaarExtractionResponse.getYearOfBirth()),
                        Utils.convertToMapEntryString("AADHAAR_GENDER", aadhaarExtractionResponse.getGender()),
                        Utils.convertToMapEntryString("AADHAAR_DATE_OF_BIRTH", aadhaarExtractionResponse.getDateOfBirth()))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
    }

    @Override
    public Map<String, String> visitPassport(final PassportExtractionResponse passportExtractionResponse,
                                             final Void data) {

        return Stream.of(Utils.convertToMapEntry("PASSPORT_NUMBER", passportExtractionResponse.getDocumentNumber()),
                        Utils.convertToMapEntry("PASSPORT_POSTAL_CODE", passportExtractionResponse.getPincode()),
                        Utils.convertToMapEntry("PASSPORT_NAME", passportExtractionResponse.getName()),
                        Utils.convertToMapEntry("PASSPORT_FATHER_NAME", passportExtractionResponse.getFatherName()),
                        Utils.convertToMapEntry("PASSPORT_ADDRESS", passportExtractionResponse.getAddress()),
                        Utils.convertToMapEntry("PASSPORT_NATIONALITY", passportExtractionResponse.getNationality()),
                        Utils.convertToMapEntry("PASSPORT_STATE", passportExtractionResponse.getState()),
                        Utils.convertToMapEntry("PASSPORT_DISTRICT", passportExtractionResponse.getDistrict()),
                        Utils.convertToMapEntry("PASSPORT_MOTHER_NAME", passportExtractionResponse.getMotherName()),
                        Utils.convertToMapEntry("PASSPORT_SPOUSE_NAME", passportExtractionResponse.getSpouseName()),
                        Utils.convertToMapEntryString("PASSPORT_DATE_OF_BIRTH", passportExtractionResponse.getDateOfBirth()),
                        Utils.convertToMapEntryString("PASSPORT_DATE_OF_ISSUE", passportExtractionResponse.getDateOfIssue()),
                        Utils.convertToMapEntry("PASSPORT_PLACE_OF_ISSUE", passportExtractionResponse.getPlaceOfIssue()),
                        Utils.convertToMapEntryString("PASSPORT_DATE_OF_EXPIRY", passportExtractionResponse.getDateOfExpiry()),
                        Utils.convertToMapEntryString("PASSPORT_GENDER", passportExtractionResponse.getGender()))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
    }

    @Override
    public Map<String, String> visitVehicleRegistrationCertificate(final VehicleRegistrationCertificateExtractionResponse rcExtractionResponse,
                                                                   final Void data) {

        return Stream.of(Utils.convertToMapEntry("RC_NUMBER", rcExtractionResponse.getDocumentNumber()),
                        Utils.convertToMapEntry("RC_NAME", rcExtractionResponse.getName()),
                        Utils.convertToMapEntry("RC_FATHER_NAME", rcExtractionResponse.getFatherName()),
                        Utils.convertToMapEntry("RC_ADDRESS", rcExtractionResponse.getAddress()),
                        Utils.convertToMapEntry("RC_VEHICLE_BODY", rcExtractionResponse.getVehicleBody()),
                        Utils.convertToMapEntry("RC_CHASSIS_NUMBER", rcExtractionResponse.getChassisNumber()),
                        Utils.convertToMapEntry("RC_VEHICLE_CLASS", rcExtractionResponse.getVehicleClass()),
                        Utils.convertToMapEntry("RC_VOLUME", rcExtractionResponse.getVolume()),
                        Utils.convertToMapEntry("RC_FIRST_DOCUMENT_SIDE", rcExtractionResponse.getFirstDocumentSide()),
                        Utils.convertToMapEntry("RC_SECOND_DOCUMENT_SIDE", rcExtractionResponse.getSecondDocumentSide()),
                        Utils.convertToMapEntry("RC_ENGINE_NUMBER", rcExtractionResponse.getEngineNumber()),
                        Utils.convertToMapEntry("RC_FUEL_TYPE", rcExtractionResponse.getFuelType()),
                        Utils.convertToMapEntry("RC_MANUFACTURER", rcExtractionResponse.getManufacturer()),
                        Utils.convertToMapEntry("RC_VEHICLE_MODEL", rcExtractionResponse.getVehicleModel()),
                        Utils.convertToMapEntry("RC_OWNER_NAME", rcExtractionResponse.getOwnerName()),
                        Utils.convertToMapEntry("RC_REGISTRATION_NUMBER", rcExtractionResponse.getRegistrationNumber()),
                        Utils.convertToMapEntry("RC_RTO_DISTRICT", rcExtractionResponse.getRtoDistrict()),
                        Utils.convertToMapEntry("RC_RTO_STATE", rcExtractionResponse.getRtoState()),
                        Utils.convertToMapEntry("RC_WHEEL_BASE", rcExtractionResponse.getWheelBase()),
                        Utils.convertToMapEntryString("RC_DATE_OF_BIRTH", rcExtractionResponse.getDateOfBirth()),
                        Utils.convertToMapEntryString("RC_REGISTRATION_DATE", rcExtractionResponse.getRegistrationDate()),
                        Utils.convertToMapEntryString("RC_MANUFACTURING_DATE", rcExtractionResponse.getManufacturingDate()))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
    }
}
