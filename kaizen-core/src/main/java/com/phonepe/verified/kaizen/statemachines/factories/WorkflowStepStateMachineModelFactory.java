package com.phonepe.verified.kaizen.statemachines.factories;

import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.CompleteWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.CreateWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.InitialActionCompleteWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.InitialActionInProgressWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.MoveBackToPseudoSuccessWorkflowStepAndRetryAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.MoveToInProgressWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.PurgeWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.UpdateWorkflowStepAction;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.statemachine.config.model.ConfigurationData;
import org.springframework.statemachine.config.model.DefaultStateMachineModel;
import org.springframework.statemachine.config.model.StateData;
import org.springframework.statemachine.config.model.StateMachineModel;
import org.springframework.statemachine.config.model.StateMachineModelFactory;
import org.springframework.statemachine.config.model.StatesData;
import org.springframework.statemachine.config.model.TransitionData;
import org.springframework.statemachine.config.model.TransitionsData;
import org.springframework.statemachine.transition.TransitionKind;

@AllArgsConstructor
public class WorkflowStepStateMachineModelFactory implements
        StateMachineModelFactory<TransitionState, TransitionEvent> {

    private final CreateWorkflowStepAction createWorkflowStepAction;

    private final UpdateWorkflowStepAction updateWorkflowStepAction;

    private final CompleteWorkflowStepAction completeWorkflowStepAction;

    private final MoveToInProgressWorkflowStepAction moveToInprogressWorkflowStepAction;

    private final InitialActionCompleteWorkflowStepAction initialActionCompleteWorkflowStepAction;

    private final InitialActionInProgressWorkflowStepAction initialActionInProgressWorkflowStepAction;

    private final MoveBackToPseudoSuccessWorkflowStepAndRetryAction moveBackToPseudoSuccessWorkflowStepAndRetryAction;

    private final PurgeWorkflowStepAction purgeWorkflowStepAction;

    @Override
    public StateMachineModel<TransitionState, TransitionEvent> build() {

        final var stateData = new ArrayList<StateData<TransitionState, TransitionEvent>>();
        stateData.add(new StateData<>(TransitionState.CREATED, true));
        stateData.add(getStateData(TransitionState.INITIAL_ACTION_IN_PROGRESS, false));
        stateData.add(getStateData(TransitionState.IN_PROGRESS, false));
        stateData.add(getStateData(TransitionState.SUCCESS, false));
        stateData.add(getStateData(TransitionState.PSEUDO_SUCCESS, false));
        stateData.add(getStateData(TransitionState.FAILURE, false));
        stateData.add(getStateData(TransitionState.ABORTED, true));
        stateData.add(getStateData(TransitionState.DISCARDED, true));
        stateData.add(getStateData(TransitionState.SKIPPED, false));
        stateData.add(getStateData(TransitionState.AUTO_SKIPPED, false));
        stateData.add(getStateData(TransitionState.INVALIDATED, false));
        stateData.add(getStateData(TransitionState.PURGED, false));

        final var statesData = new StatesData<>(stateData);

        final var transitionData = new ArrayList<TransitionData<TransitionState, TransitionEvent>>();
        transitionData.add(
                new TransitionData<>(TransitionState.CREATED, TransitionState.CREATED, TransitionEvent.CREATE_ENTRY,
                        List.of(createWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.CREATED, TransitionState.INITIAL_ACTION_IN_PROGRESS,
                TransitionEvent.TRIGGER_INITIAL_ACTION, List.of(initialActionInProgressWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.CREATED, TransitionState.PURGED, TransitionEvent.PURGE,
                List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.INITIAL_ACTION_IN_PROGRESS, TransitionState.PSEUDO_SUCCESS,
                        TransitionEvent.PSEUDO_SUCCESS, List.of(initialActionCompleteWorkflowStepAction), null,
                        TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.INITIAL_ACTION_IN_PROGRESS, TransitionState.SUCCESS,
                TransitionEvent.SUCCESS, List.of(initialActionCompleteWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.INITIAL_ACTION_IN_PROGRESS, TransitionState.FAILURE,
                TransitionEvent.FAILURE, List.of(initialActionCompleteWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.INITIAL_ACTION_IN_PROGRESS, TransitionState.PURGED,
                TransitionEvent.PURGE, List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.CREATED, TransitionState.IN_PROGRESS,
                TransitionEvent.MOVE_TO_IN_PROGRESS, List.of(moveToInprogressWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.SUCCESS, TransitionEvent.SUCCESS,
                        List.of(completeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.PSEUDO_SUCCESS,
                TransitionEvent.PSEUDO_SUCCESS, List.of(completeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.FAILURE, TransitionEvent.FAILURE,
                        List.of(completeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.PURGED, TransitionEvent.PURGE,
                        List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.FAILURE, TransitionState.IN_PROGRESS,
                TransitionEvent.MOVE_TO_IN_PROGRESS, List.of(moveToInprogressWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.FAILURE, TransitionState.PURGED, TransitionEvent.PURGE,
                List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.CREATED, TransitionState.SKIPPED, TransitionEvent.SKIP_WORKFLOW,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.CREATED, TransitionState.SKIPPED,
                TransitionEvent.SKIP_WORKFLOW_STEP, List.of(completeWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.SKIPPED,
                TransitionEvent.SKIP_WORKFLOW, List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.SKIPPED,
                TransitionEvent.SKIP_WORKFLOW_STEP, List.of(completeWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        // No transition from created because we want to enforce skipping only of IN_PROGRESS steps
        transitionData.add(new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.AUTO_SKIPPED,
                TransitionEvent.AUTO_SKIP_WORKFLOW_STEP, List.of(updateWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.PSEUDO_SUCCESS, TransitionState.SUCCESS, TransitionEvent.SUCCESS,
                        List.of(completeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.PSEUDO_SUCCESS, TransitionState.PURGED, TransitionEvent.PURGE,
                        List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.PSEUDO_SUCCESS, TransitionState.FAILURE, TransitionEvent.FAILURE,
                        List.of(completeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        // not using completeWorkflowStepAction to avoid handshake with workflowService
        // since the flow for workflow update is separate
        transitionData.add(
                new TransitionData<>(TransitionState.FAILURE, TransitionState.SKIPPED, TransitionEvent.SKIP_WORKFLOW,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.FAILURE, TransitionState.SKIPPED,
                TransitionEvent.SKIP_WORKFLOW_STEP, List.of(completeWorkflowStepAction), null,
                TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.CREATED, TransitionState.ABORTED, TransitionEvent.ABORT,
                List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.ABORTED, TransitionEvent.ABORT,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.SUCCESS, TransitionState.ABORTED, TransitionEvent.ABORT,
                List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.SUCCESS, TransitionState.PURGED, TransitionEvent.PURGE,
                List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.ABORTED, TransitionState.PURGED, TransitionEvent.PURGE,
                List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.FAILURE, TransitionState.ABORTED, TransitionEvent.ABORT,
                List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.SKIPPED, TransitionState.ABORTED, TransitionEvent.ABORT,
                List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.SKIPPED, TransitionState.PURGED, TransitionEvent.PURGE,
                List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.PSEUDO_SUCCESS, TransitionState.ABORTED, TransitionEvent.ABORT,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.INITIAL_ACTION_IN_PROGRESS, TransitionState.ABORTED,
                TransitionEvent.ABORT, List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.CREATED, TransitionState.DISCARDED, TransitionEvent.DISCARD,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.DISCARDED, TransitionEvent.DISCARD,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.DISCARDED, TransitionState.PURGED, TransitionEvent.PURGE,
                        List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.SUCCESS, TransitionState.DISCARDED, TransitionEvent.DISCARD,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.FAILURE, TransitionState.DISCARDED, TransitionEvent.DISCARD,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.SKIPPED, TransitionState.DISCARDED, TransitionEvent.DISCARD,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.PSEUDO_SUCCESS, TransitionState.DISCARDED, TransitionEvent.DISCARD,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.INITIAL_ACTION_IN_PROGRESS, TransitionState.DISCARDED,
                TransitionEvent.DISCARD, List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.CREATED, TransitionState.INVALIDATED, TransitionEvent.INVALIDATE,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.IN_PROGRESS, TransitionState.INVALIDATED,
                TransitionEvent.INVALIDATE, List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.SUCCESS, TransitionState.INVALIDATED, TransitionEvent.INVALIDATE,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.FAILURE, TransitionState.INVALIDATED, TransitionEvent.INVALIDATE,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.SKIPPED, TransitionState.INVALIDATED, TransitionEvent.INVALIDATE,
                        List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.PSEUDO_SUCCESS, TransitionState.INVALIDATED,
                TransitionEvent.INVALIDATE, List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.INITIAL_ACTION_IN_PROGRESS, TransitionState.INVALIDATED,
                TransitionEvent.INVALIDATE, List.of(updateWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.INVALIDATED, TransitionState.PURGED, TransitionEvent.PURGE,
                        List.of(purgeWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(
                new TransitionData<>(TransitionState.SUCCESS, TransitionState.IN_PROGRESS, TransitionEvent.RESUBMIT,
                        List.of(moveToInprogressWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.PSEUDO_SUCCESS, TransitionState.IN_PROGRESS,
                TransitionEvent.RESUBMIT, List.of(moveToInprogressWorkflowStepAction), null, TransitionKind.EXTERNAL));

        transitionData.add(new TransitionData<>(TransitionState.FAILURE, TransitionState.PSEUDO_SUCCESS,
                TransitionEvent.MANUAL_RETRY, List.of(moveBackToPseudoSuccessWorkflowStepAndRetryAction), null,
                TransitionKind.EXTERNAL));

        final var transitionsData = new TransitionsData<>(transitionData);

        return new DefaultStateMachineModel<>(new ConfigurationData<>(), statesData, transitionsData);
    }

    @Override
    public StateMachineModel<TransitionState, TransitionEvent> build(final String machineId) {
        return build();
    }

    private StateData<TransitionState, TransitionEvent> getStateData(final TransitionState transitionState,
                                                                     final boolean isEnd) {
        final var stateData = new StateData<TransitionState, TransitionEvent>(transitionState);
        stateData.setEnd(isEnd);
        return stateData;
    }
}
