package com.phonepe.verified.kaizen.hopelangfunctions;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.appform.hope.core.Value;
import io.appform.hope.core.functions.FunctionImplementation;
import io.appform.hope.core.functions.HopeFunction;
import io.appform.hope.core.utils.Converters;
import io.appform.hope.core.values.BooleanValue;
import io.appform.hope.core.visitors.Evaluator.EvaluationContext;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@AllArgsConstructor
@FunctionImplementation("calculateActionErrorCodeCountLowerThanGivenThresholdAndErrorCode")
public class CalculateActionErrorCodeCountLowerThanGivenThresholdAndErrorCode extends HopeFunction<BooleanValue> {

    @Inject
    private static Provider<ActionService> actionServiceProvider;

    private final Value workflowStepIdVal;

    private final Value actionMappingIdVal;

    private final Value failureErrorCodeVal;

    private final Value failureErrorCodeToThresholdVal;

    private final Value failActionForUnmappedErrorCodeVal;

    @Override
    public BooleanValue apply(final EvaluationContext evaluationContext) {

        final var workflowStepId = Converters.stringValue(evaluationContext, this.workflowStepIdVal, null);
        final var actionMappingId = Converters.stringValue(evaluationContext, this.actionMappingIdVal, null);
        final var failureErrorCodeStr = Converters.stringValue(evaluationContext, this.failureErrorCodeVal, null);
        final var failureErrorCodeToThresholdJsonString = Converters.stringValue(evaluationContext,
                this.failureErrorCodeToThresholdVal, null);
        final var failActionForUnmappedErrorCode = Converters.booleanValue(evaluationContext,
                this.failActionForUnmappedErrorCodeVal, true);

        Objects.requireNonNull(workflowStepId);
        Objects.requireNonNull(actionMappingIdVal);
        Objects.requireNonNull(failureErrorCodeStr);
        Objects.requireNonNull(failureErrorCodeToThresholdJsonString);

        final var failureErrorCode = ActionFailureErrorCode.valueOf(failureErrorCodeStr);

        final var failureErrorCodeToThresholdMap = MapperUtils.deserialize(failureErrorCodeToThresholdJsonString,
                new TypeReference<Map<ActionFailureErrorCode, Long>>() {
                });

        final var errorCodeCountForGivenAction = actionServiceProvider.get()
                .getActions(workflowStepId, actionMappingId)
                .stream()
                .filter(action -> Objects.nonNull(action.getFailureErrorCode()) && action.getFailureErrorCode()
                        .equals(failureErrorCode))
                .count();

        if (failureErrorCodeToThresholdMap.containsKey(failureErrorCode)) {
            return new BooleanValue(
                    errorCodeCountForGivenAction < failureErrorCodeToThresholdMap.get(failureErrorCode));
        }

        // If error code not found, return value of failActionForUnmappedErrorCode since true will fail the action, and false wont
        return new BooleanValue(failActionForUnmappedErrorCode);
    }
}
