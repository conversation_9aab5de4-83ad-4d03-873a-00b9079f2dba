package com.phonepe.verified.kaizen.authz.filters;

import com.phonepe.verified.kaizen.authz.annotations.WorkflowId;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Objects;
import java.util.function.Function;
import javax.inject.Inject;
import javax.inject.Singleton;
import javax.ws.rs.ext.Provider;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.model.Parameter.Source;
import org.glassfish.jersey.server.ContainerRequest;
import org.glassfish.jersey.server.internal.inject.AbstractValueParamProvider;
import org.glassfish.jersey.server.internal.inject.MultivaluedParameterExtractorProvider;
import org.glassfish.jersey.server.model.Parameter;

@Slf4j
@Provider
@Singleton
public class WorkflowIdProvider extends AbstractValueParamProvider {

    @Inject
    public WorkflowIdProvider(final javax.inject.Provider<MultivaluedParameterExtractorProvider> mpep) {
        super(mpep, Source.UNKNOWN);
    }

    @Override
    protected Function<ContainerRequest, ?> createValueProvider(final Parameter parameter) {

        if (parameter.getSourceAnnotation() instanceof WorkflowId) {

            final Class<?> classType = parameter.getRawType();

            if (Objects.nonNull(classType) && classType.equals(String.class)) {

                return containerRequest -> containerRequest.getProperty(Constants.PV_WORKFLOW_ID);
            }
            log.error(
                    "WorkflowIdProvider annotation was not placed on correct object type; Injection might not work correctly!");
        }
        return null;
    }
}
