package com.phonepe.verified.kaizen.clients.internal;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.columbus.models.core.request.unit.UnitSearchRequest;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.verified.kaizen.models.responses.search.CatalogueSearchResponse;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import io.appform.functionmetrics.MonitoredFunction;
import java.util.List;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class CatalogueClient {

    private final OlympusIMClient olympusIMClient;

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    @Inject
    public CatalogueClient(final HttpClientRegistry httpClientRegistry,
                           final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(
                ClientIds.CATALOGUE);
        this.olympusIMClient = olympusIMClient;
    }

    @MonitoredFunction
    public CatalogueSearchResponse getCatalogueSearchData(final String tenant,
                                                          final String category,
                                                          final UnitSearchRequest unitSearchRequest) {

        final var url = String.format("/unit/%s/%s/search", tenant, category);

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executePost(httpExecutorBuilderFactory, "getCatalogueSearchData", url,
                new SerializableHttpData(MediaType.APPLICATION_JSON, unitSearchRequest), List.of(authHeader),
                CatalogueSearchResponse.class, getClass());
    }
}
