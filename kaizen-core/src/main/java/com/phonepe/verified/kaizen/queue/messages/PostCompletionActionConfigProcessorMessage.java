package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PostCompletionActionConfigProcessorMessage extends BaseMessage {

    @NotEmpty
    private final String workflowId;

    @Nullable
    private final ActionFailureErrorCode failureErrorCode;

    @Builder
    @Jacksonized
    public PostCompletionActionConfigProcessorMessage(final RequestInfo requestInfo,
                                                      @NotEmpty final String workflowId,
                                                      @Nullable final ActionFailureErrorCode failureErrorCode) {

        super(ActorMessageType.POST_COMPLETION_ACTION_CONFIG_PROCESSOR, requestInfo);
        this.workflowId = workflowId;
        this.failureErrorCode = failureErrorCode;
    }
}
