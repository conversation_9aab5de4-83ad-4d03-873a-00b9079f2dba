package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.shadow.data.action.popup.impl.GenericDialogPopup;
import com.phonepe.shadow.data.action.popup.impl.RichTextBottomSheetPopup;
import com.phonepe.shadow.models.response.Action;
import com.phonepe.shadow.models.response.AutoActionConfig;
import com.phonepe.shadow.models.response.actions.ApiCallAction;
import com.phonepe.shadow.models.response.actions.ButtonAction;
import com.phonepe.shadow.models.response.actions.MoveBackAction;
import com.phonepe.shadow.models.response.actions.MoveToPreSdkScreenAction;
import com.phonepe.shadow.models.response.actions.MoveToSectionAction;
import com.phonepe.shadow.models.response.actions.MoveToSectionAndClearBackStack;
import com.phonepe.shadow.models.response.actions.OpenBottomSheetV2Action;
import com.phonepe.shadow.models.response.actions.OpenPopWithTimerAction;
import com.phonepe.shadow.models.response.actions.OpenPopupAction;
import com.phonepe.shadow.models.response.actions.OpenStatusPageAction;
import com.phonepe.shadow.models.response.actions.OpenWebViewAction;
import com.phonepe.shadow.models.response.actions.OtpHurdleV2Action;
import com.phonepe.shadow.models.response.actions.SectionRefreshAction;
import com.phonepe.shadow.models.response.actions.SelfieAction;
import com.phonepe.shadow.models.response.actions.SendSmsAction;
import com.phonepe.shadow.models.response.actions.TerminalAction;
import com.phonepe.shadow.models.response.actions.UpdateFieldsAction;
import com.phonepe.shadow.models.response.actions.ValidationAction;
import com.phonepe.shadow.models.response.actions.ValidationRequestContext;
import com.phonepe.shadow.page.field.Field;
import com.phonepe.shadow.page.field.rules.FieldRules;
import com.phonepe.shadow.template.Template;
import com.phonepe.shadow.template.TemplateSectionMapping;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.EvaluationRuleResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.MoveToSectionScreen;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.MoveToSectionScreen.MoveToSectionScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl.*;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.function.TriFunction;

@RequiredArgsConstructor
public class BuildShadowV2ActionVisitor implements ShadowV2ResponseVisitor<Action, Boolean> {

    private final Template template;

    private final JsonNode workflowContext;

    private final String nextScreenMappingId;

    private final String currentScreenMappingId;

    private final HopeLangService hopeLangService;

    private final HandleBarsService handleBarsService;

    private final boolean retryNotAllowedOrRetryExhausted;

    private final ExpressionEvaluator expressionEvaluator;

    @Override
    @SneakyThrows
    public Action visit(final MoveToSectionShadowV2ResponseConfig moveToSectionUiResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        return getMoveToSection(moveToSectionUiResponseConfig.getScreen(), shouldApplyHandleBars,
                (effectiveMappingId, templateSectionMapping, fields) -> MoveToSectionAction.builder()
                        .mappingId(effectiveMappingId)
                        .fields(fields)
                        .title(templateSectionMapping.getTitle())
                        .overrideBackAction(getRecursiveAction(moveToSectionUiResponseConfig.getBackButtonAction()))
                        .checkExistingStack(moveToSectionUiResponseConfig.isCheckExistingStack())
                        .permissionData(templateSectionMapping.getPermissionData())
                        .build());
    }

    @Override
    public Action visit(final MoveToSectionAndClearBackstackShadowV2ResponseConfig moveToSectionAndClearBackstackShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        return getMoveToSection(moveToSectionAndClearBackstackShadowV2ResponseConfig.getScreen(), shouldApplyHandleBars,
                (effectiveMappingId, templateSectionMapping, fields) -> MoveToSectionAndClearBackStack.builder()
                        .mappingId(effectiveMappingId)
                        .fields(fields)
                        .title(templateSectionMapping.getTitle())
                        .overrideBackAction(getRecursiveAction(
                                moveToSectionAndClearBackstackShadowV2ResponseConfig.getBackButtonAction()))
                        .checkExistingStack(moveToSectionAndClearBackstackShadowV2ResponseConfig.isCheckExistingStack())
                        .clearToMappingId(
                                moveToSectionAndClearBackstackShadowV2ResponseConfig.getClearStackUpToMappingId())
                        .permissionData(templateSectionMapping.getPermissionData())
                        .build());
    }

    @Override
    public Action visit(final MoveBackActionShadowV2ResponseConfig moveBackActionShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {
        return MoveBackAction.builder()
                .build();
    }


    @SuppressWarnings("java:S3740")
    private Action getMoveToSection(final MoveToSectionScreen moveToSectionScreen,
                                    final Boolean shouldApplyHandleBars,
                                    final TriFunction<String, TemplateSectionMapping, List<Field>, Action> actionTriFunction) {

        final var effectiveMappingId = moveToSectionScreen.accept(new MoveToSectionScreenVisitor<String>() {
            @Override
            public String visitCurrent() {
                return retryNotAllowedOrRetryExhausted
                       ? null
                       : currentScreenMappingId;
            }

            @Override
            public String visitNext() {
                return nextScreenMappingId;
            }
        });

        if (Objects.isNull(effectiveMappingId)) {
            return null;
        }

        final var templateSectionMapping = template.getSectionMappings()
                .stream()
                .filter(tsm -> tsm.getMappingId()
                        .equals(effectiveMappingId))
                .findFirst()
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.SECTION_MAPPING_ID_NOT_FOUND_IN_TEMPLATE,
                        Map.of()));

        final var fields = templateSectionMapping.getSection()
                .getFieldGroups()
                .stream()
                .flatMap(fieldGroup -> fieldGroup.getFields()
                        .stream())
                .toList();

        fields.forEach(field -> evaluateBackendRules(field, workflowContext));

        final var action = actionTriFunction.apply(effectiveMappingId, templateSectionMapping, fields);

        return applyHandlebarsTransformation(action, shouldApplyHandleBars);
    }

    @SuppressWarnings("java:S3740")
    private Action getMoveToSection(final String screenMappingId,
                                    final Boolean shouldApplyHandleBars,
                                    final TriFunction<String, TemplateSectionMapping, List<Field>, Action> actionTriFunction) {

        final var templateSectionMapping = template.getSectionMappings()
                .stream()
                .filter(tsm -> tsm.getMappingId()
                        .equals(screenMappingId))
                .findFirst()
                .orElseThrow(() -> KaizenException.create(
                        KaizenResponseCode.SECTION_MAPPING_ID_NOT_FOUND_IN_TEMPLATE, Map.of()));

        final var fields = templateSectionMapping.getSection()
                .getFieldGroups()
                .stream()
                .flatMap(fieldGroup -> fieldGroup.getFields()
                        .stream())
                .toList();

        fields.forEach(field -> evaluateAndClearBackendRules(field, workflowContext));

        final var action = actionTriFunction.apply(screenMappingId, templateSectionMapping, fields);

        return applyHandlebarsTransformation(action, shouldApplyHandleBars);
    }

    @SuppressWarnings({"java:S3740", "java:S1905"})
    private void evaluateAndClearBackendRules(final Field<?> field,
                                              final JsonNode workflowContextJsonNode) {

        final var rules = field.getBackendEvaluatedRules();

        if (Objects.nonNull(rules) && !rules.isEmpty()) {

            rules.stream()
                    .map(rule -> Map.entry(rule, rule.getExpression()
                            .evaluate(expressionEvaluator, workflowContextJsonNode)))
                    .forEach(entry -> entry.getKey()
                            .getResult()
                            .accept(new FieldPropertyModifier(entry.getValue(), workflowContext, handleBarsService),
                                    field));

            /*
             * Removal of evaluation result is necessary in some cases to avoid deserialization failures when handlebar transformation is applied outside (E.g. during move to section).
             * This has to happen regardless of the expression's result.
             *
             * This also prevents the unnecessary evaluation of just the rules, which saves time during template fetch.
             */
            field.setBackendEvaluatedRules(List.of());
        }
    }

    @Override
    @SneakyThrows
    public Action visit(final OpenBottomSheetShadowV2ResponseConfig openBottomSheetUiResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var leftAction = getRecursiveAction(openBottomSheetUiResponseConfig.getLeftAction());

        final var rightAction = getRecursiveAction(openBottomSheetUiResponseConfig.getRightAction());

        final var openBottomSheetV2Action = OpenBottomSheetV2Action.builder()
                .cancelable(openBottomSheetUiResponseConfig.isCancelable())
                .title(openBottomSheetUiResponseConfig.getTitle())
                .footer(openBottomSheetUiResponseConfig.getFooter())
                .leftButtonText(openBottomSheetUiResponseConfig.getLeftButtonText())
                .leftAction(leftAction)
                .rightButtonText(openBottomSheetUiResponseConfig.getRightButtonText())
                .rightAction(rightAction)
                .infoList(BuildUtils.buildInfoList(openBottomSheetUiResponseConfig.getInfoList()))
                .build();

        return applyHandlebarsTransformation(openBottomSheetV2Action, shouldApplyHandleBars);
    }

    @Override
    @SneakyThrows
    public Action visit(final OpenBottomSheetV2ShadowV2ResponseConfig openBottomSheetV2UiResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var leftAction = Objects.isNull(openBottomSheetV2UiResponseConfig.getLeftAction())
                               ? null
                               : openBottomSheetV2UiResponseConfig.getLeftAction()
                                       .accept(this, false);

        final var rightAction = Objects.isNull(openBottomSheetV2UiResponseConfig.getRightAction())
                                ? null
                                : openBottomSheetV2UiResponseConfig.getRightAction()
                                        .accept(this, false);

        final var openBottomSheetV2Action = OpenBottomSheetV2Action.builder()
                .cancelable(openBottomSheetV2UiResponseConfig.isCancelable())
                .title(openBottomSheetV2UiResponseConfig.getTitle())
                .titleStyle(openBottomSheetV2UiResponseConfig.getTitleStyle())
                .subTitle(openBottomSheetV2UiResponseConfig.getSubTitle())
                .subTitleStyle(openBottomSheetV2UiResponseConfig.getSubTitleStyle())
                .leftButtonText(openBottomSheetV2UiResponseConfig.getLeftButtonText())
                .leftButtonStyle(openBottomSheetV2UiResponseConfig.getLeftButtonStyle())
                .leftAction(leftAction)
                .rightButtonText(openBottomSheetV2UiResponseConfig.getRightButtonText())
                .rightButtonStyle(openBottomSheetV2UiResponseConfig.getRightButtonStyle())
                .rightAction(rightAction)
                .imageDetail(openBottomSheetV2UiResponseConfig.getImageDetail())
                .buttonsAxis(openBottomSheetV2UiResponseConfig.getButtonsAxis())
                .infoList(openBottomSheetV2UiResponseConfig.getInfoList())
                .footer(openBottomSheetV2UiResponseConfig.getFooter())
                .assetDetail(openBottomSheetV2UiResponseConfig.getAssetDetail())
                .build();

        return applyHandlebarsTransformation(openBottomSheetV2Action, shouldApplyHandleBars);
    }

    @Override
    @SneakyThrows
    public Action visit(final OpenStatusPageShadowV2ResponseConfig openStatusPageUiResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var action = getRecursiveAction(openStatusPageUiResponseConfig.getAction());

        final var openStatusPageAction = OpenStatusPageAction.builder()
                .footer(openStatusPageUiResponseConfig.getFooter())
                .imageUrl(openStatusPageUiResponseConfig.getImageUrl())
                .effects(openStatusPageUiResponseConfig.getEffects())
                .bottomButtonText(openStatusPageUiResponseConfig.getBottomButtonText())
                .description(openStatusPageUiResponseConfig.getDescription())
                .note(openStatusPageUiResponseConfig.getNote())
                .subtitle(openStatusPageUiResponseConfig.getSubtitle())
                .action(action)
                .autoActionConfig(AutoActionConfig.builder()
                        .delayInMs(Objects.nonNull(openStatusPageUiResponseConfig.getAutoActionConfig()
                                .getDelay())
                                   ? openStatusPageUiResponseConfig.getAutoActionConfig()
                                           .getDelay()
                                           .toMilliseconds()
                                   : 0L)
                        .enabled(openStatusPageUiResponseConfig.getAutoActionConfig()
                                .isEnabled())
                        .build())
                .assetDetail(openStatusPageUiResponseConfig.getAssetDetail())
                .build();

        return applyHandlebarsTransformation(openStatusPageAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final OtpHurdleShadowV2ResponseConfig otpHurdleShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var otpHurdleV2Action = OtpHurdleV2Action.builder()
                .title(otpHurdleShadowV2ResponseConfig.getTitle())
                .subtitle(otpHurdleShadowV2ResponseConfig.getSubtitle())
                .cancelable(otpHurdleShadowV2ResponseConfig.isCancelable())
                .otpCodeLength(otpHurdleShadowV2ResponseConfig.getOtpCodeLength())
                .otpRegex(otpHurdleShadowV2ResponseConfig.getOtpRegex())
                .autoReadRegex(otpHurdleShadowV2ResponseConfig.getAutoReadRegex())
                .autoReadType(otpHurdleShadowV2ResponseConfig.getAutoReadType())
                .autoVerify(otpHurdleShadowV2ResponseConfig.isAutoVerify())
                .resendOtpText(otpHurdleShadowV2ResponseConfig.getResendOtpText())
                .resendOtpUrl(otpHurdleShadowV2ResponseConfig.getResendOtpUrl())
                .verifyUrl(otpHurdleShadowV2ResponseConfig.getVerifyUrl())
                .verifyButtonText(otpHurdleShadowV2ResponseConfig.getVerifyButtonText())
                .verifyFailureText(otpHurdleShadowV2ResponseConfig.getVerifyFailureText())
                .timerText(otpHurdleShadowV2ResponseConfig.getTimerText())
                .timerInMs(otpHurdleShadowV2ResponseConfig.getTimerInMs())
                .keyboardAllowedWhileTimer(otpHurdleShadowV2ResponseConfig.isKeyboardAllowedWhileTimer())
                .build();

        return applyHandlebarsTransformation(otpHurdleV2Action, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final ValidationActionShadowV2ResponseConfig validationActionShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var validationAction = ValidationAction.builder()
                .validationUrl(validationActionShadowV2ResponseConfig.getValidationUrl())
                .validationRequestContext(ValidationRequestContext.builder()
                        .trapUrl(validationActionShadowV2ResponseConfig.getValidationRequestContext()
                                .getTrapUrl())
                        .build())
                .submitLoader(validationActionShadowV2ResponseConfig.getSubmitLoader())
                .build();

        return applyHandlebarsTransformation(validationAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final TerminalActionShadowV2ResponseConfig terminalActionShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var terminalAction = TerminalAction.builder()
                .build();

        return applyHandlebarsTransformation(terminalAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final EvaluatedShadowV2ResponseConfig evaluatedShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var evaluationRuleResponseConfigs = Objects.nonNull(
                evaluatedShadowV2ResponseConfig.getEvaluationRuleResponseConfigs())
                                                  ? evaluatedShadowV2ResponseConfig.getEvaluationRuleResponseConfigs()
                                                  : new ArrayList<EvaluationRuleResponseConfig>(0);

        return evaluationRuleResponseConfigs.stream()
                .filter(esc -> hopeLangService.evaluate(esc.getEvaluationRule(), workflowContext))
                .findFirst()
                .map(EvaluationRuleResponseConfig::getShadowV2ResponseConfig)
                .map(shadowV2ResponseConfig -> shadowV2ResponseConfig.accept(this, shouldApplyHandleBars))
                .orElseGet(() -> evaluatedShadowV2ResponseConfig.getDefaultResponseConfig()
                        .accept(this, shouldApplyHandleBars));

    }

    @Override
    public Action visit(final SectionRefreshShadowV2ResponseConfig sectionRefreshShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        return getMoveToSection(MoveToSectionScreen.CURRENT, shouldApplyHandleBars,
                (effectiveMappingId, templateSectionMapping, fields) -> SectionRefreshAction.builder()
                        .mappingId(effectiveMappingId)
                        .fields(getSectionRefreshFields(sectionRefreshShadowV2ResponseConfig, fields))
                        .title(templateSectionMapping.getTitle())
                        .overrideBackAction(
                                getRecursiveAction(sectionRefreshShadowV2ResponseConfig.getBackButtonAction()))
                        .build());
    }

    @Override
    public Action visit(final RichTextBottomSheetShadowV2ResponseConfig richTextBottomSheetShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var leftAction = Objects.isNull(richTextBottomSheetShadowV2ResponseConfig.getLeftAction())
                               ? null
                               : richTextBottomSheetShadowV2ResponseConfig.getLeftAction()
                                       .accept(this, false);

        final var rightAction = Objects.isNull(richTextBottomSheetShadowV2ResponseConfig.getRightAction())
                                ? null
                                : richTextBottomSheetShadowV2ResponseConfig.getRightAction()
                                        .accept(this, false);

        final var richTextBottomSheetAction = RichTextBottomSheetPopup.builder()
                .cancelable(richTextBottomSheetShadowV2ResponseConfig.isCancelable())
                .title(richTextBottomSheetShadowV2ResponseConfig.getTitle())
                .description(richTextBottomSheetShadowV2ResponseConfig.getDescription())
                .effects(richTextBottomSheetShadowV2ResponseConfig.getEffects())
                .content(richTextBottomSheetShadowV2ResponseConfig.getContent())
                .imageUrl(richTextBottomSheetShadowV2ResponseConfig.getImageUrl())
                .leftButtonText(richTextBottomSheetShadowV2ResponseConfig.getLeftButtonText())
                .leftAction(leftAction)
                .rightButtonText(richTextBottomSheetShadowV2ResponseConfig.getRightButtonText())
                .rightAction(rightAction)
                .build();

        final var openPopupAction = OpenPopupAction.builder()
                .data(richTextBottomSheetAction)
                .build();

        return applyHandlebarsTransformation(openPopupAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final OpenGenericDialogActionShadowV2ResponseConfig openGenericDialogActionShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var buttonActionList = openGenericDialogActionShadowV2ResponseConfig.getButtonActionConfigs()
                .stream()
                .map(buttonActionConfig -> ButtonAction.builder()
                        .buttonType(buttonActionConfig.getButtonType())
                        .buttonText(buttonActionConfig.getButtonText())
                        .action(getRecursiveAction(buttonActionConfig.getAction()))
                        .build())
                .toList();

        final var genericDialogAction = GenericDialogPopup.builder()
                .title(openGenericDialogActionShadowV2ResponseConfig.getTitle())
                .subtitle(openGenericDialogActionShadowV2ResponseConfig.getSubtitle())
                .cancelable(openGenericDialogActionShadowV2ResponseConfig.isCancelable())
                .buttonStackType(openGenericDialogActionShadowV2ResponseConfig.getButtonStackType())
                .iconUrl(openGenericDialogActionShadowV2ResponseConfig.getIconUrl())
                .imageDetail(openGenericDialogActionShadowV2ResponseConfig.getImageDetail())
                .buttonActionList(buttonActionList)
                .build();

        final var openPopupAction = OpenPopupAction.builder()
                .data(genericDialogAction)
                .build();

        return applyHandlebarsTransformation(openPopupAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final UpdateFieldsActionShadowV2ResponseConfig updateFieldsActionShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var fields = updateFieldsActionShadowV2ResponseConfig.getFields();
        fields.forEach(field -> evaluateBackendRules(field, workflowContext));

        final var updateFieldsAction = UpdateFieldsAction.builder()
                .fields(fields)
                .build();

        return applyHandlebarsTransformation(updateFieldsAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final ApiCallShadowV2ResponseConfig apiCallShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var apiCallAction = ApiCallAction.builder()
                .method(apiCallShadowV2ResponseConfig.getMethod())
                .url(apiCallShadowV2ResponseConfig.getUrl())
                .headers(apiCallShadowV2ResponseConfig.getHeaders())
                .payload(apiCallShadowV2ResponseConfig.getPayload())
                .submitLoader(apiCallShadowV2ResponseConfig.getSubmitLoader())
                .build();

        return applyHandlebarsTransformation(apiCallAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final OpenWebViewActionShadowV2ResponseConfig openWebViewActionShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var openWebViewAction = OpenWebViewAction.builder()
                .url(openWebViewActionShadowV2ResponseConfig.getUrl())
                .title(openWebViewActionShadowV2ResponseConfig.getTitle())
                .build();

        return applyHandlebarsTransformation(openWebViewAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final SelfieHurdleShadowV2ResponseConfig selfieHurdleShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var selfieAction = SelfieAction.builder()
                .uploadUrl(selfieHurdleShadowV2ResponseConfig.getUploadUrl())
                .submitUrl(selfieHurdleShadowV2ResponseConfig.getSubmitUrl())
                .deleteUrl(selfieHurdleShadowV2ResponseConfig.getDeleteUrl())
                .captureButton(selfieHurdleShadowV2ResponseConfig.getCaptureButton())
                .reCaptureButton(selfieHurdleShadowV2ResponseConfig.getReCaptureButton())
                .submitButton(selfieHurdleShadowV2ResponseConfig.getSubmitButton())
                .uploadFailureText(selfieHurdleShadowV2ResponseConfig.getUploadFailureText())
                .submitLoader(selfieHurdleShadowV2ResponseConfig.getSubmitLoader())
                .note(selfieHurdleShadowV2ResponseConfig.getNote())
                .build();

        return applyHandlebarsTransformation(selfieAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final SendSmsHurdleShadowV2ResponseConfig sendSmsHurdleShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var smsLinkingAction = SendSmsAction.builder()
                .title(sendSmsHurdleShadowV2ResponseConfig.getTitle())
                .subtitle(sendSmsHurdleShadowV2ResponseConfig.getSubtitle())
                .smsContent(sendSmsHurdleShadowV2ResponseConfig.getSmsContent())
                .smsDestination(sendSmsHurdleShadowV2ResponseConfig.getSmsDestination())
                .cancelable(sendSmsHurdleShadowV2ResponseConfig.getCancelable())
                .onSmsDeliveredAction(sendSmsHurdleShadowV2ResponseConfig.getOnSmsDeliveredShadowV2ResponseConfig()
                        .accept(this, true))
                .bottomButtonText(sendSmsHurdleShadowV2ResponseConfig.getBottomButtonText())
                .loadingText(sendSmsHurdleShadowV2ResponseConfig.getLoadingText())
                .build();

        return applyHandlebarsTransformation(smsLinkingAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final OpenPopupWithTimerShadowV2ResponseConfig openPopupWithTimerShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var openPopWithTimerAction = OpenPopWithTimerAction.builder()
                .intentData(openPopupWithTimerShadowV2ResponseConfig.getIntentData())
                .timerLabel(openPopupWithTimerShadowV2ResponseConfig.getTimerLabel())
                .imageDetail(openPopupWithTimerShadowV2ResponseConfig.getImageDetail())
                .sessionTimeoutAction(openPopupWithTimerShadowV2ResponseConfig.getSessionTimeoutAction())
                .statusCheckAction(openPopupWithTimerShadowV2ResponseConfig.getStatusCheckAction())
                .build();

        return applyHandlebarsTransformation(openPopWithTimerAction, shouldApplyHandleBars);
    }

    @Override
    @SneakyThrows
    public Action visit(final MoveToSectionWithScreenMappingIdShadowV2ResponseConfig moveToSectionWithScreenMappingIdShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        return getMoveToSection(moveToSectionWithScreenMappingIdShadowV2ResponseConfig.getScreenMappingId(),
                shouldApplyHandleBars,
                (effectiveMappingId, templateSectionMapping, fields) -> MoveToSectionAction.builder()
                        .mappingId(effectiveMappingId)
                        .fields(fields)
                        .title(templateSectionMapping.getTitle())
                        .overrideBackAction(getRecursiveAction(
                                moveToSectionWithScreenMappingIdShadowV2ResponseConfig.getBackButtonAction()))
                        .checkExistingStack(
                                moveToSectionWithScreenMappingIdShadowV2ResponseConfig.isCheckExistingStack())
                        .permissionData(templateSectionMapping.getPermissionData())
                        .build());
    }

    @Override
    public Action visit(final MoveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig moveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {
        return getMoveToSection(
                moveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig.getScreenMappingId(),
                shouldApplyHandleBars,
                (effectiveMappingId, templateSectionMapping, fields) -> MoveToSectionAndClearBackStack.builder()
                        .mappingId(effectiveMappingId)
                        .fields(fields)
                        .title(templateSectionMapping.getTitle())
                        .overrideBackAction(getRecursiveAction(
                                moveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig.getBackButtonAction()))
                        .checkExistingStack(
                                moveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig.isCheckExistingStack())
                        .clearToMappingId(
                                moveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig.getClearStackUpToMappingId())
                        .permissionData(templateSectionMapping.getPermissionData())
                        .build());
    }

    @Override
    public Action visit(final MoveToPreSdkScreenActionShadowV2ResponseConfig moveToPreSdkScreenActionShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        final var moveToPreSdkScreenAction = MoveToPreSdkScreenAction.builder()
                .build();

        return applyHandlebarsTransformation(moveToPreSdkScreenAction, shouldApplyHandleBars);
    }

    @Override
    public Action visit(final ShadowV2ActionShadowV2ResponseConfig shadowV2ActionShadowV2ResponseConfig,
                        final Boolean shouldApplyHandleBars) {

        return applyHandlebarsTransformation(shadowV2ActionShadowV2ResponseConfig.getAction(), shouldApplyHandleBars);
    }

    @SuppressWarnings({"java:S3740", "java:S1905"})
    private void evaluateBackendRules(final Field field,
                                      final JsonNode workflowContextJsonNode) {

        final var rules = (List<FieldRules>) field.getBackendEvaluatedRules();

        if (Objects.nonNull(rules) && !rules.isEmpty()) {

            rules.stream()
                    .map(rule -> Map.entry(rule, rule.getExpression()
                            .evaluate(expressionEvaluator, workflowContextJsonNode)))
                    .forEach(entry -> entry.getKey()
                            .getResult()
                            .accept(new FieldPropertyModifier(entry.getValue(), workflowContext, handleBarsService),
                                    field));
        }
    }

    private Action applyHandlebarsTransformation(final Action action,
                                                 final Boolean shouldApplyHandlebars) {

        if (Boolean.TRUE.equals(shouldApplyHandlebars)) {

            final var actionString = MapperUtils.serializeToString(action);
            final var transformedActionString = handleBarsService.transform(actionString, workflowContext);
            return MapperUtils.deserialize(transformedActionString, Action.class);
        }

        return action;
    }

    private Action getRecursiveAction(final ShadowV2ResponseConfig shadowV2ResponseConfig) {

        return Objects.isNull(shadowV2ResponseConfig)
               ? null
               : shadowV2ResponseConfig.accept(this, false);
    }

    private List<Field> getSectionRefreshFields(final SectionRefreshShadowV2ResponseConfig sectionRefreshShadowV2ResponseConfig,
                                                final List<Field> fields) {

        if (Objects.nonNull(sectionRefreshShadowV2ResponseConfig.getSetDefaultValueToNullForFieldIds())
                && !sectionRefreshShadowV2ResponseConfig.getSetDefaultValueToNullForFieldIds()
                .isEmpty()) {

            fields.forEach(field -> {
                if (sectionRefreshShadowV2ResponseConfig.getSetDefaultValueToNullForFieldIds()
                        .contains(field.getId())) {
                    field.visit(SetFieldDefaultValueToNullVisitor.INSTANCE);
                }
            });
        }

        return fields;
    }
}
