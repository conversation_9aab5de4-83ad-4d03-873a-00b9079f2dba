package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewResponse;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.caches.impl.AddOnProfileCache;
import com.phonepe.verified.kaizen.caches.impl.PrimaryProfileCache;
import com.phonepe.verified.kaizen.caches.impl.ProfileCache;
import com.phonepe.verified.kaizen.caches.impl.ProfileIdProfileStepsCache;
import com.phonepe.verified.kaizen.caches.impl.ProfileStepCache;
import com.phonepe.verified.kaizen.caches.key.AddOnProfileCacheKey;
import com.phonepe.verified.kaizen.caches.key.PrimaryProfileCacheKey;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.keys.ProfileKey;
import com.phonepe.verified.kaizen.models.requests.details.EntityDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.FetchDetailsFromSecondarySources;
import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileCriteria;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileIdentifier;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileVisitor;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.CacheManagementService;
import com.phonepe.verified.kaizen.services.DetailsService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.StateMachineService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.GenerateSummaryViewResponseSummaryViewConfigVisitor;
import com.phonepe.verified.kaizen.services.visitors.GenerateSummaryViewResponseSummaryViewConfigVisitor.GenerateSummaryViewResponseSummaryViewConfigVisitorMessage;
import com.phonepe.verified.kaizen.services.visitors.GetAllEvaluatedStepActionConfigsVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetAllEvaluationRuleUiResponseConfigVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetAllScreenMappingIdVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetAllStandardScreenConfigsVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetAllStandardStepActionConfigsVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetStandardStepActionConfigForGivenActionMappingIdVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetWorkflowTagConfigVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredProfileStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredAddOnProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredProfile.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ProfileRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ProfileStepRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ProfileServiceImpl implements ProfileService {

    private final CacheManagementService cacheManagementService;

    private final ProfileCache profileCache;

    private final WorkflowService workflowService;

    private final ProfileStepCache profileStepCache;

    private final ProfileRepository profileRepository;

    private final ProfileStepRepository profileStepRepository;

    private final ProfileIdProfileStepsCache profileIdProfileStepsCache;

    private final DetailsService detailsService;

    private final HopeLangService hopeLangService;

    private final StateMachineService stateMachineService;

    private final PrimaryProfileCache primaryProfileCache;

    private final AddOnProfileCache addOnProfileCache;

    private final GenerateSummaryViewResponseSummaryViewConfigVisitor generateSummaryViewResponseSummaryViewConfigVisitor;

    private final DataProvider<KaizenConfig> appConfigDataProvider;

    @Override
    public Profile create(final Profile profile,
                          final String lastUpdatedBy,
                          final String approvedBy) {

        validateUniqueProfileStepMappingIds(profile);
        validateUniqueScreenMappingIdsAndActionMappingIds(profile);
        validateProfileAlreadyExists(profile);
        validateProfileActionTypeExists(profile);
        validateProfileEvaluationAndExecutionRules(profile);

        final var storedProfile = BuildUtils.toStoredProfile(profile, lastUpdatedBy, approvedBy);

        final var outputStoredProfile = profileRepository.save(storedProfile, sp -> {
                    persistProfileSteps(sp.getProfileId(), profile.getProfileSteps(), lastUpdatedBy);
                    return sp;
                })
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.DB_ERROR, Map.of()));

        refreshAllProfileCaches();
        return get(outputStoredProfile.getProfileId(), true);
    }

    @Override
    public Profile get(final String profileId,
                       final boolean fetchProfileSteps) {

        return buildProfileResponse(() -> profileCache.get(profileId), fetchProfileSteps);
    }

    @Override
    public List<Profile> get(final String organization,
                             final String namespace,
                             final String type,
                             final boolean fetchProfileSteps) {

        return profileRepository.select(organization, namespace, type)
                .stream()
                .map(sp -> buildProfileResponse(() -> BuildUtils.toProfile(sp), fetchProfileSteps))
                .toList();
    }

    @Override
    public List<Profile> get(final List<ProfileCriteria> profileCriteria,
                             final boolean fetchProfileSteps) {

        return profileCriteria.stream()
                .map(t -> profileRepository.select(t.getOrganization(), t.getNamespace(), t.getType()))
                .flatMap(List::stream)
                .map(sp -> buildProfileResponse(() -> BuildUtils.toProfile(sp), fetchProfileSteps))
                .toList();
    }

    @Override
    public List<Profile> get(final String organization,
                             final String namespace,
                             final boolean fetchProfileSteps) {

        return profileRepository.select(organization, namespace)
                .stream()
                .map(sp -> buildProfileResponse(() -> BuildUtils.toProfile(sp), fetchProfileSteps))
                .toList();
    }

    @Override
    public List<Profile> getAll(final boolean fetchProfileSteps) {

        return profileRepository.selectAll()
                .stream()
                .map(sp -> buildProfileResponse(() -> BuildUtils.toProfile(sp), fetchProfileSteps))
                .toList();
    }

    @Override
    public Set<String> getAllOrganisations(final ProfileType profileType) {
        return profileRepository.selectAll()
                .stream()
                .filter(storedProfile -> storedProfile.getProfileType() == profileType)
                .map(StoredProfile::getOrganization)
                .collect(Collectors.toSet());
    }

    @Override
    public List<StandardProfileScreenConfig> getAllStandardProfileScreenConfigs(final Profile profile) {

        return profile.getProfileSteps()
                .stream()
                .flatMap(profileStep -> profileStep.getProfileScreenConfig()
                        .accept(GetAllStandardScreenConfigsVisitor.INSTANCE)
                        .stream())
                .toList();
    }

    @Override
    public Set<String> getNamespaces(final String organization,
                                     final ProfileType profileType) {
        return profileRepository.select(organization, profileType)
                .stream()
                .map(StoredProfile::getNamespace)
                .collect(Collectors.toSet());
    }

    @Override
    public PrimaryProfile getPrimaryProfile(final PrimaryProfileCacheKey primaryProfileCacheKey,
                                            final boolean fetchProfileSteps) {

        return buildProfileResponse(() -> primaryProfileCache.get(primaryProfileCacheKey), fetchProfileSteps);
    }

    public PrimaryProfile getPrimaryProfileFromDb(final PrimaryProfileCacheKey primaryProfileCacheKey) {

        final var storedProfile = profileRepository.selectPrimaryProfile(primaryProfileCacheKey.getOrganization(),
                        primaryProfileCacheKey.getNamespace(), primaryProfileCacheKey.getType(),
                        primaryProfileCacheKey.getVersion())
                .stream()
                .findFirst()
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                        Map.ofEntries(Map.entry(Fields.organization, primaryProfileCacheKey.getOrganization()),
                                Map.entry(Fields.namespace, primaryProfileCacheKey.getNamespace()),
                                Map.entry(Fields.type, primaryProfileCacheKey.getType()),
                                Map.entry(Fields.version, primaryProfileCacheKey.getVersion()),
                                Map.entry(Fields.profileType, primaryProfileCacheKey.getProfileType()))));

        return (PrimaryProfile) BuildUtils.toProfile(storedProfile);
    }

    public Profile getProfileFromDb(final String profileId) {

        final var storedProfile = profileRepository.select(profileId)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                        Map.of(Fields.profileId, profileId)));

        return BuildUtils.toProfile(storedProfile);
    }

    @Override
    public ProfileStep getProfileStep(final String profileStepId) {

        return profileStepCache.get(profileStepId);
    }

    @Override
    public ProfileStep getProfileStep(final String profileId,
                                      final String profileStepMappingId) {

        final var profileStepList = profileIdProfileStepsCache.get(profileId);

        return profileStepList.stream()
                .filter(profileStep -> profileStepMappingId.equals(profileStep.getProfileStepMappingId()))
                .findFirst()
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_STEP_NOT_FOUND,
                        Map.of(Fields.profileId, profileId, StoredProfileStep.Fields.profileStepMappingId,
                                profileStepMappingId)));
    }

    public ProfileStep getProfileStepFromDb(final String profileStepId) {

        final var storedProfileStep = profileStepRepository.selectProfileStep(profileStepId)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_STEP_NOT_FOUND,
                        Map.of(StoredProfileStep.Fields.profileStepId, profileStepId)));

        return BuildUtils.toProfileStep(storedProfileStep);
    }

    @Override
    public Optional<ProfileStep> getProfileStepFromGivenActionMappingId(final Profile profile,
                                                                        final String actionMappingId) {

        final var standardProfileScreenConfig = getStandardProfileScreenConfigForGivenActionMappingId(profile,
                actionMappingId);

        return profile.getProfileSteps()
                .stream()
                .filter(profileStep -> profileStep.getProfileScreenConfig()
                        .accept(GetAllScreenMappingIdVisitor.INSTANCE)
                        .contains(standardProfileScreenConfig.getScreenMappingId()))
                .findFirst();
    }

    public List<ProfileStep> getProfileStepsFromDb(final String profileId) {

        return profileStepRepository.selectProfileSteps(profileId)
                .stream()
                .map(BuildUtils::toProfileStep)
                .toList();
    }

    @Override
    public StandardProfileScreenConfig getStandardProfileScreenConfigForGivenActionMappingId(final Profile profile,
                                                                                             final String actionMappingId) {

        final var standardProfileScreenConfigs = getAllStandardProfileScreenConfigs(profile);

        return standardProfileScreenConfigs.stream()
                .filter(spsc -> !spsc.getStepActionConfig()
                        .accept(GetStandardStepActionConfigForGivenActionMappingIdVisitor.INSTANCE, actionMappingId)
                        .isEmpty())
                .findFirst()
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_MAPPING_ID_NOT_FOUND,
                        Map.of("actionMappingId", actionMappingId, "profileId", profile.getProfileId())));
    }

    @Override
    public StandardStepActionConfig getStandardStepActionConfigForGivenActionMappingId(final Profile profile,
                                                                                       final String actionMappingId) {

        final var standardProfileScreenConfigs = getAllStandardProfileScreenConfigs(profile);

        final var allStandardStepActionConfig = ActionService.getAllStandardStepActionConfig(
                standardProfileScreenConfigs);

        return ActionService.filterActionMappingId(allStandardStepActionConfig, actionMappingId);
    }

    @Override
    public SummaryViewResponse getSummaryView(final String workflowId,
                                              final String intent,
                                              final long componentKitVersion) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        final var profile = get(storedWorkflow.getProfileId(), false);

        final var summaryViewConfig = profile.getSummaryViewConfig();

        if (Objects.isNull(summaryViewConfig)) {
            throw KaizenException.create(KaizenResponseCode.SUMMARY_CONFIG_NOT_AVAILABLE, Map.of());
        }

        return summaryViewConfig.accept(generateSummaryViewResponseSummaryViewConfigVisitor,
                GenerateSummaryViewResponseSummaryViewConfigVisitorMessage.builder()
                        .workflowId(workflowId)
                        .componentKitVersion(componentKitVersion)
                        .intent(intent)
                        .build());
    }

    @Override
    public Set<String> getTypes(final String organization,
                                final String namespace,
                                final ProfileType profileType) {
        return profileRepository.select(organization, namespace, profileType)
                .stream()
                .map(StoredProfile::getType)
                .collect(Collectors.toSet());
    }

    @Override
    public AddOnProfile getAddOnProfile(final AddOnProfileCacheKey addOnProfileCacheKey,
                                        final String addOnType,
                                        final boolean fetchProfileSteps) {

        return getAddOnProfiles(addOnProfileCacheKey, fetchProfileSteps).stream()
                .filter(addOnProfile -> addOnType.equals(addOnProfile.getAddOnType()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public List<AddOnProfile> getAddOnProfiles(final AddOnProfileCacheKey addOnProfileCacheKey,
                                               final boolean fetchProfileSteps) {

        final var addOnProfileList = addOnProfileCache.get(addOnProfileCacheKey);

        return addOnProfileList.stream()
                .map(addOnProfile -> buildProfileResponse(() -> addOnProfile, fetchProfileSteps))
                .toList();
    }

    public List<AddOnProfile> getAddOnProfilesFromDb(final AddOnProfileCacheKey addOnProfileCacheKey) {

        final var storedProfileList = profileRepository.selectStoredAddOnProfile(addOnProfileCacheKey.getOrganization(),
                addOnProfileCacheKey.getNamespace(), addOnProfileCacheKey.getType(), addOnProfileCacheKey.getVersion());

        if (storedProfileList.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                    Map.ofEntries(Map.entry(Fields.organization, addOnProfileCacheKey.getOrganization()),
                            Map.entry(Fields.namespace, addOnProfileCacheKey.getNamespace()),
                            Map.entry(Fields.type, addOnProfileCacheKey.getType()),
                            Map.entry(Fields.version, addOnProfileCacheKey.getVersion()),
                            Map.entry(Fields.profileType, addOnProfileCacheKey.getProfileType())));
        }

        return storedProfileList.stream()
                .map(BuildUtils::toProfile)
                .map(AddOnProfile.class::cast)
                .toList();
    }

    @Override
    public Set<String> getAddOnTypes(final String organization,
                                     final String namespace,
                                     final String type,
                                     final String version) {

        return profileRepository.select(organization, namespace, type, version, ProfileType.ADD_ON)
                .stream()
                .map(storedProfile -> ((StoredAddOnProfile) storedProfile).getAddOnType())
                .collect(Collectors.toSet());
    }

    @Override
    public Optional<AddOnProfile> getValidAddOnProfileForEntity(final String entityId,
                                                                final EntityType entityType,
                                                                final ProfileKey profileKey,
                                                                final boolean fetchProfileSteps) {

        final var profiles = getAddOnProfiles(AddOnProfileCacheKey.builder()
                .organization(profileKey.getOrganization())
                .namespace(profileKey.getNamespace())
                .type(profileKey.getType())
                .version(profileKey.getVersion())
                .build(), fetchProfileSteps);

        if (profiles.isEmpty()) {
            return Optional.empty();
        }

        final var entityDetailsProfileCriteriaList = appConfigDataProvider.getData()
                .getEntityDetailsConfig()
                .getEntityDetailsProfileCriteriaList(profileKey.getOrganization(), profileKey.getNamespace(),
                        profileKey.getType());

        if (entityDetailsProfileCriteriaList.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.ENTITY_DETAILS_PROFILE_CRITERIA_TYPE_NOT_CONFIGURED,
                    Map.of());
        }

        final var entityDetails = detailsService.getEntityDetails(EntityDetailsRequest.builder()
                .entityId(entityId)
                .entityType(entityType)
//                .requiredDetails(Constants.ENTITY_ALL_REQUIRED_DETAILS)
                .fetchDetailsFromSecondarySources(FetchDetailsFromSecondarySources.NEVER)
                .profileCriteria(entityDetailsProfileCriteriaList)
                .build());

        return profiles.stream()
                .filter(profile -> !Objects.isNull(profile.getPriority()))
                .filter(profile -> !Objects.isNull(profile.getRule()))
                .sorted(Comparator.comparing(AddOnProfile::getPriority))
                .filter(profile -> hopeLangService.evaluate(profile.getRule(),
                        MapperUtils.convertToJsonNode(entityDetails)))
                .findFirst();
    }

    @Override
    public Set<String> getVersions(final String organization,
                                   final String namespace,
                                   final String type,
                                   final ProfileType profileType) {
        return profileRepository.select(organization, namespace, type, profileType)
                .stream()
                .map(StoredProfile::getVersion)
                .collect(Collectors.toSet());
    }

    @Override
    public Profile update(final Profile profile,
                          final String lastUpdatedBy,
                          final String approvedBy) {

        validateUniqueProfileStepMappingIds(profile);
        validateUniqueScreenMappingIdsAndActionMappingIds(profile);
        validateProfileActionTypeExists(profile);
        validateProfileEvaluationAndExecutionRules(profile);

        final var existingProfile = get(profile.getProfileId(), false);

        validateProfileIdentifierUnmodified(profile, existingProfile);

        final var profileAlreadyStored = profileRepository.select(profile.getProfileId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_NOT_FOUND,
                        Map.of(Fields.profileId, profile.getProfileId())));

        final var storedProfile = BuildUtils.toAlreadyCreatedStoredProfile(profile, profileAlreadyStored.getId(),
                lastUpdatedBy, approvedBy);

        final var outputStoredProfile = profileRepository.save(storedProfile, sp -> {
                    updateProfileSteps(sp.getProfileId(), profile.getProfileSteps(), lastUpdatedBy);
                    return sp;
                })
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.DB_ERROR, Map.of()));

        refreshAllProfileCaches();
        return get(outputStoredProfile.getProfileId(), true);
    }

    @Override
    public void validateProfileActionTypeExists(final Profile profile) {

        final var standardProfileScreenConfigs = profile.getProfileSteps()
                .stream()
                .flatMap(profileStep -> profileStep.getProfileScreenConfig()
                        .accept(GetAllStandardScreenConfigsVisitor.INSTANCE)
                        .stream())
                .toList();

        final var invalidStepActionContexts = standardProfileScreenConfigs.stream()
                .flatMap(standardProfileScreenConfig -> standardProfileScreenConfig.getStepActionConfig()
                        .accept(GetAllStandardStepActionConfigsVisitor.INSTANCE, null)
                        .stream())
                .filter(standardStepActionConfig -> !stateMachineService.checkIfExists(
                        standardStepActionConfig.getActionType(), standardStepActionConfig.getStateMachineVersion()))
                .toList();

        if (!invalidStepActionContexts.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.STATE_MACHINE_ACTION_NOT_FOUND,
                    Map.of("actionType", invalidStepActionContexts));
        }
    }

    @Override
    public void validateProfileAlreadyExists(final Profile profile) {

        final var existingProfiles = profile.accept(new ProfileVisitor<List<StoredProfile>, Void>() {
            @Override
            public List<StoredProfile> visit(final AddOnProfile addOnProfile,
                                             final Void data) {

                return profileRepository.selectAddOnProfile(addOnProfile.getOrganization(), addOnProfile.getNamespace(),
                        addOnProfile.getType(), addOnProfile.getVersion(), addOnProfile.getAddOnType());
            }

            @Override
            public List<StoredProfile> visit(final PrimaryProfile primaryProfile,
                                             final Void data) {

                return profileRepository.selectPrimaryProfile(primaryProfile.getOrganization(),
                        primaryProfile.getNamespace(), primaryProfile.getType(), primaryProfile.getVersion());
            }
        }, null);

        if (!existingProfiles.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_ALREADY_EXIST,
                    Map.of(Fields.organization, profile.getOrganization(), Fields.namespace, profile.getNamespace(),
                            Fields.type, profile.getType(), Fields.version, profile.getVersion(), Fields.profileType,
                            profile.getProfileType()));
        }
    }

    @Override
    public void validateProfileEvaluationAndExecutionRules(final Profile profile) {

        final var workflowTagConfig = profile.accept(GetWorkflowTagConfigVisitor.INSTANCE, null);

        final var workflowTagRuleFailed = Objects.isNull(workflowTagConfig)
                                          ? List.of()
                                          : workflowTagConfig.getOrderedWorkflowTagRules()
                                                  .stream()
                                                  .filter(rule -> !hopeLangService.isParsable(rule.getEvaluationRule()))
                                                  .toList();

        final var profileStepExecutionRuleFailed = profile.getProfileSteps()
                .stream()
                .filter(profileStep -> !hopeLangService.isParsable(profileStep.getExecutionRule()))
                .toList();

        final var standardProfileScreenConfigs = profile.getProfileSteps()
                .stream()
                .flatMap(profileStep -> profileStep.getProfileScreenConfig()
                        .accept(GetAllStandardScreenConfigsVisitor.INSTANCE)
                        .stream())
                .toList();

        final var stepActionEvaluationRuleFailed = standardProfileScreenConfigs.stream()
                .flatMap(standardProfileScreenConfig -> standardProfileScreenConfig.getStepActionConfig()
                        .accept(GetAllEvaluatedStepActionConfigsVisitor.INSTANCE, null)
                        .stream())
                .filter(evaluatedStepActionConfig -> !hopeLangService.isParsable(
                        evaluatedStepActionConfig.getEvaluationRule()))
                .toList();

        final var uiResponseConfigRuleFailed = standardProfileScreenConfigs.stream()
                .flatMap(standardProfileScreenConfig -> getAllUiResponseConfig(standardProfileScreenConfig).map(
                        uiResponseConfig -> uiResponseConfig.accept(
                                        GetAllEvaluationRuleUiResponseConfigVisitor.INSTANCE)
                                .stream()
                                .filter(evaluationRuleResponseConfigs -> !hopeLangService.isParsable(
                                        evaluationRuleResponseConfigs.getEvaluationRule()))
                                .toList()))
                .flatMap(List::stream)
                .toList();

        if (!workflowTagRuleFailed.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.HOPE_RULE_VALIDATION_FAILED,
                    Map.of("workflowTagRuleFailed", workflowTagRuleFailed));
        }

        if (!profileStepExecutionRuleFailed.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.HOPE_RULE_VALIDATION_FAILED,
                    Map.of("profileStepExecutionRuleFailed", profileStepExecutionRuleFailed));
        }

        if (!stepActionEvaluationRuleFailed.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.HOPE_RULE_VALIDATION_FAILED,
                    Map.of("stepActionEvaluationRuleFailed", stepActionEvaluationRuleFailed));
        }

        if (!uiResponseConfigRuleFailed.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.HOPE_RULE_VALIDATION_FAILED,
                    Map.of("uiResponseConfigRuleFailed", uiResponseConfigRuleFailed));
        }
    }

    @Override
    public void validateProfileIdentifierUnmodified(final Profile updatedProfile,
                                                    final Profile profileAlreadyStored) {
        final var updatedProfileIdentifier = ProfileIdentifier.builder()
                .organization(updatedProfile.getOrganization())
                .namespace(updatedProfile.getNamespace())
                .type(updatedProfile.getType())
                .version(updatedProfile.getVersion())
                .profileType(updatedProfile.getProfileType())
                .addOnType(updatedProfile.getAddOnType())
                .build();

        final var existingProfileIdentifier = ProfileIdentifier.builder()
                .organization(profileAlreadyStored.getOrganization())
                .namespace(profileAlreadyStored.getNamespace())
                .type(profileAlreadyStored.getType())
                .version(profileAlreadyStored.getVersion())
                .profileType(profileAlreadyStored.getProfileType())
                .addOnType(profileAlreadyStored.getAddOnType())
                .build();

        if (!existingProfileIdentifier.equals(updatedProfileIdentifier)) {
            throw new KaizenException(KaizenResponseCode.INVALID_REQUEST, Map.of());
        }
    }

    @Override
    public void validateUniqueActionMappingIds(final List<StandardProfileScreenConfig> standardProfileScreenConfigs) {

        final var actionMappingIds = standardProfileScreenConfigs.stream()
                .flatMap(standardProfileScreenConfig -> standardProfileScreenConfig.getStepActionConfig()
                        .accept(GetAllStandardStepActionConfigsVisitor.INSTANCE, null)
                        .stream())
                .map(StandardStepActionConfig::getActionMappingId)
                .toList();

        final var duplicateActionMappingIds = actionMappingIds.stream()
                .filter(actionMappingId -> Collections.frequency(actionMappingIds, actionMappingId) > 1)
                .toList();

        if (!duplicateActionMappingIds.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.DUPLICATE_ACTION_MAPPING_ID_FOUND,
                    Map.of("duplicateActionMappingIds", duplicateActionMappingIds));
        }
    }

    @Override
    public void validateUniqueProfileStepMappingIds(final Profile profile) {

        final var profileStepMappingIds = profile.getProfileSteps()
                .stream()
                .map(ProfileStep::getProfileStepMappingId)
                .toList();

        final var duplicateProfileStepMappingIds = profileStepMappingIds.stream()
                .filter(profileStepMappingId -> Collections.frequency(profileStepMappingIds, profileStepMappingId) > 1)
                .toList();

        if (!duplicateProfileStepMappingIds.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.DUPLICATE_PROFILE_STEP_MAPPING_ID_FOUND,
                    Map.of("duplicateProfileStepMappingIds", duplicateProfileStepMappingIds));
        }
    }

    @Override
    public void validateUniqueScreenMappingIdsAndActionMappingIds(final Profile profile) {

        final var standardProfileScreenConfigs = profile.getProfileSteps()
                .stream()
                .flatMap(profileStep -> profileStep.getProfileScreenConfig()
                        .accept(GetAllStandardScreenConfigsVisitor.INSTANCE)
                        .stream())
                .toList();

        final var screenMappingIds = standardProfileScreenConfigs.stream()
                .map(StandardProfileScreenConfig::getScreenMappingId)
                .toList();

        final var duplicateScreenMappingIds = screenMappingIds.stream()
                .filter(screenMappingId -> Collections.frequency(screenMappingIds, screenMappingId) > 1)
                .toList();

        if (!duplicateScreenMappingIds.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.DUPLICATE_SCREEN_MAPPING_ID_FOUND,
                    Map.of("duplicateScreenMappingIds", duplicateScreenMappingIds));
        }

        validateUniqueActionMappingIds(standardProfileScreenConfigs);
    }

    private <T extends Profile> T buildProfileResponse(final Supplier<T> profileSupplier,
                                                       final boolean fetchProfileSteps) {

        final var profile = profileSupplier.get();

        if (fetchProfileSteps) {

            return (T) profile.withProfileSteps(profileIdProfileStepsCache.get(profile.getProfileId()));
        }

        return profile;
    }

    private Stream<UiResponseConfig> getAllUiResponseConfig(final StandardProfileScreenConfig standardProfileScreenConfig) {
        return Stream.concat(Stream.concat(standardProfileScreenConfig.getSuccessResponseConfig()
                .values()
                .stream(), Objects.nonNull(standardProfileScreenConfig.getSkipWorkflowConfig())
                           ? standardProfileScreenConfig.getSkipWorkflowConfig()
                                   .getSkipWorkflowResponseConfig()
                                   .values()
                                   .stream()
                           : Stream.empty()), Objects.nonNull(standardProfileScreenConfig.getSkipWorkflowStepConfig())
                                              ? standardProfileScreenConfig.getSkipWorkflowStepConfig()
                                                      .getSkipWorkflowStepResponseConfig()
                                                      .values()
                                                      .stream()
                                              : Stream.empty());
    }

    private void persistProfileSteps(final String profileId,
                                     final List<ProfileStep> profileSteps,
                                     final String lastUpdatedBy) {

        final var storedProfileSteps = profileSteps.stream()
                .map(ps -> BuildUtils.toStoredProfileStep(profileId, ps, lastUpdatedBy))
                .toList();

        final var saveProfileStepsSucceed = profileStepRepository.saveAll(storedProfileSteps);

        if (!saveProfileStepsSucceed) {
            throw KaizenException.create(KaizenResponseCode.DB_ERROR, Map.of());
        }
    }

    private void refreshAllProfileCaches() {
        try {
            cacheManagementService.invalidateCacheOnAllNodes(
                    List.of(CacheName.PROFILE_CACHE, CacheName.PROFILE_STEP_CACHE,
                            CacheName.PROFILE_ID_PROFILE_STEPS_CACHE, CacheName.PRIMARY_PROFILE_CACHE,
                            CacheName.ADD_ON_PROFILE_CACHE));
        } catch (final Exception ex) {
            log.warn("Failed to invalidate Caches", ex);
        }
    }

    private void updateProfileSteps(final String profileId,
                                    final List<ProfileStep> profileSteps,
                                    final String lastUpdatedBy) {

        final var profileStepsToUpdate = profileSteps.stream()
                .map(ps -> {

                    final var profileStepAlreadyStored = profileStepRepository.select(ps.getProfileStepId())
                            .orElseThrow(
                                    () -> KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_STEP_NOT_FOUND,
                                            Map.of()));

                    return BuildUtils.toAlreadyCreatedStoredProfileStep(profileId, ps, profileStepAlreadyStored.getId(),
                            lastUpdatedBy);
                })
                .toList();

        final var saveProfileStepsSucceed = profileStepRepository.saveAll(profileStepsToUpdate);

        if (!saveProfileStepsSucceed) {
            throw KaizenException.create(KaizenResponseCode.DB_ERROR, Map.of());
        }
    }
}
