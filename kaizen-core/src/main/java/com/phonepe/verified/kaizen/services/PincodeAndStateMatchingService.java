package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.models.data.common.PincodeDetails;
import java.util.Optional;
import java.util.Set;

public interface PincodeAndStateMatchingService {

    Optional<String> getCityFromPincodeDetails(final String pincode,
                                               final Set<String> pincodeSources);

    Optional<String> getStateFromPincodeDetails(final String pincode,
                                                final Set<String> pincodeSources);

    Optional<PincodeDetails> getPincodeDetailsFromPincode(final String pincode,
                                                          final Set<String> pincodeSources);

    PincodeDetails getPincodeDetailsFromPincodeWithException(final String pincode,
                                                             final Set<String> pincodeSources);
}
