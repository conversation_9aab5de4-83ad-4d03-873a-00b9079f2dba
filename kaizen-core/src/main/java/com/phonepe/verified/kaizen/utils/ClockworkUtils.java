package com.phonepe.verified.kaizen.utils;

import com.phonepe.platform.clockwork.model.OnceOnlyTimeSpec;
import com.phonepe.platform.clockwork.model.Period;
import com.phonepe.platform.clockwork.model.PeriodUnit;
import com.phonepe.platform.clockwork.model.RepeatableTimeSpec;
import com.phonepe.platform.clockwork.model.SchedulingRequest;
import com.phonepe.platform.clockwork.model.TimeSpec;
import com.phonepe.platform.clockwork.model.request.ApiCallScheduledAction;
import com.phonepe.platform.clockwork.model.request.CallType;
import io.dropwizard.util.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Set;
import javax.ws.rs.core.MediaType;
import lombok.experimental.UtilityClass;
import org.apache.http.HttpStatus;

@UtilityClass
public class ClockworkUtils {

    private static final Set<Integer> SUCCESS_HTTP_CODES = Set.of(HttpStatus.SC_OK, HttpStatus.SC_ACCEPTED,
            HttpStatus.SC_NO_CONTENT);

    public LocalDateTime getSchedulingDelayDate(final Duration duration) {
        return LocalDateTime.now()
                .plusSeconds(duration.toSeconds());
    }

    public SchedulingRequest getActionMessageSchedulingRequest(final String actionMessage,
                                                               final String baseUrl,
                                                               final String callbackPath,
                                                               final LocalDateTime delay) {
        return SchedulingRequest.builder()
                .action(ApiCallScheduledAction.builder()
                        .retryOnFailure(true)
                        .ignoreOnFailure(false)
                        .callType(CallType.POST)
                        .headers(FeatureEnvironmentUtils.isFeatureEnvApplicable()
                                 ? Constants.CLOCKWORK_STAGE_HEADERS
                                 : Constants.CLOCKWORK_PROD_HEADERS)
                        .mimeType(MediaType.APPLICATION_JSON)
                        .successStatusCodes(SUCCESS_HTTP_CODES)
                        .olympusAuthRequired(true)
                        .url(String.format("%s%s", baseUrl, callbackPath))
                        .payload(actionMessage)
                        .build())
                .time(OnceOnlyTimeSpec.builder()
                        .at(Date.from(delay.atZone(ZoneId.systemDefault())
                                .toInstant()))
                        .build())
                .build();
    }

    public SchedulingRequest buildRepeatableScheduleRequest(final LocalDateTime start,
                                                            final LocalDateTime endTime,
                                                            final Object message,
                                                            final String callbackUrl,
                                                            final Duration statusCheckInterval) {

        final var apiCallScheduledAction = ApiCallScheduledAction.builder()
                .url(callbackUrl)
                .callType(CallType.POST)
                .mimeType(MediaType.APPLICATION_JSON)
                .payload(MapperUtils.serializeToString(message))
                .successStatusCodes(SUCCESS_HTTP_CODES)
                .gandalfAuthRequired(true)
                .retryOnFailure(true)
                .ignoreOnFailure(false)
                .headers(FeatureEnvironmentUtils.isFeatureEnvApplicable()
                         ? Constants.CLOCKWORK_STAGE_HEADERS
                         : Constants.CLOCKWORK_PROD_HEADERS)
                .olympusAuthRequired(true)
                .build();

        final TimeSpec timeSpec = getTimeSpec(start, endTime, statusCheckInterval);

        return SchedulingRequest.builder()
                .time(timeSpec)
                .action(apiCallScheduledAction)
                .build();
    }

    private TimeSpec getTimeSpec(final LocalDateTime start,
                                 final LocalDateTime endTime,
                                 final Duration statusCheckInterval) {

        return RepeatableTimeSpec.builder()
                .start(DateUtils.toJavaUtilDate(start))
                .end(DateUtils.toJavaUtilDate(endTime))
                .interval(Period.builder()
                        .every(statusCheckInterval.toSeconds())
                        .unit(PeriodUnit.SECONDS)
                        .build())
                .build();
    }
}
