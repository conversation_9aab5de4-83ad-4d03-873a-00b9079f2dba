package com.phonepe.verified.kaizen.models.data.contexts;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.verified.kaizen.models.data.contexts.ContextType.Names;
import com.phonepe.verified.kaizen.models.data.contexts.visitor.TransitionContextTypeVisitor;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "contextType", visible = true)
@JsonSubTypes(value = {@Type(value = EmptyTransitionContext.class, name = Names.EMPTY_TRANSITION_CONTEXT),
        @Type(value = ConsentTransitionContext.class, name = Names.CONSENT_TRANSITION_CONTEXT),
        @Type(value = DocumentUploadActionTransitionContext.class, name = Names.DOCUMENT_UPLOAD_ACTION_TRANSITION_CONTEXT),
        @Type(value = KeyValuePairsActionTransitionContext.class, name = Names.KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT),
        @Type(value = PersistKeyValuePairsActionTransitionContext.class, name = Names.PERSIST_KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT)})
public abstract class TransitionContext {

    @NotNull
    private ContextType contextType;

    public abstract <T, U> T accept(TransitionContextTypeVisitor<T, U> visitor,
                                    U data);
}
