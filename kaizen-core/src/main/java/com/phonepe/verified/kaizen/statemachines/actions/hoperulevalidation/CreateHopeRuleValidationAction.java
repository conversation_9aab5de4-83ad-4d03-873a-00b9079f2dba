package com.phonepe.verified.kaizen.statemachines.actions.hoperulevalidation;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.hoperule.HopeRuleValidationContext;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "createHopeRuleValidationAction")
public class CreateHopeRuleValidationAction extends CreateEntryBaseAction {

    private final HopeLangService hopeLangService;
    private final WorkflowStepService workflowStepService;
    private final Provider<WorkflowContextStore> workflowContextStore;
    private final Provider<ActionExecutorActor> actionExecutorActorProvider;
    private final HandleBarsService handleBarsService;

    @Inject
    public CreateHopeRuleValidationAction(final ActionService actionService,
                                          final WorkflowService workflowService,
                                          final HopeLangService hopeLangService,
                                          final ClockworkClient clockworkClient,
                                          final ActionRepository actionRepository,
                                          final WorkflowStepService workflowStepService,
                                          final DataProvider<KaizenConfig> appConfigDataProvider,
                                          final Provider<WorkflowContextStore> workflowContextStore,
                                          final Provider<EventIngestionActor> eventIngestionActorProvider,
                                          final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                          final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor,
                                          final HandleBarsService handleBarsService) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);

        this.hopeLangService = hopeLangService;
        this.workflowStepService = workflowStepService;
        this.workflowContextStore = workflowContextStore;
        this.actionExecutorActorProvider = actionExecutorActorProvider;
        this.handleBarsService = handleBarsService;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        final var hopeEvaluationContext = stateContext.getExtendedState()
                .get(StepActionContext.class, HopeRuleValidationContext.class);

        final var workflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var workflowContext = workflowContextStore.get()
                .getWorkflowContext(workflowStep.getWorkflowId());

        final var workflowContextNode = MapperUtils.convertToJsonNode(workflowContext);

        final var matchedRule = hopeEvaluationContext.getHopeEvaluationRules()
                .stream()
                .filter(evaluationRule -> hopeLangService.evaluate(evaluationRule.getEvaluationRule(),
                        workflowContextNode))
                .findFirst();

        if (matchedRule.isPresent()) {

            final var evaluatedErrorCode = handleBarsService.transform(matchedRule.get()
                    .getErrorCode(), workflowContextNode);

            final var actionFailureErrorCode = ActionFailureErrorCode.valueOf(evaluatedErrorCode);

            actionExecutorActorProvider.get()
                    .publish(ActionExecutionMessage.builder()
                            .actionId(storedAction.getActionId())
                            .eventToTrigger(Events.FAIL_HOPE_RULE_VALIDATION)
                            .userDetails(Constants.PVCORE_SYSTEM_USER)
                            .actionFailureErrorCode(actionFailureErrorCode)
                            .build());
        } else {
            actionExecutorActorProvider.get()
                    .publish(ActionExecutionMessage.builder()
                            .actionId(storedAction.getActionId())
                            .eventToTrigger(Events.SUCCEED_HOPE_RULE_VALIDATION)
                            .userDetails(Constants.PVCORE_SYSTEM_USER)
                            .build());
        }
    }
}
