package com.phonepe.verified.kaizen.statemachines.actions.confirmation;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.ConfirmationActionContext;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.UpdateStateBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.SneakyThrows;
import org.springframework.statemachine.StateContext;

@Singleton
@ActionKey("sendForConfirmationAction")
public class SendForConfirmationAction extends UpdateStateBaseAction {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowStepService workflowStepService;

    private final DynamicUiResponseService dynamicUiResponseService;

    @Inject
    public SendForConfirmationAction(final ActionService actionService,
                                     final ActionRepository actionRepository,
                                     final Provider<WorkflowContextStore> workflowContextStore,
                                     final Provider<EventIngestionActor> eventIngestionActorProvider,
                                     final ProfileService profileService,
                                     final WorkflowStepService workflowStepService,
                                     final DynamicUiResponseService dynamicUiResponseService) {
        super(actionService, actionRepository, workflowContextStore, eventIngestionActorProvider);
        this.actionService = actionService;
        this.profileService = profileService;
        this.workflowStepService = workflowStepService;
        this.dynamicUiResponseService = dynamicUiResponseService;
    }

    @Override
    @SneakyThrows
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedAction.getWorkflowStepId());

        final var confirmationActionContext = (ConfirmationActionContext) actionService.extractStepActionContext(
                storedAction.getActionId(), profileStep.getProfileScreenConfig());

        dynamicUiResponseService.sendResponseToUi(uiRequestContext, confirmationActionContext.getUiResponseConfigMap(),
                storedWorkflowStep.getWorkflowId());
    }
}
