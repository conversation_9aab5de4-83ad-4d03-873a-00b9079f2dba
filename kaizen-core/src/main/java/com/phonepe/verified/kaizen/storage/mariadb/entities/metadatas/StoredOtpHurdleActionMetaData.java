package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.services.ActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.OTP_DETAILS)
public class StoredOtpHurdleActionMetaData extends StoredActionMetadata {

    private static final long serialVersionUID = 5153641284369867110L;

    @Column(name = "name", columnDefinition = "varchar(128)")
    private String otpReferenceId;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String otp;

    @Builder
    public StoredOtpHurdleActionMetaData(@NonNull final String actionId,
                                         final LocalDateTime created,
                                         final LocalDateTime updated,
                                         final String otpReferenceId,
                                         final String otp) {
        super(BuildUtils.primaryKey(), actionId, ActionMetadataType.OTP_DETAILS, created, updated);
        this.otpReferenceId = otpReferenceId;
        this.otp = otp;
    }

    @Override
    public String getActionMetadataContextKey() {
        return Names.OTP_DETAILS;
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }
}
