package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.PersistKeyValuePairsFromApiCallActionContext;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PersistKeyValuePairsFromApiCallActionMessage extends BaseMessage {

    private final String actionId;

    private final PersistKeyValuePairsFromApiCallActionContext persistKeyValuePairsFromApiCallActionContext;

    private final TransitionContext transitionContext;

    @Builder
    @Jacksonized
    public PersistKeyValuePairsFromApiCallActionMessage(final RequestInfo requestInfo,
                                                        final String actionId,
                                                        final PersistKeyValuePairsFromApiCallActionContext persistKeyValuePairsFromApiCallActionContext,
                                                        final TransitionContext transitionContext) {

        super(ActorMessageType.PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL_ACTION, requestInfo);
        this.actionId = actionId;
        this.persistKeyValuePairsFromApiCallActionContext = persistKeyValuePairsFromApiCallActionContext;
        this.transitionContext = transitionContext;
    }
}
