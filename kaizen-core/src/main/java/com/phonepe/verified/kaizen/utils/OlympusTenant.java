package com.phonepe.verified.kaizen.utils;

import com.phonepe.olympus.im.models.authz.enums.TenantType;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class OlympusTenant {

    @NotEmpty
    private final String tenantId;

    @NotNull
    private final TenantType tenantType;
}
