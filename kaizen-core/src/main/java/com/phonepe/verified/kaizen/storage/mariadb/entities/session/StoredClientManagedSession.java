package com.phonepe.verified.kaizen.storage.mariadb.entities.session;

import com.phonepe.verified.kaizen.models.data.SessionType;
import com.phonepe.verified.kaizen.models.data.SessionType.Names;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.CLIENT_MANAGED)
public class StoredClientManagedSession extends StoredSessionManagementConfig {

    @Column(name = "principal_builder", columnDefinition = "varchar(512)", nullable = false)
    private String principalBuilder;

    @Column(name = "validate_timer", columnDefinition = "bit(1)", nullable = false)
    private boolean validateTimer;

    @Column(name = "validate_token", columnDefinition = "bit(1)", nullable = false)
    private boolean validateToken;

    @Builder
    public StoredClientManagedSession(final long id,
                                      @NonNull final String profileId,
                                      final boolean disabled,
                                      final String sourceType,
                                      final String lastUpdatedBy,
                                      final LocalDateTime created,
                                      final LocalDateTime updated,
                                      final String principalBuilder,
                                      final boolean validateTimer,
                                      final boolean validateToken) {
        super(id, profileId, SessionType.CLIENT_MANAGED, disabled, sourceType, lastUpdatedBy, created, updated);
        this.principalBuilder = principalBuilder;
        this.validateTimer = validateTimer;
        this.validateToken = validateToken;
    }

    @Override
    public <T> T accept(final StoredSessionManagementVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
