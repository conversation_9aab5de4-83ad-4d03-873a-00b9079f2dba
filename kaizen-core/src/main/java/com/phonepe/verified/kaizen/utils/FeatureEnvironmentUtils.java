package com.phonepe.verified.kaizen.utils;

import com.utils.StringUtils;
import java.util.Optional;
import lombok.experimental.UtilityClass;

@UtilityClass
public class FeatureEnvironmentUtils {

    private final boolean IS_FEATURE_ENVIRONMENT_ENABLED =
            StringUtils.isNotEmpty(System.getenv("CONFIG_ENV")) && System.getenv("CONFIG_ENV")
                    .equalsIgnoreCase("stage") && StringUtils.isNotEmpty(System.getenv("CONFIG_TAG_VALUE"));

    private final String FEATURE_ENVIRONMENT_NAME = System.getenv("NAMESPACE_ENV_NAME");


    public boolean isFeatureEnvApplicable() {

        return IS_FEATURE_ENVIRONMENT_ENABLED;
    }

    public Optional<String> getFeatureEnvironmentName() {

        return Optional.ofNullable(FEATURE_ENVIRONMENT_NAME);
    }
}