package com.phonepe.verified.kaizen.guice;

import com.google.inject.AbstractModule;
import com.google.inject.TypeLiteral;
import com.phonepe.shadow.utils.PropertyUtils;
import com.phonepe.verified.kaizen.hopelangfunctions.CalculateActionErrorCodeCountLowerThanGivenThresholdAndErrorCode;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.responses.profiles.ProfileChange;
import com.phonepe.verified.kaizen.models.responses.profiles.ProfileChangeMetadata;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AuthNService;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.CacheManagementService;
import com.phonepe.verified.kaizen.services.CatalogueService;
import com.phonepe.verified.kaizen.services.ClientCallbackService;
import com.phonepe.verified.kaizen.services.DependencyManagementService;
import com.phonepe.verified.kaizen.services.DetailsService;
import com.phonepe.verified.kaizen.services.DocumentMaskingService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.DrishtiService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.GeolocationService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.KillSwitchService;
import com.phonepe.verified.kaizen.services.MakerCheckerService;
import com.phonepe.verified.kaizen.services.ManualRetryService;
import com.phonepe.verified.kaizen.services.OtpService;
import com.phonepe.verified.kaizen.services.PincodeAndStateMatchingService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.SchedulingService;
import com.phonepe.verified.kaizen.services.SearchService;
import com.phonepe.verified.kaizen.services.SelfieHurdleService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.services.StateMachineService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowScreenService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.impl.ActionMetadataServiceImpl;
import com.phonepe.verified.kaizen.services.impl.ActionServiceImpl;
import com.phonepe.verified.kaizen.services.impl.AuthNServiceImpl;
import com.phonepe.verified.kaizen.services.impl.AuthZServiceImpl;
import com.phonepe.verified.kaizen.services.impl.AutoRetryActionServiceImpl;
import com.phonepe.verified.kaizen.services.impl.CacheManagementServiceImpl;
import com.phonepe.verified.kaizen.services.impl.CatalogueServiceImpl;
import com.phonepe.verified.kaizen.services.impl.ClientCallbackServiceImpl;
import com.phonepe.verified.kaizen.services.impl.ClockworkSchedulingServiceImpl;
import com.phonepe.verified.kaizen.services.impl.DependencyManagementServiceImpl;
import com.phonepe.verified.kaizen.services.impl.DetailsServiceImpl;
import com.phonepe.verified.kaizen.services.impl.DocumentMaskingServiceImpl;
import com.phonepe.verified.kaizen.services.impl.DocumentServiceImpl;
import com.phonepe.verified.kaizen.services.impl.DrishtiServiceImpl;
import com.phonepe.verified.kaizen.services.impl.DynamicUiResponseServiceImpl;
import com.phonepe.verified.kaizen.services.impl.GeolocationServiceImpl;
import com.phonepe.verified.kaizen.services.impl.HopeLangServiceImpl;
import com.phonepe.verified.kaizen.services.impl.KillSwitchServiceImpl;
import com.phonepe.verified.kaizen.services.impl.ManualRetryServiceImpl;
import com.phonepe.verified.kaizen.services.impl.OtpServiceImpl;
import com.phonepe.verified.kaizen.services.impl.PincodeAndStateMatchingServiceImpl;
import com.phonepe.verified.kaizen.services.impl.ProfileMakerCheckerServiceImpl;
import com.phonepe.verified.kaizen.services.impl.ProfileServiceImpl;
import com.phonepe.verified.kaizen.services.impl.SearchServiceImpl;
import com.phonepe.verified.kaizen.services.impl.SelfieHurdleServiceImpl;
import com.phonepe.verified.kaizen.services.impl.SessionManagementServiceImpl;
import com.phonepe.verified.kaizen.services.impl.StateMachineServiceImpl;
import com.phonepe.verified.kaizen.services.impl.WorkflowContextStoreImpl;
import com.phonepe.verified.kaizen.services.impl.WorkflowScreenServiceImpl;
import com.phonepe.verified.kaizen.services.impl.WorkflowServiceImpl;
import com.phonepe.verified.kaizen.services.impl.WorkflowStepServiceImpl;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.PropertyUtilsImpl;

public class BindingModule extends AbstractModule {

    private void registerStaticBindings() {
        requestStaticInjection(CalculateActionErrorCodeCountLowerThanGivenThresholdAndErrorCode.class);
        requestStaticInjection(MapperUtils.class);
    }

    @Override
    protected void configure() {
        bind(StateMachineService.class).to(StateMachineServiceImpl.class);
        bind(DocumentService.class).to(DocumentServiceImpl.class);
        bind(SchedulingService.class).to(ClockworkSchedulingServiceImpl.class);
        bind(ActionService.class).to(ActionServiceImpl.class);
        bind(ProfileService.class).to(ProfileServiceImpl.class);
        bind(WorkflowService.class).to(WorkflowServiceImpl.class);
        bind(WorkflowStepService.class).to(WorkflowStepServiceImpl.class);
        bind(WorkflowScreenService.class).to(WorkflowScreenServiceImpl.class);
        bind(HopeLangService.class).to(HopeLangServiceImpl.class);
        bind(PropertyUtils.class).to(PropertyUtilsImpl.class);
        bind(DetailsService.class).to(DetailsServiceImpl.class);
        bind(ActionMetadataService.class).to(ActionMetadataServiceImpl.class);
        bind(DynamicUiResponseService.class).to(DynamicUiResponseServiceImpl.class);
        bind(WorkflowContextStore.class).to(WorkflowContextStoreImpl.class);
        bind(OtpService.class).to(OtpServiceImpl.class);
        bind(KillSwitchService.class).to(KillSwitchServiceImpl.class);
        bind(DependencyManagementService.class).to(DependencyManagementServiceImpl.class);
        bind(SessionManagementService.class).to(SessionManagementServiceImpl.class);
        bind(PincodeAndStateMatchingService.class).to(PincodeAndStateMatchingServiceImpl.class);
        bind(ClientCallbackService.class).to(ClientCallbackServiceImpl.class);
        bind(AuthZService.class).to(AuthZServiceImpl.class);
        bind(AuthNService.class).to(AuthNServiceImpl.class);
        bind(DrishtiService.class).to(DrishtiServiceImpl.class);
        bind(AutoRetryActionService.class).to(AutoRetryActionServiceImpl.class);
        bind(SelfieHurdleService.class).to(SelfieHurdleServiceImpl.class);
        bind(DocumentMaskingService.class).to(DocumentMaskingServiceImpl.class);
        bind(SearchService.class).to(SearchServiceImpl.class);
        bind(CatalogueService.class).to(CatalogueServiceImpl.class);
        bind(ManualRetryService.class).to(ManualRetryServiceImpl.class);
        bind(GeolocationService.class).to(GeolocationServiceImpl.class);
        bind(CacheManagementService.class).to(CacheManagementServiceImpl.class);
        bind(new TypeLiteral<MakerCheckerService<ProfileChange, ProfileChangeMetadata, Profile>>() {
        }).to(ProfileMakerCheckerServiceImpl.class);
        registerStaticBindings();
    }
}
