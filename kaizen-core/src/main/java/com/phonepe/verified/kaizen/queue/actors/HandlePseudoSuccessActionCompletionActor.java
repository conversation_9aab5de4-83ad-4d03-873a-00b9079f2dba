package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.DependencyManagementService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class HandlePseudoSuccessActionCompletionActor extends BaseActor<StepActionMessage> {

    private final DependencyManagementService dependencyManagementService;

    @Inject
    protected HandlePseudoSuccessActionCompletionActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                       final ConnectionRegistry connectionRegistry,
                                                       final ObjectMapper mapper,
                                                       final RetryStrategyFactory retryStrategyFactory,
                                                       final ExceptionHandlingFactory exceptionHandlingFactory,
                                                       final DependencyManagementService dependencyManagementService) {
        super(ActorType.HANDLE_PSEUDO_SUCCESS_ACTION_COMPLETION,
                actorConfigMap.get(ActorType.HANDLE_PSEUDO_SUCCESS_ACTION_COMPLETION), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, StepActionMessage.class);
        this.dependencyManagementService = dependencyManagementService;
    }

    @Override
    protected boolean handleMessage(final StepActionMessage message) {

        dependencyManagementService.handlePseudoSuccessActionCompletion(message.getActionId());
        return true;
    }
}
