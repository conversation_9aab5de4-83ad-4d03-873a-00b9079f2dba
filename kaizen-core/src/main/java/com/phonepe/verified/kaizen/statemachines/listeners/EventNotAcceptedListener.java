package com.phonepe.verified.kaizen.statemachines.listeners;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.util.Map;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateContext.Stage;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;

public class EventNotAcceptedListener<S, E> extends StateMachineListenerAdapter<S, E> implements
        StateMachineListener<S, E> {

    @Override
    public void stateContext(final StateContext<S, E> stateContext) {
        if (Stage.EVENT_NOT_ACCEPTED == stateContext.getStage() && !stateContext.getStateMachine()
                .hasStateMachineError()) {
            stateContext.getStateMachine()
                    .setStateMachineError(KaizenException.create(KaizenResponseCode.TRANSITION_NOT_ALLOWED, Map.of()));
        }
    }
}
