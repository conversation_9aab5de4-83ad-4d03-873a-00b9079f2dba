package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowInitV2Message;
import com.phonepe.verified.kaizen.services.WorkflowService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class WorkflowInitV2Actor extends BaseActor<WorkflowInitV2Message> {

    private final WorkflowService workflowService;

    @Inject
    protected WorkflowInitV2Actor(final Map<ActorType, ActorConfig> actorConfigMap,
                                  final ConnectionRegistry connectionRegistry,
                                  final ObjectMapper mapper,
                                  final RetryStrategyFactory retryStrategyFactory,
                                  final ExceptionHandlingFactory exceptionHandlingFactory,
                                  final WorkflowService workflowService) {

        super(ActorType.WORKFLOW_INIT_V2, actorConfigMap.get(ActorType.WORKFLOW_INIT_V2), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, WorkflowInitV2Message.class);
        this.workflowService = workflowService;
    }

    // NO RETRY
    @Override
    protected boolean handleMessage(final WorkflowInitV2Message workflowInitV2Message) {

        workflowService.processInitAsyncV2Request(workflowInitV2Message.getWorkflowInitV2Request(),
                workflowInitV2Message.getRequestId(), workflowInitV2Message.getWorkflowId(),
                workflowInitV2Message.getUserDetails());
        return true;
    }
}
