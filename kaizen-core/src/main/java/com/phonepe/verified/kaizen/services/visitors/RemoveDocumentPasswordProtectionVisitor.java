package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.models.response.GenericError;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.MimeType;
import com.phonepe.verified.kaizen.models.data.MimeType.MimeTypeVisitor;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.visitors.RemoveDocumentPasswordProtectionVisitor.MimeTypeVisitorData;
import com.phonepe.verified.kaizen.utils.Constants;
import java.io.ByteArrayOutputStream;
import java.util.Map;
import javax.ws.rs.BadRequestException;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RemoveDocumentPasswordProtectionVisitor implements MimeTypeVisitor<byte[], MimeTypeVisitorData> {

    public static final RemoveDocumentPasswordProtectionVisitor INSTANCE = new RemoveDocumentPasswordProtectionVisitor();

    @Override
    public byte[] visitPdf(final MimeTypeVisitorData data) {

        try {

            final var bos = new ByteArrayOutputStream();
            final var document = PDDocument.load(data.getPlainText(), data.getPassword());
            document.setAllSecurityToBeRemoved(true);
            document.save(bos);
            document.close();
            return bos.toByteArray();

        } catch (final Exception e) {

            log.error("Error occurred while removing password protection of pdf file", e);
            throw new BadRequestException(Response.status(Status.BAD_REQUEST)
                    .entity(GenericError.builder()
                            .code(Constants.WRONG_DOCUMENT_PASSWORD_CODE)
                            .message("Please try with correct password")
                            .build())
                    .build());
        }
    }

    @Override
    public byte[] visitPng(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.PNG);
    }

    @Override
    public byte[] visitJpeg(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.JPEG);
    }

    @Override
    public byte[] visitMp4(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.MP4);
    }

    @Override
    public byte[] visitOctetStream(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.OCTET_STREAM);
    }

    @Override
    public byte[] visitPlainText(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.PLAIN_TEXT);
    }

    @Override
    public byte[] visitXml(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.XML);
    }

    @Override
    public byte[] visitJson(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.JSON);
    }

    @Override
    public byte[] visitDoc(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.DOC);
    }

    @Override
    public byte[] visitDocx(final MimeTypeVisitorData data) {

        return throwUnsupportedOperation(MimeType.DOCX);
    }

    private byte[] throwUnsupportedOperation(final MimeType mimeType) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of("MimeType", mimeType));
    }


    @Data
    @Builder
    @AllArgsConstructor
    public static class MimeTypeVisitorData {

        private final byte[] plainText;

        private final String password;
    }
}