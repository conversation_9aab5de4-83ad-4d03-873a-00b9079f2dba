package com.phonepe.verified.kaizen.configs;

import com.phonepe.verified.kaizen.caches.CacheName;
import io.dropwizard.util.Duration;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaffeineCacheConfig {

    @NotNull
    private CacheConfig defaultCacheConfig;

    @NotEmpty
    private Map<CacheName, CacheConfig> cacheConfigMap;

    public CacheConfig getCacheConfig(final CacheName cacheName) {
        return cacheConfigMap.getOrDefault(cacheName, this.defaultCacheConfig);
    }

    public Duration getExpiry(final CacheName cacheName) {
        return cacheConfigMap.getOrDefault(cacheName, this.defaultCacheConfig)
                .getExpiry();
    }

    public Duration getRefreshInterval(final CacheName cacheName) {
        return cacheConfigMap.getOrDefault(cacheName, this.defaultCacheConfig)
                .getRefreshInterval();
    }

    public int getMaxElements(final CacheName cacheName) {
        return cacheConfigMap.getOrDefault(cacheName, this.defaultCacheConfig)
                .getMaxElements();
    }
}
