package com.phonepe.verified.kaizen.storage.mariadb.entities.profile;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.summary.config.SummaryViewConfig;
import com.phonepe.verified.kaizen.models.configs.tag.WorkflowTagConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.converters.WorkflowTagConfigConverter;
import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue("0")
public class StoredPrimaryProfile extends StoredProfile {

    @Serial
    private static final long serialVersionUID = 1964300645240956233L;

    @Convert(converter = WorkflowTagConfigConverter.class)
    @Column(name = "workflow_tag_config", columnDefinition = "text")
    private WorkflowTagConfig workflowTagConfig;

    @Builder
    public StoredPrimaryProfile(final long id,
                                @NotEmpty final String profileId,
                                @NotEmpty final String organization,
                                @NotEmpty final String namespace,
                                @NotEmpty final String type,
                                final String version,
                                final WorkflowTagConfig workflowTagConfig,
                                final SummaryViewConfig summaryViewConfig,
                                final GetTemplateConfig getTemplateConfig,
                                final PostCompletionActionConfig postCompletionActionConfig,
                                @Nullable final List<PostWorkflowCreationActionConfig> postWorkflowCreationActionConfigs,
                                final String lastUpdatedBy,
                                final LocalDateTime createdAt,
                                final LocalDateTime lastUpdatedAt,
                                final String approvedBy) {
        super(id, profileId, organization, namespace, type, version, summaryViewConfig, getTemplateConfig,
                postCompletionActionConfig, postWorkflowCreationActionConfigs, ProfileType.PRIMARY, lastUpdatedBy,
                createdAt, lastUpdatedAt, approvedBy);

        this.workflowTagConfig = workflowTagConfig;
    }

    @Override
    public <T, J> T accept(final StoredProfileVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
