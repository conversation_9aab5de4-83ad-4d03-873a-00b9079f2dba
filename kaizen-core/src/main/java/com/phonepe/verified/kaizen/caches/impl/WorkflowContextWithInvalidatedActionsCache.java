package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowContext;

@Singleton
public class WorkflowContextWithInvalidatedActionsCache extends Cache<String, WorkflowContext> {

    private final WorkflowContextStore workflowContextStore;

    @Inject
    public WorkflowContextWithInvalidatedActionsCache(final CaffeineCacheConfig caffeineCacheConfig,
                                                      final MetricRegistry metricRegistry,
                                                      final WorkflowContextStore workflowContextStore) {
        super(CacheName.WORKFLOW_CONTEXT_WITH_INVALIDATED_ACTIONS, caffeineCacheConfig, metricRegistry);
        this.workflowContextStore = workflowContextStore;
    }

    @Override
    protected WorkflowContext build(final String workflowId) {
        return workflowContextStore.buildWorkflowContextWithInvalidatedActions(workflowId);
    }
}
