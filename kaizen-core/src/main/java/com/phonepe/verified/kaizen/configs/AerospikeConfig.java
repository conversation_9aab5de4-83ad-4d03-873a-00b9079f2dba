package com.phonepe.verified.kaizen.configs;

import com.aerospike.client.policy.BatchPolicy;
import com.aerospike.client.policy.Policy;
import com.aerospike.client.policy.WritePolicy;
import com.phonepe.platform.aerospike.config.AerospikeConfiguration;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeSet;
import io.dropwizard.util.Duration;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AerospikeConfig {

    @NotEmpty
    private String namespace;

    @Valid
    @NotNull
    private AerospikeConfiguration aerospikeBundleConfig;

    @NotNull
    private Policy readPolicy;

    @NotNull
    private WritePolicy writePolicy;

    @NotNull
    private BatchPolicy batchPolicy;

    private Map<AerospikeSet, Duration> ttlDurations;

    private Duration defaultTtlDuration;
}
