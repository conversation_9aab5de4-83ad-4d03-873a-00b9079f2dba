package com.phonepe.verified.kaizen.statemachines.actions.persistkeyvaluepairsfromapicall;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.PersistKeyValuePairsFromApiCallActionContext;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.PersistKeyValuePairsFromApiCallActionActor;
import com.phonepe.verified.kaizen.queue.messages.PersistKeyValuePairsFromApiCallActionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "createPersistKeyValuePairsFromApiCallAction")
public class CreatePersistKeyValuePairsFromApiCallAction extends CreateEntryBaseAction {

    private final PersistKeyValuePairsFromApiCallActionActor persistKeyValuePairsFromApiCallActionActor;

    @Inject
    public CreatePersistKeyValuePairsFromApiCallAction(final ActionService actionService,
                                                       final WorkflowService workflowService,
                                                       final ClockworkClient clockworkClient,
                                                       final ActionRepository actionRepository,
                                                       final WorkflowStepService workflowStepService,
                                                       final DataProvider<KaizenConfig> appConfigDataProvider,
                                                       final Provider<WorkflowContextStore> workflowContextStore,
                                                       final Provider<EventIngestionActor> eventIngestionActorProvider,
                                                       final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                                       final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor,
                                                       final PersistKeyValuePairsFromApiCallActionActor persistKeyValuePairsFromApiCallActionActor) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.persistKeyValuePairsFromApiCallActionActor = persistKeyValuePairsFromApiCallActionActor;
    }


    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        persistKeyValuePairsFromApiCallActionActor.publish(PersistKeyValuePairsFromApiCallActionMessage.builder()
                .persistKeyValuePairsFromApiCallActionContext(stateContext.getExtendedState()
                        .get(StepActionContext.class, PersistKeyValuePairsFromApiCallActionContext.class))
                .transitionContext(stateContext.getExtendedState()
                        .get(TransitionContext.class, TransitionContext.class))
                .actionId(storedAction.getActionId())
                .build());
    }
}
