package com.phonepe.verified.kaizen.services;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.OtpVerificationRequest;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;

public interface OtpService {

    void triggerOtpHurdle(final String actionId);

    void verifyOtpHurdle(OtpVerificationRequest otpVerificationRequest,
                         String actionId,
                         String intent,
                         long componentKitVersion,
                         RequestInfo requestInfo);

    void triggerResendOtp(final StoredAction storedAction);
}
