package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.models.MultiPartFileRequest;
import com.phonepe.verified.kaizen.models.data.DocumentIdentifier;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.FetchDocumentsResponse;
import com.phonepe.verified.kaizen.queue.actors.DeleteDocStoreFileActor;
import com.phonepe.verified.kaizen.queue.messages.DocumentMessage;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Encoding;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.InputStream;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Slf4j
@Singleton
@Path("/v1/document")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Document", description = "Document related APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class DocumentResource {

    private static final String RESOURCE_DOCUMENT = "DOCUMENT";
    private static final String OPERATION_DOCUMENT_FETCH = "FETCH";
    private static final String OPERATION_DOCUMENT_DOWNLOAD = "DOWNLOAD";

    private final AuthZService authZService;

    private final DocumentService documentService;

    private final DeleteDocStoreFileActor deleteDocStoreFileActor;

    private final SessionManagementService sessionManagementService;

    @POST
    @Authorize
    @PermitAll
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/upload/{workflowId}/{actionMappingId}/{documentType}/{documentLabel}")
    @AuthZ(gandalfPermission = "uploadDocument", olympusPermission = OlympusPermissionNames.UPLOAD_DOCUMENT)
    @Operation(summary = "Upload file from PhonePe Verified SDK", requestBody = @RequestBody(content = @Content(mediaType = "multipart/form-data", schema = @Schema(implementation = MultiPartFileRequest.class), encoding = @Encoding(name = "file", contentType = "image/png, image/jpeg, application/pdf"))))
    public DocumentIdentifier upload(@Parameter(hidden = true) @FormDataParam("file") final FormDataContentDisposition fileMetaData,
                                     @NotNull @FormDataParam("file") final InputStream fileInputStream,
                                     @FormDataParam("password") final String password,
                                     @NotEmpty @PathParam("workflowId") final String workflowId,
                                     @NotEmpty @PathParam("actionMappingId") final String actionMappingId,
                                     @NotNull @PathParam("documentType") final DocumentType documentType,
                                     @NotEmpty @PathParam("documentLabel") final String documentLabel,
                                     @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                     @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                     @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken) {

        authZService.authorizeUserWithWorkflowId(workflowId, requestInfo, userDetails);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        return documentService.uploadWithNewExternalReferenceId(fileMetaData, fileInputStream, password,
                userDetails.getUserId(), workflowId, actionMappingId, documentType, documentLabel);
    }

    @POST
    @Authorize
    @PermitAll
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/upload/{workflowId}/{actionMappingId}/{documentType}/{documentLabel}/{intent}/{componentKitVersion}")
    @AuthZ(gandalfPermission = "uploadDocument", olympusPermission = OlympusPermissionNames.UPLOAD_DOCUMENT)
    @Operation(summary = "Upload file from PhonePe Verified SDK", requestBody = @RequestBody(content = @Content(mediaType = "multipart/form-data", schema = @Schema(implementation = MultiPartFileRequest.class), encoding = @Encoding(name = "file", contentType = "image/png, image/jpeg, application/pdf"))))
    public DocumentIdentifier upload(@Parameter(hidden = true) @FormDataParam("file") final FormDataContentDisposition fileMetaData,
                                     @NotNull @FormDataParam("file") final InputStream fileInputStream,
                                     @FormDataParam("password") final String password,
                                     @NotEmpty @PathParam("workflowId") final String workflowId,
                                     @NotEmpty @PathParam("actionMappingId") final String actionMappingId,
                                     @NotNull @PathParam("documentType") final DocumentType documentType,
                                     @NotEmpty @PathParam("documentLabel") final String documentLabel,
                                     @NotEmpty @PathParam("intent") final String intent,
                                     @PathParam("componentKitVersion") final long componentKitVersion,
                                     @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                     @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                     @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken) {

        authZService.authorizeUserWithWorkflowId(workflowId, requestInfo, userDetails);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        return documentService.uploadWithNewExternalReferenceId(fileMetaData, fileInputStream, password,
                userDetails.getUserId(), workflowId, actionMappingId, documentType, documentLabel, intent,
                componentKitVersion);
    }


    @GET
    @PermitAll
    @Authorize
    @Path("/download/{workflowId}/{actionId}/{documentId}")
    @Operation(summary = "Download file for given Document Id")
    @AuthZ(gandalfPermission = "downloadDocument", olympusPermission = OlympusPermissionNames.DOWNLOAD_DOCUMENT)
    public Response download(@NotEmpty @PathParam("workflowId") final String workflowId,
                             @NotEmpty @PathParam("actionId") final String actionId,
                             @NotEmpty @PathParam("documentId") final String documentId,
                             @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                             @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken,
                             @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeUserWithWorkflowId(workflowId, requestInfo, userDetails);

        authZService.authorizeActionIdBelongsToWorkflowId(actionId, workflowId);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        return documentService.download(actionId, documentId);
    }

    @GET
    @Path("/download/{actionId}/{documentId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Download file for given Document Id")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public Response download(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                             @NotEmpty @PathParam("actionId") final String actionId,
                             @NotEmpty @PathParam("documentId") final String documentId,
                             @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenant(authZService.getTenantFromAction(actionId), RESOURCE_DOCUMENT,
                OPERATION_DOCUMENT_DOWNLOAD, serviceUserPrincipal);

        return documentService.download(actionId, documentId);
    }

    @GET
    @Path("/fetch/{actionId}/{documentType}")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Fetch documentIds for given actionId and documentType")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public FetchDocumentsResponse fetchDocuments(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                 @NotEmpty @PathParam("actionId") final String actionId,
                                                 @NotNull @PathParam("documentType") final DocumentType documentType,
                                                 @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal) {

        authZService.authorizeOperationForTenant(authZService.getTenantFromAction(actionId), RESOURCE_DOCUMENT,
                OPERATION_DOCUMENT_FETCH, serviceUserPrincipal);

        return documentService.fetchDocuments(actionId, documentType);
    }

    @POST
    @Authorize
    @PermitAll
    @SneakyThrows
    @Path("/delete")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Delete file from PhonePe Verified for given Document Id")
    @AuthZ(gandalfPermission = "deleteDocument", olympusPermission = OlympusPermissionNames.DELETE_DOCUMENT)
    public void delete(@Parameter(hidden = true) @AuthZContext final UserDetails gandalfUserDetails,
                       @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                       @Valid @NotNull final DocumentIdentifier documentIdentifier) {

        deleteDocStoreFileActor.publish(DocumentMessage.builder()
                .documentId(documentIdentifier.getDocumentId())
                .userId(gandalfUserDetails.getUserId())
                .build());
    }
}
