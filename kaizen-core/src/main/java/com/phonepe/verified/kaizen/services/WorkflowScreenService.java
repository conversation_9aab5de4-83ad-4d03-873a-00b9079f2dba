package com.phonepe.verified.kaizen.services;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;

public interface WorkflowScreenService {

    void triggerStepActionInProfileScreen(StandardProfileScreenConfig standardProfileScreenConfigToTrigger,
                                          StoredWorkflowStep storedWorkflowStep,
                                          String screenMappingId,
                                          UserDetails userDetails);

    void handleActionCompletion(String completedActionId);
}
