package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.storage.mariadb.entities.Sharded;
import io.appform.dropwizard.sharding.dao.LookupDao;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import lombok.RequiredArgsConstructor;
import org.hibernate.criterion.DetachedCriteria;

@RequiredArgsConstructor
public abstract class CrudRepository<T extends Sharded> {

    protected final RelationalDao<T> relationalDao;

    protected final LookupDao<T> lookupDao;

    public List<T> select(final String shardingKey,
                          final DetachedCriteria detachedCriteria) {
        return select(shardingKey, detachedCriteria, 0, 100_000);
    }

    public List<T> select(final String shardingKey,
                          final DetachedCriteria detachedCriteria,
                          final int start,
                          final int numRows) {
        return select(shardingKey, detachedCriteria, start, numRows, Function.identity());
    }

    public <U> U select(final String shardingKey,
                        final DetachedCriteria detachedCriteria,
                        final int start,
                        final int numRows,
                        final Function<List<T>, U> handler) {
        try {
            return relationalDao.select(shardingKey, detachedCriteria, start, numRows, handler);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DB_ERROR, e);
        }
    }

    public Optional<T> save(final T object) {
        return save(object, Function.identity());
    }

    public <U> Optional<U> save(final T object,
                                final Function<T, U> handler) {
        if (object == null) {
            return Optional.empty();
        }
        Objects.requireNonNull(handler);
        try {
            return Optional.ofNullable(relationalDao.save(object.getShardingKey(), object, handler));
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DB_ERROR, e);
        }
    }

    public boolean saveAll(final String shardingKey,
                           final Collection<T> entities) {
        try {
            return relationalDao.saveAll(shardingKey, entities);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DB_ERROR, e);
        }
    }

    public boolean update(final String shardingKey,
                          final DetachedCriteria detachedCriteria,
                          final UnaryOperator<T> updater) {
        try {
            return relationalDao.update(shardingKey, detachedCriteria, updater);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DB_ERROR, e);
        }
    }

    public void updateAll(final String shardingKey,
                          final int start,
                          final int numRows,
                          final DetachedCriteria criteria,
                          final UnaryOperator<T> updater) {
        try {
            relationalDao.updateAll(shardingKey, start, numRows, criteria, updater);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DB_ERROR, e);
        }
    }

    public List<T> scatterGather(final DetachedCriteria detachedCriteria) {
        return scatterGather(detachedCriteria, 0, 100_000);
    }

    public List<T> scatterGather(final DetachedCriteria detachedCriteria,
                                 final int start,
                                 final int numRows) {
        try {
            return relationalDao.scatterGather(detachedCriteria, start, numRows);
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.DB_ERROR, e);
        }
    }

    public int getShardId(final String shardingKey) {
        return relationalDao.getShardCalculator()
                .shardId(shardingKey);
    }
}
