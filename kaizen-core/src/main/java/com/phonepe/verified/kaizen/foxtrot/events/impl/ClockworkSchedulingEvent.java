package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import io.dropwizard.util.Duration;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.jackson.Jacksonized;

@Data
@EqualsAndHashCode(callSuper = true)
public class ClockworkSchedulingEvent extends BaseEvent {

    private String referenceId;

    private String clockworkJobId;

    private Duration callbackDuration;

    private String callbackPath;

    private boolean success;

    private String failureCode;

    @Builder
    @Jacksonized
    public ClockworkSchedulingEvent(final String clockworkJobId,
                                    final Duration callbackDuration,
                                    final boolean success,
                                    final String failureCode,
                                    final String callbackPath,
                                    final String workflowId,
                                    @NotNull final String groupingKey,
                                    final String workflowStepId,
                                    final String screenMappingId,
                                    final String actionMappingId,
                                    final String profileStepMappingId) {

        super(EventType.CLOCKWORK_CALLBACK_SCHEDULED, null, null, null, workflowId, null, null, groupingKey, null, null,
                workflowStepId, null, null, screenMappingId, actionMappingId, 0L, profileStepMappingId);

        this.clockworkJobId = clockworkJobId;
        this.callbackPath = callbackPath;
        this.callbackDuration = callbackDuration;
        this.success = success;
        this.failureCode = failureCode;
    }
}
