package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredClientCallbackConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredClientCallbackConfig.Fields;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Optional;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class ClientCallbackConfigRepository extends CrudRepository<StoredClientCallbackConfig> {

    @Inject
    public ClientCallbackConfigRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredClientCallbackConfig.class), null);
    }

    public Optional<StoredClientCallbackConfig> select(final String profileId) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredClientCallbackConfig.class)
                .add(Restrictions.eq(Fields.profileId, profileId));

        return select(Constants.PROFILE_SHARD_KEY, detachedCriteria).stream()
                .findFirst();
    }
}
