package com.phonepe.verified.kaizen.storage.mariadb.entities;

import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.zeus.models.Farm;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.With;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicUpdate;

@Data
@Entity
@Builder
@ToString
@DynamicUpdate
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "workflow_audit", indexes = {
        @Index(name = "unq_idx_workflow_id", columnList = "workflow_id, partition_id, REV", unique = true)})
public class StoredWorkflowAudit implements Sharded, Serializable {

    @Id
    @EmbeddedId
    private AuditPrimaryKey primaryKey;

    @Column(name = "REVTYPE", columnDefinition = "tinyint", updatable = false, insertable = false)
    private int revType;

    @Column(name = "workflow_id", columnDefinition = "varchar(64)", nullable = false)
    private String workflowId;

    @Column(name = "entity_id", columnDefinition = "varchar(64)", nullable = false)
    private String entityId;

    @Column(name = "phone_number", columnDefinition = "varchar(64)")
    private String phoneNumber;

    @Column(name = "email_id", columnDefinition = "varchar(64)")
    private String emailId;

    @Column(name = "entity_type", columnDefinition = "varchar(128)", nullable = false)
    @Enumerated(EnumType.STRING)
    private EntityType entityType;

    @Column(name = "profile_id", columnDefinition = "varchar(64)", nullable = false)
    private String profileId;

    @Column(name = "caller_farm_id", columnDefinition = "varchar(5)")
    @Enumerated(EnumType.STRING)
    private Farm callerFarmId;

    @Column(name = "current_state", columnDefinition = "varchar(128)", nullable = false)
    @Enumerated(EnumType.STRING)
    private TransitionState currentState;

    @Column(name = "current_event", columnDefinition = "varchar(128)", nullable = false)
    @Enumerated(EnumType.STRING)
    private TransitionEvent currentEvent;

    @With
    @Column(name = "tag", columnDefinition = "varchar(128)")
    private String tag;

    @Column(name = "last_updated_by", columnDefinition = "varchar(128)", nullable = false)
    private String lastUpdatedBy;

    @Column(name = "last_updater_type", columnDefinition = "varchar(128)", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpdaterType lastUpdaterType;

    @With
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3)", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "last_updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3)", nullable = false)
    private LocalDateTime lastUpdatedAt;

    @Override
    public String getShardingKey() {
        return workflowId;
    }

}
