package com.phonepe.verified.kaizen.statemachines.actions.workflowstep;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.actors.TriggerWorkflowStepActor;
import com.phonepe.verified.kaizen.queue.messages.TriggerWorkflowStepMessage;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class MoveToInProgressWorkflowStepAction extends UpdateWorkflowStepBaseAction {

    private final Provider<TriggerWorkflowStepActor> triggerWorkflowStepActorProvider;

    @Inject
    public MoveToInProgressWorkflowStepAction(final ProfileService profileService,
                                              final WorkflowService workflowService,
                                              final WorkflowStepService workflowStepService,
                                              final EventIngestionCommand eventIngestionCommand,
                                              final WorkflowStepRepository workflowStepRepository,
                                              final Provider<WorkflowContextStore> workflowContextStore,
                                              final Provider<TriggerWorkflowStepActor> triggerWorkflowStepActorProvider) {
        super(profileService, workflowService, workflowStepService, eventIngestionCommand, workflowStepRepository,
                workflowContextStore);
        this.triggerWorkflowStepActorProvider = triggerWorkflowStepActorProvider;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final TransitionState previousState,
                                  final StoredWorkflowStep storedWorkflow,
                                  final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var workflowStepId = stateContext.getExtendedState()
                .get(Fields.workflowStepId, String.class);

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        final var shadowV2SectionSubmitRequest = stateContext.getExtendedState()
                .get(ShadowV2SectionSubmitRequest.class, ShadowV2SectionSubmitRequest.class);

        Objects.requireNonNull(workflowStepId);
        Objects.requireNonNull(userDetails);
        Objects.requireNonNull(shadowV2SectionSubmitRequest);

        triggerWorkflowStepActorProvider.get()
                .publish(TriggerWorkflowStepMessage.builder()
                        .workflowStepId(workflowStepId)
                        .userDetails(userDetails)
                        .shadowV2SectionSubmitRequest(shadowV2SectionSubmitRequest)
                        .build());

    }

}
