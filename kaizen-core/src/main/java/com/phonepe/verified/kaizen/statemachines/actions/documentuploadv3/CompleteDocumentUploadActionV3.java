package com.phonepe.verified.kaizen.statemachines.actions.documentuploadv3;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.stratos.kaizen.models.data.ActionStatus;
import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.data.contexts.DocumentUploadActionTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.SuccessStateBaseAction;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadWithMetadataActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

import java.util.Map;

@Slf4j
@Singleton
@ActionKey(value = "completeDocumentUploadActionV3")
public class CompleteDocumentUploadActionV3 extends SuccessStateBaseAction {

    private final ActionMetadataStoreCommand actionMetadataStoreCommand;
    private final ActionMetadataRepository actionMetadataRepository;
    private final HandleBarsService handleBarsService;
    private final WorkflowStepService workflowStepService;
    private final Provider<WorkflowContextStore> workflowContextStore;

    @Inject
    protected CompleteDocumentUploadActionV3(final ActionService actionService,
                                             final ActionRepository actionRepository,
                                             final HandleBarsService handleBarsService,
                                             final WorkflowStepService workflowStepService,
                                             final AutoRetryActionService autoRetryActionService,
                                             final Provider<AutoRetryActionActor> autoRetryActionActorProvider,
                                             final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider,
                                             final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider,
                                             final Provider<WorkflowContextStore> workflowContextStore,
                                             final Provider<EventIngestionActor> eventIngestionActorProvider,
                                             ActionMetadataStoreCommand actionMetadataStoreCommand,
                                             ActionMetadataRepository actionMetadataRepository) {

        super(actionService, actionRepository, autoRetryActionService, autoRetryActionActorProvider,
                handleActionCompletionActorProvider, handlePseudoSuccessActionCompletionActorProvider,
                workflowContextStore, eventIngestionActorProvider);
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
        this.actionMetadataRepository = actionMetadataRepository;
        this.workflowStepService = workflowStepService;
        this.handleBarsService = handleBarsService;
        this.workflowContextStore = workflowContextStore;
    }

    @Override
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var documentUploadWithMetadataActionMetadata = (DocumentUploadWithMetadataActionMetadata)
                actionMetadataStoreCommand.get(
                        ActionMetadataStoreKey.builder()
                                .actionId(storedAction.getActionId())
                                .build());

        final var storedDocumentUploadWithMetadataActionMetadataList = documentUploadWithMetadataActionMetadata.getDocuments()
                .stream()
                .flatMap(doc -> doc.getDocuments()
                        .stream()
                        .map(docIdLabel -> (StoredActionMetadata) StoredDocumentUploadWithMetaDataActionMetadata.builder()
                                .actionId(storedAction.getActionId())
                                .documentId(docIdLabel.getDocumentId())
                                .documentType(doc.getDocumentType())
                                .documentLabel(docIdLabel.getDocumentLabel())
                                .status(ActionStatus.SUBMITTED)
                                .metadata(MapperUtils.serializeToBytes(doc.getMetadata()))
                                .build()))
                .toList();

        storedDocumentUploadWithMetadataActionMetadataList.forEach(actionMetadataRepository::save);
    }
}
