package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.util.Map;

public abstract class ActionMetadataExceptionThrowingVisitor<T, J> extends ActionMetadataDefaultVisitor<T, J> {

    @Override
    public T defaultOperation(J data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }
}
