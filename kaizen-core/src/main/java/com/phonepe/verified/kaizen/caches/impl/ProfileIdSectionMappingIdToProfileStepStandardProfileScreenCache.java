package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.common.ProfileStepStandardProfileScreen;
import com.phonepe.verified.kaizen.models.data.keys.ProfileIdSectionMappingIdKey;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.visitors.StandardProfileScreenConfigFromProfileStepVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow.Fields;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Singleton
public class ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache extends
        Cache<ProfileIdSectionMappingIdKey, ProfileStepStandardProfileScreen> {

    private final ProfileService profileService;

    @Inject
    public ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache(final CaffeineCacheConfig caffeineCacheConfig,
                                                                            final MetricRegistry metricRegistry,
                                                                            final ProfileService profileService) {
        super(CacheName.PROFILE_ID_SECTION_MAPPING_ID_TO_PROFILE_STEP_STANDARD_PROFILE_SCREEN_CACHE,
                caffeineCacheConfig, metricRegistry);
        this.profileService = profileService;
    }

    @Override
    protected ProfileStepStandardProfileScreen build(final ProfileIdSectionMappingIdKey profileIdSectionMappingIdKey) {

        final var profile = profileService.get(profileIdSectionMappingIdKey.getProfileId(), true);

        final var profileSteps = profile.getProfileSteps();

        return profileSteps.stream()
                .map(ps -> ProfileStepStandardProfileScreen.builder()
                        .profileStep(ps)
                        .standardProfileScreenConfig(getStandardProfileScreenConfig(ps,
                                profileIdSectionMappingIdKey.getSectionMappingId()).orElse(null))
                        .build())
                .filter(psSps -> Objects.nonNull(psSps.getStandardProfileScreenConfig()))
                .findFirst()
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.VERIFICATION_PROFILE_STEPS_NOT_FOUND,
                        Map.of(Fields.profileId, profile.getProfileId())));
    }

    private Optional<StandardProfileScreenConfig> getStandardProfileScreenConfig(final ProfileStep profileStep,
                                                                                 final String sectionMappingId) {

        return profileStep.getProfileScreenConfig()
                .accept(new StandardProfileScreenConfigFromProfileStepVisitor(sectionMappingId));
    }
}
