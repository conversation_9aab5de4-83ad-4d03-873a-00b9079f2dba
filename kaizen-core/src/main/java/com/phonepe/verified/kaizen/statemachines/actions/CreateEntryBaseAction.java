package com.phonepe.verified.kaizen.statemachines.actions;

import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.dependency.DependencyConfig;
import com.phonepe.verified.kaizen.models.configs.ttl.TtlConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.queue.messages.eventingestion.ActionInitEventIngestionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.ClockworkUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.IdUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.States;
import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@AllArgsConstructor
public abstract class CreateEntryBaseAction extends BaseTransitionAction<String, String> {

    private final ActionService actionService;

    private final WorkflowService workflowService;

    private final ClockworkClient clockworkClient;

    private final ActionRepository actionRepository;

    private final WorkflowStepService workflowStepService;

    private final DataProvider<KaizenConfig> appConfigDataProvider;

    private final Provider<WorkflowContextStore> workflowContextStore;

    private final Provider<EventIngestionActor> eventIngestionActorProvider;

    private final Provider<ActionExecutorActor> actionExecutorActorProvider;

    private final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor;

    @Override
    @SneakyThrows
    protected void performTransition(final StateContext<String, String> stateContext) {

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        final var workflowStepId = stateContext.getExtendedState()
                .get(Fields.workflowStepId, String.class);

        final var actionType = stateContext.getExtendedState()
                .get(Fields.actionType, ActionType.class);

        final var stateMachineVersion = stateContext.getExtendedState()
                .get(Fields.stateMachineVersion, String.class);

        final var actionMappingId = stateContext.getExtendedState()
                .get(Fields.actionMappingId, String.class);

        final var screenMappingId = stateContext.getExtendedState()
                .get(Fields.screenMappingId, String.class);

        Objects.requireNonNull(userDetails);
        Objects.requireNonNull(workflowStepId);
        Objects.requireNonNull(actionType);
        Objects.requireNonNull(stateMachineVersion);
        Objects.requireNonNull(actionMappingId);
        Objects.requireNonNull(screenMappingId);

        preTransition(stateContext);

        final var actionId = IdUtils.createIdInSameShard("A", workflowStepId, actionRepository::getShardId);

        final var storedAction = BuildUtils.toStoredAction(workflowStepId, actionId, actionType, stateMachineVersion,
                actionMappingId, stateContext, screenMappingId, userDetails);

        actionRepository.save(storedAction, savedStoredAction -> {
            transition(storedAction, stateContext);
            workflowContextStore.get()
                    .addActionInWorkflowContext(storedAction);
            return savedStoredAction;
        });

        registerTtl(storedAction, stateContext);

        final var neededActionOptional = handleDependencyConfig(storedAction, stateContext);

        eventIngestionActorProvider.get()
                .publish(EventIngestionMessage.builder()
                        .actionId(actionId)
                        .eventType(EventType.ACTION_INIT)
                        .actionInitEventIngestionMessage(ActionInitEventIngestionMessage.builder()
                                .isPseudoSuccess(neededActionOptional.isPresent())
                                .neededActionMappingId(neededActionOptional.map(StoredAction::getActionMappingId)
                                        .orElse(null))
                                .build())
                        .build());

        if (neededActionOptional.isEmpty()) {
            postTransition(storedAction, stateContext);
        }
    }

    private void registerTtl(final StoredAction storedAction,
                             final StateContext<String, String> stateContext) {

        final var ttlConfig = stateContext.getExtendedState()
                .get(TtlConfig.class, TtlConfig.class);

        if (Objects.nonNull(ttlConfig)) {

            final var schedulingRequest = ClockworkUtils.getActionMessageSchedulingRequest("{}",
                    appConfigDataProvider.getData()
                            .getBaseUrl(),
                    String.format("/v1/internal/callback/clockwork/dependency/action/%s", storedAction.getActionId()),
                    ClockworkUtils.getSchedulingDelayDate(ttlConfig.getTtl()));

            clockworkClient.schedule(schedulingRequest, Constants.APP_NAME);
        }
    }

    @SneakyThrows
    private Optional<StoredAction> handleDependencyConfig(final StoredAction storedAction,
                                                          final StateContext<String, String> stateContext) {

        final var dependencyConfig = stateContext.getExtendedState()
                .get(DependencyConfig.class, DependencyConfig.class);

        if (Objects.nonNull(dependencyConfig)) {

            final var workflowStepId = storedAction.getWorkflowStepId();

            final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(workflowStepId);

            final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

            final var workflowContext = workflowContextStore.get()
                    .getWorkflowContext(storedWorkflow.getWorkflowId());

            final var workflowContextJsonNode = MapperUtils.convertToJsonNode(workflowContext);

            final var workflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                    storedWorkflowStep.getWorkflowId());

            final var workflowStepIds = workflowSteps.stream()
                    .map(StoredWorkflowStep::getWorkflowStepId)
                    .collect(Collectors.toSet());

            final var neededActionOptional = actionService.getActions(workflowStepIds)
                    .stream()
                    .collect(Collectors.groupingBy(StoredAction::getActionMappingId,
                            Collectors.maxBy(Comparator.comparing(StoredAction::getCreatedAt))))
                    .getOrDefault(dependencyConfig.accept(getDependentActionMappingIdDependencyConfigVisitor,
                            workflowContextJsonNode), Optional.empty());

            if (neededActionOptional.isPresent() && States.PSEUDO_SUCCESS.equals(neededActionOptional.get()
                    .getCurrentState())) {

                actionExecutorActorProvider.get()
                        .publish(ActionExecutionMessage.builder()
                                .actionId(storedAction.getActionId())
                                .eventToTrigger(Events.MOVE_TO_PSEUDO_SUCCESS)
                                .userDetails(Constants.PVCORE_SYSTEM_USER)
                                .build());
                return neededActionOptional;
            }
        }

        return Optional.empty();
    }

    protected void preTransition(final StateContext<String, String> stateContext) {
        // NOOP
    }

    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {
        // Persist respective metas in this
    }

    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {
        // publishing to respective actor will be done here
    }
}
