package com.phonepe.verified.kaizen.statemachines.actions.documentuploadv2;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.drishti.models.responses.DrishtiAsyncResponse;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.SuccessStateBaseAction;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import io.dropwizard.util.Strings;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "completeDocumentUploadActionV2")
public class CompleteDocumentUploadActionV2 extends SuccessStateBaseAction {

    private final ActionMetadataStoreCommand actionMetadataStoreCommand;
    private final ActionMetadataRepository actionMetadataRepository;

    @Inject
    protected CompleteDocumentUploadActionV2(final ActionService actionService,
                                             final ActionRepository actionRepository,
                                             final AutoRetryActionService autoRetryActionService,
                                             final Provider<AutoRetryActionActor> autoRetryActionActorProvider,
                                             final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider,
                                             final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider,
                                             final Provider<WorkflowContextStore> workflowContextStore,
                                             final Provider<EventIngestionActor> eventIngestionActorProvider,
                                             ActionMetadataStoreCommand actionMetadataStoreCommand,
                                             ActionMetadataRepository actionMetadataRepository) {

        super(actionService, actionRepository, autoRetryActionService, autoRetryActionActorProvider,
                handleActionCompletionActorProvider, handlePseudoSuccessActionCompletionActorProvider,
                workflowContextStore, eventIngestionActorProvider);
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
        this.actionMetadataRepository = actionMetadataRepository;
    }

    @Override
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var drishtiAsyncResponse = stateContext.getExtendedState()
                .get(DrishtiAsyncResponse.class, DrishtiAsyncResponse.class);

        final var documentUploadActionMetadata = (DocumentUploadActionMetadata) actionMetadataStoreCommand.get(
                ActionMetadataStoreKey.builder()
                        .actionId(storedAction.getActionId())
                        .build());

        final var storedDocumentUploadActionMetadataList = documentUploadActionMetadata.getDocuments()
                .stream()
                .flatMap(doc -> doc.getDocuments()
                        .stream()
                        .map(docIdLabel -> (StoredActionMetadata) StoredDocumentUploadActionMetadata.builder()
                                .actionId(storedAction.getActionId())
                                .requestId(Objects.nonNull(drishtiAsyncResponse) && !Strings.isNullOrEmpty(
                                        drishtiAsyncResponse.getRequestId())
                                           ? drishtiAsyncResponse.getRequestId()
                                           : null)
                                .documentId(docIdLabel.getDocumentId())
                                .documentType(doc.getDocumentType())
                                .documentLabel(docIdLabel.getDocumentLabel())
                                .build()))
                .toList();

        storedDocumentUploadActionMetadataList.forEach(actionMetadataRepository::save);
    }
}
