package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import com.phonepe.verified.kaizen.models.configs.action.impl.AndStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.EvaluatedStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.OrStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import java.util.List;
import java.util.stream.Stream;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GetAllStandardStepActionConfigsVisitor implements StepActionVisitor<List<StandardStepActionConfig>, Void> {

    public static final GetAllStandardStepActionConfigsVisitor INSTANCE = new GetAllStandardStepActionConfigsVisitor();

    @Override
    public @NotNull List<StandardStepActionConfig> visit(final StandardStepActionConfig standardStepActionConfig,
                                                         final Void data) {

        return List.of(standardStepActionConfig);
    }

    @Override
    public @NotNull List<StandardStepActionConfig> visit(final AndStepActionConfig andStepActionConfig,
                                                         final Void data) {

        final var storedActionListFromLeftAction = andStepActionConfig.getLeft()
                .accept(this, null);

        final var storedActionListFromRightAction = andStepActionConfig.getRight()
                .accept(this, null);

        return Stream.concat(storedActionListFromLeftAction.stream(), storedActionListFromRightAction.stream())
                .toList();
    }

    @Override
    public List<StandardStepActionConfig> visit(final OrStepActionConfig orStepActionConfig,
                                                final Void data) {

        final var storedActionListFromLeftAction = orStepActionConfig.getLeft()
                .accept(this, null);

        final var storedActionListFromRightAction = orStepActionConfig.getRight()
                .accept(this, null);

        return Stream.concat(storedActionListFromLeftAction.stream(), storedActionListFromRightAction.stream())
                .toList();
    }

    @Override
    public @NotNull List<StandardStepActionConfig> visit(final EvaluatedStepActionConfig evaluatedStepActionConfig,
                                                         final Void data) {

        return evaluatedStepActionConfig.getConfig()
                .accept(this, null);
    }
}
