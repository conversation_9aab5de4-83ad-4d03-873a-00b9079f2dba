package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EntityDetailsFetchEvent extends BaseEvent {

    private String requester;

    private String requiredProfiles;

    private String requiredDetails;

    @Builder
    public EntityDetailsFetchEvent(final String entityId,
                                   final EntityType entityType,
                                   final String requiredProfiles,
                                   final String requiredDetails,
                                   final String groupingKey,
                                   final String requester) {

        super(EventType.ENTITY_DETAILS_FETCH, null, entityId, null, null, null, null, groupingKey, null, null, null,
                entityType, null, null, null, 0, null);

        this.requiredProfiles = requiredProfiles;
        this.requiredDetails = requiredDetails;
        this.requester = requester;
    }

}
