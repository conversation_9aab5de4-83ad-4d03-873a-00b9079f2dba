package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.common.CompletionState.CompletionStateVisitor;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.util.Map;

public abstract class CompletionStateBaseVisitor<T, U> implements CompletionStateVisitor<T, U> {

    @Override
    public T visitSuccess(final U data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitPseudoSuccess(final U data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitFailure(final U data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitInProgress(final U data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitNotStarted(final U data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitInvalidated(final U data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitSkipped(final U data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitAborted(final U data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitDiscarded(final U data) {
        return throwUnsupportedOperation();
    }

    private T throwUnsupportedOperation() {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }
}
