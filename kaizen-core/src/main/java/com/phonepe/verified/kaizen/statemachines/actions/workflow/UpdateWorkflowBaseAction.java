package com.phonepe.verified.kaizen.statemachines.actions.workflow;

import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.responses.workflow.ActionFailureResponse;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.statemachines.actions.BaseTransitionAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import com.phonepe.verified.kaizen.utils.Utils;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@AllArgsConstructor
public abstract class UpdateWorkflowBaseAction extends BaseTransitionAction<TransitionState, TransitionEvent> {

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowRepository workflowRepository;

    private final EventIngestionCommand eventIngestionCommand;

    private final Provider<WorkflowContextStore> workflowContextStore;

    @Override
    protected void performTransition(final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var workflowId = stateContext.getExtendedState()
                .get(Fields.workflowId, String.class);

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        Objects.requireNonNull(workflowId);
        Objects.requireNonNull(userDetails);

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        preTransition(storedWorkflow, stateContext);

        final var previousState = storedWorkflow.getCurrentState();

        final var previousEvent = storedWorkflow.getCurrentEvent();

        storedWorkflow.setCurrentState(stateContext.getTarget()
                .getId());
        storedWorkflow.setCurrentEvent(stateContext.getEvent());
        storedWorkflow.setLastUpdatedBy(userDetails.getUserId());
        storedWorkflow.setLastUpdaterType(UpdaterType.valueOf(userDetails.getUserType()
                .name()));

        workflowRepository.save(storedWorkflow, savedWorkflow -> {
            transition(storedWorkflow, stateContext);
            workflowContextStore.get()
                    .updateWorkflowContext(storedWorkflow);
            return savedWorkflow;
        });

        final var actionFailureReason = workflowService.fetchWorkflowActionFailure(storedWorkflow)
                .map(ActionFailureResponse::getReason)
                .orElse(null);

        eventIngestionCommand.workflowTransitionEvent(storedWorkflow, profile, previousState, previousEvent,
                Utils.getEventType(storedWorkflow.getCurrentState()), actionFailureReason);

        postTransition(previousState, storedWorkflow, stateContext);
    }

    protected void preTransition(final StoredWorkflow storedWorkflow,
                                 final StateContext<TransitionState, TransitionEvent> stateContext) {
        // NOOP
    }

    protected void transition(final StoredWorkflow storedWorkflow,
                              final StateContext<TransitionState, TransitionEvent> stateContext) {
        // NOOP
    }

    protected void postTransition(final TransitionState previousState,
                                  final StoredWorkflow storedWorkflow,
                                  final StateContext<TransitionState, TransitionEvent> stateContext) {
        // NOOP
    }
}
