package com.phonepe.verified.kaizen.statemachines.actions.workflowstep;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.actors.HandleInitialActionWorkflowStepCompletionActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepMessage;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class InitialActionCompleteWorkflowStepAction extends UpdateWorkflowStepBaseAction {

    private final HandleInitialActionWorkflowStepCompletionActor handleInitialActionWorkflowStepCompletionActor;

    @Inject
    public InitialActionCompleteWorkflowStepAction(final ProfileService profileService,
                                                   final WorkflowService workflowService,
                                                   final WorkflowStepService workflowStepService,
                                                   final EventIngestionCommand eventIngestionCommand,
                                                   final WorkflowStepRepository workflowStepRepository,
                                                   final Provider<WorkflowContextStore> workflowContextStore,
                                                   final HandleInitialActionWorkflowStepCompletionActor handleInitialActionWorkflowStepCompletionActor) {

        super(profileService, workflowService, workflowStepService, eventIngestionCommand, workflowStepRepository,
                workflowContextStore);
        this.handleInitialActionWorkflowStepCompletionActor = handleInitialActionWorkflowStepCompletionActor;
    }

    @Override
    @SneakyThrows
    protected void transition(final StoredWorkflowStep storedWorkflowStep,
                              final StateContext<TransitionState, TransitionEvent> stateContext) {

        handleInitialActionWorkflowStepCompletionActor.publish(WorkflowStepMessage.builder()
                .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                .build());
    }
}
