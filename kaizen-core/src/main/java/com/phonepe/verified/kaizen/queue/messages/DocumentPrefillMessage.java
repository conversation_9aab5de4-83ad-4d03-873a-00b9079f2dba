package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.requests.document.prefill.DocumentPrefillRequest;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DocumentPrefillMessage extends BaseMessage {

    @NonNull
    private final DocumentType documentType;

    @NonNull
    private final DocumentPrefillRequest documentPrefillRequest;

    @NotEmpty
    private final String workflowId;

    @NotEmpty
    private final String actionMappingId;

    @NonNull
    private final ApiVersion apiVersion;

    private final long componentKitVersion;

    @NotEmpty
    private final String intent;

    @Builder
    @Jacksonized
    public DocumentPrefillMessage(final RequestInfo requestInfo,
                                  @NonNull final DocumentType documentType,
                                  @NonNull final DocumentPrefillRequest documentPrefillRequest,
                                  final String workflowId,
                                  final String actionMappingId,
                                  @NonNull final ApiVersion apiVersion,
                                  final long componentKitVersion,
                                  final String intent) {
        super(ActorMessageType.DOCUMENT_PREFILL, requestInfo);
        this.documentType = documentType;
        this.documentPrefillRequest = documentPrefillRequest;
        this.workflowId = workflowId;
        this.actionMappingId = actionMappingId;
        this.apiVersion = apiVersion;
        this.componentKitVersion = componentKitVersion;
        this.intent = intent;
    }
}
