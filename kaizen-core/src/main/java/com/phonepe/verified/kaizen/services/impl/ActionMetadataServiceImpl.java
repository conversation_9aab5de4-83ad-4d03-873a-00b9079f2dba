package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.visitors.StoredActionMetadataToPurgedStoredActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataRepository;
import io.appform.functionmetrics.MonitoredFunction;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ActionMetadataServiceImpl implements ActionMetadataService {

    private final ActionMetadataRepository actionMetadataRepository;

    private final StoredActionMetadataToPurgedStoredActionMetadataVisitor storedActionMetadataToPurgedStoredActionMetadataVisitor;

    @Override
    public Optional<StoredActionMetadata> save(final StoredActionMetadata storedActionMetadata) {
        return actionMetadataRepository.save(storedActionMetadata);
    }

    @Override
    public boolean saveAll(final String actionId,
                           final List<StoredActionMetadata> entities) {
        return actionMetadataRepository.saveAll(actionId, entities);
    }

    @Override
    public List<StoredActionMetadata> getActionMetadataList(final String actionId) {

        if (Objects.isNull(actionId)) {
            return List.of();
        }

        return actionMetadataRepository.select(actionId);
    }

    @Override
    public List<StoredActionMetadata> getActionMetadataList(final Set<String> actionIds) {

        if (Objects.isNull(actionIds) || actionIds.isEmpty()) {
            return List.of();
        }

        return actionMetadataRepository.select(actionIds);
    }

    @Override
    public boolean isDocumentIdLinkedWithActionMetadata(final String documentId) {

        if (Objects.isNull(documentId) || documentId.isBlank()) {
            return false;
        }

        return actionMetadataRepository.selectDocumentUploadMetadata(documentId)
                .stream()
                .filter(StoredDocumentUploadActionMetadata.class::isInstance)
                .map(StoredDocumentUploadActionMetadata.class::cast)
                .findFirst()
                .isPresent();
    }

    @Override
    public List<StoredActionMetadata> getActionMedataByActionIdAndActionMetaDataType(final String actionId,
                                                                                     final ActionMetadataType actionMetadataType) {

        if (Objects.isNull(actionId)) {
            return List.of();
        }

        return actionMetadataRepository.select(actionId, actionMetadataType);
    }

    @Override
    @MonitoredFunction
    public void updateActionMetadataForPurge(final String actionId) {

        // For each actionMetadata entry, update the data
        getActionMetadataList(actionId).forEach(storedActionMetadata -> storedActionMetadata.accept(
                storedActionMetadataToPurgedStoredActionMetadataVisitor, null));
    }
}
