package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredSessionManagementConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredSessionManagementConfig.Fields;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Optional;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class SessionManagementConfigRepository extends CrudRepository<StoredSessionManagementConfig> {

    @Inject
    public SessionManagementConfigRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredSessionManagementConfig.class), null);
    }

    public Optional<StoredSessionManagementConfig> select(final String profileId,
                                                          final String sourceType) {
        final var detachedCriteria = DetachedCriteria.forClass(StoredSessionManagementConfig.class)
                .add(Restrictions.eq(Fields.profileId, profileId))
                .add(Restrictions.eq(Fields.sourceType, sourceType));
        return select(Constants.PROFILE_SHARD_KEY, detachedCriteria).stream()
                .findFirst();
    }
}
