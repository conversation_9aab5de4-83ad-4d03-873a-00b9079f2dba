package com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowScreenContext {

    private String workflowStepId;

    //get it from storedAction.screenMappingId
    private String screenMappingId;

    //key storedAction.getActionMappingId()
    @Builder.Default
    private Map<String, ActionContext> actionContextMap = new HashMap<>();

}
