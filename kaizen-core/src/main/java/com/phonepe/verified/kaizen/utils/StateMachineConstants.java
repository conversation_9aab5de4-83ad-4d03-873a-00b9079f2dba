package com.phonepe.verified.kaizen.utils;

import lombok.experimental.UtilityClass;

@UtilityClass
public class StateMachineConstants {

    // states
    @UtilityClass
    public static class States {

        public static final String CREATED = "CREATED";
        public static final String HOPE_RULE_VALIDATION_SUCCEEDED = "HOPE_RULE_VALIDATION_SUCCEEDED";
        public static final String HOPE_RULE_VALIDATION_FAILED = "HOPE_RULE_VALIDATION_FAILED";
        public static final String OTP_HURDLE_TRIGGERED = "OTP_HURDLE_TRIGGERED";
        public static final String OTP_GENERATION_FAILED = "OTP_GENERATION_FAILED";
        public static final String OTP_VERIFICATION_FAILED = "OTP_VERIFICATION_FAILED";
        public static final String PERSISTED = "PERSISTED";
        public static final String COMPLETED = "COMPLETED";
        public static final String INVALIDATED = "INVALIDATED";
        public static final String PSEUDO_SUCCESS = "PSEUDO_SUCCESS";
        public static final String ABORTED = "ABORTED";
        public static final String DISCARDED = "DISCARDED";
        public static final String SUCCESS = "SUCCESS";
        public static final String FAILURE = "FAILURE";
        public static final String SKIPPED = "SKIPPED";
        public static final String DOCUMENT_DETAILS_PERSISTED = "DOCUMENT_DETAILS_PERSISTED";
        public static final String EVALUATING = "EVALUATING";
    }


    // events
    @UtilityClass
    public static class Events {

        public static final String CREATE_ENTRY = "CREATE_ENTRY";
        public static final String SENT_FOR_AUTOMATED_MATCHING_TO_VISION = "SENT_FOR_AUTOMATED_MATCHING_TO_VISION";
        public static final String SEND_FOR_MANUAL_VERIFICATION_TO_SALESFORCE = "SEND_FOR_MANUAL_VERIFICATION_TO_SALESFORCE";
        public static final String MANUAL_VERIFICATION_APPROVED_FROM_SALESFORCE = "MANUAL_VERIFICATION_APPROVED_FROM_SALESFORCE";
        public static final String MANUAL_VERIFICATION_REJECTED_FROM_SALESFORCE = "MANUAL_VERIFICATION_REJECTED_FROM_SALESFORCE";
        public static final String SEND_FOR_AUTOMATED_MATCHING_TO_VISION = "SEND_FOR_AUTOMATED_MATCHING_TO_VISION";
        public static final String AUTOMATICALLY_APPROVED_BY_VISION = "AUTOMATICALLY_APPROVED_BY_VISION";
        public static final String AUTOMATICALLY_REJECTED_BY_VISION = "AUTOMATICALLY_REJECTED_BY_VISION";
        public static final String KYC_DATA_SUBMISSION_FAILED_BY_VISION = "KYC_DATA_SUBMISSION_FAILED_BY_VISION";
        public static final String KYC_DATA_SUCCESSFULLY_SUBMITTED_BY_VISION = "KYC_DATA_SUCCESSFULLY_SUBMITTED_BY_VISION";
        public static final String SEND_FOR_SOURCE_VERIFICATION_TO_VISION = "SEND_FOR_SOURCE_VERIFICATION_TO_VISION";
        public static final String AADHAAR_VERIFICATION_SUCCEEDED = "AADHAAR_VERIFICATION_SUCCEEDED";
        public static final String AADHAAR_VERIFICATION_FAILED = "AADHAAR_VERIFICATION_FAILED";
        public static final String ACCEPT_NO_DOCUMENT_AVAILABLE = "ACCEPT_NO_DOCUMENT_AVAILABLE";
        public static final String TRIGGER_OTP_HURDLE = "TRIGGER_OTP_HURDLE";
        public static final String TRIGGER_SELFIE_HURDLE = "TRIGGER_SELFIE_HURDLE";
        public static final String SEND_KYC_DATA_TO_VISION = "SEND_KYC_DATA_TO_VISION";
        public static final String OTP_HURDLE_COMPLETE = "OTP_HURDLE_COMPLETE";
        public static final String FAIL_OTP_GENERATION = "FAIL_OTP_GENERATION";
        public static final String SUCCESS_OTP_GENERATION = "SUCCESS_OTP_GENERATION";
        public static final String FAIL_OTP_VERIFICATION = "FAIL_OTP_VERIFICATION";
        public static final String AUTO_APPROVED_BY_VISION = "AUTO_APPROVED_BY_VISION";
        public static final String COMPLETE_REDIRECTION_URL_GENERATION = "COMPLETE_REDIRECTION_URL_GENERATION";
        public static final String FAIL_REDIRECTION_URL_GENERATION = "FAIL_REDIRECTION_URL_GENERATION";
        public static final String AUTO_REJECTED_BY_VISION = "AUTO_REJECTED_BY_VISION";
        public static final String PERSIST_METADATA = "PERSIST_METADATA";
        public static final String PERSIST_FORENSICS_DATA = "PERSIST_FORENSICS_DATA";
        public static final String FETCH_UPI_ACCOUNT = "FETCH_UPI_ACCOUNT";
        public static final String PROCESS_COMPARATOR = "PROCESS_COMPARATOR";
        public static final String COMPLETE_TRIGGER_REDIRECTION_HURDLE = "COMPLETE_TRIGGER_REDIRECTION_HURDLE";
        public static final String FAIL_TRIGGER_REDIRECTION_HURDLE = "FAIL_TRIGGER_REDIRECTION_HURDLE";
        public static final String COMPLETE_REDIRECTION_HURDLE = "COMPLETE_REDIRECTION_HURDLE";
        public static final String FAIL_REDIRECTION_HURDLE = "FAIL_REDIRECTION_HURDLE";
        public static final String COMPLETE_DOCUMENT_FETCH = "COMPLETE_DOCUMENT_FETCH";
        public static final String FAIL_DOCUMENT_FETCH = "FAIL_DOCUMENT_FETCH";
        public static final String FAIL_HOPE_RULE_VALIDATION = "FAIL_HOPE_RULE_VALIDATION";
        public static final String SUCCEED_HOPE_RULE_VALIDATION = "SUCCEED_HOPE_RULE_VALIDATION";
        public static final String FRAUD_BLOCKED = "FRAUD_BLOCKED";
        public static final String AUTO_APPROVED_BY_FRA = "AUTO_APPROVED_BY_FRA";
        public static final String INVALIDATE_ACTION = "INVALIDATE_ACTION";
        public static final String ABORT_ACTION = "ABORT_ACTION";
        public static final String DISCARD_ACTION = "DISCARD_ACTION";
        public static final String PERSIST_DOCUMENT_DETAILS = "PERSIST_DOCUMENT_DETAILS";
        public static final String DOCUMENT_UPLOAD_SUCCEEDED = "DOCUMENT_UPLOAD_SUCCEEDED";
        public static final String DOCUMENT_UPLOAD_FAILED = "DOCUMENT_UPLOAD_FAILED";
        public static final String MOVE_TO_PSEUDO_SUCCESS = "MOVE_TO_PSEUDO_SUCCESS";
        public static final String PARENT_PSEUDO_ACTION_SUCCEED = "PARENT_PSEUDO_ACTION_SUCCEED";
        public static final String PARENT_PSEUDO_ACTION_FAILED = "PARENT_PSEUDO_ACTION_FAILED";
        public static final String SUCCEED_MANUAL_VERIFICATION = "SUCCEED_MANUAL_VERIFICATION";
        public static final String FAIL_MANUAL_VERIFICATION = "FAIL_MANUAL_VERIFICATION";
        public static final String EXECUTION_NOT_REQUIRED = "EXECUTION_NOT_REQUIRED";
        public static final String TRIGGER_FALLBACK = "TRIGGER_FALLBACK";
        public static final String INPUT_ACCOUNT_VALIDATION_COMPLETED = "INPUT_ACCOUNT_VALIDATION_COMPLETED";
        public static final String INPUT_ACCOUNT_VALIDATION_FAILED = "INPUT_ACCOUNT_VALIDATION_FAILED";
        public static final String ACCOUNT_VALIDATED = "ACCOUNT_VALIDATED";
        public static final String INITIATE_PENNY_DROP = "INITIATE_PENNY_DROP";
        public static final String TEXT_COMPARISON_FAILED = "TEXT_COMPARISON_FAILED";
        public static final String TEXT_COMPARISON_SUCCEEDED = "TEXT_COMPARISON_SUCCEEDED";
        public static final String PENNY_DROP_FAILED = "PENNY_DROP_FAILED";
        public static final String PENNY_DROP_COMPLETED = "PENNY_DROP_COMPLETED";
        public static final String FRA_ACCOUNT_CHECK = "FRA_ACCOUNT_CHECK";
        public static final String FAIL_FORENSICS = "FAIL_FORENSICS";
        public static final String MIGRATE_PAN = "MIGRATE_PAN";
        public static final String MIGRATION_SUCCESS = "MIGRATION_SUCCESS";
        public static final String FAIL_MIGRATION = "FAIL_MIGRATION";
        public static final String FAIL_MIGRATE_PAN = "FAIL_MIGRATE_PAN";
        public static final String MIGRATE_ACCOUNT = "MIGRATE_ACCOUNT";
        public static final String FAIL_MIGRATE_ACCOUNT = "FAIL_MIGRATE_ACCOUNT";
        public static final String COMPLETE_CONSENT = "COMPLETE_CONSENT";
        public static final String SEND_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH = "SEND_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH";
        public static final String KYC_ESIGN_DOCUMENT_SUCCESSFULLY_FETCHED = "KYC_ESIGN_DOCUMENT_SUCCESSFULLY_FETCHED";
        public static final String KYC_ESIGN_DOCUMENT_FETCH_FAILED = "KYC_ESIGN_DOCUMENT_FETCH_FAILED";
        public static final String FAIL_KYC_STATUS_CHECK = "FAIL_KYC_STATUS_CHECK";
        public static final String UPDATE_KYC_STATUS = "UPDATE_KYC_STATUS";
        public static final String COMPLETE_KYC_STATUS_CHECK = "COMPLETE_KYC_STATUS_CHECK";
        public static final String COMPLETE = "COMPLETE";
        public static final String SKIP_WORKFLOW_STEP = "SKIP_WORKFLOW_STEP";
        public static final String RESEND_OTP_GENERATION_FAILED = "RESEND_OTP_GENERATION_FAILED";
        public static final String USER_DOCUMENT_BINDING_FAILED = "USER_DOCUMENT_BINDING_FAILED";
        public static final String USER_DOCUMENT_BINDING_SUCCESS = "USER_DOCUMENT_BINDING_SUCCESS";
        public static final String SEND_FOR_KRA_STATUS_FETCH_TO_VISION = "SEND_FOR_KRA_STATUS_FETCH_TO_VISION";
        public static final String SEND_FOR_KRA_KYC_DATA_FETCH_TO_VISION = "SEND_FOR_KRA_KYC_DATA_FETCH_TO_VISION";
        public static final String KRA_FETCH_PAN_STATUS_SUCCEEDED = "KRA_FETCH_PAN_STATUS_SUCCEEDED";
        public static final String KRA_PAN_CHECK_FAILED = "KRA_PAN_CHECK_FAILED";
        public static final String KRA_DATA_FETCH_SUCCEEDED = "KRA_DATA_FETCH_SUCCEEDED";
        public static final String KRA_DATA_FETCH_FAILED = "KRA_DATA_FETCH_FAILED";
        public static final String CKYC_SUCCESS = "CKYC_SUCCESS";
        public static final String SEND_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED = "SEND_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED";
        public static final String SEND_TO_DRISHTI_FOR_LIVENESS_CHECK_FAILED = "SEND_TO_DRISHTI_FOR_LIVENESS_CHECK_FAILED";
        public static final String LIVENESS_CHECK_SUCCEEDED = "LIVENESS_CHECK_SUCCEEDED";
        public static final String LIVENESS_CHECK_FAILED = "LIVENESS_CHECK_FAILED";
        public static final String INSURER_KYC_SUBMITTED = "INSURER_KYC_SUBMITTED";
        public static final String TRIGGER_CLIENT_KYC_VERIFICATION = "TRIGGER_CLIENT_KYC_VERIFICATION";
        public static final String INSURER_KYC_SUCCESS = "INSURER_KYC_SUCCESS";
        public static final String INSURER_KYC_FAILURE = "INSURER_KYC_FAILURE";
        public static final String CLIENT_KYC_VERIFICATION_SUCCESS = "CLIENT_KYC_VERIFICATION_SUCCESS";
        public static final String CLIENT_KYC_VERIFICATION_FAILURE = "CLIENT_KYC_VERIFICATION_FAILURE";
        public static final String SEND_FOR_CONFIRMATION = "SEND_FOR_CONFIRMATION";
        public static final String CONFIRMED = "CONFIRMED";
        public static final String SCHEDULE_WORKFLOW_ABORT_SUCCESS = "SCHEDULE_WORKFLOW_ABORT_SUCCESS";
        public static final String SCHEDULE_WORKFLOW_ABORT_FAILURE = "SCHEDULE_WORKFLOW_ABORT_FAILURE";
        public static final String COMPLETE_VIDEO_VERIFICATION = "COMPLETE_VIDEO_VERIFICATION";
        public static final String FAIL_VIDEO_VERIFICATION = "FAIL_VIDEO_VERIFICATION";
        public static final String SEND_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED = "SEND_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED";
        public static final String SEND_TO_DRISHTI_FOR_FACE_MATCH_FAILED = "SEND_TO_DRISHTI_FOR_FACE_MATCH_FAILED";
        public static final String FACE_MATCH_SUCCEEDED = "FACE_MATCH_SUCCEEDED";
        public static final String FACE_MATCH_FAILED = "FACE_MATCH_FAILED";
        public static final String SEND_FOR_MANUAL_VERIFICATION_TO_DICTAT0R = "SEND_FOR_MANUAL_VERIFICATION_TO_DICTAT0R";
        public static final String SEND_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED = "SEND_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED";
        public static final String SEND_TO_VISION_FOR_AOF_GENERATION_FAILED = "SEND_TO_VISION_FOR_AOF_GENERATION_FAILED";
        public static final String AOF_GENERATION_SUCCESS = "AOF_GENERATION_SUCCESS";
        public static final String AOF_GENERATION_FAILURE = "AOF_GENERATION_FAILURE";
        public static final String KRA_STATUS_FETCH_SCHEDULE_CLOCKWORK = "KRA_STATUS_FETCH_SCHEDULE_CLOCKWORK";
        public static final String KYC_SUBMIT_STATUS_SUCCESS = "KYC_SUBMIT_STATUS_SUCCESS";
        public static final String KYC_SUBMIT_STATUS_FAILED = "KYC_SUBMIT_STATUS_FAILED";
        public static final String TRIGGER_CKYC_PULL = "TRIGGER_CKYC_PULL";
        public static final String PULL_CKYC_DATA_FROM_CLIENT = "PULL_CKYC_DATA_FROM_CLIENT";
        public static final String CKYC_PULL_SUCCESS = "CKYC_PULL_SUCCESS";
        public static final String SUCCESS = "SUCCESS";
        public static final String SELFIE_HURDLE_COMPLETE = "SELFIE_HURDLE_COMPLETE";
        public static final String SEND_TO_DRISHTI_FOR_OCR_SUCCEEDED = "SEND_TO_DRISHTI_FOR_OCR_SUCCEEDED";
        public static final String SEND_TO_DRISHTI_FOR_OCR_FAILED = "SEND_TO_DRISHTI_FOR_OCR_FAILED";
        public static final String OCR_SUCCEEDED = "OCR_SUCCEEDED";
        public static final String OCR_FAILED = "OCR_FAILED";
        public static final String EVALUATING = "EVALUATING";
        public static final String WAIT_FOR_CONDITION_SUCCESS = "WAIT_FOR_CONDITION_SUCCESS";
        public static final String COMPLETE_OTP_HURDLE = "COMPLETE_OTP_HURDLE";
        public static final String CLIENT_KYC_SUCCESS = "CLIENT_KYC_SUCCESS";
        public static final String CLIENT_KYC_PENDING = "CLIENT_KYC_PENDING";
        public static final String CLIENT_KYC_FAILURE = "CLIENT_KYC_FAILURE";
        public static final String CKYC_PULL_FAILURE = "CKYC_PULL_FAILURE";
        public static final String RENT_PAY_RECEIVER_DETAILS_FETCH_SUCCEED = "RENT_PAY_RECEIVER_DETAILS_FETCH_SUCCEED";
        public static final String RENT_PAY_RECEIVER_DETAILS_FETCH_FAILED = "RENT_PAY_RECEIVER_DETAILS_FETCH_FAILED";
        public static final String SEND_TERMINAL_ACTION_TO_SDK = "SEND_TERMINAL_ACTION_TO_SDK";
        public static final String RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILED = "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILED";
        public static final String RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCEEDED = "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCEEDED";
        public static final String SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCEEDED = "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCEEDED";
        public static final String CALLBACK_NOT_RECEIVED_FROM_PAYMENTS = "CALLBACK_NOT_RECEIVED_FROM_PAYMENTS";
        public static final String TRIGGER_SMS_CONSENT = "TRIGGER_SMS_CONSENT";
        public static final String COMPLETE_SMS_CONSENT = "COMPLETE_SMS_CONSENT";
        public static final String API_CALL_FAILED = "API_CALL_FAILED";
        public static final String SEND_TO_VISION_FOR_TEMPLATE_GENERATION_FAILED = "SEND_TO_VISION_FOR_TEMPLATE_GENERATION_FAILED";
        public static final String SEND_TO_VISION_FOR_TEMPLATE_GENERATION_SUCCEEDED = "SEND_TO_VISION_FOR_TEMPLATE_GENERATION_SUCCEEDED";
        public static final String TEMPLATE_GENERATION_SUCCESS = "TEMPLATE_GENERATION_SUCCESS";
        public static final String TEMPLATE_GENERATION_FAILURE = "TEMPLATE_GENERATION_FAILURE";
        public static final String SENT_TO_VISION_FOR_PAN_AADHAAR_LINK_DETAILS_FETCH = "SENT_TO_VISION_FOR_PAN_AADHAAR_LINK_DETAILS_FETCH";
        public static final String PAN_AADHAAR_LINK_DETAILS_FETCH_SUCCESS = "PAN_AADHAAR_LINK_DETAILS_FETCH_SUCCESS";
        public static final String PAN_AADHAAR_LINK_DETAILS_FETCH_FAILURE = "PAN_AADHAAR_LINK_DETAILS_FETCH_FAILURE";
        public static final String INTENT_GENERATION_CALL_FAILED = "INTENT_GENERATION_CALL_FAILED";
        public static final String INTENT_GENERATION_FAILED = "INTENT_GENERATION_FAILED";
        public static final String INTENT_GENERATION_CALL_TO_GULLAK_SUCCEEDED = "INTENT_GENERATION_CALL_TO_GULLAK_SUCCEEDED";
        public static final String TRIGGER_UI_RESPONSE = "TRIGGER_UI_RESPONSE";
        public static final String PAYMENT_SUCCESS = "PAYMENT_SUCCESS";
        public static final String PAYMENT_FAILURE = "PAYMENT_FAILURE";

    }

    // action keys
    @UtilityClass
    public static class ActionKeys {

        public static final String INVALIDATE_STATE_ACTION_KEY = "invalidateStateAction";
        public static final String ABORT_STATE_ACTION_KEY = "abortStateAction";
        public static final String DISCARD_STATE_ACTION_KEY = "discardStateAction";
    }

}
