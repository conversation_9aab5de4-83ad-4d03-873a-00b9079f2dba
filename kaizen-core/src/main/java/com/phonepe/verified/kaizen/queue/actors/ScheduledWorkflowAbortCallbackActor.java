package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.ScheduleWorkflowAbortActionContext;
import com.phonepe.verified.kaizen.models.requests.workflow.AbortWorkflowRequest;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.AbortWorkflowMessage;
import com.phonepe.verified.kaizen.queue.messages.ScheduledWorkflowAbortCallbackMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ScheduledWorkflowAbortCallbackActor extends BaseActor<ScheduledWorkflowAbortCallbackMessage> {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final ClockworkClient clockworkClient;

    private final WorkflowService workflowService;

    private final AbortWorkflowActor abortWorkflowActor;

    private final WorkflowStepService workflowStepService;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final EventIngestionCommand eventIngestionCommand;

    @Inject
    protected ScheduledWorkflowAbortCallbackActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                  final ConnectionRegistry connectionRegistry,
                                                  final ObjectMapper mapper,
                                                  final RetryStrategyFactory retryStrategyFactory,
                                                  final ExceptionHandlingFactory exceptionHandlingFactory,
                                                  final ActionService actionService,
                                                  final ProfileService profileService,
                                                  final ClockworkClient clockworkClient,
                                                  final WorkflowService workflowService,
                                                  final AbortWorkflowActor abortWorkflowActor,
                                                  final WorkflowStepService workflowStepService,
                                                  final EventIngestionCommand eventIngestionCommand,
                                                  final DataProvider<KaizenConfig> appConfigDataProvider) {
        super(ActorType.SCHEDULED_WORKFLOW_ABORT_CALLBACK,
                actorConfigMap.get(ActorType.SCHEDULED_WORKFLOW_ABORT_CALLBACK), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, ScheduledWorkflowAbortCallbackMessage.class);
        this.actionService = actionService;
        this.profileService = profileService;
        this.clockworkClient = clockworkClient;
        this.workflowService = workflowService;
        this.abortWorkflowActor = abortWorkflowActor;
        this.workflowStepService = workflowStepService;
        appConfigProvider = appConfigDataProvider;
        this.eventIngestionCommand = eventIngestionCommand;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final ScheduledWorkflowAbortCallbackMessage scheduledWorkflowAbortCallbackMessage) {

        final var storedAction = actionService.validateAndGetAction(
                scheduledWorkflowAbortCallbackMessage.getActionId());

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var actionContext = (ScheduleWorkflowAbortActionContext) actionService.extractStepActionContext(
                storedAction.getActionId(), profileStep.getProfileScreenConfig());

        if (TransitionState.PENDING_STATES.contains(storedWorkflow.getCurrentState())) {

            eventIngestionCommand.ingestScheduledAbortWorkflowEvent(storedWorkflow, profile,
                    EventType.WORKFLOW_ABORT_TRIGGERED_ON_SCHEDULED_CALLBACK, storedAction,
                    storedWorkflowStep.getWorkflowStepId(), profileStep.getProfileStepMappingId(),
                    actionContext.getAbortReason(), actionContext.getAbortDuration(), null, null);

            final var abortWorkflowRequest = AbortWorkflowRequest.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .build();

            abortWorkflowActor.publish(AbortWorkflowMessage.builder()
                    .abortWorkflowRequest(abortWorkflowRequest)
                    .userDetails(scheduledWorkflowAbortCallbackMessage.getUserDetails())
                    .build());
        } else {

            eventIngestionCommand.ingestScheduledAbortWorkflowEvent(storedWorkflow, profile,
                    EventType.WORKFLOW_ABORT_NOT_TRIGGERED_ON_SCHEDULED_CALLBACK, storedAction,
                    storedWorkflowStep.getWorkflowStepId(), profileStep.getProfileStepMappingId(),
                    actionContext.getAbortReason(), actionContext.getAbortDuration(), null, null);
        }

        return true;
    }
}
