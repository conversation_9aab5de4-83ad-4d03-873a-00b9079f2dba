package com.phonepe.verified.kaizen.models;

import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.zeus.models.Farm;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class WorkflowInitRequestContext {

    private String emailId;

    @NotEmpty
    private String entityId;

    private String phoneNumber;

    @NotEmpty
    private String profileId;

    @NotNull
    private EntityType entityType;

    private String userReferenceId;

    private Farm callerFarmId;

}