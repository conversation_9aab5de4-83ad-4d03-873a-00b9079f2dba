package com.phonepe.verified.kaizen.models.data.common;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowInitAsyncDetails {

    @NotNull
    private String workflowId;

    @NotNull
    private String requestId;

    @Nullable
    private String userReferenceId;
}