package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class SectionSubmitReceivedEvent extends BaseEvent {

    private String requestId;

    private UpdaterType updaterType;

    private String lastUpdatedBy;

    @Builder
    public SectionSubmitReceivedEvent(final String intent,
                                      final String entityId,
                                      final String namespace,
                                      final String workflowId,
                                      @NonNull final String groupingKey,
                                      final String organization,
                                      final String workflowType,
                                      final String workflowStepId,
                                      final EntityType entityType,
                                      final String workflowVersion,
                                      final String screenMappingId,
                                      final String actionMappingId,
                                      final long componentKitVersion,
                                      final String profileStepMappingId,
                                      final String requestId,
                                      final UpdaterType updaterType,
                                      final ProfileType profileType,
                                      final String addOnType,
                                      final String lastUpdatedBy) {
        super(EventType.SECTION_SUBMIT_RECEIVED, intent, entityId, namespace, workflowId, profileType, addOnType,
                groupingKey, organization, workflowType, workflowStepId, entityType, workflowVersion, screenMappingId,
                actionMappingId, componentKitVersion, profileStepMappingId);
        this.requestId = requestId;
        this.updaterType = updaterType;
        this.lastUpdatedBy = lastUpdatedBy;
    }
}
