package com.phonepe.verified.kaizen.services.visitors.data;

import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ActionCompletionEventIngestionVisitorData {

    private final Profile profile;

    private final ProfileStep profileStep;

    private final StoredAction storedAction;

    private final StoredWorkflow storedWorkflow;

    private final StoredWorkflowStep storedWorkflowStep;

    private final EventIngestionMessage eventIngestionMessage;

    private final ShadowV2UiRequestContext shadowV2UiRequestContext;

}
