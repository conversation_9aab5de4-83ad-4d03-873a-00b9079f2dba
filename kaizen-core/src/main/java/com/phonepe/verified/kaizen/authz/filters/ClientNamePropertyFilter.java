package com.phonepe.verified.kaizen.authz.filters;

import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.olympus.im.models.user.UserDetails;
import java.io.IOException;
import java.security.Principal;
import java.util.Optional;
import javax.annotation.Priority;
import javax.ws.rs.Priorities;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Priority(Priorities.AUTHENTICATION + 10)
public class ClientNamePropertyFilter implements ContainerRequestFilter {

    public static final String CLIENT_NAME_ATTRIBUTE = "RESOLVED_CLIENT_NAME";

    @Override
    public void filter(final ContainerRequestContext containerRequestContext) throws IOException {

        final var userPrincipal = containerRequestContext.getSecurityContext()
                .getUserPrincipal();

        if (userPrincipal instanceof final ServiceUserPrincipal serviceUserPrincipal) {

            Optional.ofNullable(serviceUserPrincipal.getUserAuthDetails())
                    .map(UserAuthDetails::getUserDetails)
                    .map(UserDetails::getUserId)
                    .ifPresent(userId -> containerRequestContext.setProperty(CLIENT_NAME_ATTRIBUTE, userId));

        } else {

            Optional.ofNullable(userPrincipal)
                    .map(Principal::getName)
                    .ifPresent(userName -> containerRequestContext.setProperty(CLIENT_NAME_ATTRIBUTE, userName));
        }
    }
}
