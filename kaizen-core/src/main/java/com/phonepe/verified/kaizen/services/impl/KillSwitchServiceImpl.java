package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.clients.internal.KillSwitchInternalClient;
import com.phonepe.verified.kaizen.clients.models.killswitch.impl.WorkflowInitKillSwitchContext;
import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowAddOnRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowInitRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowInitV2Request;
import com.phonepe.verified.kaizen.services.KillSwitchService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class KillSwitchServiceImpl implements KillSwitchService {

    private final KillSwitchInternalClient killSwitchInternalClient;


    @Override
    public void evaluateWorkflowInitKillSwitch(final WorkflowInitRequest workflowInitRequest,
                                               final Profile profile,
                                               final String workflowId) {

        final var workflowInitKillSwitchContext = WorkflowInitKillSwitchContext.builder()
                .organization(profile.getOrganization())
                .namespace(profile.getNamespace())
                .profileVersion(profile.getVersion())
                .profileType(profile.getProfileType())
                .workflowType(profile.getType())
                .workflowId(workflowId)
                .entityType(workflowInitRequest.getEntityType())
                .entityId(workflowInitRequest.getEntityId())
                .build();

        killSwitchInternalClient.evaluateAndThrow(workflowInitKillSwitchContext);
    }

    @Override
    public void evaluateWorkflowInitV2KillSwitch(final WorkflowInitV2Request workflowInitV2Request,
                                                 final Profile profile,
                                                 final String workflowId) {

        final var workflowInitKillSwitchContext = WorkflowInitKillSwitchContext.builder()
                .organization(profile.getOrganization())
                .namespace(profile.getNamespace())
                .profileVersion(profile.getVersion())
                .profileType(profile.getProfileType())
                .workflowType(profile.getType())
                .workflowId(workflowId)
                .entityType(workflowInitV2Request.getEntityType())
                .entityId(workflowInitV2Request.getEntityId())
                .build();

        killSwitchInternalClient.evaluateAndThrow(workflowInitKillSwitchContext);
    }

    @Override
    public void evaluateWorkflowAddOnInitKillSwitch(final WorkflowAddOnRequest workflowAddOnRequest,
                                                    final AddOnProfile addOnProfile,
                                                    final String workflowId) {

        final var workflowInitKillSwitchContext = WorkflowInitKillSwitchContext.builder()
                .organization(addOnProfile.getOrganization())
                .namespace(addOnProfile.getNamespace())
                .profileVersion(addOnProfile.getVersion())
                .profileType(addOnProfile.getProfileType())
                .workflowType(addOnProfile.getType())
                .workflowId(workflowId)
                .entityType(workflowAddOnRequest.getEntityType())
                .entityId(workflowAddOnRequest.getEntityId())
                .build();

        killSwitchInternalClient.evaluateAndThrow(workflowInitKillSwitchContext);

    }

    @Override
    public void evaluateWorkflowResumeKillSwitch(final Profile profile,
                                                 final StoredWorkflow storedWorkflow) {

        final var workflowInitKillSwitchContext = WorkflowInitKillSwitchContext.builder()
                .organization(profile.getOrganization())
                .namespace(profile.getNamespace())
                .profileVersion(profile.getVersion())
                .profileType(profile.getProfileType())
                .workflowType(profile.getType())
                .workflowId(storedWorkflow.getWorkflowId())
                .entityType(storedWorkflow.getEntityType())
                .entityId(storedWorkflow.getEntityId())
                .build();

        killSwitchInternalClient.evaluateAndThrow(workflowInitKillSwitchContext);
    }
}
