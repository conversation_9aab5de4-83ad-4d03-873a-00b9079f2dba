package com.phonepe.verified.kaizen.statemachines.actions.commons;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.SuccessStateBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@ActionKey(value = "successStateAction")
public class SuccessStateAction extends SuccessStateBaseAction {

    @Inject
    public SuccessStateAction(final ActionService actionService,
                              final ActionRepository actionRepository,
                              final AutoRetryActionService autoRetryActionService,
                              final Provider<AutoRetryActionActor> autoRetryActionActorProvider,
                              final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider,
                              final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider,
                              final Provider<WorkflowContextStore> workflowContextStore,
                              final Provider<EventIngestionActor> eventIngestionActorProvider) {
        super(actionService, actionRepository, autoRetryActionService, autoRetryActionActorProvider,
                handleActionCompletionActorProvider, handlePseudoSuccessActionCompletionActorProvider,
                workflowContextStore, eventIngestionActorProvider);
    }
}
