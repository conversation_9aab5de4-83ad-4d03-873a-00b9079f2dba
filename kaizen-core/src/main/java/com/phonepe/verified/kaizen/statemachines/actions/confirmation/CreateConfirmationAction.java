package com.phonepe.verified.kaizen.statemachines.actions.confirmation;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import lombok.SneakyThrows;
import org.springframework.statemachine.StateContext;

@Singleton
@ActionKey(value = "createConfirmationAction")
public class CreateConfirmationAction extends CreateEntryBaseAction {

    private final ActionExecutorActor actionExecutorActor;

    @Inject
    public CreateConfirmationAction(final ActionService actionService,
                                    final WorkflowService workflowService,
                                    final ClockworkClient clockworkClient,
                                    final ActionRepository actionRepository,
                                    final WorkflowStepService workflowStepService,
                                    final DataProvider<KaizenConfig> appConfigDataProvider,
                                    final Provider<WorkflowContextStore> workflowContextStore,
                                    final Provider<EventIngestionActor> eventIngestionActorProvider,
                                    final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                    final ActionExecutorActor actionExecutorActor,
                                    final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.actionExecutorActor = actionExecutorActor;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        actionExecutorActor.publish(ActionExecutionMessage.builder()
                .actionId(storedAction.getActionId())
                .eventToTrigger(Events.SEND_FOR_CONFIRMATION)
                .userDetails(Constants.PVCORE_SYSTEM_USER)
                .build());
    }
}
