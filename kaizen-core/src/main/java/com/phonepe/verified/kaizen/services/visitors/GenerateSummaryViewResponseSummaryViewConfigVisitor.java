package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.shadow.models.response.Action;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewItem;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewResponse;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewStatus;
import com.phonepe.shadow.services.TemplateServiceV2;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl.MoveToSectionShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.SummaryViewConfigV1;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.SummaryViewItemConfigV1;
import com.phonepe.verified.kaizen.models.configs.summary.config.visitor.SummaryViewConfigVisitor;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GenerateSummaryViewResponseSummaryViewConfigVisitor.GenerateSummaryViewResponseSummaryViewConfigVisitorMessage;
import com.phonepe.verified.kaizen.services.visitors.SummaryViewEditableVisitor.SummaryViewEditableVisitorData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Singleton
@SuppressWarnings("java:S1874")
@AllArgsConstructor(onConstructor = @__(@Inject))
public class GenerateSummaryViewResponseSummaryViewConfigVisitor implements
        SummaryViewConfigVisitor<SummaryViewResponse, GenerateSummaryViewResponseSummaryViewConfigVisitorMessage> {

    private static final Set<SummaryViewStatus> ACTIONABLE_SUMMARY_STATUS = Set.of(SummaryViewStatus.IN_PROGRESS,
            SummaryViewStatus.FAILED, SummaryViewStatus.NOT_STARTED);

    private final TemplateServiceV2 templateService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final ProfileService profileService;

    private final HandleBarsService handleBarsService;

    private final WorkflowContextStore workflowContextStore;

    private final HopeLangService hopeLangService;

    private final ExpressionEvaluator expressionEvaluator;

    private final GetFirstStandardProfileScreenConfig getFirstStandardProfileScreenConfig;

    private final SummaryViewEditableVisitor summaryViewEditableVisitor;

    @Override
    public SummaryViewResponse visit(final SummaryViewConfigV1 summaryViewConfigV1,
                                     final GenerateSummaryViewResponseSummaryViewConfigVisitorMessage data) {

        final var summaryViewItemList = summaryViewConfigV1.getSummaryViewItemConfigList()
                .stream()
                .map(summaryItemV1Config -> calculateSummaryStatusAndBuildSummaryViewItem(summaryItemV1Config, data))
                .toList();

        final var workflowContext = workflowContextStore.getWorkflowContext(data.getWorkflowId());

        final var handleBarResolvedTitle = handleBarsService.transform(summaryViewConfigV1.getTitle(), workflowContext);

        final var handleBarResolvedSubTitle = handleBarsService.transform(summaryViewConfigV1.getSubtitle(),
                workflowContext);

        final var successSummaryItemList = summaryViewItemList.stream()
                .filter(summaryItem -> SummaryViewStatus.SUCCEED.equals(summaryItem.getStatus()))
                .toList();

        final var progressPercentage = new BigDecimal(
                (successSummaryItemList.size() * 100) / summaryViewItemList.size());

        return BuildUtils.toSummaryViewResponse(handleBarResolvedTitle, handleBarResolvedSubTitle,
                summaryViewConfigV1.getProgressDetail(), summaryViewItemList, progressPercentage);
    }

    private SummaryViewItem calculateSummaryStatusAndBuildSummaryViewItem(final SummaryViewItemConfigV1 summaryViewItemConfigV1,
                                                                          final GenerateSummaryViewResponseSummaryViewConfigVisitorMessage data) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(data.getWorkflowId());

        final var workflowContext = workflowContextStore.getWorkflowContext(data.getWorkflowId());

        final var handleBarResolvedTemplate = handleBarsService.transform(
                MapperUtils.serializeToString(summaryViewItemConfigV1), workflowContext);

        final var transformedSummaryViewItemConfigV1 = MapperUtils.deserialize(handleBarResolvedTemplate,
                SummaryViewItemConfigV1.class);

        final var identifierNumber = handleBarsService.transform(summaryViewItemConfigV1.getIdentifierNumberTemplate(),
                workflowContext);

        final var workflowContextJsonNode = (ObjectNode) MapperUtils.convertToJsonNode(workflowContext);
        workflowContextJsonNode.put("intent", data.getIntent());
        workflowContextJsonNode.put("componentKitVersion", data.getComponentKitVersion());

        final var profileStep = profileService.getProfileStep(storedWorkflow.getProfileId(),
                summaryViewItemConfigV1.getProfileStepMappingId());

        final var storedWorkflowStepOptional = workflowStepService.getValidWorkflowStep(data.getWorkflowId(),
                profileStep.getProfileStepId());

        final var summaryViewStatus = storedWorkflowStepOptional.map(this::getSummaryViewStatus)
                .orElseGet(() -> hopeLangService.evaluate(profileStep.getExecutionRule(), workflowContextJsonNode)
                                 ? SummaryViewStatus.NOT_STARTED
                                 : SummaryViewStatus.DISABLED);

        final var editable = summaryViewItemConfigV1.getEditableConfig()
                .evaluate(summaryViewEditableVisitor, SummaryViewEditableVisitorData.builder()
                        .editable(summaryViewItemConfigV1.isEditable())
                        .workflowContextJsonNode(workflowContextJsonNode)
                        .build());

        final MoveToSectionShadowV2ResponseConfig moveToSectionShadowV2ResponseConfig;

        if (ACTIONABLE_SUMMARY_STATUS.contains(summaryViewStatus)) {
            moveToSectionShadowV2ResponseConfig = Constants.MOVE_TO_NEXT_SECTION_SHADOW_V2_RESPONSE_CONFIG;
        } else if (SummaryViewStatus.SUCCEED.equals(summaryViewStatus) && Boolean.TRUE.equals(editable)) {
            moveToSectionShadowV2ResponseConfig = Constants.MOVE_TO_CURRENT_SECTION_SHADOW_V2_RESPONSE_CONFIG;
        } else {
            moveToSectionShadowV2ResponseConfig = null;
        }

        final var action = Objects.nonNull(moveToSectionShadowV2ResponseConfig)
                           ? getActionBasedOnMoveToSectionShadowV2ResponseConfig(storedWorkflow.getProfileId(),
                data.getIntent(), data.getComponentKitVersion(), workflowContextJsonNode, profileStep,
                moveToSectionShadowV2ResponseConfig, storedWorkflowStepOptional.orElse(null))
                           : null;

        return BuildUtils.toSummaryViewItem(transformedSummaryViewItemConfigV1, summaryViewStatus, action,
                identifierNumber);
    }

    private Action getActionBasedOnMoveToSectionShadowV2ResponseConfig(final String profileId,
                                                                       final String intent,
                                                                       final long componentKitVersion,
                                                                       final JsonNode workflowContext,
                                                                       final ProfileStep profileStep,
                                                                       final MoveToSectionShadowV2ResponseConfig moveToSectionShadowV2ResponseConfig,
                                                                       final StoredWorkflowStep storedWorkflowStep) {

        final var standardProfileScreenConfig = profileStep.getProfileScreenConfig()
                .accept(getFirstStandardProfileScreenConfig)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND, Map.of()));

        final var profile = profileService.get(profileId, false);

        final var templateWorkflowType = BuildUtils.getTemplateWorkflowType(profile);

        final var template = templateService.getTemplate(templateWorkflowType, intent, componentKitVersion);

        final var retryAvailable = isRetryAvailableForGivenWorkflowStep(profileStep, storedWorkflowStep);

        return moveToSectionShadowV2ResponseConfig.accept(new BuildShadowV2ActionVisitor(template, workflowContext,
                standardProfileScreenConfig.getScreenMappingId(), standardProfileScreenConfig.getScreenMappingId(),
                hopeLangService, handleBarsService, !retryAvailable, expressionEvaluator), true);
    }

    private boolean isRetryAvailableForGivenWorkflowStep(final ProfileStep profileStep,
                                                         final StoredWorkflowStep storedWorkflowStep) {

        try {
            return workflowStepService.isWorkflowStepRetryAvailable(storedWorkflowStep.getWorkflowStepId(),
                    profileStep.getProfileStepId());
        } catch (final Exception e) {
            return true;
        }
    }

    private SummaryViewStatus getSummaryViewStatus(final StoredWorkflowStep storedWorkflowStep) {

        return switch (storedWorkflowStep.getCurrentState()) {
            case CREATED, IN_PROGRESS, INITIAL_ACTION_IN_PROGRESS, READY, PSEUDO_SUCCESS ->
                    SummaryViewStatus.IN_PROGRESS;
            case SUCCESS, SKIPPED, AUTO_SKIPPED -> SummaryViewStatus.SUCCEED;
            case FAILURE -> SummaryViewStatus.FAILED;
            case ABORTED, INVALIDATED, DISCARDED, PURGED -> SummaryViewStatus.DISABLED;
        };
    }

    @Data
    @Builder
    public static class GenerateSummaryViewResponseSummaryViewConfigVisitorMessage {

        private String workflowId;

        private String intent;

        private long componentKitVersion;
    }
}
