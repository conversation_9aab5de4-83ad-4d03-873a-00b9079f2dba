package com.phonepe.verified.kaizen.clients.internal;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.verified.drishti.models.requests.extraction.ExtractionRequest;
import com.phonepe.verified.drishti.models.requests.facematch.FaceMatchRequest;
import com.phonepe.verified.drishti.models.requests.liveness.LivenessCheckRequest;
import com.phonepe.verified.drishti.models.requests.masking.MaskingRequest;
import com.phonepe.verified.drishti.models.responses.DrishtiAsyncResponse;
import com.phonepe.verified.drishti.models.responses.DrishtiResponse;
import com.phonepe.verified.drishti.models.responses.extraction.ExtractionResponse;
import com.phonepe.verified.drishti.models.responses.facematch.FaceMatchResponse;
import com.phonepe.verified.drishti.models.responses.liveness.LivenessCheckResponse;
import com.phonepe.verified.drishti.models.responses.masking.MaskingResponse;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import java.util.List;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class DrishtiClient {


    private static final String LIVENESS_CHECK_URI = "/v1/liveness-check/selfie";

    private static final String LIVENESS_CHECK_STATUS_URI = "/v1/liveness-check/selfie/status/%s";

    private static final String FACE_MATCH_URI = "/v1/face-match/selfie";

    private static final String FACE_MATCH_STATUS_URI = "/v1/face-match/selfie/status/%s";

    private static final String MASKING_URI = "/v1/mask";

    private static final String FETCH_MASKED_DOCUMENT_URI = "/v1/mask/%s";

    private static final String DOCUMENT_DOWNLOAD_URI = "/v1/document/download/%s";

    private static final String DOCUMENT_OCR_STATUS_URL = "/v1/extract/%s";

    private final OlympusIMClient olympusIMClient;

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    @Inject
    public DrishtiClient(final HttpClientRegistry httpClientRegistry,
                         final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(ClientIds.DRISHTI);
        this.olympusIMClient = olympusIMClient;
    }

    public com.phonepe.verified.drishti.models.commons.documents.DocumentType pvDocumentTypeToDrishtiDocumentType(final DocumentType pvDocumentType) {

        return com.phonepe.verified.drishti.models.commons.documents.DocumentType.valueOf(pvDocumentType.name());
    }

    public DrishtiAsyncResponse submitExtractionRequest(final ExtractionRequest documentExtractionRequest) {

        final var url = "/v1/extract?verifyDocumentType=true";

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executePost(httpExecutorBuilderFactory, "extractDocument", url,
                new SerializableHttpData(MediaType.APPLICATION_JSON, documentExtractionRequest), List.of(authHeader),
                DrishtiAsyncResponse.class, getClass());
    }

    public DrishtiAsyncResponse checkLivenesss(final LivenessCheckRequest livenessCheckRequest) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executePost(httpExecutorBuilderFactory, "livenessCheck", LIVENESS_CHECK_URI,
                new SerializableHttpData(MediaType.APPLICATION_JSON, livenessCheckRequest), List.of(authHeader),
                DrishtiAsyncResponse.class, getClass());

    }

    public DrishtiResponse<LivenessCheckResponse> fetchLivenessCheckStatus(final String requestId) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "drishtiAsyncLivenessCheckStatus",
                String.format(LIVENESS_CHECK_STATUS_URI, requestId), List.of(authHeader), new TypeReference<>() {
                }, getClass());
    }

    public DrishtiAsyncResponse matchFace(final FaceMatchRequest faceMatchRequest) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executePost(httpExecutorBuilderFactory, "faceMatch", FACE_MATCH_URI,
                new SerializableHttpData(MediaType.APPLICATION_JSON, faceMatchRequest), List.of(authHeader),
                DrishtiAsyncResponse.class, getClass());

    }

    public DrishtiResponse<FaceMatchResponse> fetchFaceMatchStatus(final String requestId) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "drishtiAsyncFaceMatchStatus",
                String.format(FACE_MATCH_STATUS_URI, requestId), List.of(authHeader), new TypeReference<>() {
                }, getClass());
    }

    public DrishtiAsyncResponse maskDocument(final MaskingRequest maskingRequest) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executePost(httpExecutorBuilderFactory, "maskDocument", MASKING_URI,
                new SerializableHttpData(MediaType.APPLICATION_JSON, maskingRequest), List.of(authHeader),
                DrishtiAsyncResponse.class, getClass());
    }

    public DrishtiResponse<MaskingResponse> fetchMaskingStatus(final String requestId) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "drishtiAsyncMaskingStatus",
                String.format(FETCH_MASKED_DOCUMENT_URI, requestId), List.of(authHeader), new TypeReference<>() {
                }, getClass());
    }

    public byte[] downloadDocumentFromDrishti(final String documentId) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "drishtiDocumentDownload",
                String.format(DOCUMENT_DOWNLOAD_URI, documentId), List.of(authHeader), byte[].class, getClass());
    }

    public DrishtiResponse<ExtractionResponse> fetchDocumentOcrResponse(final String requestId) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "drishtiAsyncExtractDocumentStatus",
                String.format(DOCUMENT_OCR_STATUS_URL, requestId), List.of(authHeader), new TypeReference<>() {
                }, getClass());
    }
}
