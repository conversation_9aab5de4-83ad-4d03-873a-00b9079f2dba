package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.ProcessAutoRetryClockworkActionMessage;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@SuppressWarnings("java:S3516")
@EqualsAndHashCode(callSuper = true)
public class ProcessAutoRetryActionClockworkActor extends BaseActor<ProcessAutoRetryClockworkActionMessage> {

    private final AutoRetryActionService autoRetryActionService;

    private final EventIngestionActor eventIngestionActor;

    @Inject
    protected ProcessAutoRetryActionClockworkActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                   final ConnectionRegistry connectionRegistry,
                                                   final ObjectMapper mapper,
                                                   final RetryStrategyFactory retryStrategyFactory,
                                                   final ExceptionHandlingFactory exceptionHandlingFactory,
                                                   final AutoRetryActionService autoRetryActionService,
                                                   final EventIngestionActor eventIngestionActor) {
        super(ActorType.AUTO_RETRY_ACTION_CLOCKWORK_CALLBACK,
                actorConfigMap.get(ActorType.AUTO_RETRY_ACTION_CLOCKWORK_CALLBACK), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, ProcessAutoRetryClockworkActionMessage.class);
        this.autoRetryActionService = autoRetryActionService;
        this.eventIngestionActor = eventIngestionActor;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final ProcessAutoRetryClockworkActionMessage processAutoRetryClockworkActionMessage) {

        final var autoRetryActionDetails = autoRetryActionService.decryptAutoRetryActionDetails(
                processAutoRetryClockworkActionMessage.getEncryptedBase64AutoRetryActionDetailsString());

        autoRetryActionService.triggerActionCreation(autoRetryActionDetails);

        return true;
    }
}
