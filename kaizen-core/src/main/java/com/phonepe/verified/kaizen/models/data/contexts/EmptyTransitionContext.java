package com.phonepe.verified.kaizen.models.data.contexts;

import com.phonepe.verified.kaizen.models.data.contexts.visitor.TransitionContextTypeVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyTransitionContext extends TransitionContext {

    @Builder
    public EmptyTransitionContext() {
        super(ContextType.EMPTY_TRANSITION_CONTEXT);
    }

    @Override
    public <T, J> T accept(final TransitionContextTypeVisitor<T, J> transitionContextTypeVisitor,
                           final J data) {
        return transitionContextTypeVisitor.visit(this, data);
    }
}
