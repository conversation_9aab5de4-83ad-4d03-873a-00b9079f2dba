package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import io.dropwizard.util.Duration;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ScheduledWorkflowAbortEvent extends BaseEvent {

    private String actionId;

    private String abortReason;

    private String clockworkJobId;

    private Duration abortDuration;

    private String failureReason;

    @Builder
    public ScheduledWorkflowAbortEvent(final EventType eventType,
                                       final String intent,
                                       final String entityId,
                                       final String namespace,
                                       final String workflowId,
                                       @NotNull final String groupingKey,
                                       final String organization,
                                       final String workflowType,
                                       final String workflowStepId,
                                       final EntityType entityType,
                                       final String workflowVersion,
                                       final String screenMappingId,
                                       final String actionMappingId,
                                       final long componentKitVersion,
                                       final String profileStepMappingId,
                                       final String actionId,
                                       final String abortReason,
                                       final String clockworkJobId,
                                       final ProfileType profileType,
                                       final String addOnType,
                                       final Duration abortDuration,
                                       final String failureReason) {

        super(eventType, intent, entityId, namespace, workflowId, profileType, addOnType, groupingKey, organization,
                workflowType, workflowStepId, entityType, workflowVersion, screenMappingId, actionMappingId,
                componentKitVersion, profileStepMappingId);
        this.actionId = actionId;
        this.abortReason = abortReason;
        this.abortDuration = abortDuration;
        this.clockworkJobId = clockworkJobId;
        this.failureReason = failureReason;
    }
}
