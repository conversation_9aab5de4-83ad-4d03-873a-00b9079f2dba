package com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContextVisitor;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants(innerTypeName = "SubFields")
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings({"java:S6355", "java:S1874", "java:S1133"})
public class ShadowV2UiRequestContext extends UiRequestContext {

    /**
     * @deprecated Keeping it only for backward compatibility
     **/
    @Deprecated
    private String userId;

    private String intent;

    private long componentKitVersion;

    @Valid
    @NotNull
    private SectionInputData sectionInputData;


    public ShadowV2UiRequestContext() {
        super(TemplateType.SHADOW_V2);
    }

    @Builder
    public ShadowV2UiRequestContext(final RequestInfo requestInfo,
                                    final ApiVersion apiVersion,
                                    final String userId,
                                    final String intent,
                                    final long componentKitVersion,
                                    @NonNull final SectionInputData sectionInputData,
                                    final String workflowId) {

        super(TemplateType.SHADOW_V2, requestInfo, apiVersion, workflowId);
        this.userId = userId;
        this.intent = intent;
        this.componentKitVersion = componentKitVersion;
        this.sectionInputData = sectionInputData;
    }

    @Override
    public <T> T accept(final UiRequestContextVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
