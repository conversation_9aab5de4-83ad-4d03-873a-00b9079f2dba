package com.phonepe.verified.kaizen.storage.aerospike.data;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.dependency.DependencyConfig;
import com.phonepe.verified.kaizen.models.configs.ttl.TtlConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoRetryActionDetails {

    private String workflowStepId;

    private ActionType actionType;

    private String stateMachineVersion;

    private String actionMappingId;

    private TransitionContext transitionContext;

    private String screenMappingId;

    private UserDetails userDetails;

    private StepActionContext stepActionContext;

    private TtlConfig ttlConfig;

    private DependencyConfig dependencyConfig;

    private UiRequestContext uiRequestContext;

}
