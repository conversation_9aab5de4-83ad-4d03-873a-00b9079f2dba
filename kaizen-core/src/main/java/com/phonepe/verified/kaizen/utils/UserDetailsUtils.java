package com.phonepe.verified.kaizen.utils;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.authn.UserType;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.util.Map;
import java.util.Objects;
import lombok.experimental.UtilityClass;

@UtilityClass
public class UserDetailsUtils {

    public static UserDetails getGandalfUserDetails(final UserDetails gandalfUserDetails,
                                                    final ServiceUserPrincipal serviceUserPrincipal) {

        if (Objects.nonNull(gandalfUserDetails)) {
            return gandalfUserDetails;

        } else {
            return getGandalfUserDetails(serviceUserPrincipal);
        }
    }

    public static UserDetails getGandalfUserDetails(final ServiceUserPrincipal serviceUserPrincipal) {

        final var olympusUserDetails = serviceUserPrincipal.getUserAuthDetails()
                .getUserDetails();

        if (Objects.nonNull(olympusUserDetails)) {

            return UserDetails.builder()
                    .userId(olympusUserDetails.getUserId())
                    .userType(olympusUserDetails.getUserType() == UserType.HUMAN
                              ? com.phonepe.gandalf.models.authn.UserType.USER
                              : com.phonepe.gandalf.models.authn.UserType.SYSTEM)
                    .name(olympusUserDetails.getName())
                    .token(olympusUserDetails.getToken())
                    .build();
        } else {
            throw KaizenException.create(KaizenResponseCode.AUTH_TOKEN_NOT_AVAILABLE, Map.of());
        }
    }
}