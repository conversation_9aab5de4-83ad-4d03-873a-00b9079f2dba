package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import com.phonepe.verified.kaizen.services.ActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredConsentActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredKeyValueMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredOtpHurdleActionMetaData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredWaitForConditionMetadata;

public abstract class ActionMetadataDefaultVisitor<T, J> implements ActionMetadataVisitor<T, J> {

    public abstract T defaultOperation(J data);

    @Override
    public T visit(final StoredDocumentUploadActionMetadata documentUploadActionMetadata,
                   final J data) {
        return defaultOperation(data);
    }

    @Override
    public T visit(final StoredKeyValueMetadata keyValuePairsActionMetadata,
                   final J data) {
        return defaultOperation(data);
    }

    @Override
    public T visit(final StoredConsentActionMetadata consentActionMetadata,
                   final J data) {
        return defaultOperation(data);
    }

    @Override
    public T visit(final StoredWaitForConditionMetadata storedWaitForConditionMetadata,
                   final J data) {
        return defaultOperation(data);
    }

    @Override
    public T visit(final StoredOtpHurdleActionMetaData storedCkycMetaData,
                   final J data) {
        return defaultOperation(data);
    }

    @Override
    public T visit(final StoredDocumentUploadWithMetaDataActionMetadata storedDocumentUploadWithMetaDataActionMetadata,
                   final J data) {
        return defaultOperation(data);
    }
}
