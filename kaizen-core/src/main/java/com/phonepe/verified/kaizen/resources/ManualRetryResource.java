package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.ManualRetryService;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.constraints.NotEmpty;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/manual/retry")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Manual Retry Resource", description = "Manual Retry related APIs")
public class ManualRetryResource {


    private static final String RESOURCE_WORKFLOW = "WORKFLOW";
    private static final String OPERATION_MANUAL_RETRY = "MANUAL_RETRY";
    private final AuthZService authZService;
    private final ManualRetryService manualRetryService;


    @POST
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/workflow/{workflowId}/workflowStep/{workflowStepId}/action/{actionId}")
    @Operation(summary = "Manual Retry of action for a given workflow id with workflowStepId and actionId")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void retryActionManually(@NotEmpty @PathParam("workflowId") final String workflowId,
                                    @NotEmpty @PathParam("workflowStepId") final String workflowStepId,
                                    @NotEmpty @PathParam("actionId") final String actionId,
                                    @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                    @Parameter(hidden = true) @GandalfUserContext final UserDetails userDetails,
                                    @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo) {

        authZService.authorizeOperationForTenant(authZService.getTenantFromWorkflow(workflowId), RESOURCE_WORKFLOW,
                OPERATION_MANUAL_RETRY, serviceUserPrincipal);

        manualRetryService.triggerManualRetry(workflowId, workflowStepId, actionId);
    }
}
