package com.phonepe.verified.kaizen.statemachines.actions.waitforcondition;

import com.fasterxml.jackson.databind.JsonNode;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.WaitForConditionActionContext;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigForGivenAction;
import com.phonepe.verified.kaizen.services.visitors.StandardProfileScreenConfigFromProfileStepVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.UpdateStateBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredWaitForConditionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "evaluatingConditionAction")
public class EvaluatingConditionAction extends UpdateStateBaseAction {

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final WorkflowContextStore workflowContextStore;

    private final HopeLangService hopeLangService;

    private final Provider<ActionExecutorActor> actionExecutorActorProvider;

    private final ActionMetadataRepository actionMetadataRepository;


    @Inject
    public EvaluatingConditionAction(final ActionService actionService,
                                     final ActionRepository actionRepository,
                                     final Provider<WorkflowContextStore> workflowContextStore,
                                     final Provider<EventIngestionActor> eventIngestionActorProvider,
                                     final ProfileService profileService,
                                     final WorkflowService workflowService,
                                     final WorkflowStepService workflowStepService,
                                     final HopeLangService hopeLangService,
                                     final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                     final ActionMetadataRepository actionMetadataRepository) {
        super(actionService, actionRepository, workflowContextStore, eventIngestionActorProvider);
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
        this.workflowContextStore = workflowContextStore.get();
        this.hopeLangService = hopeLangService;
        this.actionExecutorActorProvider = actionExecutorActorProvider;
        this.actionMetadataRepository = actionMetadataRepository;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var profileScreenConfig = profileStep.getProfileScreenConfig();

        final var standardProfileScreenConfig = profileScreenConfig.accept(
                        new StandardProfileScreenConfigFromProfileStepVisitor(storedAction.getScreenMappingId()))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                        Map.of(Fields.screenMappingId, storedAction.getScreenMappingId())));

        final var standardStepActionConfig = standardProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Fields.actionMappingId, storedAction.getActionMappingId())));

        final var waitForConditionActionContext = (WaitForConditionActionContext) standardStepActionConfig.getStepActionContext();

        Objects.requireNonNull(waitForConditionActionContext);

        final Retryer<Boolean> checkConditionRetryer = RetryerBuilder.<Boolean>newBuilder()
                .retryIfResult(aBoolean -> Objects.equals(aBoolean, false))
                .withWaitStrategy(WaitStrategies.fixedWait(waitForConditionActionContext.getRetryIntervalInSeconds(),
                        TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterDelay(waitForConditionActionContext.getTimeoutInSeconds(),
                        TimeUnit.SECONDS))
                .build();

        final var result = evaluateConditionBasedOnRetryer(checkConditionRetryer,
                waitForConditionActionContext.getEvaluationRule(), storedWorkflow);

        actionMetadataRepository.save(StoredWaitForConditionMetadata.builder()
                .actionId(storedAction.getActionId())
                .value(String.valueOf(result))
                .build());

        actionExecutorActorProvider.get()
                .publish(ActionExecutionMessage.builder()
                        .actionId(storedAction.getActionId())
                        .eventToTrigger(Events.WAIT_FOR_CONDITION_SUCCESS)
                        .userDetails(Constants.PVCORE_SYSTEM_USER)
                        .build());

    }

    private boolean evaluateConditionBasedOnRetryer(final Retryer<Boolean> checkConditionRetryer,
                                                    final String evaluationRule,
                                                    final StoredWorkflow storedWorkflow) {
        try {
            return checkConditionRetryer.call(() -> hopeLangService.evaluate(evaluationRule,
                    getWorkFlowContextJsonNode(storedWorkflow.getWorkflowId())));

        } catch (final Exception e) {
            return false;
        }
    }

    private JsonNode getWorkFlowContextJsonNode(final String workflowId) {

        final var workflowContext = workflowContextStore.getWorkflowContext(workflowId);

        return MapperUtils.convertToJsonNode(workflowContext);

    }

}
