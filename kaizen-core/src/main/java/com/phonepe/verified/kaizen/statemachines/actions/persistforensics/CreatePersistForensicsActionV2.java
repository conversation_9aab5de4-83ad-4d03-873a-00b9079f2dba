package com.phonepe.verified.kaizen.statemachines.actions.persistforensics;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.PersistForensicsActor;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "createPersistForensicsActionV2")
public class CreatePersistForensicsActionV2 extends CreateEntryBaseAction {

    private final PersistForensicsActor persistForensicsActor;

    @Inject
    public CreatePersistForensicsActionV2(final WorkflowService workflowService,
                                          final ActionService actionService,
                                          final ClockworkClient clockworkClient,
                                          final ActionRepository actionRepository,
                                          final WorkflowStepService workflowStepService,
                                          final PersistForensicsActor persistForensicsActor,
                                          final DataProvider<KaizenConfig> appConfigDataProvider,
                                          final Provider<WorkflowContextStore> workflowContextStore,
                                          final Provider<EventIngestionActor> eventIngestionActorProvider,
                                          final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                          final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.persistForensicsActor = persistForensicsActor;
    }

    @Override
    @SneakyThrows
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {
        persistForensicsActor.publish(StepActionMessage.builder()
                .actionId(storedAction.getActionId())
                .build());
    }

}
