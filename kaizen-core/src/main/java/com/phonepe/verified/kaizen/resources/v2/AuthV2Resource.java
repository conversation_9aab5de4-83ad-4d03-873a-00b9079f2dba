package com.phonepe.verified.kaizen.resources.v2;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.primer.PrimerTokenResponseV2;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2LoginRequest;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2LogoutRequest;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2RefreshRequest;
import com.phonepe.verified.kaizen.models.responses.auth.AuthTokenResponse;
import com.phonepe.verified.kaizen.services.AuthNService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v2/auth")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Auth V2", description = "Auth Related V2 APIs")
public class AuthV2Resource {

    private final AuthNService authNService;

    private final WorkflowService workflowService;

    @POST
    @Path("/login")
    @Operation(summary = "Login to get Auth Token")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    public Response login(@NotNull @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                          @Nullable @HeaderParam(Headers.ACTX) final String actxHeader,
                          @Valid @NotNull final AuthenticationV2LoginRequest authenticationV2LoginRequest) {

        final var accessTokenData = authNService.validateAndGetAccessToken(authenticationV2LoginRequest);

        final var storedWorkflow = workflowService.validateAndGetWorkflow(accessTokenData.getWorkflowId());

        final var tokenResponseV2 = authNService.getPrimerV2Token(actxHeader, requestInfo, storedWorkflow,
                accessTokenData);

        authNService.deleteAccessToken(authenticationV2LoginRequest.getAccessToken());

        return getResponse(tokenResponseV2);
    }

    @DELETE
    @Path("/logout")
    @Operation(summary = "logout")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    public void logout(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                       @Valid @NotNull final AuthenticationV2LogoutRequest authenticationV2LogoutRequest) {

        authNService.clear(authenticationV2LogoutRequest);
    }

    @PUT
    @Path("/refresh")
    @Operation(summary = "Refresh primer token")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    public Response resume(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                           @Nullable @HeaderParam(Headers.ACTX) final String actxHeader,
                           @Valid @NotNull final AuthenticationV2RefreshRequest authenticationV2RefreshRequest) {

        final var tokenResponseV2 = authNService.refresh(actxHeader, authenticationV2RefreshRequest);

        return getResponse(tokenResponseV2);
    }

    private Response getResponse(final PrimerTokenResponseV2 tokenResponseV2) {

        final var authToken = AuthTokenResponse.builder()
                .refreshTokenExpiresAt(tokenResponseV2.getRefreshTokenExpiresAt())
                .expiresAt(tokenResponseV2.getExpiresAt())
                .refreshToken(tokenResponseV2.getRefreshToken())
                .authToken(tokenResponseV2.getToken())
                .build();

        return Response.ok(authToken)
                .build();

    }
}