package com.phonepe.verified.kaizen.statemachines.actions.workflow;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionType;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.tag.OrderedWorkflowTagRule;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.actors.ClientWorkflowCallbackActor;
import com.phonepe.verified.kaizen.queue.actors.PostCompletionActionConfigProcessorActor;
import com.phonepe.verified.kaizen.queue.messages.PostCompletionActionConfigProcessorMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigForGivenAction;
import com.phonepe.verified.kaizen.services.visitors.GetWorkflowTagConfigVisitor;
import com.phonepe.verified.kaizen.services.visitors.SearchPostCompletionActionVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.Comparator;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class CompleteWorkflowAction extends UpdateWorkflowBaseAction {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final HopeLangService hopeLangService;

    private final WorkflowStepService workflowStepService;

    private final DynamicUiResponseService dynamicUiResponseService;

    private final Provider<WorkflowContextStore> workflowContextStore;

    private final Provider<ClientWorkflowCallbackActor> clientWorkflowCallbackActorProvider;

    private final PostCompletionActionConfigProcessorActor postCompletionActionConfigProcessorActor;

    @Inject
    public CompleteWorkflowAction(final ProfileService profileService,
                                  final WorkflowService workflowService,
                                  final HopeLangService hopeLangService,
                                  final WorkflowRepository workflowRepository,
                                  final EventIngestionCommand eventIngestionCommand,
                                  final ActionService actionService,
                                  final WorkflowStepService workflowStepService,
                                  final Provider<WorkflowContextStore> workflowContextStore,
                                  final DynamicUiResponseService dynamicUiResponseService,
                                  final Provider<ClientWorkflowCallbackActor> clientWorkflowCallbackActorProvider,
                                  final PostCompletionActionConfigProcessorActor postCompletionActionConfigProcessorActor) {
        super(profileService, workflowService, workflowRepository, eventIngestionCommand, workflowContextStore);
        this.profileService = profileService;
        this.workflowStepService = workflowStepService;
        this.actionService = actionService;
        this.hopeLangService = hopeLangService;
        this.dynamicUiResponseService = dynamicUiResponseService;
        this.workflowContextStore = workflowContextStore;
        this.clientWorkflowCallbackActorProvider = clientWorkflowCallbackActorProvider;
        this.postCompletionActionConfigProcessorActor = postCompletionActionConfigProcessorActor;
    }

    @Override
    protected void transition(final StoredWorkflow storedWorkflow,
                              final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        if (Objects.isNull(profile.getPostCompletionActionConfig()) ||
                (Boolean.FALSE == profile.getPostCompletionActionConfig()
                        .accept(SearchPostCompletionActionVisitor.INSTANCE,
                                PostCompletionActionType.TAG_CALCULATION_ON_WORKFLOW_CONTEXT))
                        && Boolean.FALSE == profile.getPostCompletionActionConfig()
                        .accept(SearchPostCompletionActionVisitor.INSTANCE,
                                PostCompletionActionType.TAG_CALCULATION_ON_ENTITY_DETAILS)) {

            final var workflowTagConfig = profile.accept(GetWorkflowTagConfigVisitor.INSTANCE, null);

            if (TransitionState.COMPLETION_STATES_TO_BE_TAGGED.contains(storedWorkflow.getCurrentState())
                    && Objects.nonNull(workflowTagConfig)) {

                final var workflowContext = workflowContextStore.get()
                        .getWorkflowContext(storedWorkflow.getWorkflowId());

                final var workflowContextJsonNode = MapperUtils.convertToJsonNode(workflowContext);

                final var tag = workflowTagConfig.getOrderedWorkflowTagRules()
                        .stream()
                        .sorted(Comparator.comparing(OrderedWorkflowTagRule::getOrder))
                        .filter(tagRule -> hopeLangService.evaluate(tagRule.getEvaluationRule(),
                                workflowContextJsonNode))
                        .findFirst()
                        .map(OrderedWorkflowTagRule::getTag)
                        .orElse(workflowTagConfig.getDefaultTag());

                storedWorkflow.setTag(tag);
            }
        }
    }

    @Override
    @SneakyThrows
    protected void postTransition(final TransitionState previousState,
                                  final StoredWorkflow storedWorkflow,
                                  final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var actionId = stateContext.getExtendedState()
                .get(StoredAction.Fields.actionId, String.class);

        final var failureErrorCodeOptional = getFailureErrorCode(storedWorkflow, actionId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var workflowMessageForClientCallback = WorkflowMessage.builder()
                .workflowId(storedWorkflow.getWorkflowId())
                .failureErrorCode(failureErrorCodeOptional.orElse(null))
                .build();

        if (Objects.isNull(profile.getPostCompletionActionConfig())) {

            clientWorkflowCallbackActorProvider.get()
                    .publish(workflowMessageForClientCallback);
        } else {

            postCompletionActionConfigProcessorActor.publish(PostCompletionActionConfigProcessorMessage.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .failureErrorCode(failureErrorCodeOptional.orElse(null))
                    .build());
        }

        /*
         * Not triggering UI Response in previousState PSEUDO_SUCCESS
         * because PSEUDO_SUCCESS to SUCCESS/FAILURE is a background process
         */
        if (TransitionState.SUCCESS_STATES.contains(storedWorkflow.getCurrentState())
                && TransitionState.PSEUDO_SUCCESS != previousState) {

            final var standardProfileScreenConfig = stateContext.getExtendedState()
                    .get(StandardProfileScreenConfig.class, StandardProfileScreenConfig.class);

            final var workflowStepId = stateContext.getExtendedState()
                    .get(Fields.workflowStepId, String.class);

            Objects.requireNonNull(standardProfileScreenConfig);
            Objects.requireNonNull(workflowStepId);

            final var uiRequestContext = workflowStepService.getUiRequestContext(workflowStepId);

            //TODO:-workflow completion event Ingestion

            final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(workflowStepId);

            if (TransitionState.SKIPPED == storedWorkflowStep.getCurrentState()) {

                dynamicUiResponseService.sendResponseToUi(uiRequestContext,
                        standardProfileScreenConfig.getSkipWorkflowStepConfig()
                                .getSkipWorkflowStepResponseConfig(), storedWorkflowStep.getWorkflowId());
            } else {

                dynamicUiResponseService.sendWorkflowCompletionResponseToUi(uiRequestContext,
                        standardProfileScreenConfig, storedWorkflowStep.getWorkflowId());
            }
        }

        if (TransitionState.FAILURE == storedWorkflow.getCurrentState()) {

            final var standardProfileScreenConfig = stateContext.getExtendedState()
                    .get(StandardProfileScreenConfig.class, StandardProfileScreenConfig.class);

            if (Objects.nonNull(standardProfileScreenConfig)) {

                final var workflowStepId = stateContext.getExtendedState()
                        .get(Fields.workflowStepId, String.class);

                Objects.requireNonNull(workflowStepId);
                Objects.requireNonNull(actionId);

                final var storedAction = actionService.validateAndGetAction(actionId);

                final var standardStepActionConfig = standardProfileScreenConfig.getStepActionConfig()
                        .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                        .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                                Map.of(StoredAction.Fields.actionMappingId, storedAction.getActionMappingId())));

                final var uiResponseConfigMap = standardStepActionConfig.getFailureResponseConfig()
                        .get(storedAction.getFailureErrorCode());

                final var uiRequestContext = workflowStepService.getUiRequestContext(workflowStepId);

                dynamicUiResponseService.sendResponseToUi(uiRequestContext, uiResponseConfigMap,
                        storedWorkflow.getWorkflowId());
            }
        }
    }

    private Optional<ActionFailureErrorCode> getFailureErrorCode(final StoredWorkflow storedWorkflow,
                                                                 final String actionId) {

        if (Objects.nonNull(actionId) && TransitionState.FAILURE == storedWorkflow.getCurrentState()) {

            return Optional.ofNullable(actionService.validateAndGetAction(actionId)
                    .getFailureErrorCode());
        }

        return Optional.empty();
    }
}
