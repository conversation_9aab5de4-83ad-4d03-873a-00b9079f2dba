package com.phonepe.verified.kaizen.utils;

import com.google.common.collect.ImmutableMap;
import com.phonepe.gandalf.models.authn.UserType;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.http.v2.executor.BaseHttpData;
import com.phonepe.platform.http.v2.executor.httpdata.EmptyHttpData;
import com.phonepe.platform.requestinfo.models.headers.RequestInfoHeaders;
import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.MoveToSectionScreen;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl.MoveToSectionShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.data.contexts.EmptyTransitionContext;
import com.phonepe.zeus.models.Farm;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import javax.ws.rs.core.HttpHeaders;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;


@UtilityClass
@SuppressWarnings("java:S1075")
public class Constants {

    public final String MESSAGE = "message";
    public final String KEY = "key";
    public final String VALUE = "value";
    public final String UNAUTHORIZED = "UNAUTHORIZED";
    public final String SERVICE_NAME = "service";
    public final String COMMAND_NAME = "command";
    public final String URL = "url";
    public final String RESPONSE = "response";
    public final String CODE = "code";
    public final String NOT_FOUND = "Not Found";
    public final Set<String> STAGING_EQUIVALENT_ENVIRONMENTS = Set.of("STAGE", "LOCAL");
    public final String OLYMPUS_AUTH_NAME = "O-Bearer";
    public final String CBS_NAME = "cbsName";
    public final String AUTO_ABORT_CALLBACK_PATH_FORMAT = "/v1/internal/callback/workflow/auto/abort/%s";
    public final String AUTO_SKIP_CALLBACK_PATH_FORMAT = "/v1/internal/callback/workflow/auto/skip/%s";
    public static final String ADD_NEW_ACCOUNT = "ADD_NEW_ACCOUNT";
    public static final String PV_AUTH_Z_CONTEXT = "PV_AUTH_Z_CONTEXT";
    public static final String PV_WORKFLOW_ID = "PV_WORKFLOW_ID";
    public final String SOURCE_PVCORE = "PVCORE";

    public final int PENNY_CALLBACK_WAIT_TIME = 24;

    public final String POPULAR_BANKS = "Popular Banks";
    public final String OTHER_BANKS = "Other Banks";

    public final String DEFAULT_BIN = "default";
    public final int MASKED_IDENTIFIER_DEFAULT_LENGTH = 4;

    public final String USER_ID = "USER_ID";

    public final String PURGE_STRING = "__PURGED__";

    public final String STATE_MACHINE_SHARD_KEY = "STATE_MACHINE_SHARD_KEY";
    public final String PROFILE_SHARD_KEY = "PROFILE_SHARD_KEY";

    public final String SM_ACTION_PACKAGE = "com.phonepe.verified.kaizen.statemachines.actions";

    public final String WORKFLOW_STEP_LOCK_POSTFIX = "_WORKFLOW_STEP";
    public final String WORKFLOW_STEP_IN_WORKFLOW_LOCK_POSTFIX = "WORKFLOW_STEP_IN_WORKFLOW";
    public final String WORKFLOW_STEP_ACTION_LOCK_POSTFIX = "_ACTION";

    public final String IS_RETRYABLE = "isRetryable";

    public final String HEIMDALL_ERROR_CODE = "HEIMDALL_ERROR_CODE";
    public final String SENTINEL_ERROR_CODE = "SENTINEL_ERROR_CODE";
    public final String HEIMDALL_ERROR_MESSAGE = "HEIMDALL_ERROR_MESSAGE";
    public final String SENTINEL_ERROR_MESSAGE = "SENTINEL_ERROR_MESSAGE";
    public final String SHOULD_MOVE_TO_IN_PROGRESS = "SHOULD_MOVE_TO_IN_PROGRESS";

    public static final String UPI_ACCOUNT_ID = "upiAccountId";
    public static final String UPI_IFSC = "upiIfsc";

    public static final String FRAUD_ACTION_RESPONSE_CODE = "fraudActionResponseCode";
    public static final String FRAUD_ACTION_TYPE = "fraudActionType";

    public static final String KYC_STATUS_STATE = "kycState";
    public static final String KYC_REJECTION_REASON_CODES = "kycRejectionReasonCodes";
    public static final String KYC_REJECTION_REASON = "kycRejectionReason";
    public static final String WALLET_NAMESPACE = "WALLET";
    public static final String DOCUMENT_TYPE = "documentType";
    public static final String NAME = "name";
    public static final String DOCUMENT_NUMBER = "documentNumber";

    public final String APPLICATION_JSON_STRING = "application/json";

    public final String CONTENT_TYPE_STRING = "Content-Type";

    public final Map<String, String> CLOCKWORK_PROD_HEADERS = Map.of("Accept", APPLICATION_JSON_STRING,
            HttpHeaders.CONTENT_TYPE, APPLICATION_JSON_STRING);

    public final Map<String, String> CLOCKWORK_STAGE_HEADERS = Map.of("Accept", APPLICATION_JSON_STRING,
            HttpHeaders.CONTENT_TYPE, APPLICATION_JSON_STRING, RequestInfoHeaders.REQUEST_ENV,
            FeatureEnvironmentUtils.getFeatureEnvironmentName()
                    .orElse("stage"));

    public final String SOMETHING_WENT_WRONG_MESSAGE = "Something went wrong. Please resend OTP.";
    public final String SOMETHING_WENT_WRONG_CODE = "SOMETHING_WENT_WRONG";

    public final String UNIQUE_WORKFLOW_STEP_ID = "%s:%s";

    public final String PYXIS_CLIENT_REFERENCE_ID = "%s:%s:%s";

    public final String AADHAAR_RESEND_OTP_CODE = "PVC001";

    public final String AADHAAR_SHADOW_V2_UI_REQUEST_CONTEXT_CODE = "PVC002";

    public final String WRONG_DOCUMENT_PASSWORD_CODE = "INVALID_PASSWORD";

    public final String AADHAAR_RESEND_OTP_MESSAGE = "OTP generation limited exceeded, try after 15 mins.";

    public final String AADHAAR_SHADOW_V2_UI_REQUEST_CONTEXT_MESSAGE = "Try resending otp from first page.";
    public final String DEFAULT_KYC_CITY = "Bengaluru";
    public final String CONSUMER_APP = "CONSUMER_APP";
    public final String PHONE_NUMBER = "phoneNumber";
    public final String EMAIL = "email";
    public final String SUCCESS = "SUCCESS";
    public final String PERMISSION_SEPARATOR = "/";
    public final Map<String, String> HEIMDALL_ERROR_CODE_RESPONSE = ImmutableMap.<String, String>builder()
            .put("HM001", "USER_BANNED")
            .put("HM003", "OTP_ALREADY_VERIFIED")
            .put("HM004", "VERIFICATION_ATTEMPTS_EXHAUSTED")
            .put("HM005", "OTP_EXPIRED")
            .put("HM007", "ERROR_SENDING_OTP")
            .put("HM008", "INVALID_OTP")
            .build();

    public final Map<String, String> HEIMDALL_ERROR_MESSAGE_RESPONSE = ImmutableMap.<String, String>builder()
            .put("HM001", "User is banned. Please retry after sometime.")
            .put("HM003", "OTP is already verified. Please resend OTP.")
            .put("HM004", "Verification attempts exhausted. Please resend OTP.")
            .put("HM005", "OTP expired. Please resend OTP.")
            .put("HM007", SOMETHING_WENT_WRONG_MESSAGE)
            .put("HM008", "Incorrect OTP. Please enter the correct OTP.")
            .build();
    public final Map<String, String> SENTINEL_ERROR_CODE_RESPONSE = ImmutableMap.<String, String>builder()
            .put("SEN1001", "ERROR_SENDING_OTP")
            .put("SEN1003", "NOT_AUTHORIZED")
            .put("SEN1006", "INVALID_OTP_INIT")
            .put("SEN1100", "USER_OTP_BLOCKED")
            .put("SEN1101", "OTP_LIMIT_EXHAUSTED")
            .put("SEN1102", "OTP_ALREADY_VERIFIED")
            .put("SEN1103", "OTP_TRY_LATER")
            .put("SEN1104", "INVALID_OTP")
            .put("SEN1105", "OTP_EXPIRED")
            .put("SEN1107", "USER_NOT_FOUND")
            .put("SEN1108", "USER_BLACKLISTED")
            .put("SEN1122", "INVALID_MOBILE_NUMBER")
            .put("SEN1124", "INVALID_SESSION")
            .put("SEN1161", "LIMIT_BREACHED")
            .build();

    public final Map<String, String> SENTINEL_ERROR_MESSAGE_RESPONSE = ImmutableMap.<String, String>builder()
            .put("SEN1001", SOMETHING_WENT_WRONG_MESSAGE)
            .put("SEN1003", "Not authorized")
            .put("SEN1006", "Please try again later")
            .put("SEN1100", "User is blocked. Try later")
            .put("SEN1101", "OTP limit exhausted. Try later")
            .put("SEN1102", "OTP already verified")
            .put("SEN1103", "Service unavailable. Try later")
            .put("SEN1104", "Invalid OTP")
            .put("SEN1105", "OTP Expired. Try later")
            .put("SEN1107", "User not found")
            .put("SEN1108", "User is blacklisted")
            .put("SEN1122", "Invalid Mobile Number")
            .put("SEN1124", "SessionId mismatch")
            .put("SEN1161", "Limit breached")
            .build();

    public final BaseHttpData EMPTY_HTTP_DATA = new EmptyHttpData();

    public final EmptyTransitionContext EMPTY_TRANSITION_CONTEXT = EmptyTransitionContext.builder()
            .build();

    public final String APP_NAME = "kaizen";
    public final UserDetails PVCORE_SYSTEM_USER = UserDetails.builder()
            .userId(Constants.APP_NAME)
            .userType(UserType.SYSTEM)
            .build();
    public static final String ACCESS_TOKEN = "access_token";
    public static final String SALESFORCE_ACCESS_TOKEN = "SALESFORCE_ACCESS_TOKEN";

    public final String INITIAL_ACTION_STEP = "INITIAL_ACTION_STEP";
    public final String BEARER = "Bearer ";
    public final MathContext MATH_CONTEXT_FOR_NAME_MATCHING = new MathContext(4, RoundingMode.HALF_EVEN);
    public final BigDecimal DEFAULT_TOKEN_THRESHOLD = new BigDecimal("0.75", Constants.MATH_CONTEXT_FOR_NAME_MATCHING);
    public final Farm FARM_ID = Farm.valueOf(System.getenv("DC_ID")
            .toUpperCase());

    public final String CONFIG_ENV = Optional.ofNullable(System.getenv("CONFIG_ENV"))
            .orElse("LOCAL")
            .toUpperCase();

    public final String PVCORE_FARM_ID_TEXT = String.format("pvcore%s", Constants.FARM_ID);
    public final SectionInputData DUMMY_SECTION_INPUT_DATA = SectionInputData.builder()
            .mappingId("DUMMY_MAPPING_ID")
            .fieldDataList(List.of())
            .build();
    public static final String EXPIRY = "expiry";
    public static final String PYXIS_WORKFLOW_ID = "pyxisWorkflowId";
    public static final String PYXIS_EXPIRY_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    public static final String ERROR_CODE = "errorCode";
    public static final String AGENT_ACTION = "agentAction";
    public static final String REVIEWER_ACTION = "reviewerAction";
    public static final String DOCUMENT_UPLOAD_STATE = "documentUploadState";
    public final String PVC_ID_PREFIX = "PVC";
    public final String FATHER_NAME_KEY = "fatherName";
    public final String MOTHER_NAME_KEY = "motherName";
    public final String DATE_OF_BIRTH_KEY = "dateOfBirth";
    public final String ANNUAL_INCOME_KEY = "annualIncome";
    public final String OCCUPATION_KEY = "occupation";
    public final String OCULUS_USER_ROLE = "oculusUser";
    public final String CONSUMER_ROLE = "consumer";
    public final String AGENT_ID = "agentId";
    public final String DICTAT0R_CASE_ID = "caseId";
    public final String DICTAT0R_SECTION_MAPPING_ID = "sectionMappingId";
    public final String DICTAT0R_REJECTION_REASON_COUNTS = "rejectionReasonCount";
    public final String MANUAL_VERIFICATION_REJECTION_REASON = "manualVerificationRejectionReason";

    public final MoveToSectionShadowV2ResponseConfig MOVE_TO_NEXT_SECTION_SHADOW_V2_RESPONSE_CONFIG = MoveToSectionShadowV2ResponseConfig.builder()
            .screen(MoveToSectionScreen.NEXT)
            .build();
    public final MoveToSectionShadowV2ResponseConfig MOVE_TO_CURRENT_SECTION_SHADOW_V2_RESPONSE_CONFIG = MoveToSectionShadowV2ResponseConfig.builder()
            .screen(MoveToSectionScreen.CURRENT)
            .build();

    public final String FIRST_3CHARS = "FIRST_3CHARS";
    public final String EMPTY_STRING = "EMPTY_STRING";
    public final String EXACT_MATCH = "EXACT_MATCH";
    public final String ABORT_REASON_FOR_INCOMPLETE_WORKFLOW_STEP = "Scheduled workflow abort for incomplete workflow step";
    public static final String ORIGINAL_FILE_NAME = "ORIGINAL_FILE_NAME";
    public static final String FILE_SIZE = "FILE_SIZE";

    private final String UNKNOWN = "UNKNOWN";
    public final String MESOS_HOSTNAME = StringUtils.defaultIfBlank(System.getenv("HOST"), UNKNOWN);
    public final String MESOS_TASK_ID = StringUtils.defaultIfBlank(System.getenv("MESOS_TASK_ID"), UNKNOWN);

    @UtilityClass
    public static final class Headers {

        public static final String AUTHORIZATION = "Authorization";
        public static final String X_RESPONSE_CODE = "X-RESPONSE-CODE";
        public static final String X_RESPONSE_DYNAMIC_MAILBOX = "X-RESPONSE-DYNAMIC-MAILBOX";
        public static final String X_POLLING_TIME = "X-POLLING-TIME";
        public static final String X_POLLING_FREQUENCY = "X-POLLING-FREQUENCY";
        public static final String X_DEVICE_FINGERPRINT = "X-Device-Fingerprint";
        public static final String X_SESSION_TOKEN = "x-session-token";
        public static final String X_AUTH_TOKEN = "X-Auth-Token";
        public static final String ACTX = "ACTX";
        public static final String X_TOKEN_ID = "X-Token-Id";
        public static final String X_CALLBACK_URI = "X-CALLBACK-URI";
        public static final String X_CALL_MODE = "X-CALL-MODE";
        public static final String X_MAILBOX_TTL = "X-MAILBOX-TTL";
        public static final String X_REQUEST_ID = "X-Request-Id";
    }

    @UtilityClass
    public static final class OlympusPermissionNames {

        public static final String FETCH_ACCOUNT_DETAILS = "FETCH_ACCOUNT_DETAILS";
        public static final String ACCOUNT_VERIFICATION_CALLBACK = "ACCOUNT_VERIFICATION_CALLBACK";
        public static final String VIDEO_VERIFICATION_CALLBACK = "VIDEO_VERIFICATION_CALLBACK";
        public static final String CACHE_REFRESH = "CACHE_REFRESH";
        public static final String CLOCKWORK = "CLOCKWORK";
        public static final String DELETE_DOCUMENT = "DELETE_DOCUMENT";
        public static final String DOCUMENT_FETCH_CALLBACK = "DOCUMENT_FETCH_CALLBACK";
        public static final String DOCUMENT_EXTRACT_CALLBACK = "DOCUMENT_EXTRACT_CALLBACK";
        public static final String DOCUMENT_VERIFICATION_CALLBACK = "DOCUMENT_VERIFICATION_CALLBACK";
        public static final String ESIGN_DOCUMENT_FETCH_CALLBACK = "ESIGN_DOCUMENT_FETCH_CALLBACK";
        public static final String GET_GST_DETAILS = "GET_GST_DETAILS";
        public static final String GET_ALL_PROFILE = "GET_ALL_PROFILE";
        public static final String GET_BANKS = "GET_BANKS";
        public static final String GET_BANK_ACCOUNTS = "GET_BANK_ACCOUNTS";
        public static final String GET_TEMPLATE = "GET_TEMPLATE";
        public static final String HOUSEKEEPING_GET_WORKFLOW_CONTEXT = "HOUSEKEEPING_GET_WORKFLOW_CONTEXT";
        public static final String GET_DETAILS = "GET_DETAILS";
        public static final String PROCESS_RESPONSE = "PROCESS_RESPONSE";
        public static final String KYC_DATA_SUBMISSION_CALLBACK = "KYC_DATA_SUBMISSION_CALLBACK";
        public static final String KYC_CONSOLE = "KYC_CONSOLE";
        public static final String KYC_CONSOLE_MASKED = "KYC_CONSOLE_MASKED";
        public static final String KYC_STATUS_CALLBACK = "KYC_STATUS_CALLBACK";
        public static final String KRA_PAN_STATUS_FETCH_CALLBACK = "KRA_PAN_STATUS_FETCH_CALLBACK";
        public static final String MIN_KYC_SATORI_MIGRATION = "MIN_KYC_SATORI_MIGRATION";
        public static final String MO_BATCH_MIGRATION = "MO_BATCH_MIGRATION";
        public static final String REDIRECT_ACK = "REDIRECT_ACK";
        public static final String REDIRECTION_URL_GENERATION_CALLBACK = "REDIRECTION_URL_GENERATION_CALLBACK";
        public static final String RESEND_OTP = "RESEND_OTP";
        public static final String SAVE_VALHALLA_ACCOUNT_DETAILS = "SAVE_VALHALLA_ACCOUNT_DETAILS";
        public static final String SECTION_SUBMIT = "SECTION_SUBMIT";
        public static final String STATE_MACHINE_MANAGEMENT = "STATE_MACHINE_MANAGEMENT";
        public static final String UPLOAD_DOCUMENT = "UPLOAD_DOCUMENT";
        public static final String EXTRACT_DOCUMENT = "EXTRACT_DOCUMENT";
        public static final String VERIFY_OTP = "VERIFY_OTP";
        public static final String SELFIE_LIVENESS_CHECK_CALLBACK = "SELFIE_LIVENESS_CHECK_CALLBACK";
        public static final String INSURANCE_KYC_DETAILS_ACCEPTANCE = "INSURANCE_KYC_DETAILS_ACCEPTANCE";
        public static final String ONCALL = "ONCALL";
        public static final String SB_KYC_MIGRATION_WORKFLOW_INIT = "SB_KYC_MIGRATION_WORKFLOW_INIT";
        public static final String SB_KYC_MIGRATION_PERSIST_DOCUMENTS = "SB_KYC_MIGRATION_PERSIST_DOCUMENTS";
        public static final String CONFIRM_ACTION = "CONFIRM_ACTION";
        public static final String SKIP_WORKFLOW_STEP = "SKIP_WORKFLOW_STEP";
        public static final String GET_SUMMARY = "GET_SUMMARY";
        public static final String SELFIE_FACE_MATCH_CALLBACK = "SELFIE_FACE_MATCH_CALLBACK";
        public static final String MANUAL_VERIFICATION_CALLBACK = "MANUAL_VERIFICATION_CALLBACK";
        public static final String AOF_GENERATION_CALLBACK = "AOF_GENERATION_CALLBACK";
        public static final String HOUSEKEEPING_CHANGE_WORKFLOW_STATE = "HOUSEKEEPING_CHANGE_WORKFLOW_STATE";
        public static final String HOUSEKEEPING_RECONCILE_KYC_STATUS = "HOUSEKEEPING_RECONCILE_KYC_STATUS";
        public static final String KYC_SUBMIT_STATUS_CHECK_CALLBACK = "KYC_SUBMIT_STATUS_CHECK_CALLBACK";
        public static final String CLIENT_KYC_VERIFICATION_CALLBACK = "CLIENT_KYC_VERIFICATION_CALLBACK";
        public static final String CLIENT_CKYC_DATA_AVAILABLE_CALLBACK = "CLIENT_CKYC_DATA_AVAILABLE_CALLBACK";
        public static final String DOCUMENT_MASKING_CALLBACK = "DOCUMENT_MASKING_CALLBACK";
        public static final String SEARCH_CLIENT_DATA = "SEARCH_CLIENT_DATA";
        public static final String VALIDATE_NEFT_ENABLED_IFSC = "VALIDATE_NEFT_ENABLED_IFSC";
        public static final String VALIDATE_CCD_PINCODE = "VALIDATE_CCD_PINCODE";
        public static final String FETCH_PAN_AADHAAR_LINK_DETAILS_CALLBACK = "FETCH_PAN_AADHAAR_LINK_DETAILS_CALLBACK";
        public static final String RESOLVE_VIRTUAL_PAYMENT_ADDRESS_CALLBACK = "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_CALLBACK";
        public static final String KYC_CONSOLE_DOCUMENT_DOWNLOAD = "KYC_CONSOLE_DOCUMENT_DOWNLOAD";
        public static final String ACKNOWLEDGE_SMS_CONSENT = "ACKNOWLEDGE_SMS_CONSENT";
        public static final String RECONCILE_EXTERNAL_KYC_STATE = "RECONCILE_EXTERNAL_KYC_STATE";
        public static final String FORM_TEMPLATING_CALLBACK = "FORM_TEMPLATING_CALLBACK";
        public static final String TENANT_DETAILS = "TENANT_DETAILS";
        public static final String RECALCULATE_MO_TAG = "RECALCULATE_MO_TAG";
        public static final String DOWNLOAD_DOCUMENT = "DOWNLOAD_DOCUMENT";
        public static final String REVERSE_PENNY_DROP_CALLBACK = "REVERSE_PENNY_DROP_CALLBACK";
        public static final String REVERSE_PENNY_DROP_STATUS_CHECK = "REVERSE_PENNY_DROP_STATUS_CHECK";
        public static final String CORRECTIVE_ACTION = "CORRECTIVE_ACTION";
    }

    @UtilityClass
    public static final class ClientIds {

        public static final String ATLAS = "atlas";
        public static final String DOCSTORE = "docstore";
        public static final String API = "api";
        public static final String KILLSWITCH = "killswitch";
        public static final String DRISHTI = "drishti";
        public static final String CLOCKWORK = "clockwork";
        public static final String SENTINEL = "sentinel";
        public static final String PRIMER = "primer";
        public static final String CATALOGUE = "catalogue-service";
        public static final String GEOLOCATION = "geolocation";

        public static final Set<String> WHITELISTED_CLIENT_IDS_FOR_HTTP_CLIENT_REGISTRY = Set.of(ClientIds.ATLAS,
                ClientIds.DOCSTORE, ClientIds.API, ClientIds.DRISHTI, ClientIds.CLOCKWORK, ClientIds.PRIMER,
                ClientIds.CATALOGUE, ClientIds.GEOLOCATION);
    }
}
