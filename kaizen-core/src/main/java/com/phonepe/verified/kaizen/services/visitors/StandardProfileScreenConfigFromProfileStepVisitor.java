package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import java.util.Comparator;
import java.util.Optional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

// todo{Ankit}:: Create Static method for this Visitor as a wrapper
@RequiredArgsConstructor
public class StandardProfileScreenConfigFromProfileStepVisitor implements
        ProfileScreenVisitor<Optional<StandardProfileScreenConfig>> {

    @NonNull
    private final String screenMappingId;

    @Override
    public Optional<StandardProfileScreenConfig> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        if (standardProfileScreenConfig.getScreenMappingId()
                .equals(screenMappingId)) {

            return Optional.of(standardProfileScreenConfig);
        }
        return Optional.empty();
    }

    @Override
    public Optional<StandardProfileScreenConfig> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        for (final var orderedProfileScreen : sortedOrderedProfileScreenList) {

            final var standardProfileScreenConfigOptional = orderedProfileScreen.getProfileScreenConfig()
                    .accept(this);

            if (standardProfileScreenConfigOptional.isPresent()) {
                return standardProfileScreenConfigOptional;
            }
        }

        return Optional.empty();
    }
}
