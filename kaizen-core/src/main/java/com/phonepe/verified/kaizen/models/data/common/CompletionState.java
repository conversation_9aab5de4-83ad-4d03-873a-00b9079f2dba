package com.phonepe.verified.kaizen.models.data.common;

import java.util.Set;
import lombok.experimental.UtilityClass;

public enum CompletionState {

    // todo{Ankit}:: Use State enum

    SUCCESS {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitSuccess(data);
        }
    },
    PSEUDO_SUCCESS {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitPseudoSuccess(data);
        }
    },
    FAILURE {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitFailure(data);
        }
    },
    IN_PROGRESS {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitInProgress(data);
        }
    },
    NOT_STARTED {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitNotStarted(data);
        }
    },
    INVALIDATED {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitInvalidated(data);
        }
    },
    SKIPPED {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitSkipped(data);
        }
    },
    ABORTED {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitAborted(data);
        }
    },
    DISCARDED {
        @Override
        public <T, U> T accept(final CompletionStateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitDiscarded(data);
        }
    };

    public static final Set<CompletionState> SUCCESS_STATES = Set.of(CompletionState.SUCCESS,
            CompletionState.PSEUDO_SUCCESS);

    public static final Set<CompletionState> TERMINAL_STATES = Set.of(CompletionState.SUCCESS,
            CompletionState.PSEUDO_SUCCESS, CompletionState.FAILURE);

    public static final Set<CompletionState> NON_COMPLETED_STATES = Set.of(CompletionState.IN_PROGRESS,
            CompletionState.INVALIDATED, CompletionState.NOT_STARTED);

    public static final Set<CompletionState> NON_STARTED_STATES = Set.of(CompletionState.INVALIDATED,
            CompletionState.NOT_STARTED);

    public static final Set<CompletionState> NON_SUCCESS_COMPLETED_STATES = Set.of(CompletionState.FAILURE,
            CompletionState.ABORTED, CompletionState.DISCARDED);

    public static final Set<CompletionState> DEDUPE_CONSIDERABLE_ACTION_STATES = Set.of(CompletionState.SUCCESS,
            CompletionState.PSEUDO_SUCCESS, CompletionState.IN_PROGRESS, CompletionState.DISCARDED);


    public abstract <T, U> T accept(CompletionState.CompletionStateVisitor<T, U> visitor,
                                    U data);

    public interface CompletionStateVisitor<T, U> {

        T visitSuccess(U data);

        T visitPseudoSuccess(U data);

        T visitFailure(U data);

        T visitInProgress(U data);

        T visitNotStarted(U data);

        T visitInvalidated(U data);

        T visitSkipped(U data);

        T visitAborted(U data);

        T visitDiscarded(U data);
    }

    @UtilityClass
    public static final class Names {

        public static final String SUCCESS = "SUCCESS";
        public static final String PSEUDO_SUCCESS = "PSEUDO_SUCCESS";
        public static final String FAILURE = "FAILURE";
        public static final String IN_PROGRESS = "IN_PROGRESS";
        public static final String SKIPPED = "SKIPPED";
        public static final String NOT_STARTED = "NOT_STARTED";
        public static final String INVALIDATED = "INVALIDATED";
        public static final String ABORTED = "ABORTED";
        public static final String DISCARDED = "DISCARDED";
    }
}
