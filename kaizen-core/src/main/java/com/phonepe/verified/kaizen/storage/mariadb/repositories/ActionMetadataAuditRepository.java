package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadataAudit;
import io.appform.dropwizard.sharding.DBShardingBundleBase;

public class ActionMetadataAuditRepository extends CrudRepository<StoredActionMetadataAudit> {

    @Inject
    public ActionMetadataAuditRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredActionMetadataAudit.class), null);
    }
}
