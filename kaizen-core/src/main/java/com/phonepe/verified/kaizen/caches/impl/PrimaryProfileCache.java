package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.caches.key.PrimaryProfileCacheKey;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.services.impl.ProfileServiceImpl;

@Singleton
public class PrimaryProfileCache extends Cache<PrimaryProfileCacheKey, PrimaryProfile> {

    private final Provider<ProfileServiceImpl> profileService;

    @Inject
    public PrimaryProfileCache(final CaffeineCacheConfig caffeineCacheConfig,
                               final MetricRegistry metricRegistry,
                               final Provider<ProfileServiceImpl> profileService) {
        super(CacheName.PRIMARY_PROFILE_CACHE, caffeineCacheConfig, metricRegistry);
        this.profileService = profileService;
    }

    @Override
    protected PrimaryProfile build(final PrimaryProfileCacheKey primaryProfileCacheKey) {
        return profileService.get()
                .getPrimaryProfileFromDb(primaryProfileCacheKey);
    }
}
