package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.CurrentProfileScreenNextProfileScreen;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.actors.ScheduleWorkflowAbortForStepActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowScreenService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.CollectActionsToInvalidateDuringResubmitVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetFirstStandardProfileScreenConfig;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigForGivenAction;
import com.phonepe.verified.kaizen.services.visitors.IdentifyProfileScreenToTrigger;
import com.phonepe.verified.kaizen.services.visitors.ProfileScreenCompletionVisitor;
import com.phonepe.verified.kaizen.services.visitors.RetryConfigExecutor;
import com.phonepe.verified.kaizen.services.visitors.RetryConfigExecutor.RetryConfigExecutorData;
import com.phonepe.verified.kaizen.services.visitors.StandardProfileScreenConfigFromProfileStepVisitor;
import com.phonepe.verified.kaizen.statemachines.WorkflowStepStateMachine;
import com.phonepe.verified.kaizen.storage.aerospike.commands.UiRequestContextCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.TransitionLockKey;
import com.phonepe.verified.kaizen.storage.aerospike.keys.UiRequestContextKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepAuditRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.IdUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants;
import io.appform.functionmetrics.MonitoredFunction;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachine;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class WorkflowStepServiceImpl implements WorkflowStepService {

    public static final Set<CompletionState> FILTERED_STATES_FOR_NOT_INVALIDATE = Set.of(CompletionState.INVALIDATED,
            CompletionState.ABORTED, CompletionState.DISCARDED, CompletionState.FAILURE);

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final RetryConfigExecutor retryConfigExecutor;

    private final WorkflowContextStore workflowContextStore;

    private final WorkflowScreenService workflowScreenService;

    private final WorkflowStepRepository workflowStepRepository;

    private final WorkflowStepAuditRepository workflowStepAuditRepository;

    private final UiRequestContextCommand uiRequestContextCommand;

    private final WorkflowStepStateMachine workflowStepStateMachine;

    private final DynamicUiResponseService dynamicUiResponseService;

    private final ScheduleWorkflowAbortForStepActor scheduleWorkflowAbortForStepActor;

    @Override
    public void createWorkflowStep(final String workflowId,
                                   final String entityId,
                                   final String workflowStepId,
                                   final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                   final ProfileStep profileStep,
                                   final UserDetails userDetails,
                                   final boolean shouldMoveToInProgress) {

        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(StoredWorkflow.Fields.entityId, entityId);
        stateMachineTransitionContext.put(Fields.workflowId, workflowId);
        stateMachineTransitionContext.put(Fields.workflowStepId, workflowStepId);
        if (Objects.nonNull(shadowV2SectionSubmitRequest)) {
            stateMachineTransitionContext.put(ShadowV2SectionSubmitRequest.class, shadowV2SectionSubmitRequest);
        }
        stateMachineTransitionContext.put(ProfileStep.class, profileStep);
        stateMachineTransitionContext.put(UserDetails.class, userDetails);
        stateMachineTransitionContext.put(Constants.SHOULD_MOVE_TO_IN_PROGRESS, shouldMoveToInProgress);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(workflowId + Constants.WORKFLOW_STEP_IN_WORKFLOW_LOCK_POSTFIX)
                .build());

        workflowStepStateMachine.sendEvent(TransitionState.CREATED, TransitionEvent.CREATE_ENTRY,
                stateMachineTransitionContext);
    }

    @Override
    public StoredWorkflowStep createAndGetWorkflowStep(final String workflowId,
                                                       final String entityId,
                                                       final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                                       final ProfileStep profileStep,
                                                       final UserDetails userDetails) {

        final var workflowStepId = IdUtils.createIdInSameShard("WS", workflowId, workflowStepRepository::getShardId);

        createWorkflowStep(workflowId, entityId, workflowStepId, shadowV2SectionSubmitRequest, profileStep, userDetails,
                false);

        return validateAndGetWorkflowStep(workflowStepId);
    }

    @Override
    public void triggerWorkflowStep(final StoredWorkflowStep storedWorkflowStep,
                                    final String screenMappingId,
                                    final UserDetails userDetails) {

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var profileScreenStatus = profileStep.getProfileScreenConfig()
                .accept(new IdentifyProfileScreenToTrigger(storedWorkflowStep.getWorkflowStepId(), screenMappingId,
                        actionService, workflowService, hopeLangService, workflowContextStore))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND, Map.of()));

        invalidateScreensDuringResubmit(storedWorkflowStep.getWorkflowStepId(),
                profileScreenStatus.getProfileScreenConfigToTrigger(), profileStep.getProfileScreenConfig());

        workflowScreenService.triggerStepActionInProfileScreen(profileScreenStatus.getProfileScreenConfigToTrigger(),
                storedWorkflowStep, screenMappingId, userDetails);
    }

    @Override
    public void createAndTriggerInitialActionWorkflowStep(final StoredWorkflow storedWorkflow) {

        final var profileStep = profileService.getProfileStep(storedWorkflow.getProfileId(),
                Constants.INITIAL_ACTION_STEP);

        final var storedWorkflowStep = createAndGetWorkflowStep(storedWorkflow.getWorkflowId(),
                storedWorkflow.getEntityId(), null, profileStep, Constants.PVCORE_SYSTEM_USER);

        triggerEvent(storedWorkflowStep, TransitionEvent.TRIGGER_INITIAL_ACTION);
    }

    @Override
    @SneakyThrows
    public void triggerInitialActionWorkflowStep(final StoredWorkflowStep storedWorkflowStep) {

        final var profile = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var initialActionStandardProfileScreenConfig = profile.getProfileScreenConfig()
                .accept(GetFirstStandardProfileScreenConfig.INSTANCE)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.INITIAL_ACTION_PROFILE_STEP_NOT_FOUND,
                        Map.of()));

        final var screenMappingId = initialActionStandardProfileScreenConfig.getScreenMappingId();

        triggerWorkflowStep(storedWorkflowStep, screenMappingId, Constants.PVCORE_SYSTEM_USER);

        // Abort workflow after configured time if initialization fails / takes longer time
        scheduleWorkflowAbortForStepActor.publish(WorkflowStepMessage.builder()
                .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                .build());
    }

    private void invalidateScreensDuringResubmit(final String workflowStepId,
                                                 final StandardProfileScreenConfig currentProfileScreenConfig,
                                                 final ProfileScreenConfig profileScreenConfig) {

        final var actionMappingIdsToInvalidate = profileScreenConfig.accept(
                new CollectActionsToInvalidateDuringResubmitVisitor(currentProfileScreenConfig));

        final var storedActionsToInvalidate = actionService.getActions(workflowStepId, actionMappingIdsToInvalidate);

        final var actionMappingIdToStoredActionsMap = storedActionsToInvalidate.stream()
                .collect(Collectors.groupingBy(StoredAction::getActionMappingId));

        for (final List<StoredAction> storedActionList : actionMappingIdToStoredActionsMap.values()) {

            storedActionList.stream()
                    .max(Comparator.comparing(StoredAction::getCreatedAt))
                    .filter(storedAction -> !FILTERED_STATES_FOR_NOT_INVALIDATE.contains(
                            storedAction.getCompletionState()))
                    .ifPresent(this::triggerInvalidateActionEvent);
        }
    }

    private void triggerInvalidateActionEvent(final StoredAction storedActionToInvalidate) {

        actionService.triggerEvent(storedActionToInvalidate.getActionId(), storedActionToInvalidate.getActionType(),
                storedActionToInvalidate.getStateMachineVersion(), StateMachineConstants.Events.INVALIDATE_ACTION,
                Constants.EMPTY_TRANSITION_CONTEXT, Constants.PVCORE_SYSTEM_USER, null);
    }

    @Override
    public Optional<StoredWorkflowStep> getValidWorkflowStep(final String workflowId,
                                                             final String profileStepId) {

        return getWorkflowSteps(workflowId, profileStepId).stream()
                .filter(workflowStep -> TransitionState.INVALIDATED != workflowStep.getCurrentState())
                .findFirst();
    }

    @Override
    public List<StoredWorkflowStep> getWorkflowSteps(final String workflowId,
                                                     final String profileStepId) {

        return workflowStepRepository.selectAll(workflowId, profileStepId);
    }

    @Override
    public List<StoredWorkflowStep> getValidWorkflowStepsFromWorkflowId(final String workflowId) {

        final var allWorkflowSteps = getWorkflowStepsFromWorkflowId(workflowId);

        return allWorkflowSteps.stream()
                .filter(workflowStep -> TransitionState.INVALIDATED != workflowStep.getCurrentState())
                .toList();
    }

    @Override
    public List<StoredWorkflowStep> getWorkflowStepsFromWorkflowId(final String workflowId) {

        if (Objects.isNull(workflowId)) {
            return List.of();
        }

        return workflowStepRepository.selectAll(workflowId);
    }

    @Override
    public List<StoredWorkflowStep> getWorkflowStepsFromWorkflowIds(final Set<String> workflowIds) {

        if (Objects.isNull(workflowIds) || workflowIds.isEmpty()) {
            return List.of();
        }

        return workflowStepRepository.selectAll(workflowIds);
    }

    @Override
    public StoredWorkflowStep validateAndGetWorkflowStep(final String workflowStepId) {
        return workflowStepRepository.select(workflowStepId)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.WORKFLOW_STEP_NOT_FOUND,
                        Map.of(Constants.MESSAGE, "Unable to find workflow step for given inputs",
                                Fields.workflowStepId, workflowStepId)));
    }

    @Override
    public List<StoredWorkflowStep> getWorkflowStepsFromAllShards(final Set<String> workflowStepIds) {

        return workflowStepRepository.selectFromAllShards(workflowStepIds);
    }

    @Override
    public StateMachine<TransitionState, TransitionEvent> triggerMoveToInProgressEvent(final String workflowStepId,
                                                                                       final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                                                                       final UserDetails userDetails) {

        return triggerEvent(workflowStepId, shadowV2SectionSubmitRequest, userDetails,
                TransitionEvent.MOVE_TO_IN_PROGRESS);
    }

    @Override
    public StateMachine<TransitionState, TransitionEvent> triggerResubmitEvent(final String workflowStepId,
                                                                               final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                                                               final UserDetails userDetails) {

        return triggerEvent(workflowStepId, shadowV2SectionSubmitRequest, userDetails, TransitionEvent.RESUBMIT);
    }

    @Override
    public void handleScreenCompletion(final CompletionState completionStateOfScreen,
                                       final String completedActionId) {

        final var storedAction = actionService.validateAndGetAction(completedActionId);

        final var storedWorkflowStep = validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var uiRequestContext = uiRequestContextCommand.get(UiRequestContextKey.builder()
                .workflowStepId(storedAction.getWorkflowStepId())
                .build());

        final var currentProfileScreenNextProfileScreen = profileStep.getProfileScreenConfig()
                .accept(new ProfileScreenCompletionVisitor(storedAction.getScreenMappingId()));

        if (!currentProfileScreenNextProfileScreen.isMappingIdFound()) {

            throw KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                    Map.of(Fields.workflowStepId, storedWorkflowStep.getWorkflowStepId(), Fields.workflowId,
                            storedWorkflowStep.getWorkflowId(), Fields.profileStepId,
                            storedWorkflowStep.getProfileStepId()));
        }

        if (CompletionState.SUCCESS_STATES.contains(completionStateOfScreen)) {

            handleScreenSuccess(completedActionId, storedWorkflowStep, uiRequestContext,
                    currentProfileScreenNextProfileScreen, completionStateOfScreen);

        } else if (completionStateOfScreen == CompletionState.FAILURE) {

            handleScreenFailed(completedActionId, storedAction, storedWorkflowStep,
                    currentProfileScreenNextProfileScreen);
        }
    }

    @Override
    public void autoSkipWorkflowStep(final StoredWorkflowStep storedWorkflowStep,
                                     final UserDetails userDetails,
                                     final boolean invalidateAllActions) {

        if (TransitionState.AUTO_SKIPPED == storedWorkflowStep.getCurrentState()) {
            return;
        }

        if (invalidateAllActions) {
            actionService.invalidateActionsInWorkflowSteps(Set.of(storedWorkflowStep.getWorkflowStepId()), userDetails);
        }

        triggerEvent(storedWorkflowStep, TransitionEvent.AUTO_SKIP_WORKFLOW_STEP, null, null, false);
    }

    @Override
    public void skipWorkflowStep(final StoredWorkflowStep storedWorkflowStep,
                                 final StandardProfileScreenConfig standardProfileScreenConfig,
                                 final UserDetails userDetails,
                                 final ActionType actionType,
                                 final TransitionEvent transitionEvent,
                                 final boolean invalidateAllActions) {

        if (TransitionState.SKIPPED == storedWorkflowStep.getCurrentState()) {
            return;
        }

        final var actionId = actionService.createAndSkipAction(storedWorkflowStep, standardProfileScreenConfig,
                userDetails, actionType, transitionEvent);

        if (invalidateAllActions) {

            actionService.invalidateActionsInWorkflowSteps(Set.of(storedWorkflowStep.getWorkflowStepId()), userDetails);

        } else {

            final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

            invalidateScreensDuringResubmit(storedWorkflowStep.getWorkflowStepId(), standardProfileScreenConfig,
                    profileStep.getProfileScreenConfig());
        }

        triggerEvent(storedWorkflowStep, transitionEvent, standardProfileScreenConfig, actionId, false);
    }

    private void handleScreenSuccess(final String completedActionId,
                                     final StoredWorkflowStep storedWorkflowStep,
                                     final UiRequestContext uiRequestContext,
                                     final CurrentProfileScreenNextProfileScreen currentProfileScreenNextProfileScreen,
                                     final CompletionState completionStateOfScreen) {

        final var transitionEvent = completionStateOfScreen == CompletionState.PSEUDO_SUCCESS
                ? TransitionEvent.PSEUDO_SUCCESS
                : TransitionEvent.SUCCESS;

        if (Objects.isNull(currentProfileScreenNextProfileScreen.getNextProfileScreenConfig())) {

            triggerEvent(storedWorkflowStep, transitionEvent,
                    currentProfileScreenNextProfileScreen.getCurrentProfileScreenConfig(), completedActionId, false);

        } else {

            dynamicUiResponseService.sendSuccessResponseToUi(completedActionId, uiRequestContext,
                    currentProfileScreenNextProfileScreen.getCurrentProfileScreenConfig(),
                    currentProfileScreenNextProfileScreen.getNextProfileScreenConfig());
        }
    }

    @SneakyThrows
    private void handleScreenFailed(final String completedActionId,
                                    final StoredAction storedAction,
                                    final StoredWorkflowStep storedWorkflowStep,
                                    final CurrentProfileScreenNextProfileScreen currentProfileScreenNextProfileScreen) {

        final var isRetryAvailable = isRetryAvailable(storedAction,
                currentProfileScreenNextProfileScreen.getCurrentProfileScreenConfig());

        triggerEvent(storedWorkflowStep, TransitionEvent.FAILURE,
                currentProfileScreenNextProfileScreen.getCurrentProfileScreenConfig(), completedActionId,
                isRetryAvailable);
    }

    @Override
    public boolean isWorkflowStepRetryAvailable(final String workflowStepId,
                                                final String profileStepId) {

        final var storedAction = actionService.getActions(Set.of(workflowStepId))
                .stream()
                .max(Comparator.comparing(StoredAction::getCreatedAt))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Fields.workflowStepId, workflowStepId)));

        final var profileStep = profileService.getProfileStep(profileStepId);

        final var standardProfileScreenConfig = profileStep.getProfileScreenConfig()
                .accept(new StandardProfileScreenConfigFromProfileStepVisitor(storedAction.getScreenMappingId()))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                        Map.of(Fields.workflowStepId, workflowStepId, StoredAction.Fields.screenMappingId,
                                storedAction.getScreenMappingId())));

        return isRetryAvailable(storedAction, standardProfileScreenConfig);
    }

    @Override
    public boolean isRetryAvailable(final StoredAction storedAction,
                                    final StandardProfileScreenConfig standardProfileScreenConfig) {

        final var standardStepActionConfig = standardProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND, Map.of()));

        final var actionList = actionService.getActions(storedAction.getWorkflowStepId(),
                standardStepActionConfig.getActionMappingId());

        final var failedActions = actionList.stream()
                .filter(action -> CompletionState.FAILURE == action.getCompletionState())
                .toList();

        final var retryConfig = standardStepActionConfig.getRetryConfig();

        return Objects.nonNull(retryConfig) && retryConfig.accept(retryConfigExecutor, RetryConfigExecutorData.builder()
                .failedActionCount(failedActions.size())
                .workflowStepId(storedAction.getWorkflowStepId())
                .build());

    }

    private void triggerEvent(final StoredWorkflowStep storedWorkflowStep,
                              final TransitionEvent transitionEvent,
                              final StandardProfileScreenConfig currentProfileScreenConfig,
                              final String completedActionId,
                              final boolean isRetryable) {

        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(Fields.workflowStepId, storedWorkflowStep.getWorkflowStepId());
        stateMachineTransitionContext.put(UserDetails.class, Constants.PVCORE_SYSTEM_USER);
        if (Objects.nonNull(currentProfileScreenConfig)) {
            stateMachineTransitionContext.put(StandardProfileScreenConfig.class, currentProfileScreenConfig);
        }
        if (Objects.nonNull(completedActionId)) {
            stateMachineTransitionContext.put(StoredAction.Fields.actionId, completedActionId);
        }
        stateMachineTransitionContext.put(Constants.IS_RETRYABLE, isRetryable);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(storedWorkflowStep.getWorkflowStepId() + Constants.WORKFLOW_STEP_LOCK_POSTFIX)
                .build());

        workflowStepStateMachine.sendEvent(storedWorkflowStep.getCurrentState(), transitionEvent,
                stateMachineTransitionContext);
    }

    private void triggerMoveBackToPseudoSuccessEvent(final StoredWorkflowStep storedWorkflowStep,
                                                     final TransitionEvent transitionEvent,
                                                     final String actionId) {
        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(Fields.workflowStepId, storedWorkflowStep.getWorkflowStepId());
        stateMachineTransitionContext.put(UserDetails.class, Constants.PVCORE_SYSTEM_USER);
        stateMachineTransitionContext.put(StoredAction.Fields.actionId, actionId);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(storedWorkflowStep.getWorkflowStepId() + Constants.WORKFLOW_STEP_LOCK_POSTFIX)
                .build());

        workflowStepStateMachine.sendEvent(storedWorkflowStep.getCurrentState(), transitionEvent,
                stateMachineTransitionContext);
    }

    @Override
    public void triggerEvent(final StoredWorkflowStep storedWorkflowStep,
                             final TransitionEvent transitionEvent) {

        triggerEvent(storedWorkflowStep, transitionEvent, null, null, false);
    }


    @Override
    public void triggerEvent(final StoredWorkflowStep storedWorkflowStep,
                             final TransitionEvent transitionEvent,
                             final String completionActionId) {

        triggerEvent(storedWorkflowStep, transitionEvent, null, completionActionId, false);
    }


    @Override
    public void abortWorkflowSteps(final String workflowId,
                                   final UserDetails userDetails) {

        final var allWorkflowSteps = getWorkflowStepsFromWorkflowId(workflowId);

        final var allWorkflowStepIds = allWorkflowSteps.stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        actionService.abortActions(allWorkflowStepIds, userDetails);

        allWorkflowSteps.stream()
                .filter(ws -> !TransitionState.FILTERED_STATES_FOR_CANCEL.contains(ws.getCurrentState()))
                .forEach(ws -> triggerEvent(ws, TransitionEvent.ABORT));
    }

    @Override
    public void discardWorkflowSteps(final String workflowId,
                                     final UserDetails userDetails) {

        final var allWorkflowSteps = getWorkflowStepsFromWorkflowId(workflowId);

        final var allWorkflowStepIds = allWorkflowSteps.stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        actionService.discardActions(allWorkflowStepIds, userDetails);

        allWorkflowSteps.stream()
                .filter(ws -> !TransitionState.FILTERED_STATES_FOR_CANCEL.contains(ws.getCurrentState()))
                .forEach(ws -> triggerEvent(ws, TransitionEvent.DISCARD));
    }

    @Override
    public UiRequestContext getUiRequestContext(final String workflowStepId) {

        final var uiRequestContextKey = UiRequestContextKey.builder()
                .workflowStepId(workflowStepId)
                .build();

        return uiRequestContextCommand.get(uiRequestContextKey);
    }

    @Override
    public void saveUiRequestContext(final String workflowStepId,
                                     final UiRequestContext uiRequestContext) {

        final var uiRequestContextKey = UiRequestContextKey.builder()
                .workflowStepId(workflowStepId)
                .build();

        uiRequestContextCommand.save(uiRequestContextKey, uiRequestContext);
    }

    @Override
    public UiRequestContext buildUiRequestContextWithDummySectionInputData(final String userId,
                                                                           final String workflowId,
                                                                           final String actionMappingId,
                                                                           final ApiVersion apiVersion,
                                                                           final RequestInfo requestInfo,
                                                                           final String intent,
                                                                           final long componentKitVersion) {

        final var uiRequestContextKey = UiRequestContextKey.builder()
                .workflowStepId(String.format(Constants.UNIQUE_WORKFLOW_STEP_ID, actionMappingId, workflowId))
                .build();

        final var uiRequestContext = BuildUtils.buildShadowV2UiRequestContext(userId, intent, apiVersion, requestInfo,
                componentKitVersion, Constants.DUMMY_SECTION_INPUT_DATA, workflowId);

        uiRequestContextCommand.save(uiRequestContextKey, uiRequestContext);

        return uiRequestContext;
    }

    @Override
    public void invalidateCurrentAndLaterWorkflowSteps(final String workflowId,
                                                       final String currentProfileStepId,
                                                       final UserDetails userDetails) {

        invalidateWorkflowSteps(workflowId, currentProfileStepId,
                (currentStoredWorkflowStep, storedWorkflowStepToTest) -> storedWorkflowStepToTest.getCreatedAt()
                        .isAfter(currentStoredWorkflowStep.getCreatedAt())
                        || storedWorkflowStepToTest.getWorkflowStepId()
                        .equals(currentStoredWorkflowStep.getWorkflowStepId()), userDetails);
    }

    @Override
    public void invalidateWorkflowStepsAfterCurrentStep(final String workflowId,
                                                        final String currentProfileStepId,
                                                        final UserDetails userDetails) {

        invalidateWorkflowSteps(workflowId, currentProfileStepId,
                (currentStoredWorkflowStep, storedWorkflowStepToTest) -> storedWorkflowStepToTest.getCreatedAt()
                        .isAfter(currentStoredWorkflowStep.getCreatedAt()), userDetails);
    }

    @Override
    public void moveToPseudoSuccessAndRetryAction(final String workflowStepId,
                                                  final String actionId) {

        final var storedWorkflowStep = validateAndGetWorkflowStep(workflowStepId);

        triggerMoveBackToPseudoSuccessEvent(storedWorkflowStep, TransitionEvent.MANUAL_RETRY, actionId);
    }

    private void invalidateWorkflowSteps(final String workflowId,
                                         final String currentProfileStepId,
                                         final BiPredicate<StoredWorkflowStep, StoredWorkflowStep> currentStoredWorkflowStepStoredWorkflowStepToTestBiPredicate,
                                         final UserDetails userDetails) {

        final var allWorkflowSteps = getWorkflowStepsFromWorkflowId(workflowId);

        final var currentWorkflowStepOptional = allWorkflowSteps.stream()
                .filter(storedWorkflowStep -> !TransitionState.FILTERED_STATES_FOR_CANCEL.contains(
                        storedWorkflowStep.getCurrentState()))
                .filter(storedWorkflowStep -> currentProfileStepId.equals(storedWorkflowStep.getProfileStepId()))
                .findFirst();

        if (currentWorkflowStepOptional.isEmpty()) {
            return;
        }

        final var currentWorkflowStep = currentWorkflowStepOptional.get();

        final var workflowStepsToBeInvalidated = allWorkflowSteps.stream()
                .filter(storedWorkflowStepToTest -> currentStoredWorkflowStepStoredWorkflowStepToTestBiPredicate.test(
                        currentWorkflowStep, storedWorkflowStepToTest))
                .collect(Collectors.toSet());

        if (workflowStepsToBeInvalidated.isEmpty()) {
            return;
        }

        final var workflowStepIdsToBeInvalidated = workflowStepsToBeInvalidated.stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        actionService.invalidateActionsInWorkflowSteps(workflowStepIdsToBeInvalidated, userDetails);

        workflowStepsToBeInvalidated.stream()
                .filter(storedWorkflowStep -> !TransitionState.FILTERED_STATES_FOR_CANCEL.contains(
                        storedWorkflowStep.getCurrentState()))
                .forEach(storedWorkflowStep -> triggerEvent(storedWorkflowStep, TransitionEvent.INVALIDATE));
    }

    @Override
    public ShadowV2UiRequestContext getUiRequestContextAndSaveNewUiRequestContextWithExistingRequestDetails(final String workflowStepId,
                                                                                                            final SectionInputData sectionInputData,
                                                                                                            final RequestInfo requestInfo,
                                                                                                            final long componentKitVersion,
                                                                                                            final String entityId,
                                                                                                            final String intent,
                                                                                                            final ApiVersion apiVersion,
                                                                                                            final String workflowId) {

        final var existingUiRequestContext = (ShadowV2UiRequestContext) getUiRequestContext(workflowStepId);

        final UiRequestContext uiRequestContext = getUiRequestContext(sectionInputData, requestInfo,
                componentKitVersion, entityId, intent, apiVersion, existingUiRequestContext, workflowId);

        saveUiRequestContext(workflowStepId, uiRequestContext);

        return (ShadowV2UiRequestContext) uiRequestContext;
    }

    @Override
    @MonitoredFunction
    public void purgeWorkflowSteps(final String workflowId,
                                   final UserDetails userDetails) {

        final var allWorkflowSteps = getWorkflowStepsFromWorkflowId(workflowId);

        if (!allWorkflowSteps.isEmpty()) {

            final var allWorkflowStepIds = allWorkflowSteps.stream()
                    .map(StoredWorkflowStep::getWorkflowStepId)
                    .collect(Collectors.toSet());

            // For each action metadata, update the data to constant according to data type
            actionService.updateActionMetaDataForPurgeInWorkflowStepIds(allWorkflowStepIds, userDetails);

            // Invalidate all the actions(Future Scope: Update the status to Purge)
            actionService.invalidateActionsInWorkflowSteps(allWorkflowStepIds, userDetails);

            allWorkflowSteps.stream()
                    .filter(ws -> !TransitionState.FILTERED_STATES_FOR_CANCEL.contains(ws.getCurrentState()))
                    .forEach(ws -> triggerEvent(ws, TransitionEvent.PURGE));
        }
    }

    private UiRequestContext getUiRequestContext(final SectionInputData sectionInputData,
                                                 final RequestInfo requestInfo,
                                                 final long componentKitVersion,
                                                 final String entityId,
                                                 final String intent,
                                                 final ApiVersion apiVersion,
                                                 final ShadowV2UiRequestContext existingUiRequestContext,
                                                 final String workflowId) {

        if (Objects.nonNull(existingUiRequestContext)) {

            return BuildUtils.buildShadowV2UiRequestContext(entityId, existingUiRequestContext.getIntent(), apiVersion,
                    requestInfo, existingUiRequestContext.getComponentKitVersion(), sectionInputData, workflowId);
        } else {

            return BuildUtils.buildShadowV2UiRequestContext(entityId, intent, apiVersion, requestInfo,
                    componentKitVersion, sectionInputData, workflowId);
        }
    }

    private StateMachine<TransitionState, TransitionEvent> triggerEvent(final String workflowStepId,
                                                                        final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                                                        final UserDetails userDetails,
                                                                        final TransitionEvent transitionEvent) {

        final var storedWorkflowStep = validateAndGetWorkflowStep(workflowStepId);

        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(Fields.workflowStepId, workflowStepId);
        stateMachineTransitionContext.put(ShadowV2SectionSubmitRequest.class, shadowV2SectionSubmitRequest);
        stateMachineTransitionContext.put(UserDetails.class, userDetails);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(workflowStepId + Constants.WORKFLOW_STEP_LOCK_POSTFIX)
                .build());

        return workflowStepStateMachine.sendEvent(storedWorkflowStep.getCurrentState(), transitionEvent,
                stateMachineTransitionContext);
    }
}