package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl.AnyStepInSuccessWorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl.HopeRuleEvaluationWorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.SchedulingService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoSkipCallbackHandler.WorkflowAutoSkipCallbackHandlerData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SuppressWarnings("java:S3516")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class WorkflowAutoSkipCallbackHandler implements
        WorkflowAutoSkipConfigVisitor<Boolean, WorkflowAutoSkipCallbackHandlerData> {

    private final WorkflowContextStore workflowContextStore;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final SchedulingService schedulingService;

    private final HopeLangService hopeLangService;

    @Override
    public Boolean visit(final AnyStepInSuccessWorkflowAutoSkipConfig config,
                         final WorkflowAutoSkipCallbackHandlerData data) {

        final var storedWorkflow = data.storedWorkflow();

        final var workflowState = storedWorkflow.getCurrentState();

        // Step 1
        if (workflowState != TransitionState.IN_PROGRESS) {
            return true;
        }

        final var validWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        // Step 2 & 3
        if (checkStepsForLastUpdatedTimeAndScheduleCallback(config, storedWorkflow, validWorkflowSteps)
                || handleAnyStepInSuccessAndRescheduling(config, validWorkflowSteps, storedWorkflow)) {
            return true;
        }

        // Not firing any events for this because transition events are sufficient
        autoSkipWorkflowAndInProgressSteps(storedWorkflow, data.userDetails(), validWorkflowSteps);

        return true;
    }

    @Override
    public Boolean visit(final HopeRuleEvaluationWorkflowAutoSkipConfig config,
                         final WorkflowAutoSkipCallbackHandlerData data) {

        /*
        What we want -> If all required details specified in config are present in the Workflow, move it to a terminal state.

        How we will do it ->
        1. Check if WF is NOT in progress. If so, NFA.
        2. If WF is IN_PROGRESS, ensure all WS are in terminal state (NOT PSEUDO SUCCESS) or IN_PROGRESS. If not, NFA.
        3. Check the timestamp of the last updated WS. If timestamp + delay >= current timestamp, then schedule another callback. NFA.
        4. Evaluate rule on the workflow context. If false, ideally we shouldn't reschedule a callback (since WF is idle), but do this based on config param anyway. NFA.
        6. Finally, transition workflow state to AUTO_SKIPPED
         */

        final var storedWorkflow = data.storedWorkflow();

        final var workflowState = storedWorkflow.getCurrentState();

        // Step 1
        if (workflowState != TransitionState.IN_PROGRESS) {
            return true;
        }

        final var validWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        // Step 2 & 3
        if (checkStepsForLastUpdatedTimeAndScheduleCallback(config, storedWorkflow, validWorkflowSteps)
                || checkStepsForAnyBlacklistedState(config, validWorkflowSteps) || handleRuleEvaluationAndRescheduling(
                config, storedWorkflow)) {
            return true;
        }

        // Not firing any events for this because transition events are sufficient
        autoSkipWorkflowAndInProgressSteps(storedWorkflow, data.userDetails(), validWorkflowSteps);

        return true;
    }

    public void autoSkipWorkflowAndInProgressSteps(final StoredWorkflow storedWorkflow,
                                                   final UserDetails userDetails,
                                                   final List<StoredWorkflowStep> validWorkflowSteps) {

        final var inProgressSteps = validWorkflowSteps.stream()
                .filter(storedWorkflowStep -> TransitionState.IN_PROGRESS.equals(storedWorkflowStep.getCurrentState()))
                .toList();

        workflowService.autoSkipWorkflow(storedWorkflow, userDetails, inProgressSteps);
    }

    private boolean handleAnyStepInSuccessAndRescheduling(final AnyStepInSuccessWorkflowAutoSkipConfig config,
                                                          final List<StoredWorkflowStep> validWorkflowSteps,
                                                          final StoredWorkflow storedWorkflow) {

        final var anyStepInSuccess = validWorkflowSteps.stream()
                .anyMatch(storedWorkflowStep -> TransitionState.SUCCESS.equals(storedWorkflowStep.getCurrentState()));

        final var anyStepInPseudoSuccess = validWorkflowSteps.stream()
                .anyMatch(storedWorkflowStep -> TransitionState.PSEUDO_SUCCESS.equals(
                        storedWorkflowStep.getCurrentState()));

        if (!anyStepInSuccess || (anyStepInPseudoSuccess && config.isRescheduleForStepInPseudoSuccess())) {

            log.debug("Rescheduling Auto Skip callback for WF {} after {}", storedWorkflow.getWorkflowId(),
                    config.getSkipAfter());

            schedulingService.scheduleClockworkCallback(config,
                    Constants.AUTO_SKIP_CALLBACK_PATH_FORMAT.formatted(storedWorkflow.getWorkflowId()),
                    config.getSkipAfter(), storedWorkflow.getWorkflowId(), config.getType()
                            .name());

            return true;
        }

        return false;
    }

    private boolean handleRuleEvaluationAndRescheduling(final HopeRuleEvaluationWorkflowAutoSkipConfig config,
                                                        final StoredWorkflow storedWorkflow) {

        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflow.getWorkflowId());

        final var shouldSkip = hopeLangService.evaluate(config.getHopeRule(),
                MapperUtils.convertToJsonNode(workflowContext));

        if (!shouldSkip && config.isRescheduleIfEvaluationFails()) {
            schedulingService.scheduleClockworkCallback(config,
                    Constants.AUTO_SKIP_CALLBACK_PATH_FORMAT.formatted(storedWorkflow.getWorkflowId()),
                    config.getSkipAfter(), storedWorkflow.getWorkflowId(), config.getType()
                            .name());
        }

        return !shouldSkip;
    }

    private boolean checkStepsForAnyBlacklistedState(final HopeRuleEvaluationWorkflowAutoSkipConfig config,
                                                     final List<StoredWorkflowStep> validWorkflowSteps) {

        // In case we hit any WS whose state is in the blacklist. We should step scheduling callbacks.
        // This is used to handle cases where WS are in PS and we dont want to wait for PS to go to success/failure.
        return validWorkflowSteps.stream()
                .map(StoredWorkflowStep::getCurrentState)
                .anyMatch(transitionState -> config.getWorkflowStepStateBlacklist()
                        .contains(State.valueOf(transitionState.name())));
    }

    private boolean checkStepsForLastUpdatedTimeAndScheduleCallback(final WorkflowAutoSkipConfig config,
                                                                    final StoredWorkflow storedWorkflow,
                                                                    final List<StoredWorkflowStep> validWorkflowSteps) {
        // Keep default of WF last updated at to handle Case 1 explained above
        final var lastUpdatedTimestamp = validWorkflowSteps.stream()
                .map(StoredWorkflowStep::getLastUpdatedAt)
                .max(Comparator.naturalOrder())
                .orElse(storedWorkflow.getLastUpdatedAt());

        // If last updated at + Delay > Current timestamp, then we need to schedule a new callback and wait for that
        if (lastUpdatedTimestamp.plusSeconds(config.getSkipAfter()
                        .toSeconds())
                .isAfter(LocalDateTime.now())) {

            log.debug("Rescheduling Auto Skip callback for WF {} after {}", storedWorkflow.getWorkflowId(),
                    config.getSkipAfter());

            schedulingService.scheduleClockworkCallback(config,
                    Constants.AUTO_SKIP_CALLBACK_PATH_FORMAT.formatted(storedWorkflow.getWorkflowId()),
                    config.getSkipAfter(), storedWorkflow.getWorkflowId(), config.getType()
                            .name());

            return true;
        }

        return false;
    }


    @Builder
    @Jacksonized
    public record WorkflowAutoSkipCallbackHandlerData(UserDetails userDetails, StoredWorkflow storedWorkflow) {

    }
}
