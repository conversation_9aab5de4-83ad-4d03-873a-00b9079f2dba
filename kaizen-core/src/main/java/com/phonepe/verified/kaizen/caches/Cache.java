package com.phonepe.verified.kaizen.caches;

import com.codahale.metrics.MetricRegistry;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import java.util.function.Function;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class Cache<K, V> {

    @Getter
    private final CacheName cacheName;

    private final LoadingCache<K, V> caffeineCache;

    protected Cache(@NonNull final CacheName cacheName,
                    @NonNull final CaffeineCacheConfig caffeineCacheConfig,
                    @NonNull final MetricRegistry metricRegistry) {

        this.cacheName = cacheName;
        this.caffeineCache = Caffeine.newBuilder()
                .recordStats(() -> new MetricsStatsCounter(metricRegistry, "cache." + cacheName.name()))
                .maximumSize(caffeineCacheConfig.getMaxElements(cacheName))
                .expireAfterWrite(caffeineCacheConfig.getExpiry(cacheName)
                        .getQuantity(), caffeineCacheConfig.getExpiry(cacheName)
                        .getUnit())
                .refreshAfterWrite(caffeineCacheConfig.getRefreshInterval(cacheName)
                        .getQuantity(), caffeineCacheConfig.getRefreshInterval(cacheName)
                        .getUnit())
                .build(this::build);
    }

    protected abstract V build(K key);

    public V get(final K key) {
        return caffeineCache.get(key);
    }

    public V get(final K key,
                 Function<? super K, ? extends V> defaultGetter) {
        return caffeineCache.get(key, defaultGetter);
    }

    public void invalidateAll() {
        caffeineCache.invalidateAll();
    }

    public void invalidate(final K key) {
        caffeineCache.invalidate(key);
    }
}
