package com.phonepe.verified.kaizen.registries;

import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredStateMachineTransition;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.springframework.statemachine.config.model.ConfigurationData;
import org.springframework.statemachine.config.model.DefaultStateMachineModel;
import org.springframework.statemachine.config.model.StateData;
import org.springframework.statemachine.config.model.StateMachineModel;
import org.springframework.statemachine.config.model.StateMachineModelFactory;
import org.springframework.statemachine.config.model.StatesData;
import org.springframework.statemachine.config.model.TransitionData;
import org.springframework.statemachine.config.model.TransitionsData;
import org.springframework.statemachine.transition.TransitionKind;

@AllArgsConstructor
public class StateMachineRegistryModelFactory implements StateMachineModelFactory<String, String> {

    private final SpringActionRegistry springActionRegistry;
    private final Set<String> allStates;
    private final String initialState;
    private final Set<String> terminalStates;
    private final List<StoredStateMachineTransition> stateMachineTransitions;

    @Override
    public StateMachineModel<String, String> build() {

        final var transitionData = stateMachineTransitions.stream()
                .map(this::buildTransitionData)
                .toList();

        return new DefaultStateMachineModel<>(new ConfigurationData<>(),
                new StatesData<>(generateStateDataList(allStates, initialState, terminalStates)),
                new TransitionsData<>(transitionData));
    }

    @Override
    public StateMachineModel<String, String> build(final String machineId) {
        return build();
    }

    private List<StateData<String, String>> generateStateDataList(final Set<String> allStates,
                                                                  final String initialState,
                                                                  final Set<String> terminalStates) {

        final var initialStateStream = Stream.of(new StateData<String, String>(initialState, true));

        final var terminalStateStream = terminalStates.stream()
                .map(state -> {
                    final var stateData = new StateData<String, String>(state);
                    stateData.setEnd(true);
                    return stateData;
                });

        final var intermediateStateStream = allStates.stream()
                .filter(s -> !initialState.equals(s))
                .filter(s -> !terminalStates.contains(s))
                .map((Function<String, StateData<String, String>>) StateData::new);

        return Stream.of(initialStateStream, terminalStateStream, intermediateStateStream)
                .flatMap(Function.identity())
                .toList();
    }

    private TransitionData<String, String> buildTransitionData(final StoredStateMachineTransition storedStateMachineTransition) {
        return new TransitionData<>(storedStateMachineTransition.getSource(), storedStateMachineTransition.getTarget(),
                storedStateMachineTransition.getEvent(),
                List.of(springActionRegistry.getAction(storedStateMachineTransition.getActionKey())), null,
                TransitionKind.EXTERNAL);
    }

}
