package com.phonepe.verified.kaizen.clients.internal;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.platform.http.v2.executor.Consumer;
import com.phonepe.platform.http.v2.executor.ExtractedResponse;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.verified.kaizen.queue.messages.RevolverCallbackMessage;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.ErrorUtils;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import com.phonepe.verified.kaizen.utils.RequestInfoUtils;
import io.appform.functionmetrics.MonitoredFunction;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;

@Slf4j
@Singleton
public class RevolverClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    @Inject
    public RevolverClient(final HttpClientRegistry httpClientRegistry) {
        this.httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(ClientIds.API);
    }

    @MonitoredFunction
    public Optional<ExtractedResponse> revolverCallback(final RevolverCallbackMessage message) {

        final var requestId = message.getRequestId();

        final var url = String.format("/revolver/v1/callback/%s", requestId);

        final var responseCodeHeader = HttpClientUtils.generateHeaderPair(Headers.X_RESPONSE_CODE,
                Integer.toString(message.getStatus()));

        return httpExecutorBuilderFactory.<Optional<ExtractedResponse>>httpPostExecutorBuilder()
                .command("revolverCallback")
                .url(url)
                .headers(List.of(responseCodeHeader))
                .httpData(new SerializableHttpData(MediaType.APPLICATION_JSON, message.getData()))
                .exceptionConsumer(ErrorUtils.exceptionConsumer())
                .nonSuccessResponseConsumer(nonSuccessResponseHandler())
                .successResponseConsumer(Optional::of)
                .callerClass(getClass())
                .requestInfo(RequestInfoUtils.getOrBuildEmptyRequestInfoWithEnvField())
                .build()
                .executeTracked();
    }

    public Consumer<ExtractedResponse, Optional<ExtractedResponse>> nonSuccessResponseHandler() {

        return extractedResponse -> {

            final var responseBody = extractedResponse.getBody();
            final var code = extractedResponse.getCode();
            final var responseContent = Objects.nonNull(responseBody)
                                        ? new String(responseBody, StandardCharsets.UTF_8)
                                        : null;

            final var executionContext = extractedResponse.getExecutionContext();
            final var callerClass = executionContext.getCallerClass();
            final var commandName = executionContext.getCommand();

            final var logger = LoggerFactory.getLogger(callerClass);

            logger.error("Error in call for service {}, commandName {}, response {} and code {}",
                    callerClass.getSimpleName(), commandName, responseContent, code);

            return Optional.empty();
        };
    }
}
