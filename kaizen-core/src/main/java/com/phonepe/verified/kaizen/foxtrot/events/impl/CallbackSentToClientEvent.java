package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class CallbackSentToClientEvent extends BaseEvent {

    private final Boolean success;

    private final String callBackUrl;

    private final String errorMessage;

    private final String callBackService;

    @Builder
    public CallbackSentToClientEvent(final String intent,
                                     final String entityId,
                                     final Boolean success,
                                     final String namespace,
                                     final String workflowId,
                                     @NonNull final String groupingKey,
                                     final String errorMessage,
                                     final String organization,
                                     final String workflowType,
                                     final String callBackUrl,
                                     final String workflowStepId,
                                     final EntityType entityType,
                                     final String workflowVersion,
                                     final String callBackService,
                                     final String screenMappingId,
                                     final String actionMappingId,
                                     final long componentKitVersion,
                                     final ProfileType profileType,
                                     final String addOnType,
                                     final String profileStepMappingId) {
        super(EventType.CALLBACK_SENT_TO_CLIENT, intent, entityId, namespace, workflowId, profileType, addOnType,
                groupingKey, organization, workflowType, workflowStepId, entityType, workflowVersion, screenMappingId,
                actionMappingId, componentKitVersion, profileStepMappingId);
        this.success = success;
        this.callBackUrl = callBackUrl;
        this.errorMessage = errorMessage;
        this.callBackService = callBackService;
    }
}
