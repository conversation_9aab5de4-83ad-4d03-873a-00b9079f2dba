package com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext;

import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionContext {

    private String actionId;

    private String workflowStepId;

    private ActionType actionType;

    private String stateMachineVersion;

    private String screenMappingId;

    private String currentState;

    private String currentEvent;

    private boolean completed;

    private CompletionState completionState;

    private ActionFailureErrorCode failureErrorCode;

    private String lastUpdatedBy;

    private UpdaterType updaterType;

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;

    //key actionMetadataContextKey
    @Builder.Default
    private Map<String, StoredActionMetadata> actionMetadataMap = new HashMap<>();

}
