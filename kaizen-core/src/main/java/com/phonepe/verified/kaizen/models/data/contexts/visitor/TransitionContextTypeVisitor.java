package com.phonepe.verified.kaizen.models.data.contexts.visitor;

import com.phonepe.verified.kaizen.models.data.contexts.ConsentTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.DocumentUploadActionTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.EmptyTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.KeyValuePairsActionTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.PersistKeyValuePairsActionTransitionContext;

public interface TransitionContextTypeVisitor<T, J> {

    T visit(ConsentTransitionContext consentTransitionContext,
            J data);

    T visit(DocumentUploadActionTransitionContext documentUploadActionTransitionContext,
            J data);

    T visit(EmptyTransitionContext emptyTransitionContext,
            J data);

    T visit(KeyValuePairsActionTransitionContext keyValuePairsActionTransitionContext,
            J data);

    T visit(PersistKeyValuePairsActionTransitionContext persistKeyValuePairsActionTransitionContext,
            J data);
}
