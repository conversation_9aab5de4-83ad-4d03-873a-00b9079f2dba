package com.phonepe.verified.kaizen.storage.mariadb.entities;

import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.utils.Constants;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Builder
@Audited
@ToString
@FieldNameConstants
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "state_machine_transition")
@AuditTable(value = "state_machine_transition_audit")
public class StoredStateMachineTransition implements Sharded, Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) AUTO_INCREMENT", insertable = false, updatable = false, nullable = false)
    private long id;

    @Column(name = "action_type", columnDefinition = "varchar(128)", nullable = false)
    @Enumerated(EnumType.STRING)
    private ActionType actionType;

    @Column(name = "version", columnDefinition = "varchar(64)", nullable = false)
    private String version;

    @Column(name = "source", columnDefinition = "varchar(128)", nullable = false)
    private String source;

    @Column(name = "target", columnDefinition = "varchar(128)", nullable = false)
    private String target;

    @Column(name = "event", columnDefinition = "varchar(128)", nullable = false)
    private String event;

    @Column(name = "action_key", columnDefinition = "varchar(128)", nullable = false)
    private String actionKey;

    @Column(name = "last_updated_by", columnDefinition = "varchar(128)", nullable = false)
    private String lastUpdatedBy;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3)", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "last_updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3)", nullable = false)
    private LocalDateTime lastUpdatedAt;

    @Override
    public String getShardingKey() {
        return Constants.STATE_MACHINE_SHARD_KEY;
    }

}
