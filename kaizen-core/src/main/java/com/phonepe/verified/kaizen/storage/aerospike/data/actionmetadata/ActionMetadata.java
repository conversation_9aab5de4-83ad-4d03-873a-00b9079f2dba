package com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.ConsentActionMetaData;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadWithMetadataActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.KeyValuePairsActionMetadata;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "actionMetadataType", visible = true)
@JsonSubTypes(value = {@Type(value = DocumentUploadActionMetadata.class, name = Names.DOCUMENT_UPLOAD),
        @Type(value = DocumentUploadWithMetadataActionMetadata.class, name = Names.DOCUMENT_UPLOAD_WITH_METADATA),
        @Type(value = KeyValuePairsActionMetadata.class, name = Names.KEY_VALUE),
        @Type(value = ConsentActionMetaData.class, name = Names.CONSENT)})
public abstract class ActionMetadata {

    private String actionId;

    @NotNull
    private ActionMetadataType actionMetadataType;

    protected ActionMetadata(final ActionMetadataType actionMetadataType) {
        this.actionMetadataType = actionMetadataType;
    }

    public abstract <T, J> T accept(ActionMetadataVisitor<T, J> actionMetadataVisitor,
                                    J data);
}
