package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WorkflowAutoSkipCallbackMessage extends BaseMessage {

    @NotEmpty
    private final String workflowId;

    @NotNull
    private final WorkflowAutoSkipConfig workflowAutoSkipConfig;

    @NotNull
    private final UserDetails userDetails;

    @Builder
    @Jacksonized
    public WorkflowAutoSkipCallbackMessage(final RequestInfo requestInfo,
                                           @NotEmpty final String workflowId,
                                           final WorkflowAutoSkipConfig workflowAutoSkipConfig,
                                           @NotNull final UserDetails userDetails) {

        super(ActorMessageType.WORKFLOW_AUTO_SKIP_CALLBACK, requestInfo);
        this.workflowId = workflowId;
        this.workflowAutoSkipConfig = workflowAutoSkipConfig;
        this.userDetails = userDetails;
    }
}
