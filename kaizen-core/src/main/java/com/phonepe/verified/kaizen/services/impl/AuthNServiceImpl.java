package com.phonepe.verified.kaizen.services.impl;

import com.google.common.hash.Hashing;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.clients.internal.PrimerClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.primer.GenerateTokenV2Request;
import com.phonepe.verified.kaizen.models.primer.PrimerTokenResponseV2;
import com.phonepe.verified.kaizen.models.primer.RefreshTokenV2Request;
import com.phonepe.verified.kaizen.models.primer.TokenExpiry;
import com.phonepe.verified.kaizen.models.primer.TokenTtl;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2LoginRequest;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2LogoutRequest;
import com.phonepe.verified.kaizen.models.requests.authentication.v2.AuthenticationV2RefreshRequest;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.AuthNService;
import com.phonepe.verified.kaizen.storage.aerospike.commands.AccessTokenCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.AccessTokenData;
import com.phonepe.verified.kaizen.storage.aerospike.keys.AccessTokenKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import io.dropwizard.primer.util.CryptUtil;
import io.dropwizard.util.Strings;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.SneakyThrows;
import org.jose4j.jwt.JwtClaims;
import org.jose4j.jwt.consumer.JwtConsumer;
import org.jose4j.jwt.consumer.JwtConsumerBuilder;

@Singleton
public class AuthNServiceImpl implements AuthNService {

    private final JwtConsumer jwtConsumer;

    private final PrimerClient primerClient;

    private final SecretKeySpec secretKeySpec;

    private final GCMParameterSpec ivParameterSpec;

    private final AccessTokenCommand accessTokenCommand;

    private final DataProvider<KaizenConfig> appConfigProvider;

    @Inject
    public AuthNServiceImpl(final PrimerClient primerClient,
                            final AccessTokenCommand accessTokenCommand,
                            final DataProvider<KaizenConfig> appConfigProvider) {

        this.primerClient = primerClient;
        this.accessTokenCommand = accessTokenCommand;
        this.appConfigProvider = appConfigProvider;

        this.jwtConsumer = (new JwtConsumerBuilder()).setSkipSignatureVerification()
                .setSkipAllDefaultValidators()
                .build();

        this.secretKeySpec = new SecretKeySpec(Hashing.murmur3_128()
                .hashString(appConfigProvider.getData()
                        .getPrimerBundleConfig()
                        .getPrivateKey(), StandardCharsets.UTF_8)
                .asBytes(), "AES");

        this.ivParameterSpec = new GCMParameterSpec(16 * 8, Arrays.copyOf(appConfigProvider.getData()
                .getPrimerBundleConfig()
                .getPrivateKey()
                .getBytes(), 8));
    }


    @Override
    public PrimerTokenResponseV2 getPrimerV2Token(final String actxHeader,
                                                  final RequestInfo requestInfo,
                                                  final StoredWorkflow storedWorkflow,
                                                  final AccessTokenData accessTokenData) {

        final var authZConfig = appConfigProvider.getData()
                .getAuthZConfig();

        final var tokenExpiry = TokenExpiry.builder()
                .expiryInSeconds(authZConfig.getExpiry()
                        .toSeconds())
                .build();

        //time for which refresh is allowed
        final var tokenTtl = TokenTtl.builder()
                .ttlInSeconds((Strings.isNullOrEmpty(actxHeader)
                               ? authZConfig.getTtl()
                               : authZConfig.getTtlWeb()).toSeconds())
                .build();

        final var generateTokenRequest = GenerateTokenV2Request.builder()
                .id(accessTokenData.getUserReferenceId())
                .userId(accessTokenData.getUserReferenceId())
                .tokenId(accessTokenData.getUserReferenceId())
                .name(storedWorkflow.getWorkflowId())
                .role(authZConfig.getRoleName())
                .deviceId(requestInfo.getDeviceFingerprint())
                .tokenExpiry(tokenExpiry)
                .tokenTtl(tokenTtl)
                .build();

        final var tokenResponseV2 = primerClient.generateTokenV2(authZConfig.getAppName(),
                accessTokenData.getUserReferenceId(), actxHeader, generateTokenRequest);

        return setRefreshTokenExpiresAt(tokenResponseV2, actxHeader);
    }

    private PrimerTokenResponseV2 setRefreshTokenExpiresAt(final PrimerTokenResponseV2 tokenResponseV2,
                                                           final String actxHeader) {

        final var authZConfig = appConfigProvider.getData()
                .getAuthZConfig();

        final var ttl = Strings.isNullOrEmpty(actxHeader)
                        ? authZConfig.getTtl()
                        : authZConfig.getTtlWeb();

        //adding diff between tokenTtl and expiryTtl in expiresAt
        tokenResponseV2.setRefreshTokenExpiresAt(
                tokenResponseV2.getExpiresAt() + (ttl.toSeconds() - authZConfig.getExpiry()
                        .toSeconds()));

        return tokenResponseV2;
    }

    @Override
    public String createAndGetAccessToken(final String workflowId,
                                          final String userReferenceId) {

        final var accessToken = UUID.randomUUID()
                .toString();

        final var accessTokenKey = AccessTokenKey.builder()
                .accessToken(accessToken)
                .build();

        final var accessTokenData = AccessTokenData.builder()
                .workflowId(workflowId)
                .userReferenceId(userReferenceId)
                .build();

        accessTokenCommand.save(accessTokenKey, accessTokenData);

        return accessToken;
    }

    @Override
    public AccessTokenData validateAndGetAccessToken(final AuthenticationV2LoginRequest authenticationV2LoginRequest) {

        final var accessTokenData = accessTokenCommand.get(AccessTokenKey.builder()
                .accessToken(authenticationV2LoginRequest.getAccessToken())
                .build());

        if (Objects.isNull(accessTokenData)) {
            throw KaizenException.create(KaizenResponseCode.ACCESS_TOKEN_DATA_NOT_AVAILABLE_OR_EXPIRED, Map.of());
        }

        return accessTokenData;
    }

    @Override
    public void deleteAccessToken(final String accessToken) {

        accessTokenCommand.delete(AccessTokenKey.builder()
                .accessToken(accessToken)
                .build());
    }

    @Override
    public void clear(final AuthenticationV2LogoutRequest authenticationV2LogoutRequest) {

        final var authZConfig = appConfigProvider.getData()
                .getAuthZConfig();

        primerClient.clearTokenV2(authZConfig.getAppName(), authenticationV2LogoutRequest.getAuthToken());

    }

    @Override
    public PrimerTokenResponseV2 refresh(final String actxHeader,
                                         final AuthenticationV2RefreshRequest authenticationV2RefreshRequest) {

        final String userReferenceId = getUserReferenceId(authenticationV2RefreshRequest.getAuthToken());

        final var authZConfig = appConfigProvider.getData()
                .getAuthZConfig();

        final var tokenExpiry = TokenExpiry.builder()
                .expiryInSeconds(authZConfig.getExpiry()
                        .toSeconds())
                .build();

        final var refreshTokenRequest = getRefreshTokenV2Request(authenticationV2RefreshRequest.getRefreshToken(),
                tokenExpiry);

        final var tokenResponseV2 = primerClient.refreshTokenV2(authZConfig.getAppName(),
                authenticationV2RefreshRequest.getAuthToken(), userReferenceId, actxHeader, refreshTokenRequest);

        return setRefreshTokenExpiresAt(tokenResponseV2, actxHeader);
    }

    private RefreshTokenV2Request getRefreshTokenV2Request(final String refreshToken,
                                                           final TokenExpiry tokenExpiry) {

        if (!Strings.isNullOrEmpty(refreshToken)) {

            return RefreshTokenV2Request.builder()
                    .refreshToken(refreshToken)
                    .tokenExpiry(tokenExpiry)
                    .build();
        } else {

            throw KaizenException.create(KaizenResponseCode.ACCESS_TOKEN_NOT_AVAILABLE, Map.of());
        }
    }

    private String getUserReferenceId(final String authToken) {

        if (!Strings.isNullOrEmpty(authToken)) {

            final var effectiveAuthToken = getEffectiveToken(authToken);
            return getUserReferenceIdFromAuthToken(effectiveAuthToken);
        }

        throw KaizenException.create(KaizenResponseCode.ACCESS_TOKEN_NOT_AVAILABLE, Map.of());
    }

    @SneakyThrows
    private String getUserReferenceIdFromAuthToken(final String effectiveToken) {

        final var jwtClaims = getJwtClaims(effectiveToken);

        return (String) jwtClaims.getClaimsMap()
                .getOrDefault("user_id", jwtClaims.getSubject());
    }

    @Override
    public String getEffectiveToken(final String token) {

        final var splitToken = token.split("\\.");

        // This will be an encrypted token
        if (splitToken.length < 3) {
            return CryptUtil.tokenDecrypt(token, secretKeySpec, ivParameterSpec);
        }

        return token;
    }

    @Override
    @SneakyThrows
    public JwtClaims getJwtClaims(final String authToken) {

        return jwtConsumer.processToClaims(authToken);
    }
}