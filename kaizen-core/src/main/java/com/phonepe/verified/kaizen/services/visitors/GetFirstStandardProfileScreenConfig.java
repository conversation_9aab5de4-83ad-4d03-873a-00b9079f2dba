package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import java.util.Comparator;
import java.util.Optional;

public class GetFirstStandardProfileScreenConfig implements
        ProfileScreenVisitor<Optional<StandardProfileScreenConfig>> {

    public static final GetFirstStandardProfileScreenConfig INSTANCE = new GetFirstStandardProfileScreenConfig();

    @Override
    public Optional<StandardProfileScreenConfig> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        return Optional.of(standardProfileScreenConfig);
    }

    @Override
    public Optional<StandardProfileScreenConfig> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        return sortedOrderedProfileScreenList.iterator()
                .next()
                .getProfileScreenConfig()
                .accept(GetFirstStandardProfileScreenConfig.INSTANCE);
    }
}
