package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import com.phonepe.verified.kaizen.queue.messages.eventingestion.ActionInitEventIngestionMessage;
import com.phonepe.verified.kaizen.queue.messages.eventingestion.ActionUpdateEventIngestionMessage;
import com.phonepe.verified.kaizen.queue.messages.eventingestion.CallbackReceivedFromDrishtiMessage;
import javax.annotation.Nullable;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EventIngestionMessage extends BaseMessage {

    @NonNull
    private final String actionId;

    @NonNull
    private final EventType eventType;

    @Nullable
    private final ActionInitEventIngestionMessage actionInitEventIngestionMessage;

    @Nullable
    private final ActionUpdateEventIngestionMessage actionUpdateEventIngestionMessage;

    @Nullable
    private final CallbackReceivedFromDrishtiMessage callbackReceivedFromDrishtiMessage;

    @Builder
    @Jacksonized
    public EventIngestionMessage(final RequestInfo requestInfo,
                                 @NonNull final String actionId,
                                 @NonNull final EventType eventType,
                                 @Nullable final ActionInitEventIngestionMessage actionInitEventIngestionMessage,
                                 @Nullable final ActionUpdateEventIngestionMessage actionUpdateEventIngestionMessage,
                                 @Nullable final CallbackReceivedFromDrishtiMessage callbackReceivedFromDrishtiMessage) {

        super(ActorMessageType.EVENT_INGESTION, requestInfo);
        this.actionId = actionId;
        this.eventType = eventType;
        this.actionInitEventIngestionMessage = actionInitEventIngestionMessage;
        this.actionUpdateEventIngestionMessage = actionUpdateEventIngestionMessage;
        this.callbackReceivedFromDrishtiMessage = callbackReceivedFromDrishtiMessage;
    }
}
