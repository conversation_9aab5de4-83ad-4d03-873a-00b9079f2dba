package com.phonepe.verified.kaizen.exceptions;

import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.io.Serial;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

public class KaizenException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -5105588657160671262L;

    @Getter
    private final KaizenResponseCode errorCode;

    @Getter
    private final transient Map<String, Object> context;

    public KaizenException(final KaizenResponseCode errorCode,
                           final Map<String, Object> context) {
        this.errorCode = errorCode;
        this.context = context;
    }

    @Builder
    public KaizenException(final KaizenResponseCode errorCode,
                           final String message,
                           final Throwable e,
                           final Map<String, Object> context) {
        super(message, e);
        this.errorCode = errorCode;
        this.context = context;
    }

    public KaizenException(final KaizenResponseCode errorCode,
                           final String message,
                           final Map<String, Object> context) {
        super(message);
        this.errorCode = errorCode;
        this.context = context;
    }

    public KaizenException(final KaizenResponseCode errorCode,
                           final Throwable e,
                           final Map<String, Object> context) {
        super(e);
        this.errorCode = errorCode;
        this.context = context;
    }

    public static KaizenException create(final KaizenResponseCode errorCode,
                                         final Map<String, Object> context) {
        return new KaizenException(errorCode, errorCode.getMessage(), context);
    }

    public static KaizenException create(final KaizenResponseCode errorCode,
                                         final Throwable t,
                                         final Map<String, Object> context) {
        return new KaizenException(errorCode, errorCode.getMessage(), t, context);
    }

    public static KaizenException propagate(final Throwable e) {

        if (e instanceof final KaizenException kaizenException) {
            return kaizenException;
        }

        return new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, e.getMessage(), e, Map.of());
    }

    public static KaizenException propagate(final Throwable e,
                                            final Map<String, Object> context) {

        if (e instanceof final KaizenException kaizenException) {
            return kaizenException;
        }

        return new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, e.getMessage(), e, context);
    }

    public static KaizenException propagate(final String message,
                                            final Throwable e) {

        if (e instanceof final KaizenException kaizenException) {
            return kaizenException;
        }

        return new KaizenException(KaizenResponseCode.INTERNAL_SERVER_ERROR, message + " Error:" + e.getMessage(), e,
                Map.of());
    }

    public static KaizenException propagate(final KaizenResponseCode errorCode,
                                            final Throwable e) {

        if (e instanceof final KaizenException kaizenException) {
            return kaizenException;
        }

        return new KaizenException(errorCode, " Error:" + e.getMessage(), e, Map.of());
    }

    public static KaizenException propagate(final KaizenResponseCode errorCode,
                                            final Throwable e,
                                            final Map<String, Object> context) {

        if (e instanceof final KaizenException kaizenException) {
            return kaizenException;
        }

        return new KaizenException(errorCode, errorCode.getMessage() + " Error: " + e.getMessage(), e, context);
    }
}
