package com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata;

import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.ConsentActionMetaData;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadWithMetadataActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.KeyValuePairsActionMetadata;

public interface ActionMetadataVisitor<T, J> {

    T visit(DocumentUploadActionMetadata documentUploadActionMetadata,
            J data);

    T visit(KeyValuePairsActionMetadata keyValuePairsActionMetadata,
            J data);

    T visit(ConsentActionMetaData consentActionMetaData,
            J data);

    T visit(DocumentUploadWithMetadataActionMetadata documentUploadWithMetadataActionMetadata,
            J data);
}
