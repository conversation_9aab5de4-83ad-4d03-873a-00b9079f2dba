package com.phonepe.verified.kaizen.configs;

import io.dropwizard.util.Duration;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CacheConfig {

    public static final int DEFAULT_MAX_ELEMENTS = 10_000;

    private Duration expiry;

    private Duration refreshInterval;

    @Min(10)
    @Max(1048576)
    @Builder.Default
    private int maxElements = DEFAULT_MAX_ELEMENTS;
}
