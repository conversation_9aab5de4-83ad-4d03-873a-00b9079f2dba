package com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.ConsentActionMetaData;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadWithMetadataActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.KeyValuePairsActionMetadata;
import java.util.Map;

public abstract class ActionMetadataBaseVisitor<T, J> implements ActionMetadataVisitor<T, J> {

    @Override
    public T visit(final DocumentUploadActionMetadata documentUploadActionMetadata,
                   final J data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visit(final DocumentUploadWithMetadataActionMetadata documentUploadWithMetadataActionMetadata,
                   final J data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visit(final KeyValuePairsActionMetadata keyValuePairsActionMetadata,
                   final J data) {
        return throwUnsupportedOperation();
    }

    @Override
    public T visit(final ConsentActionMetaData consentActionMetaData,
                   final J data) {
        return throwUnsupportedOperation();
    }

    private T throwUnsupportedOperation() {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }
}
