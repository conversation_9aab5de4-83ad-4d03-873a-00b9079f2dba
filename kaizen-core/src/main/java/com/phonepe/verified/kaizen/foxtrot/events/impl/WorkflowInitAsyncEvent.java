package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowInitAsyncEvent extends BaseEvent {

    private String requestId;

    @Builder
    public WorkflowInitAsyncEvent(final String intent,
                                  final String entityId,
                                  final String namespace,
                                  final String workflowId,
                                  @NotNull final String groupingKey,
                                  final String organization,
                                  final String workflowType,
                                  final String workflowStepId,
                                  final EntityType entityType,
                                  final String workflowVersion,
                                  final String screenMappingId,
                                  final String actionMappingId,
                                  final long componentKitVersion,
                                  final String profileStepMappingId,
                                  final ProfileType profileType,
                                  final String addOnType,
                                  final String requestId) {

        super(EventType.WORKFLOW_INIT_ASYNC, intent, entityId, namespace, workflowId, profileType, addOnType,
                groupingKey, organization, workflowType, workflowStepId, entityType, workflowVersion, screenMappingId,
                actionMappingId, componentKitVersion, profileStepMappingId);
        this.requestId = requestId;
    }
}
