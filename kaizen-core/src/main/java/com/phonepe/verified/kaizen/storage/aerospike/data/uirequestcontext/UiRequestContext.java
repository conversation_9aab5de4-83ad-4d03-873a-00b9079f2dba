package com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType.Names;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;

import java.util.Objects;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "templateType", visible = true)
@JsonSubTypes(value = {@Type(value = ShadowV2UiRequestContext.class, name = Names.SHADOW_V2),})
public abstract class UiRequestContext {

    @NotEmpty
    private String workflowId;

    @NotNull
    private ApiVersion apiVersion;

    @NotNull
    private RequestInfo requestInfo;

    @NotNull
    private TemplateType templateType;

    protected UiRequestContext(@NonNull final TemplateType templateType,
                               @NonNull final RequestInfo requestInfo,
                               final ApiVersion apiVersion,
                               final String workflowId) {

        this.apiVersion = Objects.isNull(apiVersion)
                ? ApiVersion.V1
                : apiVersion;
        this.requestInfo = requestInfo;
        this.templateType = templateType;
        this.workflowId = workflowId;
    }

    protected UiRequestContext(final TemplateType templateType) {
        this.templateType = templateType;
    }

    public abstract <T> T accept(UiRequestContextVisitor<T> visitor);

}
