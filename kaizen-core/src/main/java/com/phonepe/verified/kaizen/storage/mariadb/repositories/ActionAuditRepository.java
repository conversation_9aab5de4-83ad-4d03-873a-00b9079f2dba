package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionAudit;
import io.appform.dropwizard.sharding.DBShardingBundleBase;

public class ActionAuditRepository extends CrudRepository<StoredActionAudit> {

    @Inject
    public ActionAuditRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredActionAudit.class), null);
    }
}
