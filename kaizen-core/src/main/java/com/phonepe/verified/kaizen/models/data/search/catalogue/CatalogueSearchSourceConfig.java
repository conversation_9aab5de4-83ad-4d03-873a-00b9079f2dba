package com.phonepe.verified.kaizen.models.data.search.catalogue;

import com.phonepe.verified.kaizen.models.data.search.SearchSourceConfig;
import com.phonepe.verified.kaizen.models.data.search.SearchSourceTypeVisitor;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CatalogueSearchSourceConfig extends SearchSourceConfig {

    @NotEmpty
    private String tenant;

    @NotEmpty
    private String category;

    @NotEmpty
    private String filterOnColumnName;

    @NotEmpty
    private String filterPattern;

    @NotEmpty
    private String sortingPattern;

    @Override
    public <T, U> T accept(final SearchSourceTypeVisitor<T, U> visitor,
                           final U data) {
        return visitor.visit(this, data);
    }
}
