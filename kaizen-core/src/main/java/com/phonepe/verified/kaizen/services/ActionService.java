package com.phonepe.verified.kaizen.services;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.DependencyConfig;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.ttl.TtlConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.workflow.ActionFailureResponse;
import com.phonepe.verified.kaizen.services.visitors.GetAllStandardStepActionConfigsVisitor;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.springframework.statemachine.StateMachine;

public interface ActionService {

    static List<StandardStepActionConfig> getAllStandardStepActionConfig(final List<StandardProfileScreenConfig> standardProfileScreenConfigs) {

        return standardProfileScreenConfigs.stream()
                .flatMap(standardProfileScreenConfig -> standardProfileScreenConfig.getStepActionConfig()
                        .accept(GetAllStandardStepActionConfigsVisitor.INSTANCE, null)
                        .stream())
                .toList();
    }

    static StandardStepActionConfig filterActionMappingId(final List<StandardStepActionConfig> allStandardStepActionConfigs,
                                                          final String actionMappingId) {

        return allStandardStepActionConfigs.stream()
                .filter(ssac -> actionMappingId.equals(ssac.getActionMappingId()))
                .findFirst()
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_MAPPING_ID_NOT_FOUND,
                        Map.of(Fields.actionMappingId, actionMappingId)));
    }

    StoredAction validateAndGetAction(final String actionId);

    List<StoredAction> getActionsFromAllShards(final Set<String> actionIds);

    Optional<StoredAction> getLatestAction(final String workflowStepId,
                                           final String actionMappingId);

    List<StoredAction> getActions(final String workflowStepId,
                                  final String actionMappingId);

    List<StoredAction> getActions(final String workflowStepId,
                                  final List<String> actionMappingIds);

    List<StoredAction> getActions(final Set<String> workflowStepIds);

    List<StoredAction> getActions(final Set<String> workflowStepIds,
                                  final String actionMappingId);

    Map<String, Optional<StoredAction>> getActionMappingIdStoredActionMap(final Set<String> workflowStepIds);

    StateMachine<String, String> createAction(final String workflowStepId,
                                              final ActionType actionType,
                                              final String stateMachineVersion,
                                              final String actionMappingId,
                                              final TransitionContext transitionContext,
                                              final String screenMappingId,
                                              final UserDetails userDetails,
                                              final StepActionContext stepActionContext,
                                              final TtlConfig ttlConfig,
                                              final DependencyConfig dependencyConfig);

    StateMachine<String, String> triggerEvent(final String actionId,
                                              final ActionType actionType,
                                              final String stateMachineVersion,
                                              final String actionEvent,
                                              final TransitionContext transitionContext,
                                              final UserDetails userDetails,
                                              final Map<Object, Object> auxiliaryStateMachineTransitionMap);

    String createAndSkipAction(StoredWorkflowStep storedWorkflowStep,
                               StandardProfileScreenConfig standardProfileScreenConfig,
                               UserDetails userDetails,
                               ActionType actionType,
                               TransitionEvent transitionEvent);

    StepActionContext extractStepActionContext(final String actionId,
                                               final ProfileScreenConfig profileScreenConfig);

    StepActionContext extractStepActionContext(final StoredAction storedAction,
                                               final ProfileScreenConfig profileScreenConfig);

    void abortActions(Set<String> allWorkflowStepIds,
                      UserDetails userDetails);

    void discardActions(Set<String> allWorkflowStepIds,
                        UserDetails userDetails);

    void invalidateActionsInWorkflowSteps(final Set<String> allWorkflowStepIds,
                                          final UserDetails userDetails);

    void invalidateAction(final String actionId,
                          final UserDetails userDetails);

    void identifyNextActionAndTrigger(final StoredAction storedAction,
                                      final StandardProfileScreenConfig standardProfileScreenConfig);

    TransitionContext buildTransitionContext(final JsonNode transitionContextTemplate,
                                             final String transitionContextTemplateAsString,
                                             final UiRequestContext uiRequestContext);

    Optional<ActionFailureResponse> getFailureDetailsFromFailedAction(StoredAction storedAction);

    void triggerActionsViaManualRetry(final String workflowStepId,
                                      final String actionId);

    void updateActionMetaDataForPurgeInWorkflowStepIds(final Set<String> allWorkflowStepIds,
                                                       final UserDetails userDetails);
}
