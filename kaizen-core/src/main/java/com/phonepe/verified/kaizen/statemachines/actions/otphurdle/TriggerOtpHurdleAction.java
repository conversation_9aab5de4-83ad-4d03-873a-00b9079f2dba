package com.phonepe.verified.kaizen.statemachines.actions.otphurdle;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.TriggerOtpHurdleActor;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.UpdateStateBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "triggerOtpHurdleAction")
public class TriggerOtpHurdleAction extends UpdateStateBaseAction {

    private final Provider<TriggerOtpHurdleActor> triggerOtpHurdleActorProvider;

    @Inject
    public TriggerOtpHurdleAction(final ActionService actionService,
                                  final ActionRepository actionRepository,
                                  final Provider<WorkflowContextStore> workflowContextStore,
                                  final Provider<EventIngestionActor> eventIngestionActorProvider,
                                  final Provider<TriggerOtpHurdleActor> triggerOtpHurdleActorProvider) {
        super(actionService, actionRepository, workflowContextStore, eventIngestionActorProvider);
        this.triggerOtpHurdleActorProvider = triggerOtpHurdleActorProvider;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        triggerOtpHurdleActorProvider.get()
                .publish(StepActionMessage.builder()
                        .actionId(storedAction.getActionId())
                        .build());
    }
}
