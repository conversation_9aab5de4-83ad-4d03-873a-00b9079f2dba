package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.PurgeWorkflowMessage;
import com.phonepe.verified.kaizen.services.WorkflowService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class PurgeWorkflowActor extends BaseActor<PurgeWorkflowMessage> {

    private final WorkflowService workflowService;

    private final DataProvider<KaizenConfig> appConfigDataProvider;

    @Inject
    protected PurgeWorkflowActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                 final ConnectionRegistry connectionRegistry,
                                 final ObjectMapper mapper,
                                 final RetryStrategyFactory retryStrategyFactory,
                                 final ExceptionHandlingFactory exceptionHandlingFactory,
                                 final WorkflowService workflowService,
                                 final DataProvider<KaizenConfig> appConfigDataProvider) {

        super(ActorType.PURGE_WORKFLOW, actorConfigMap.get(ActorType.PURGE_WORKFLOW), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, PurgeWorkflowMessage.class);
        this.workflowService = workflowService;
        this.appConfigDataProvider = appConfigDataProvider;
    }

    @Override
    protected boolean handleMessage(final PurgeWorkflowMessage purgeWorkflowMessage) {

        // Read rosey and perform purging only if flag is not set in rosey
        if (!appConfigDataProvider.getData()
                .isStopPurgeWorkflowFlag()) {

            log.info("Purging workflow with workflow id : {} triggered by : {}",
                    purgeWorkflowMessage.getPurgeWorkflowRequest()
                            .getWorkflowId(), purgeWorkflowMessage.getUserDetails()
                            .getUserId());

            workflowService.purge(purgeWorkflowMessage.getPurgeWorkflowRequest(),
                    purgeWorkflowMessage.getUserDetails());
        }

        return true;
    }
}
