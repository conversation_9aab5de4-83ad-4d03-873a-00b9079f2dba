package com.phonepe.verified.kaizen.services.visitors;


import com.phonepe.verified.kaizen.models.configs.session.SessionManagementConfig;
import com.phonepe.verified.kaizen.models.configs.session.impl.ClientManagedSessionConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredClientManagedSession;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredSessionManagementVisitor;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SessionManagementDbToConfigVisitor implements StoredSessionManagementVisitor<SessionManagementConfig> {

    public static final SessionManagementDbToConfigVisitor INSTANCE = new SessionManagementDbToConfigVisitor();

    @Override
    public SessionManagementConfig visit(final StoredClientManagedSession clientManagedSession) {
        return ClientManagedSessionConfig.builder()
                .disabled(clientManagedSession.isDisabled())
                .profileId(clientManagedSession.getProfileId())
                .validateToken(clientManagedSession.isValidateToken())
                .validateTimer(clientManagedSession.isValidateTimer())
                .principalBuilder(clientManagedSession.getPrincipalBuilder())
                .sourceType(clientManagedSession.getSourceType())
                .build();
    }
}
