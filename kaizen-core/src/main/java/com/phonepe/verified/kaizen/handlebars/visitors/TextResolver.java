package com.phonepe.verified.kaizen.handlebars.visitors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.textsource.TextSourceTypeVisitor;
import com.phonepe.verified.kaizen.models.configs.textsource.impl.ActionHandleBarsTextSourceConfig;
import com.phonepe.verified.kaizen.models.configs.textsource.impl.ContextHandleBarsTextSourceConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.utils.WorkflowContextStoreUtils;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TextResolver implements TextSourceTypeVisitor<String, StoredWorkflow> {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final HandleBarsService handleBarsService;

    private final WorkflowStepService workflowStepService;

    private final WorkflowContextStore workflowContextStore;

    private final ActionMetadataService actionMetadataService;

    @Override
    public String visit(final ActionHandleBarsTextSourceConfig actionHandleBarsTextSourceConfig,
                        final StoredWorkflow storedWorkflow) {

        final var profileStep = profileService.getProfileStep(storedWorkflow.getProfileId(),
                actionHandleBarsTextSourceConfig.getProfileStepMappingId());

        final var storedWorkflowStep = workflowStepService.getValidWorkflowStep(storedWorkflow.getWorkflowId(),
                        profileStep.getProfileStepId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.WORKFLOW_STEP_NOT_FOUND,
                        Map.of(StoredWorkflowStep.Fields.workflowId, storedWorkflow.getWorkflowId(),
                                Fields.profileStepId, profileStep.getProfileStepId())));

        final var storedActionOptional = actionService.getLatestAction(storedWorkflowStep.getWorkflowStepId(),
                actionHandleBarsTextSourceConfig.getActionMappingId());

        if (actionHandleBarsTextSourceConfig.isReturnNullWhenActionNotFound() && storedActionOptional.isEmpty()) {
            return null;
        }

        final var storedAction = storedActionOptional.orElseThrow(
                () -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Fields.workflowStepId, storedWorkflowStep.getWorkflowStepId(),
                                StoredAction.Fields.actionMappingId,
                                actionHandleBarsTextSourceConfig.getActionMappingId())));

        if (!CompletionState.SUCCESS.equals(storedAction.getCompletionState())) {
            return null;
        }

        final var storedActionMetadataList = actionMetadataService.getActionMetadataList(storedAction.getActionId());
        final var storedActionMetadataMap = storedActionMetadataList.stream()
                .collect(Collectors.toMap(StoredActionMetadata::getActionMetadataContextKey, Function.identity(),
                        (a, b) -> b));

        final var actionContext = WorkflowContextStoreUtils.buildActionContextFromStoredAction(storedAction,
                storedActionMetadataMap);

        // Take action returned in above step and create ActionContext object and run handlebars on it
        return handleBarsService.transform(actionHandleBarsTextSourceConfig.getHandlebarsTranslator(), actionContext);
    }

    @Override
    public String visit(final ContextHandleBarsTextSourceConfig contextHandleBarsTextSourceConfig,
                        final StoredWorkflow storedWorkflow) {
        final WorkflowContext workflowContext = workflowContextStore.getWorkflowContext(storedWorkflow.getWorkflowId());
        return handleBarsService.transform(contextHandleBarsTextSourceConfig.getHandlebarsTranslator(),
                workflowContext);
    }
}
