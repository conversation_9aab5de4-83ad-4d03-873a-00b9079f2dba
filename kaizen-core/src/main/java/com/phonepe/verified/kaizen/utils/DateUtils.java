package com.phonepe.verified.kaizen.utils;

import io.dropwizard.util.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import lombok.experimental.UtilityClass;

@UtilityClass
public class DateUtils {

    public Optional<LocalDate> convertToLocalDate(final String datePattern,
                                                  final String dateRaw) {

        try {
            return Optional.ofNullable(LocalDate.parse(dateRaw, DateTimeFormatter.ofPattern(datePattern)));
        } catch (final Exception ex) {
            return Optional.empty();
        }
    }

    public Optional<LocalDateTime> convertToLocalDateTime(final String datePattern,
                                                          final String dateRaw) {

        try {
            return Optional.of(LocalDateTime.parse(dateRaw, DateTimeFormatter.ofPattern(datePattern)));
        } catch (final Exception ex) {
            return Optional.empty();
        }
    }

    public Date toJavaUtilDate(final LocalDateTime localDateTime) {

        if (Objects.isNull(localDateTime)) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault())
                .toInstant());
    }

    public Date toJavaUtilDate(final LocalDate localDate) {

        if (Objects.isNull(localDate)) {
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault())
                .toInstant());
    }

    public LocalDateTime convertToLocalDateTime(final Date dateToConvert) {

        if (Objects.isNull(dateToConvert)) {
            return null;
        }
        return dateToConvert.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    public LocalDate convertToLocalDate(final Date dateToConvert) {

        if (Objects.isNull(dateToConvert)) {
            return null;
        }
        return dateToConvert.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

    public LocalDateTime addDuration(final LocalDateTime localDateTime,
                                     final Duration duration) {

        return localDateTime.plusSeconds(duration.toSeconds());
    }


    public Optional<String> convertToString(final LocalDateTime localDateTime,
                                            final String pattern) {

        final var dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return Optional.of(localDateTime.format(dateTimeFormatter));
    }

    public String convertToHumanReadableTime(final long timeInMilliSec) {

        final var inSeconds = (timeInMilliSec / 1000) % 60;

        final var inMinutes = (timeInMilliSec / (1000 * 60)) % 60;

        final var inHours = (timeInMilliSec / (1000 * 60 * 60)) % 24;

        final var inDays = (timeInMilliSec / (1000 * 60 * 60 * 24)) % 365;

        return String.format("%s days, %s hours, %s minutes, %s seconds", inDays, inHours, inMinutes, inSeconds);
    }
}
