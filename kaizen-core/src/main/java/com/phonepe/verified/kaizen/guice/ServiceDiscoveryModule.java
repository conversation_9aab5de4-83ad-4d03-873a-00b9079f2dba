package com.phonepe.verified.kaizen.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import io.dropwizard.Configuration;
import io.dropwizard.setup.Environment;
import lombok.AllArgsConstructor;
import org.apache.curator.framework.CuratorFramework;

@AllArgsConstructor
public class ServiceDiscoveryModule<T extends Configuration & KaizenConfig> extends AbstractModule {

    private final HttpDiscoveryBundle<T> httpDiscoveryBundle;

    @Provides
    public CuratorFramework curatorFramework() {
        return httpDiscoveryBundle.getEndpointProviderFactory()
                .getCuratorFramework();
    }

    @Provides
    @Singleton
    public ServiceEndpointProviderFactory serviceEndpointProviderFactory(final KaizenConfig config,
                                                                         final Environment environment) {
        return httpDiscoveryBundle.getEndpointProviderFactory();
    }
}
