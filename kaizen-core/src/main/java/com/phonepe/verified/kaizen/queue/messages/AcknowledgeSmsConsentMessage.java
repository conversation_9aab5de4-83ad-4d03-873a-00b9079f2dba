package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AcknowledgeSmsConsentMessage extends BaseMessage {

    @NonNull
    private final String actionId;

    @NonNull
    private final String intent;

    @NonNull
    private final String workflowId;

    @NonNull
    private final ApiVersion apiVersion;

    private final long componentKitVersion;

    @Builder
    @Jacksonized
    public AcknowledgeSmsConsentMessage(final RequestInfo requestInfo,
                                        final @NonNull String actionId,
                                        final @NonNull String intent,
                                        final @NonNull String workflowId,
                                        final @NonNull ApiVersion apiVersion,
                                        final long componentKitVersion) {

        super(ActorMessageType.ACKNOWLEDGE_SMS_CONSENT, requestInfo);
        this.actionId = actionId;
        this.intent = intent;
        this.workflowId = workflowId;
        this.apiVersion = apiVersion;
        this.componentKitVersion = componentKitVersion;
    }
}
