package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.keys.StateMachineRegistryKey;
import com.phonepe.verified.kaizen.models.requests.statemachines.AddStateMachineRegistryRequest;
import com.phonepe.verified.kaizen.models.requests.statemachines.StateMachineTransition;
import com.phonepe.verified.kaizen.models.responses.statemachines.StateMachineTallyResponse;
import java.util.List;
import lombok.Builder;

public interface StateMachineService {

    boolean addRegistry(AddStateMachineRegistryRequest addStateMachineRegistryRequest,
                        String addedBy);

    List<StateMachineTransitions> getAllActionTransitions();

    String getStateMachineSvgGraph(ActionType actionType,
                                   String stateMachineVersion);

    StateMachineTallyResponse tallyStateMachines();

    boolean checkIfExists(ActionType actionType,
                          String stateMachineVersion);

    @Builder
    record StateMachineData(StateMachineRegistryKey key, StateMachineTransition stateMachineTransitions) {

    }

    @Builder
    record StateMachineTransitions(ActionType actionType, String version,
                                   List<StateMachineTransition> stateMachineTransitions) {

    }
}
