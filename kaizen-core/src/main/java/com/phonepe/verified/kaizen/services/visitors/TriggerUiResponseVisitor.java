package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonepe.shadow.models.response.Action;
import com.phonepe.shadow.models.response.SectionSubmissionResponse;
import com.phonepe.shadow.services.TemplateServiceV2;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.ApiVersion.ApiVersionVisitor;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.template.impl.ShadowV2UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.contexts.AuxiliaryWorkflowContext;
import com.phonepe.verified.kaizen.models.responses.ApiResponse;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.RevolverCallbackActor;
import com.phonepe.verified.kaizen.queue.messages.RevolverCallbackMessage;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.storage.aerospike.commands.RespondedToRevolverAgainstRequestIdCommand;
import com.phonepe.verified.kaizen.storage.aerospike.commands.RespondedToRevolverAgainstRequestIdLockCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.RequestKey;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

@Slf4j
@RequiredArgsConstructor
public class TriggerUiResponseVisitor implements UiResponseConfigVisitor<Void> {

    private final String nextScreenMappingId;

    private final String currentScreenMappingId;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final UiRequestContext uiRequestContext;

    private final HandleBarsService handleBarsService;

    private final TemplateServiceV2 templateServiceV2;

    private final boolean retryNotAllowedOrRetryExhausted;

    private final ExpressionEvaluator expressionEvaluator;

    private final WorkflowContextStore workflowContextStore;

    private final RevolverCallbackActor revolverCallbackActor;

    // auxiliaryWorkflowContext will be used to store any data to the workflowContext which will be used by HandleBars
    private final AuxiliaryWorkflowContext auxiliaryWorkflowContext;

    private final String workflowId;

    private final RespondedToRevolverAgainstRequestIdCommand respondedToRevolverAgainstRequestIdCommand;

    private final RespondedToRevolverAgainstRequestIdLockCommand respondedToRevolverAgainstRequestIdLockCommand;

    @Override
    @SneakyThrows
    public Void visit(final ShadowV2UiResponseConfig shadowV2TemplateConfig) {

        final var shadowV2UiRequestContext = (ShadowV2UiRequestContext) uiRequestContext;

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var templateWorkflowType = BuildUtils.getTemplateWorkflowType(profile);

        final var template = templateServiceV2.getTemplate(templateWorkflowType, shadowV2UiRequestContext.getIntent(),
                shadowV2UiRequestContext.getComponentKitVersion());

        final var workflowContext = workflowContextStore.getWorkflowContext(workflowId);

        final var workflowContextJsonNode = (ObjectNode) MapperUtils.convertToJsonNode(workflowContext);

        workflowContextJsonNode.put("intent", shadowV2UiRequestContext.getIntent());
        workflowContextJsonNode.put("componentKitVersion", shadowV2UiRequestContext.getComponentKitVersion());

        if (Objects.nonNull(auxiliaryWorkflowContext)) {
            workflowContextJsonNode.set(auxiliaryWorkflowContext.getKey(),
                    MapperUtils.convertToJsonNode(auxiliaryWorkflowContext.getValueMap()));
        }

        final var action = shadowV2TemplateConfig.getShadowV2ResponseConfig()
                .accept(new BuildShadowV2ActionVisitor(template, workflowContextJsonNode, nextScreenMappingId,
                        currentScreenMappingId, hopeLangService, handleBarsService, retryNotAllowedOrRetryExhausted,
                        expressionEvaluator), true);

        final var sectionSubmissionResponse = getSectionSubmissionResponse(action);

        final var requestKey = RequestKey.builder()
                .requestId(shadowV2UiRequestContext.getRequestInfo()
                        .getRequestId())
                .build();

        try {
            //take lock
            respondedToRevolverAgainstRequestIdLockCommand.strictSaveWithRetries(requestKey, Boolean.TRUE);
        } catch (final Exception e) {

            log.error(KaizenResponseCode.UNABLE_TO_ACQUIRE_RESPONDED_TO_REVOLVER_AGAINST_REQUEST_ID_LOCK.name(), e,
                    Map.of("requestId", shadowV2UiRequestContext.getRequestInfo()
                            .getRequestId()));
            return null;
        }

        try {
            if (BooleanUtils.isNotTrue(respondedToRevolverAgainstRequestIdCommand.get(requestKey))) {

                log.info("Pushing revolver response in RevolverCallbackActor for requestId: {}",
                        shadowV2UiRequestContext.getRequestInfo()
                                .getRequestId());

                revolverCallbackActor.publish(RevolverCallbackMessage.builder()
                        .requestId(shadowV2UiRequestContext.getRequestInfo()
                                .getRequestId())
                        .status(200)
                        .data(ApiResponse.<SectionSubmissionResponse>builder()
                                .success(true)
                                .data(sectionSubmissionResponse)
                                .build())
                        .build());

                respondedToRevolverAgainstRequestIdCommand.save(requestKey, Boolean.TRUE);
            }
        } finally {
            //release lock
            respondedToRevolverAgainstRequestIdLockCommand.delete(requestKey);
        }

        return null;
    }

    private SectionSubmissionResponse getSectionSubmissionResponse(final Action action) {

        return uiRequestContext.getApiVersion()
                .accept(new ApiVersionVisitor<>() {
                    @Override
                    public SectionSubmissionResponse visitV1() {
                        return SectionSubmissionResponse.builder()
                                .workflowId(workflowId)
                                .action(action)
                                .build();

                    }

                    @Override
                    public SectionSubmissionResponse visitV2() {
                        return SectionSubmissionResponse.builder()
                                .action(action)
                                .build();
                    }
                });
    }
}
