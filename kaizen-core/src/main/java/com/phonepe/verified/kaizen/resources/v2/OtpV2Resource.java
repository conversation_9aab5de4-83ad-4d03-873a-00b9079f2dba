package com.phonepe.verified.kaizen.resources.v2;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.authz.annotations.WorkflowId;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.OtpHurdleV2ActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.common.MailboxApiName;
import com.phonepe.verified.kaizen.models.requests.OtpVerificationRequest;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.VerifyOtpHurdleActor;
import com.phonepe.verified.kaizen.queue.actors.VerifyOtpHurdleV2Actor;
import com.phonepe.verified.kaizen.queue.messages.VerifyOtpHurdleMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.OtpService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.OtpProviderConfigToResendOtpVisitor;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v2/otp")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "OTP V2", description = "OTP related V2 APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class OtpV2Resource {

    private final OtpService otpService;

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final VerifyOtpHurdleActor verifyOtpHurdleActor;

    private final VerifyOtpHurdleV2Actor verifyOtpHurdleV2Actor;

    private final SessionManagementService sessionManagementService;

    private final OtpProviderConfigToResendOtpVisitor otpProviderConfigToResendOtpVisitor;

    @POST
    @AuthZ
    @SneakyThrows
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Verify OTP for actionId")
    @Path("/verify/{actionId}/{intent}/{componentKitVersion}")
    public Response verifyOtp(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                              @Valid @NotNull final OtpVerificationRequest otpVerificationRequest,
                              @NotEmpty @PathParam("actionId") final String actionId,
                              @NotEmpty @PathParam("intent") final String intent,
                              @NotNull @PathParam("componentKitVersion") final Long componentKitVersion,
                              @Parameter(hidden = true) @WorkflowId final String workflowId,
                              @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                              @Parameter(hidden = true) @HeaderParam(Headers.X_SESSION_TOKEN) final String sessionToken) {

        final var storedAction = actionService.validateAndGetAction(actionId);
        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        if (!storedWorkflow.getWorkflowId()
                .equals(workflowId)) {
            throw KaizenException.create(KaizenResponseCode.AUTH_ERROR, Map.of());
        }

        sessionManagementService.validateSessionAndThrowException(storedWorkflow, requestInfo, userDetails,
                sessionToken);

        if (ActionType.OTP_HURDLE_V2 == storedAction.getActionType()) {

            verifyOtpHurdleV2Actor.publish(VerifyOtpHurdleMessage.builder()
                    .otpVerificationRequest(otpVerificationRequest)
                    .intent(intent)
                    .actionId(actionId)
                    .requestInfo(requestInfo)
                    .componentKitVersion(componentKitVersion)
                    .build());
        } else {

            verifyOtpHurdleActor.publish(VerifyOtpHurdleMessage.builder()
                    .otpVerificationRequest(otpVerificationRequest)
                    .intent(intent)
                    .actionId(actionId)
                    .requestInfo(requestInfo)
                    .componentKitVersion(componentKitVersion)
                    .build());
        }

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.VERIFY_OTP)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.VERIFY_OTP)
                        .toMilliseconds())
                .build();
    }

    @POST
    @AuthZ
    @SneakyThrows
    @Path("/resend/{actionId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Resend OTP for actionId")
    public void resendOtp(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                          @NotEmpty @PathParam("actionId") final String actionId,
                          @Parameter(hidden = true) @WorkflowId final String workflowId) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        if (!storedWorkflowStep.getWorkflowId()
                .equals(workflowId)) {
            throw KaizenException.create(KaizenResponseCode.AUTH_ERROR, Map.of());
        }

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var otpActionContext = actionService.extractStepActionContext(storedAction.getActionId(),
                profileStep.getProfileScreenConfig());

        if (otpActionContext instanceof final OtpHurdleV2ActionContext otpHurdleV2ActionContext) {

            otpHurdleV2ActionContext.getOtpProviderConfig()
                    .accept(otpProviderConfigToResendOtpVisitor, storedAction);
        } else {

            otpService.triggerResendOtp(storedAction);
        }
    }
}
