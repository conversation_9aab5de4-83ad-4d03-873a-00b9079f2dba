package com.phonepe.verified.kaizen.configs;

import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.shadow.config.BaseShadowConfiguration;
import com.phonepe.verified.kaizen.models.data.common.AlertsConfig;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.utils.OlympusTenancyConfig;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.config.RMQConfig;
import io.dropwizard.primer.model.PrimerBundleConfiguration;
import io.dropwizard.util.Duration;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;

public interface KaizenConfig extends BaseShadowConfiguration {

    OlympusTenancyConfig getOlympusTenancyConfig();

    FoxtrotTenancyConfiguration getFoxtrotTenancyConfiguration();

    String getDocstoreNamespace();

    PrimerBundleConfiguration getPrimerBundleConfig();

    EventIngestorClientConfig getEventIngestorClientConfig();

    boolean isEnableEventIngestion();

    RMQConfig getRmqConfig();

    Map<ActorType, ActorConfig> getActorsConfig();

    RangerHubConfiguration getRangerHubConfiguration();

    String getBaseUrl();

    AerospikeConfig getAerospikeConfig();

    MailboxConfig getMailboxConfig();

    CaffeineCacheConfig getCaffeineCacheConfig();

    Map<Integer, String> getAesCipherKeyConfig();

    @Nullable
    AlertsConfig getAlertsConfig();

    AuthZConfig getAuthZConfig();

    Duration getSentinelWorkFlowExpiryDuration();

    RmqOpsConfig getRmqOpsConfig();

    Duration getAsyncWorkflowAbortDuration();

    List<AtlasPincodeSourceConfig> getAtlasPincodeSourcesList();

    EntityDetailsConfig getEntityDetailsConfig();

    KillswitchConfig getKillswitchConfig();

    long getProfileMakerCheckerTTLInSec();

    boolean isStopPurgeWorkflowFlag();
}
