package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.caches.impl.ClientCallbackCache;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.callback.ClientCallbackConfig;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.registries.CacheRegistry;
import com.phonepe.verified.kaizen.services.ClientCallbackService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredClientCallbackConfig.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ClientCallbackConfigRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ClientCallbackServiceImpl implements ClientCallbackService {

    private final CacheRegistry cacheRegistry;

    private final ProfileService profileService;

    private final ClientCallbackCache clientCallbackCache;

    private final ClientCallbackConfigRepository clientCallbackConfigRepository;

    @Override
    public ClientCallbackConfig createClientCallbackConfig(final ClientCallbackConfig clientCallbackConfig,
                                                           final String lastUpdatedBy) {

        // Validation for profile exists
        profileService.get(clientCallbackConfig.getProfileId(), false);

        final var storedClientCallback = BuildUtils.toStoredClientCallback(clientCallbackConfig, lastUpdatedBy);

        final var outputStoredClientCallback = clientCallbackConfigRepository.save(storedClientCallback)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.DB_ERROR, Map.of()));

        return BuildUtils.toClientCallbackConfig(outputStoredClientCallback);
    }

    @Override
    public ClientCallbackConfig updateClientCallbackConfig(final ClientCallbackConfig clientCallbackConfig,
                                                           final String lastUpdatedBy) {

        final var alreadyStoredClientCallback = clientCallbackConfigRepository.select(
                        clientCallbackConfig.getProfileId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.CALLBACK_CLIENT_CONFIG_NOT_FOUND,
                        Map.of(Constants.MESSAGE, "Unable to find a client callback config for the profileId",
                                Fields.profileId, clientCallbackConfig.getProfileId())));

        final var storedClientCallback = BuildUtils.toAlreadyCreatedStoredClientCallback(clientCallbackConfig,
                lastUpdatedBy, alreadyStoredClientCallback.getId());

        final var outputStoredClientCallback = clientCallbackConfigRepository.save(storedClientCallback)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.DB_ERROR, Map.of()));

        cacheRegistry.invalidateCache(CacheName.CLIENT_CALLBACK_CACHE);

        return BuildUtils.toClientCallbackConfig(outputStoredClientCallback);
    }

    public Optional<ClientCallbackConfig> getClientCallbackConfigFromDB(final String profileId) {

        return clientCallbackConfigRepository.select(profileId)
                .map(BuildUtils::toClientCallbackConfig);
    }

    @Override
    public ClientCallbackConfig getClientCallbackConfig(final String profileId) {

        return clientCallbackCache.get(profileId)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.CALLBACK_CLIENT_CONFIG_NOT_FOUND,
                        Map.of(Constants.MESSAGE, "Unable to find a client callback config for the profileId",
                                Fields.profileId, profileId)));
    }
}
