package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.DocumentMessage;
import com.phonepe.verified.kaizen.services.DocumentService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class DeleteDocStoreFileActor extends BaseActor<DocumentMessage> {

    private final DocumentService documentService;

    @Inject
    protected DeleteDocStoreFileActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                      final ConnectionRegistry connectionRegistry,
                                      final ObjectMapper mapper,
                                      final RetryStrategyFactory retryStrategyFactory,
                                      final ExceptionHandlingFactory exceptionHandlingFactory,
                                      final DocumentService documentService) {
        super(ActorType.DELETE_DOCSTORE_FILE, actorConfigMap.get(ActorType.DELETE_DOCSTORE_FILE), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, DocumentMessage.class);
        this.documentService = documentService;
    }

    @Override
    protected boolean handleMessage(final DocumentMessage documentMessage) {

        documentService.delete(documentMessage.getDocumentId(), documentMessage.getUserId());
        return true;
    }
}
