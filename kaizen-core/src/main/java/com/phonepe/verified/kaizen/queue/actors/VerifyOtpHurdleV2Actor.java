package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.OtpHurdleV2ActionContext;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.VerifyOtpHurdleMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.OtpProviderConfigToVerifyOtpHurdleVisitor;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class VerifyOtpHurdleV2Actor extends BaseActor<VerifyOtpHurdleMessage> {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final OtpProviderConfigToVerifyOtpHurdleVisitor otpProviderConfigToVerifyOtpHurdleVisitor;

    @Inject
    protected VerifyOtpHurdleV2Actor(final Map<ActorType, ActorConfig> actorConfigMap,
                                     final ConnectionRegistry connectionRegistry,
                                     final ObjectMapper mapper,
                                     final RetryStrategyFactory retryStrategyFactory,
                                     final ExceptionHandlingFactory exceptionHandlingFactory,
                                     final ActionService actionService,
                                     final ProfileService profileService,
                                     final WorkflowService workflowService,
                                     final WorkflowStepService workflowStepService,
                                     final OtpProviderConfigToVerifyOtpHurdleVisitor otpProviderConfigToVerifyOtpHurdleVisitor) {
        super(ActorType.VERIFY_OTP_HURDLE_V2, actorConfigMap.get(ActorType.VERIFY_OTP_HURDLE_V2), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, VerifyOtpHurdleMessage.class);
        this.actionService = actionService;
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
        this.otpProviderConfigToVerifyOtpHurdleVisitor = otpProviderConfigToVerifyOtpHurdleVisitor;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final VerifyOtpHurdleMessage verifyOtpHurdleMessage) {

        final var storedAction = actionService.validateAndGetAction(verifyOtpHurdleMessage.getActionId());

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var otpActionContext = (OtpHurdleV2ActionContext) actionService.extractStepActionContext(
                storedAction.getActionId(), profileStep.getProfileScreenConfig());

        otpActionContext.getOtpProviderConfig()
                .accept(otpProviderConfigToVerifyOtpHurdleVisitor, verifyOtpHurdleMessage);

        return true;
    }
}
