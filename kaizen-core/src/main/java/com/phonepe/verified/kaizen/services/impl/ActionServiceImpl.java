package com.phonepe.verified.kaizen.services.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.inject.Provider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.DependencyConfig;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.ttl.TtlConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.data.contexts.EmptyTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.workflow.ActionFailureResponse;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.registries.StateMachineRegistry;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.ActionMetaRejectionReasonVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigFromProfileScreenConfig;
import com.phonepe.verified.kaizen.services.visitors.IdentifyNextActionToTriggerActionConfigVisitor;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.TransitionLockKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.IdUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import io.appform.functionmetrics.MonitoredFunction;
import io.dropwizard.util.Strings;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachine;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class ActionServiceImpl implements ActionService {

    public static final Set<CompletionState> FILTERED_ACTION_STATES_FOR_CANCEL = Set.of(CompletionState.SKIPPED,
            CompletionState.INVALIDATED, CompletionState.ABORTED, CompletionState.DISCARDED, CompletionState.FAILURE);

    private final ActionRepository actionRepository;

    private final ActionMetadataService actionMetadataService;

    private final WorkflowContextStore workflowContextStore;

    private final WorkflowService workflowService;

    private final ProfileService profileService;

    private final HopeLangService hopeLangService;

    private final HandleBarsService handleBarsService;

    private final WorkflowStepService workflowStepService;

    private final Provider<StateMachineRegistry> stateMachineRegistry;

    private final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor;

    @Override
    public StateMachine<String, String> createAction(final String workflowStepId,
                                                     final ActionType actionType,
                                                     final String stateMachineVersion,
                                                     final String actionMappingId,
                                                     final TransitionContext transitionContext,
                                                     final String screenMappingId,
                                                     final UserDetails userDetails,
                                                     final StepActionContext stepActionContext,
                                                     final TtlConfig ttlConfig,
                                                     final DependencyConfig dependencyConfig) {

        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(TransitionContext.class, transitionContext);
        stateMachineTransitionContext.put(Fields.workflowStepId, workflowStepId);
        stateMachineTransitionContext.put(Fields.actionType, actionType);
        stateMachineTransitionContext.put(Fields.stateMachineVersion, stateMachineVersion);
        stateMachineTransitionContext.put(Fields.actionMappingId, actionMappingId);
        stateMachineTransitionContext.put(Fields.screenMappingId, screenMappingId);
        stateMachineTransitionContext.put(UserDetails.class, userDetails);
        if (Objects.nonNull(stepActionContext)) {
            stateMachineTransitionContext.put(StepActionContext.class, stepActionContext);
        }
        if (Objects.nonNull(ttlConfig)) {
            stateMachineTransitionContext.put(TtlConfig.class, ttlConfig);
        }
        if (Objects.nonNull(dependencyConfig)) {
            stateMachineTransitionContext.put(DependencyConfig.class, dependencyConfig);
        }
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(workflowStepId + Constants.WORKFLOW_STEP_ACTION_LOCK_POSTFIX)
                .build());

        final var baseStateMachine = stateMachineRegistry.get()
                .getBaseStateMachine(actionType, stateMachineVersion);

        return baseStateMachine.sendEvent(StateMachineConstants.States.CREATED,
                StateMachineConstants.Events.CREATE_ENTRY, stateMachineTransitionContext);

    }

    @Override
    public StateMachine<String, String> triggerEvent(final String actionId,
                                                     final ActionType actionType,
                                                     final String stateMachineVersion,
                                                     final String actionEvent,
                                                     final TransitionContext transitionContext,
                                                     final UserDetails userDetails,
                                                     final Map<Object, Object> auxiliaryStateMachineTransitionMap) {

        final var stateMachineTransitionContext = generateStateMachineTransitionContext(transitionContext, actionId,
                userDetails, auxiliaryStateMachineTransitionMap);

        final var storedAction = validateAndGetAction(actionId);

        final var actionStateMachine = stateMachineRegistry.get()
                .getBaseStateMachine(actionType, stateMachineVersion);

        return actionStateMachine.sendEvent(storedAction.getCurrentState(), actionEvent, stateMachineTransitionContext);
    }

    @Override
    public String createAndSkipAction(final StoredWorkflowStep storedWorkflowStep,
                                      final StandardProfileScreenConfig standardProfileScreenConfig,
                                      final UserDetails userDetails,
                                      final ActionType actionType,
                                      final TransitionEvent transitionEvent) {

        final var actionId = IdUtils.createIdInSameShard("A", storedWorkflowStep.getWorkflowStepId(),
                actionRepository::getShardId);

        final var storedAction = StoredAction.builder()
                .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                .actionId(actionId)
                .actionType(actionType)
                .currentState(TransitionState.SKIPPED.name())
                .currentEvent(transitionEvent.name())
                .stateMachineVersion("V1")
                .actionMappingId(transitionEvent.name())
                .screenMappingId(standardProfileScreenConfig.getScreenMappingId())
                .lastUpdatedBy(userDetails.getUserId())
                .lastUpdaterType(UpdaterType.valueOf(userDetails.getUserType()
                        .name()))
                .completed(true)
                .completionState(CompletionState.SKIPPED)
                .primaryKey(BuildUtils.primaryKey())
                .build();

        actionRepository.save(storedAction, saveAction -> {

            workflowContextStore.addActionInWorkflowContext(storedAction);

            return saveAction;
        });

        return actionId;
    }

    @Override
    public StoredAction validateAndGetAction(final String actionId) {
        return actionRepository.select(actionId)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Constants.MESSAGE, "Unable to find Action workflow for given inputs", Fields.actionId,
                                actionId)));
    }

    @Override
    public List<StoredAction> getActionsFromAllShards(final Set<String> actionIds) {

        return actionRepository.selectFromAllShards(actionIds);
    }

    @Override
    public Optional<StoredAction> getLatestAction(final String workflowStepId,
                                                  final String actionMappingId) {

        final var storedActionList = getActions(workflowStepId, actionMappingId);

        return storedActionList.stream()
                .max(Comparator.comparing(StoredAction::getCreatedAt));
    }

    @Override
    public List<StoredAction> getActions(final String workflowStepId,
                                         final String actionMappingId) {

        return actionRepository.select(workflowStepId, actionMappingId);
    }

    @Override
    public List<StoredAction> getActions(final String workflowStepId,
                                         final List<String> actionMappingIds) {

        return actionRepository.select(workflowStepId, actionMappingIds);
    }

    @Override
    public List<StoredAction> getActions(final Set<String> workflowStepIds,
                                         final String actionMappingId) {

        return actionRepository.select(workflowStepIds, actionMappingId);
    }

    @Override
    public List<StoredAction> getActions(final Set<String> workflowStepIds) {

        if (Objects.isNull(workflowStepIds) || workflowStepIds.isEmpty()) {
            return List.of();
        }

        return actionRepository.select(workflowStepIds);
    }

    @Override
    public Map<String, Optional<StoredAction>> getActionMappingIdStoredActionMap(final Set<String> workflowStepIds) {

        return getActions(workflowStepIds).stream()
                .collect(Collectors.groupingBy(StoredAction::getActionMappingId,
                        Collectors.maxBy(Comparator.comparing(StoredAction::getCreatedAt))));
    }

    @Override
    public StepActionContext extractStepActionContext(final String actionId,
                                                      final ProfileScreenConfig profileScreenConfig) {

        return extractStepActionContext(validateAndGetAction(actionId), profileScreenConfig);
    }

    @Override
    public StepActionContext extractStepActionContext(final StoredAction storedAction,
                                                      final ProfileScreenConfig profileScreenConfig) {

        final var standardStepActionConfig = profileScreenConfig.accept(
                        new GetStandardActionConfigFromProfileScreenConfig(storedAction.getActionMappingId()))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Fields.actionId, storedAction.getActionId())));

        return standardStepActionConfig.getStepActionContext();
    }

    @Override
    public void abortActions(final Set<String> allWorkflowStepIds,
                             final UserDetails userDetails) {

        cancelActions(allWorkflowStepIds, userDetails, EventType.ABORT_ACTION, Events.ABORT_ACTION);
    }

    @Override
    public void discardActions(final Set<String> allWorkflowStepIds,
                               final UserDetails userDetails) {

        cancelActions(allWorkflowStepIds, userDetails, EventType.DISCARD_ACTION, Events.DISCARD_ACTION);
    }

    @Override
    @MonitoredFunction
    public void invalidateActionsInWorkflowSteps(final Set<String> allWorkflowStepIds,
                                                 final UserDetails userDetails) {

        cancelActions(allWorkflowStepIds, userDetails, EventType.ACTION_INVALIDATED, Events.INVALIDATE_ACTION);
    }


    @Override
    @MonitoredFunction
    public void invalidateAction(final String actionId,
                                 final UserDetails userDetails) {

        final var storedAction = validateAndGetAction(actionId);

        cancelActionsFromActionIds(userDetails, EventType.ACTION_INVALIDATED, Events.INVALIDATE_ACTION,
                List.of(storedAction));
    }

    private void cancelActions(final Set<String> allWorkflowStepIds,
                               final UserDetails userDetails,
                               final EventType eventType,
                               final String cancelEvent) {

        final var allActions = getActions(allWorkflowStepIds);

        cancelActionsFromActionIds(userDetails, eventType, cancelEvent, allActions);
    }

    private void cancelActionsFromActionIds(final UserDetails userDetails,
                                            final EventType eventType,
                                            final String cancelEvent,
                                            final List<StoredAction> allActions) {
        final var auxiliaryStateMachineTransitionMap = new HashMap<>();
        auxiliaryStateMachineTransitionMap.put(EventType.class, eventType);

        allActions.stream()
                .filter(action -> !FILTERED_ACTION_STATES_FOR_CANCEL.contains(action.getCompletionState()))
                .sorted(Comparator.comparing(StoredAction::getLastUpdatedAt))
                .forEach(action -> triggerEvent(action.getActionId(), action.getActionType(),
                        action.getStateMachineVersion(), cancelEvent, Constants.EMPTY_TRANSITION_CONTEXT, userDetails,
                        auxiliaryStateMachineTransitionMap));
    }

    @Override
    public void identifyNextActionAndTrigger(final StoredAction storedAction,
                                             final StandardProfileScreenConfig standardProfileScreenConfig) {

        final var standardStepActionConfigCompletionTimestamp = standardProfileScreenConfig.getStepActionConfig()
                .accept(new IdentifyNextActionToTriggerActionConfigVisitor(storedAction.getWorkflowStepId(), this,
                        workflowService, hopeLangService, workflowContextStore), null);

        final var actionToTrigger = standardStepActionConfigCompletionTimestamp.getStandardStepActionConfig();

        final var uiRequestContext = (ShadowV2UiRequestContext) workflowStepService.getUiRequestContext(
                storedAction.getWorkflowStepId());

        final var transitionContext = buildTransitionContext(actionToTrigger.getTransitionContextTemplate(),
                actionToTrigger.getTransitionContextTemplateAsString(), uiRequestContext);

        createAction(storedAction.getWorkflowStepId(), actionToTrigger.getActionType(),
                actionToTrigger.getStateMachineVersion(), actionToTrigger.getActionMappingId(), transitionContext,
                storedAction.getScreenMappingId(), Constants.PVCORE_SYSTEM_USER, actionToTrigger.getStepActionContext(),
                actionToTrigger.getTtlConfig(), actionToTrigger.getDependencyConfig());
    }

    @Override
    public TransitionContext buildTransitionContext(final JsonNode transitionContextTemplate,
                                                    final String transitionContextTemplateAsString,
                                                    final UiRequestContext uiRequestContext) {

        // We first give preference to JsonNode
        final var transitionContextTemplateToRender = Objects.isNull(transitionContextTemplate)
                                                      ? transitionContextTemplateAsString
                                                      : transitionContextTemplate.toString();

        final var transitionContextJson = handleBarsService.transform(transitionContextTemplateToRender,
                uiRequestContext);

        return MapperUtils.deserialize(transitionContextJson, TransitionContext.class);
    }

    private Map<Object, Object> generateStateMachineTransitionContext(final TransitionContext transitionContext,
                                                                      final String actionId,
                                                                      final UserDetails userDetails,
                                                                      final Map<Object, Object> auxiliaryStateMachineTransitionMap) {
        final var stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(TransitionContext.class, transitionContext);
        stateMachineTransitionContext.put(Fields.actionId, actionId);
        stateMachineTransitionContext.put(UserDetails.class, userDetails);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
                .transitionKey(actionId)
                .build());

        if (Objects.nonNull(auxiliaryStateMachineTransitionMap)) {
            stateMachineTransitionContext.putAll(auxiliaryStateMachineTransitionMap);
        }
        return stateMachineTransitionContext;
    }

    @Override
    public Optional<ActionFailureResponse> getFailureDetailsFromFailedAction(final StoredAction storedAction) {

        if (Objects.nonNull(storedAction.getFailureErrorCode())) {

            final var storedActionFailureErrorCode = storedAction.getFailureErrorCode();
            final var storedActionMappingId = storedAction.getActionMappingId();
            final var actionMetaDataList = actionMetadataService.getActionMetadataList(storedAction.getActionId());

            final var rejectionReason = actionMetaDataList.stream()
                    .map(storedActionMetadata -> storedActionMetadata.accept(ActionMetaRejectionReasonVisitor.INSTANCE,
                            storedActionFailureErrorCode.getReason()))
                    .distinct()
                    .collect(Collectors.joining(","));

            return Optional.of(ActionFailureResponse.builder()
                    .actionFailureMappingId(storedActionMappingId)
                    .actionFailureErrorCode(storedActionFailureErrorCode)
                    .reason(Strings.isNullOrEmpty(rejectionReason)
                            ? storedActionFailureErrorCode.getReason()
                            : rejectionReason)
                    .build());
        }

        return Optional.empty();
    }

    @Override
    public void triggerActionsViaManualRetry(final String workflowStepId,
                                             final String actionId) {

        final var auxiliaryStateMachineTransitionMap = new HashMap<>();
        auxiliaryStateMachineTransitionMap.put(EventType.class, EventType.ACTION_INVALIDATED);

        final var storedAction = validateAndGetAction(actionId);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(workflowStepId);

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflow.getWorkflowId());

        final var workflowContextJsonNode = MapperUtils.convertToJsonNode(workflowContext);

        // Identify the root node of the dependency tree
        final var profile = profileService.get(storedWorkflow.getProfileId(), true);

        final var allStandardProfileScreenConfigs = profileService.getAllStandardProfileScreenConfigs(profile);

        final var allStandardStepActionConfig = ActionService.getAllStandardStepActionConfig(
                allStandardProfileScreenConfigs);

        final var queue = new LinkedList<StandardStepActionConfig>();

        final var currentStandardStepActionConfig = allStandardStepActionConfig.stream()
                .filter(stepActionConfig -> stepActionConfig.getActionMappingId()
                        .equals(storedAction.getActionMappingId()))
                .findFirst()
                .orElseThrow(
                        () -> KaizenException.create(KaizenResponseCode.MANUAL_RETRY_OF_ACTION_NOT_ALLOWED, Map.of()));

        queue.add(currentStandardStepActionConfig);

        final var storedWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        final var allStoredWorkflowStepIds = storedWorkflowSteps.stream()
                .map(StoredWorkflowStep::getWorkflowStepId)
                .collect(Collectors.toSet());

        final var actionMappingIdStoredActionMap = getActionMappingIdStoredActionMap(allStoredWorkflowStepIds);

        while (!queue.isEmpty()) {

            final var currentStepActionConfig = queue.pop();

            allStandardStepActionConfig.stream()
                    .filter(standardStepActionConfig -> isDependentOnActionMappingId(standardStepActionConfig,
                            currentStepActionConfig.getActionMappingId(), workflowContextJsonNode))
                    .forEach(queue::push);

            final var action = actionMappingIdStoredActionMap.get(currentStepActionConfig.getActionMappingId())
                    .orElseThrow(() -> KaizenException.create(KaizenResponseCode.MANUAL_RETRY_OF_ACTION_NOT_ALLOWED,
                            Map.of()));

            if (!CompletionState.INVALIDATED.equals(action.getCompletionState())) {
                triggerEvent(action.getActionId(), action.getActionType(), action.getStateMachineVersion(),
                        Events.INVALIDATE_ACTION, Constants.EMPTY_TRANSITION_CONTEXT, Constants.PVCORE_SYSTEM_USER,
                        auxiliaryStateMachineTransitionMap);
            }
        }

        createAction(storedAction.getWorkflowStepId(), currentStandardStepActionConfig.getActionType(),
                currentStandardStepActionConfig.getStateMachineVersion(), storedAction.getActionMappingId(),
                EmptyTransitionContext.builder()
                        .build(), storedAction.getScreenMappingId(), Constants.PVCORE_SYSTEM_USER,
                currentStandardStepActionConfig.getStepActionContext(), currentStandardStepActionConfig.getTtlConfig(),
                currentStandardStepActionConfig.getDependencyConfig());
    }

    @Override
    @MonitoredFunction
    public void updateActionMetaDataForPurgeInWorkflowStepIds(final Set<String> allWorkflowStepIds,
                                                              final UserDetails userDetails) {

        // Get all actions
        // For each action, update the action metadata according to its type

        final var allActions = getActions(allWorkflowStepIds);

        allActions.forEach(action -> actionMetadataService.updateActionMetadataForPurge(action.getActionId()));
    }


    private boolean isDependentOnActionMappingId(final StandardStepActionConfig standardStepActionConfig,
                                                 final String actionMappingId,
                                                 final JsonNode workflowContextJsonNode) {
        return Objects.nonNull(standardStepActionConfig.getDependencyConfig()) && actionMappingId.equals(
                standardStepActionConfig.getDependencyConfig()
                        .accept(getDependentActionMappingIdDependencyConfigVisitor, workflowContextJsonNode));
    }
}
