package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowAutoAbortCallbackMessage;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoAbortCallbackHandler;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoAbortCallbackHandler.WorkflowAutoAbortCallbackHandlerData;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import java.util.Objects;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class WorkflowAutoAbortCallbackActor extends BaseActor<WorkflowAutoAbortCallbackMessage> {


    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final AbortWorkflowActor abortWorkflowActor;

    private final WorkflowAutoAbortCallbackHandler workflowAutoAbortCallbackHandler;

    @Inject
    protected WorkflowAutoAbortCallbackActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                             final ConnectionRegistry connectionRegistry,
                                             final ObjectMapper mapper,
                                             final RetryStrategyFactory retryStrategyFactory,
                                             final ExceptionHandlingFactory exceptionHandlingFactory,
                                             final ProfileService profileService,
                                             final WorkflowService workflowService,
                                             final AbortWorkflowActor abortWorkflowActor,
                                             final WorkflowAutoAbortCallbackHandler workflowAutoAbortCallbackHandler) {

        super(ActorType.WORKFLOW_AUTO_ABORT, actorConfigMap.get(ActorType.WORKFLOW_AUTO_ABORT), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, WorkflowAutoAbortCallbackMessage.class);

        this.profileService = profileService;
        this.workflowService = workflowService;
        this.abortWorkflowActor = abortWorkflowActor;
        this.workflowAutoAbortCallbackHandler = workflowAutoAbortCallbackHandler;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final WorkflowAutoAbortCallbackMessage workflowAutoAbortCallbackMessage) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(
                workflowAutoAbortCallbackMessage.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        // If somehow the auto abort config is removed from the profile after clockwork callback is scheduled
        if (Objects.isNull(workflowAutoAbortCallbackMessage.getWorkflowAutoAbortConfig())) {
            log.warn(
                    "Received workflow auto abort clockwork callback for WF {} with PF {} without any auto abort config in profile. Ignoring callback.",
                    storedWorkflow.getWorkflowId(), profile.getProfileId());
            return true;
        }

        return workflowAutoAbortCallbackMessage.getWorkflowAutoAbortConfig()
                .accept(workflowAutoAbortCallbackHandler, WorkflowAutoAbortCallbackHandlerData.builder()
                        .storedWorkflow(storedWorkflow)
                        .profile(profile)
                        .userDetails(workflowAutoAbortCallbackMessage.getUserDetails())
                        .build());
    }
}
