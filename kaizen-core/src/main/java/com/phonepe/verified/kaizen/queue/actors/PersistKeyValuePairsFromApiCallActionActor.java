package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.PersistKeyValuePairsFromApiCallActionContext;
import com.phonepe.verified.kaizen.models.data.contexts.KeyValuePairsActionTransitionContext;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.PersistKeyValuePairsFromApiCallActionMessage;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.MediaType;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class PersistKeyValuePairsFromApiCallActionActor extends
        BaseActor<PersistKeyValuePairsFromApiCallActionMessage> {

    private final ActionService actionService;

    private final OlympusIMClient olympusIMClient;

    private final HandleBarsService handleBarsService;

    private final HttpClientRegistry httpClientRegistry;

    private final WorkflowStepService workflowStepService;

    private final Provider<WorkflowContextStore> workflowContextStore;


    @Inject
    protected PersistKeyValuePairsFromApiCallActionActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                         final ConnectionRegistry connectionRegistry,
                                                         final ObjectMapper mapper,
                                                         final RetryStrategyFactory retryStrategyFactory,
                                                         final ExceptionHandlingFactory exceptionHandlingFactory,
                                                         final ActionService actionService,
                                                         final OlympusIMClient olympusIMClient,
                                                         final HandleBarsService handleBarsService,
                                                         final HttpClientRegistry httpClientRegistry,
                                                         final WorkflowStepService workflowStepService,
                                                         final Provider<WorkflowContextStore> workflowContextStore) {
        super(ActorType.PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL,
                actorConfigMap.get(ActorType.PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, PersistKeyValuePairsFromApiCallActionMessage.class);
        this.actionService = actionService;
        this.olympusIMClient = olympusIMClient;
        this.handleBarsService = handleBarsService;
        this.httpClientRegistry = httpClientRegistry;
        this.workflowStepService = workflowStepService;
        this.workflowContextStore = workflowContextStore;
    }


    @Override
    @SneakyThrows
    protected boolean handleMessage(final PersistKeyValuePairsFromApiCallActionMessage persistKeyValuePairsFromApiCallActionMessage) {

        final var storedAction = actionService.validateAndGetAction(
                persistKeyValuePairsFromApiCallActionMessage.getActionId());

        try {

            final var persistKeyValuePairsFromApiCallActionContext = persistKeyValuePairsFromApiCallActionMessage.getPersistKeyValuePairsFromApiCallActionContext();

            final var httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(
                    persistKeyValuePairsFromApiCallActionContext.getServiceName());

            final var headerPairs = HttpClientUtils.generateHeaderPair(
                    persistKeyValuePairsFromApiCallActionContext.getHeaders());

            final var commandName = "persistKeyValuePairsFromApiCall" + "_"
                    + persistKeyValuePairsFromApiCallActionContext.getServiceName();

            final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(
                    storedAction.getWorkflowStepId());
            final var workflowContext = workflowContextStore.get()
                    .getWorkflowContext(storedWorkflowStep.getWorkflowId());

            final var urlPath = handleBarsService.transform(persistKeyValuePairsFromApiCallActionContext.getUrlPath(),
                    workflowContext);

            final var auxiliaryWorkflowContextObjectNode = (ObjectNode) MapperUtils.convertToJsonNode(workflowContext);

            final var transitionContext = persistKeyValuePairsFromApiCallActionMessage.getTransitionContext();

            if (transitionContext instanceof final KeyValuePairsActionTransitionContext keyValuePairsActionTransitionContext) {

                auxiliaryWorkflowContextObjectNode.setAll((ObjectNode) MapperUtils.convertToJsonNode(
                        keyValuePairsActionTransitionContext.getKeyValuePairs()));
            }

            final var requestBody = handleBarResolvePayload(auxiliaryWorkflowContextObjectNode,
                    persistKeyValuePairsFromApiCallActionContext.getPayload());

            final var response = switch (persistKeyValuePairsFromApiCallActionContext.getMethod()) {
                case GET -> HttpClientUtils.executeGet(httpExecutorBuilderFactory, commandName, urlPath,
                        getHeadersIncludingOlympusAuthToken(headerPairs), JsonNode.class, getClass());
                case POST -> HttpClientUtils.executePost(httpExecutorBuilderFactory, commandName, urlPath,
                        new SerializableHttpData(MediaType.APPLICATION_JSON, requestBody),
                        getHeadersIncludingOlympusAuthToken(headerPairs), JsonNode.class, getClass());

                case DELETE, PUT -> throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());

            };

            final var auxiliaryStateMachineTransitionMap = new HashMap<>();

            final var handleBarResolvedPersistKeyValuePairsFromApiCallActionContext = handleBarResolve(
                    persistKeyValuePairsFromApiCallActionContext, response);

            auxiliaryStateMachineTransitionMap.put(PersistKeyValuePairsFromApiCallActionContext.class,
                    handleBarResolvedPersistKeyValuePairsFromApiCallActionContext);

            actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                    storedAction.getStateMachineVersion(), Events.PERSIST_METADATA, Constants.EMPTY_TRANSITION_CONTEXT,
                    Constants.PVCORE_SYSTEM_USER, auxiliaryStateMachineTransitionMap);

        } catch (final Exception e) {

            log.error(String.format("Error during persistKeyValuePairsFromApiCall actionId: %s",
                    storedAction.getActionId()), e);

            actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                    storedAction.getStateMachineVersion(), Events.API_CALL_FAILED, Constants.EMPTY_TRANSITION_CONTEXT,
                    Constants.PVCORE_SYSTEM_USER, new HashMap<>());
        }

        return true;
    }

    private PersistKeyValuePairsFromApiCallActionContext handleBarResolve(final PersistKeyValuePairsFromApiCallActionContext keyValuePairsActionContextTemplate,
                                                                          final JsonNode apiResponse) {

        final var stringActionContext = handleBarsService.transform(
                MapperUtils.serializeToString(keyValuePairsActionContextTemplate), apiResponse);

        return MapperUtils.deserialize(stringActionContext, PersistKeyValuePairsFromApiCallActionContext.class);
    }


    private JsonNode handleBarResolvePayload(final JsonNode workflowContextJsonNode,
                                             final JsonNode payload) {

        final var stringActionContext = handleBarsService.transform(MapperUtils.serializeToString(payload),
                workflowContextJsonNode);

        return MapperUtils.deserialize(stringActionContext, JsonNode.class);
    }

    private List<HeaderPair> getHeadersIncludingOlympusAuthToken(final List<HeaderPair> headerPairs) {

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        if (headerPairs.isEmpty()) {

            return List.of(authHeader);
        }

        headerPairs.add(authHeader);

        return headerPairs;
    }
}