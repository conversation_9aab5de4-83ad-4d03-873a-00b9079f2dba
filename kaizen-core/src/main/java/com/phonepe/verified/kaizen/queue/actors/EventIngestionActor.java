package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.ActionCompletionEventIngestionVisitor;
import com.phonepe.verified.kaizen.services.visitors.EventIngestionVisitor;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class EventIngestionActor extends BaseActor<EventIngestionMessage> {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final EventIngestionCommand eventIngestionCommand;

    private final ActionMetadataService actionMetadataService;

    private final ActionCompletionEventIngestionVisitor actionCompletionEventIngestionVisitor;

    @Inject
    protected EventIngestionActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                  final ConnectionRegistry connectionRegistry,
                                  final ObjectMapper mapper,
                                  final RetryStrategyFactory retryStrategyFactory,
                                  final ExceptionHandlingFactory exceptionHandlingFactory,
                                  final ActionService actionService,
                                  final ProfileService profileService,
                                  final WorkflowService workflowService,
                                  final WorkflowStepService workflowStepService,
                                  final EventIngestionCommand eventIngestionCommand,
                                  final ActionMetadataService actionMetadataService,
                                  final ActionCompletionEventIngestionVisitor actionCompletionEventIngestionVisitor) {

        super(ActorType.EVENT_INGESTION, actorConfigMap.get(ActorType.EVENT_INGESTION), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, EventIngestionMessage.class);
        this.actionService = actionService;
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
        this.eventIngestionCommand = eventIngestionCommand;
        this.actionMetadataService = actionMetadataService;
        this.actionCompletionEventIngestionVisitor = actionCompletionEventIngestionVisitor;
    }

    @Override
    protected boolean handleMessage(final EventIngestionMessage message) {

        final var storedAction = actionService.validateAndGetAction(message.getActionId());

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var shadowV2UiRequestContext = (ShadowV2UiRequestContext) workflowStepService.getUiRequestContext(
                storedWorkflowStep.getWorkflowStepId());

        message.getEventType()
                .accept(new EventIngestionVisitor(profile, profileStep, storedAction, storedWorkflow,
                        storedWorkflowStep, eventIngestionCommand, shadowV2UiRequestContext,
                        actionCompletionEventIngestionVisitor), message);

        return true;
    }
}
