package com.phonepe.verified.kaizen.configs;

import io.dropwizard.util.Duration;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CallbackTtlConfig {

    @NotEmpty
    private Map<String, Duration> commandNameCallbackTtlMap;

    @NotNull
    private Duration defaultCallbackTtl;


    public Duration getCallbackTtl(@NonNull final String commandName) {
        return commandNameCallbackTtlMap.getOrDefault(commandName, this.defaultCallbackTtl);
    }


}
