package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.ScheduleWorkflowAbortActionContext;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.utils.ClockworkUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ScheduleWorkflowAbortActor extends BaseActor<StepActionMessage> {

    private static final String CALLBACK_URI = "/v1/internal/callback/clockwork/workflow/abort/%s";

    private final ActionService actionService;

    private final ProfileService profileService;

    private final ClockworkClient clockworkClient;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final EventIngestionCommand eventIngestionCommand;

    @Inject
    protected ScheduleWorkflowAbortActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                         final ConnectionRegistry connectionRegistry,
                                         final ObjectMapper mapper,
                                         final RetryStrategyFactory retryStrategyFactory,
                                         final ExceptionHandlingFactory exceptionHandlingFactory,
                                         final ActionService actionService,
                                         final ProfileService profileService,
                                         final ClockworkClient clockworkClient,
                                         final WorkflowService workflowService,
                                         final WorkflowStepService workflowStepService,
                                         final EventIngestionCommand eventIngestionCommand,
                                         final DataProvider<KaizenConfig> appConfigProvider) {
        super(ActorType.SCHEDULE_WORKFLOW_ABORT, actorConfigMap.get(ActorType.SCHEDULE_WORKFLOW_ABORT),
                connectionRegistry, mapper, retryStrategyFactory, exceptionHandlingFactory, StepActionMessage.class);
        this.actionService = actionService;
        this.profileService = profileService;
        this.clockworkClient = clockworkClient;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
        this.appConfigProvider = appConfigProvider;
        this.eventIngestionCommand = eventIngestionCommand;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final StepActionMessage actionMessage) {

        final var storedAction = actionService.validateAndGetAction(actionMessage.getActionId());

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var actionContext = (ScheduleWorkflowAbortActionContext) actionService.extractStepActionContext(
                storedAction.getActionId(), profileStep.getProfileScreenConfig());

        final var schedulingRequest = ClockworkUtils.getActionMessageSchedulingRequest("{}", appConfigProvider.getData()
                        .getBaseUrl(), String.format(CALLBACK_URI, actionMessage.getActionId()),
                ClockworkUtils.getSchedulingDelayDate(actionContext.getAbortDuration()));

        try {

            final var clockworkSchedulingResponse = clockworkClient.schedule(schedulingRequest, Constants.APP_NAME);

            eventIngestionCommand.ingestScheduledAbortWorkflowEvent(storedWorkflow, profile,
                    EventType.ABORT_WORKFLOW_SCHEDULE_CREATED_ON_CLOCKWORK, storedAction,
                    storedAction.getWorkflowStepId(), profileStep.getProfileStepMappingId(),
                    actionContext.getAbortReason(), actionContext.getAbortDuration(),
                    clockworkSchedulingResponse.getData()
                            .getJobId(), clockworkSchedulingResponse.isSuccess()
                                         ? null
                                         : clockworkSchedulingResponse.getErrorCode()
                                                 .name());

            actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                    storedAction.getStateMachineVersion(), Events.SCHEDULE_WORKFLOW_ABORT_SUCCESS,
                    Constants.EMPTY_TRANSITION_CONTEXT, Constants.PVCORE_SYSTEM_USER, null);

        } catch (final Exception e) {

            actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                    storedAction.getStateMachineVersion(), Events.SCHEDULE_WORKFLOW_ABORT_FAILURE,
                    Constants.EMPTY_TRANSITION_CONTEXT, Constants.PVCORE_SYSTEM_USER, null);
        }

        return true;
    }
}
