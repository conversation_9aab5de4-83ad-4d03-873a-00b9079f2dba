package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.request.SearchFieldRequest;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SearchClientDataMessage extends BaseMessage {

    @NotEmpty
    final String workflowId;

    @NotEmpty
    final String fieldId;

    @NotEmpty
    final String intent;

    final long componentKitVersion;

    @NonNull
    private final SearchFieldRequest searchFieldRequest;

    @Builder
    @Jacksonized
    public SearchClientDataMessage(@NonNull final RequestInfo requestInfo,
                                   @NotEmpty final String workflowId,
                                   @NotEmpty final String fieldId,
                                   @NotEmpty final String intent,
                                   final long componentKitVersion,
                                   @NonNull final SearchFieldRequest searchFieldRequest) {

        super(ActorMessageType.SEARCH_CLIENT_DATA, requestInfo);
        this.workflowId = workflowId;
        this.fieldId = fieldId;
        this.intent = intent;
        this.componentKitVersion = componentKitVersion;
        this.searchFieldRequest = searchFieldRequest;
    }
}
