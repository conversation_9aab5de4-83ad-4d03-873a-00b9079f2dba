package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.drishti.models.commons.documents.impl.Base64EncodedDocument;
import com.phonepe.verified.drishti.models.requests.extraction.ExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.DrivingLicenseExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.PanExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.PassportExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.VoterExtractionContext;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.Document;
import com.phonepe.verified.kaizen.models.data.DocumentLabel;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DocumentExtractionContextVisitor implements
        DocumentType.DocumentTypeVisitor<ExtractionContext, List<Document>> {

    public static final DocumentExtractionContextVisitor INSTANCE = new DocumentExtractionContextVisitor();

    @Override
    public ExtractionContext visitPan(final List<Document> documents) {

        final var panDocument = documents.stream()
                .filter(document -> DocumentType.PAN.equals(document.getDocumentType()))
                .findFirst()
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.OCR_DOC_NOT_FOUND, Map.of()));
        final var document = Base64EncodedDocument.builder()
                .content(Base64.getEncoder()
                        .encodeToString(panDocument.getFileContent()))
                .build();

        return PanExtractionContext.builder()
                .document(document)
                .build();
    }

    @Override
    public ExtractionContext visitVoter(final List<Document> documents) {

        final var documentLabelToDocumentContentMap = getDocumentLabelToDocumentContentMap(documents,
                DocumentType.VOTER);
        final var frontDocument = Base64EncodedDocument.builder()
                .content(Base64.getEncoder()
                        .encodeToString(documentLabelToDocumentContentMap.get(DocumentLabel.FRONT.name())))
                .build();
        final var backDocument = Base64EncodedDocument.builder()
                .content(Base64.getEncoder()
                        .encodeToString(documentLabelToDocumentContentMap.get(DocumentLabel.BACK.name())))
                .build();

        return VoterExtractionContext.builder()
                .documentFront(frontDocument)
                .documentBack(backDocument)
                .build();
    }

    @Override
    public ExtractionContext visitDrivingLicense(final List<Document> documents) {

        final var documentLabelToDocumentContentMap = getDocumentLabelToDocumentContentMap(documents,
                DocumentType.DRIVING_LICENSE);
        final var frontDocument = Base64EncodedDocument.builder()
                .content(Base64.getEncoder()
                        .encodeToString(documentLabelToDocumentContentMap.get(DocumentLabel.FRONT.name())))
                .build();
        final var backDocument = Base64EncodedDocument.builder()
                .content(Base64.getEncoder()
                        .encodeToString(documentLabelToDocumentContentMap.get(DocumentLabel.BACK.name())))
                .build();

        return DrivingLicenseExtractionContext.builder()
                .documentFront(frontDocument)
                .documentBack(backDocument)
                .build();
    }

    @Override
    public ExtractionContext visitPassport(final List<Document> documents) {

        final var documentLabelToDocumentContentMap = getDocumentLabelToDocumentContentMap(documents,
                DocumentType.PASSPORT);
        final var frontDocument = Base64EncodedDocument.builder()
                .content(Base64.getEncoder()
                        .encodeToString(documentLabelToDocumentContentMap.get(DocumentLabel.FRONT.name())))
                .build();
        final var backDocument = Base64EncodedDocument.builder()
                .content(Base64.getEncoder()
                        .encodeToString(documentLabelToDocumentContentMap.get(DocumentLabel.BACK.name())))
                .build();

        return PassportExtractionContext.builder()
                .documentFront(frontDocument)
                .documentBack(backDocument)
                .build();
    }

    @Override
    public ExtractionContext visitAadhaar(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAadhaarVid(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAadhaarUidToken(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitGst(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitElectricityBill(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryAadhaar(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryPassport(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryPan(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryDrivingLicense(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitCertificateOfIncorporation(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitPartnershipDeed(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitSeaCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitUaCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitVatCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitTradeLicense(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitMcdCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitFoodLicense(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitCstCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitPropertyTaxReceipt(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitMunicipalKhataReceipt(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitBankProof(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitIeCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitLlpCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitMunicipalCorporationTaxCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitTanCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryDeclaration(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryAppointmentLetter(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryMandateLetterResolution(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryManagingBodyResolution(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryBoardResolution(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryPartnerResolution(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAuthorizedSignatoryKartaDeclaration(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitBrnCertificateOfTheStateOfRajasthan(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitSelfie(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitDigitalSignature(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitDrugLicense(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitCentralSalesTax(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitMunicipalCorporationDepartmentCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitTrustDeed(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitOtpBasedVideoKyc(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitEsignedAccountOpeningForm(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitDigioKycWorkflowDetails(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitTinCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitLabourCertificate(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitLiquorLicense(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitDiagnosticLicense(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitNregaJobCard(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitLetterFromNationalPopulationRegister(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitAadhaarFaceImage(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitCentralGovtId(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitStateGovtId(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitCkycJsonBlob(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitGramPanchayatDocuments(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitIncomeProof(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitCancelledCheck(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitBankStatement(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitNationalIdCard(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitForm16(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitSalarySlip(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitInvoiceCopy(List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitProofOfDelivery(List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitDunsNumber(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    @Override
    public ExtractionContext visitBankAccount(final List<Document> data) {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }

    private Map<String, byte[]> getDocumentLabelToDocumentContentMap(final List<Document> documents,
                                                                     final DocumentType documentType) {
        return documents.stream()
                .filter(document -> documentType.equals(document.getDocumentType()))
                .collect(Collectors.toMap(Document::getDocumentLabel, Document::getFileContent));
    }
}
