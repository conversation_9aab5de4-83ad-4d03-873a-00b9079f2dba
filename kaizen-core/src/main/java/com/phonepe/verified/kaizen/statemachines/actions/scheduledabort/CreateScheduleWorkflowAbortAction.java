package com.phonepe.verified.kaizen.statemachines.actions.scheduledabort;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.ScheduleWorkflowAbortActor;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "createScheduleWorkflowAbortAction")
public class CreateScheduleWorkflowAbortAction extends CreateEntryBaseAction {

    private final ScheduleWorkflowAbortActor scheduleWorkflowAbortActor;

    @Inject
    public CreateScheduleWorkflowAbortAction(final ActionService actionService,
                                             final WorkflowService workflowService,
                                             final ClockworkClient clockworkClient,
                                             final ActionRepository actionRepository,
                                             final WorkflowStepService workflowStepService,
                                             final ScheduleWorkflowAbortActor scheduleWorkflowAbortActor,
                                             final DataProvider<KaizenConfig> appConfigDataProvider,
                                             final Provider<WorkflowContextStore> workflowContextStore,
                                             final Provider<EventIngestionActor> eventIngestionActorProvider,
                                             final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                             final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor) {

        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.scheduleWorkflowAbortActor = scheduleWorkflowAbortActor;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        scheduleWorkflowAbortActor.publish(StepActionMessage.builder()
                .actionId(storedAction.getActionId())
                .build());
    }
}
