package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.request.SearchFieldRequest;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.data.common.MailboxApiName;
import com.phonepe.verified.kaizen.queue.actors.ProcessSearchClientDataActor;
import com.phonepe.verified.kaizen.queue.messages.SearchClientDataMessage;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/search")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Search", description = "Search related APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class SearchResource {

    private final AuthZService authZService;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final SessionManagementService sessionManagementService;

    private final ProcessSearchClientDataActor processSearchClientDataActor;

    @POST
    @Authorize
    @PermitAll
    @SneakyThrows
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/client/data/{workflowId}/{fieldId}/{intent}/{componentKitVersion}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Operation(summary = "Search API call from PhonePe Verified SDK")
    @AuthZ(gandalfPermission = "searchClientData", olympusPermission = OlympusPermissionNames.SEARCH_CLIENT_DATA)
    public Response search(@NotEmpty @PathParam("workflowId") final String workflowId,
                           @NotEmpty @PathParam("fieldId") final String fieldId,
                           @NotEmpty @PathParam("intent") final String intent,
                           @PathParam("componentKitVersion") final long componentKitVersion,
                           @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                           @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                           @Valid @NotNull final SearchFieldRequest searchFieldRequest,
                           @Parameter(hidden = true) @HeaderParam(Headers.X_SESSION_TOKEN) final String sessionToken) {

        authZService.authorizeUserWithWorkflowId(workflowId, requestInfo, userDetails);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        processSearchClientDataActor.publish(SearchClientDataMessage.builder()
                .workflowId(workflowId)
                .fieldId(fieldId)
                .intent(intent)
                .componentKitVersion(componentKitVersion)
                .searchFieldRequest(searchFieldRequest)
                .requestInfo(requestInfo)
                .build());

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.SEARCH_CLIENT_DATA)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.SEARCH_CLIENT_DATA)
                        .toMilliseconds())
                .build();
    }
}
