package com.phonepe.verified.kaizen.statemachines.actions.otphurdle;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;


@Slf4j
@Singleton
@ActionKey(value = "createOtpHurdleAction")
public class CreateOtpHurdleAction extends CreateEntryBaseAction {

    private final Provider<ActionExecutorActor> actionExecutorActorProvider;

    @Inject
    public CreateOtpHurdleAction(final ActionService actionService,
                                 final WorkflowService workflowService,
                                 final ClockworkClient clockworkClient,
                                 final ActionRepository actionRepository,
                                 final WorkflowStepService workflowStepService,
                                 final DataProvider<KaizenConfig> appConfigDataProvider,
                                 final Provider<WorkflowContextStore> workflowContextStore,
                                 final Provider<EventIngestionActor> eventIngestionActorProvider,
                                 final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                 final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.actionExecutorActorProvider = actionExecutorActorProvider;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        actionExecutorActorProvider.get()
                .publish(ActionExecutionMessage.builder()
                        .actionId(storedAction.getActionId())
                        .eventToTrigger(Events.TRIGGER_OTP_HURDLE)
                        .userDetails(Constants.PVCORE_SYSTEM_USER)
                        .build());
    }

}
