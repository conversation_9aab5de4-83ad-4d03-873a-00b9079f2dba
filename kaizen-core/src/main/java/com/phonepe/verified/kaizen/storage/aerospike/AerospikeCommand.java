package com.phonepe.verified.kaizen.storage.aerospike;

import com.aerospike.client.Bin;
import com.aerospike.client.IAerospikeClient;
import com.aerospike.client.Key;
import com.aerospike.client.policy.RecordExistsAction;
import com.aerospike.client.policy.WritePolicy;
import com.aerospike.client.query.Statement;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.appform.functionmetrics.MonitoredFunction;
import io.dropwizard.util.Strings;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public abstract class AerospikeCommand<K extends AerospikeKey, V> {

    private final IAerospikeClient aerospikeClient;
    private final AerospikeConfig aerospikeConfig;
    private final AerospikeSet aerospikeSet;
    private final Class<V> valueClazz;

    private final Retryer<Void> aerospikeStrictStoreRetryer = RetryerBuilder.<Void>newBuilder()
            .retryIfException()
            .withWaitStrategy(WaitStrategies.exponentialWait(2, 100, TimeUnit.MILLISECONDS))
            .withStopStrategy(StopStrategies.stopAfterDelay(1000, TimeUnit.MILLISECONDS))
            .build();

    public void delete(final K key) {
        try {
            final var writePolicy = new WritePolicy(aerospikeClient.getWritePolicyDefault());
            aerospikeClient.delete(writePolicy, getKey(key));
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.AEROSPIKE_DELETE_ERROR, e,
                    Map.of(Constants.MESSAGE, "Error while deleting data from AeroSpike", Constants.KEY, key.getKey()));
        }
    }

    @MonitoredFunction
    public V get(final K key,
                 final String bin) {
        try {
            final var aerospikeRecord = aerospikeClient.get(aerospikeClient.getReadPolicyDefault(), getKey(key));
            if (Objects.nonNull(aerospikeRecord)) {
                final var value = aerospikeRecord.getString(bin);
                if (!Strings.isNullOrEmpty(value)) {
                    return MapperUtils.deserialize(value, valueClazz);
                }
            } else {
                log.warn("No record found for key: {}", key.getKey());
            }
            return null;
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.AEROSPIKE_READ_ERROR, e,
                    Map.of(Constants.MESSAGE, "Error while reading data from AeroSpike", Constants.KEY, key.getKey()));
        }
    }

    @MonitoredFunction
    public V get(final K key) {
        return get(key, Constants.DEFAULT_BIN);
    }

    public int getTtlSeconds() {
        return (int) aerospikeConfig.getTtlDurations()
                .getOrDefault(aerospikeSet, aerospikeConfig.getDefaultTtlDuration())
                .toSeconds();
    }

    @MonitoredFunction
    public void save(final K key,
                     final V value) {
        save(key, value, Constants.DEFAULT_BIN, getTtlSeconds(), RecordExistsAction.REPLACE);
    }

    public Map<String, V> scan() {
        Map<String, V> scanResult = new HashMap<>();
        try {
            final var queryPolicy = aerospikeClient.getQueryPolicyDefault();
            final var statement = new Statement();
            statement.setNamespace(aerospikeConfig.getNamespace());
            statement.setSetName(aerospikeSet.name());
            statement.setBinNames(Constants.DEFAULT_BIN);
            final var recordSet = aerospikeClient.query(queryPolicy, statement);
            while (recordSet.next()) {
                final var value = recordSet.getRecord()
                        .getString(Constants.DEFAULT_BIN);
                scanResult.put(recordSet.getKey().userKey.toString(), MapperUtils.deserialize(value, valueClazz));
            }
        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.AEROSPIKE_SCAN_ERROR, e, Map.of());
        }
        return scanResult;
    }

    @MonitoredFunction
    public void strictSave(final K key,
                           final V value) {
        save(key, value, Constants.DEFAULT_BIN, getTtlSeconds(), RecordExistsAction.CREATE_ONLY);
    }

    //todo {PK} accept retryer config
    @MonitoredFunction
    public void strictSaveWithRetries(final K key,
                                      final V value) {
        try {
            aerospikeStrictStoreRetryer.call(() -> {
                save(key, value, Constants.DEFAULT_BIN, getTtlSeconds(), RecordExistsAction.CREATE_ONLY);
                return null;
            });
        } catch (final ExecutionException e) {
            throw KaizenException.create(KaizenResponseCode.EXECUTION_EXCEPTION, e, Map.of("key", key));
        } catch (final RetryException e) {
            throw KaizenException.create(KaizenResponseCode.RETRY_EXCEPTION, e, Map.of("key", key));
        }
    }

    private Bin getBin(final String bin,
                       final V value) {
        return new Bin(bin, MapperUtils.serializeToString(value));
    }

    private Key getKey(final K key) {
        return new Key(aerospikeConfig.getNamespace(), aerospikeSet.name(), key.getKey());
    }

    @MonitoredFunction
    private void save(final K key,
                      final V value,
                      final String bin,
                      final int expirySeconds,
                      final RecordExistsAction recordExistsAction) {

        try {
            final var writePolicy = new WritePolicy(aerospikeClient.getWritePolicyDefault());
            writePolicy.recordExistsAction = recordExistsAction;
            writePolicy.expiration = expirySeconds;
            aerospikeClient.put(writePolicy, getKey(key), getBin(bin, value));

        } catch (final Exception e) {
            throw KaizenException.propagate(KaizenResponseCode.AEROSPIKE_WRITE_ERROR, e,
                    Map.of(Constants.MESSAGE, "Error while writing data to AeroSpike", Constants.KEY, key.getKey(),
                            Constants.VALUE, value));
        }
    }
}
