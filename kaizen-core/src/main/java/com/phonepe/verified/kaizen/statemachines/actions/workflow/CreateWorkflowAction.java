package com.phonepe.verified.kaizen.statemachines.actions.workflow;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.WorkflowInitRequestContext;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.statemachines.actions.BaseTransitionAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class CreateWorkflowAction extends BaseTransitionAction<TransitionState, TransitionEvent> {

    private final ProfileService profileService;

    private final WorkflowRepository workflowRepository;

    private final EventIngestionCommand eventIngestionCommand;

    private final Provider<WorkflowContextStore> workflowContextStore;

    @Override
    protected void performTransition(final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var verificationInitRequest = stateContext.getExtendedState()
                .get(WorkflowInitRequestContext.class, WorkflowInitRequestContext.class);

        final var workflowId = stateContext.getExtendedState()
                .get(Fields.workflowId, String.class);

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        Objects.requireNonNull(verificationInitRequest);
        Objects.requireNonNull(workflowId);
        Objects.requireNonNull(userDetails);

        final var profile = profileService.get(verificationInitRequest.getProfileId(), false);

        final var storedWorkflow = BuildUtils.toStoredWorkflow(verificationInitRequest, profile, workflowId,
                stateContext, userDetails);

        workflowRepository.save(storedWorkflow);

        eventIngestionCommand.workflowTransitionEvent(storedWorkflow, profile, null, null,
                EventType.VERIFICATION_WORKFLOW_INIT, null);

        workflowContextStore.get()
                .createAndPersistWorkflowContext(storedWorkflow);
    }
}
