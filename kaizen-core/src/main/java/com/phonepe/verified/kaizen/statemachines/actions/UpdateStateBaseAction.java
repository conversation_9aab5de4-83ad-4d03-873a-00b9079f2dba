package com.phonepe.verified.kaizen.statemachines.actions;

import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.queue.messages.eventingestion.ActionUpdateEventIngestionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@AllArgsConstructor
public abstract class UpdateStateBaseAction extends BaseTransitionAction<String, String> {

    private final ActionService actionService;
    private final ActionRepository actionRepository;
    private final Provider<WorkflowContextStore> workflowContextStore;
    private final Provider<EventIngestionActor> eventIngestionActorProvider;

    @Override
    @SneakyThrows
    protected void performTransition(final StateContext<String, String> stateContext) {

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        final var actionId = stateContext.getExtendedState()
                .get(Fields.actionId, String.class);

        Objects.requireNonNull(userDetails);
        Objects.requireNonNull(actionId);

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var previousState = storedAction.getCurrentState();

        final var previousEvent = storedAction.getCurrentEvent();

        preTransition(storedAction, stateContext);

        storedAction.setCurrentState(stateContext.getTarget()
                .getId());
        storedAction.setCurrentEvent(stateContext.getEvent());
        storedAction.setLastUpdatedBy(userDetails.getUserId());
        storedAction.setLastUpdaterType(UpdaterType.valueOf(userDetails.getUserType()
                .name()));

        actionRepository.save(storedAction, savedAction -> {
            transition(storedAction, stateContext);
            workflowContextStore.get()
                    .updateActionContextInWorkflowContext(storedAction);
            return savedAction;
        });

        eventIngestionActorProvider.get()
                .publish(EventIngestionMessage.builder()
                        .actionId(actionId)
                        .eventType(EventType.ACTION_UPDATE)
                        .actionUpdateEventIngestionMessage(ActionUpdateEventIngestionMessage.builder()
                                .previousState(previousState)
                                .previousEvent(previousEvent)
                                .build())
                        .build());

        postTransition(storedAction, stateContext);
    }

    protected void preTransition(final StoredAction storedAction,
                                 final StateContext<String, String> stateContext) {
        // NOOP
    }

    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {
        // NOOP
    }

    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {
        // NOOP
    }
}
