package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.response.TemplateInitResponse;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewResponse;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.data.common.MailboxApiName;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.requests.details.WorkflowDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.WorkflowStepsDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.AbortWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.FilterAllWorkflowIdsRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.LatestWorkflowDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.LatestWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.PurgeWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowAddOnRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowInitRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowStatusRequest;
import com.phonepe.verified.kaizen.models.responses.ApiResponse;
import com.phonepe.verified.kaizen.models.responses.WorkflowClientCallback;
import com.phonepe.verified.kaizen.models.responses.details.WorkflowDetailsResponse;
import com.phonepe.verified.kaizen.models.responses.details.WorkflowStepsDetailsResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.LatestWorkflowResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowAddOnResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowInfo;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowInitAsyncResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowInitResponse;
import com.phonepe.verified.kaizen.queue.actors.AbortWorkflowActor;
import com.phonepe.verified.kaizen.queue.actors.PurgeWorkflowActor;
import com.phonepe.verified.kaizen.queue.actors.SectionSubmitActor;
import com.phonepe.verified.kaizen.queue.actors.SkipWorkflowStepActor;
import com.phonepe.verified.kaizen.queue.messages.AbortWorkflowMessage;
import com.phonepe.verified.kaizen.queue.messages.PurgeWorkflowMessage;
import com.phonepe.verified.kaizen.queue.messages.SectionSubmitMessage;
import com.phonepe.verified.kaizen.queue.messages.SkipWorkflowStepMessage;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.DetailsService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.UserDetailsUtils;
import io.appform.functionmetrics.MonitoredFunction;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/workflow")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Workflow", description = "Workflow Related APIs")
public class WorkflowResource {

    public static final String RESOURCE_WORKFLOW = "WORKFLOW";
    public static final String OPERATION_WORKFLOW_DETAILS = "DETAILS";
    private static final String OPERATION_WORKFLOW_INIT = "INIT";
    private static final String OPERATION_WORKFLOW_ADD_ON = "ADD_ON";
    private static final String OPERATION_WORKFLOW_LATEST = "LATEST";
    private static final String OPERATION_WORKFLOW_STEP_DETAILS = "STEP_DETAILS";
    private static final String OPERATION_WORKFLOW_ALL = "ALL";
    private static final String OPERATION_WORKFLOW_FILTER = "FILTER";
    private static final String OPERATION_WORKFLOW_ABORT = "ABORT";
    private static final String OPERATION_WORKFLOW_PURGE = "PURGE";
    private static final String OPERATION_WORKFLOW_STEP_INVALIDATE = "INVALIDATE_WORKFLOW_STEP";

    private final DetailsService detailsService;

    private final WorkflowService workflowService;

    private final ProfileService profileService;

    private final AbortWorkflowActor abortWorkflowActor;

    private final PurgeWorkflowActor purgeWorkflowActor;

    private final SkipWorkflowStepActor skipWorkflowStepActor;

    private final SectionSubmitActor sectionSubmitActor;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final SessionManagementService sessionManagementService;

    private final AuthZService authZService;

    @POST
    @Path("/init")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Initiate workflow for given user")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public WorkflowInitResponse init(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                     @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                     @Valid @NotNull final WorkflowInitRequest workflowInitRequest) {

        authZService.authorizeOperationForTenant(authZService.getTenant(workflowInitRequest.getProfileKey()
                .getOrganization(), workflowInitRequest.getProfileKey()
                .getNamespace()), RESOURCE_WORKFLOW, OPERATION_WORKFLOW_INIT, serviceUserPrincipal);

        return workflowService.init(workflowInitRequest, UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal));

    }

    @POST
    @SneakyThrows
    @Path("/init/async")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Initiate workflow for given user in async")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public WorkflowInitAsyncResponse initAsync(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                               @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                               @Valid @NotNull final WorkflowInitRequest workflowInitRequest) {

        authZService.authorizeOperationForTenant(authZService.getTenant(workflowInitRequest.getProfileKey()
                .getOrganization(), workflowInitRequest.getProfileKey()
                .getNamespace()), RESOURCE_WORKFLOW, OPERATION_WORKFLOW_INIT, serviceUserPrincipal);

        final var requestId = workflowService.initAsync(workflowInitRequest,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal));

        return WorkflowInitAsyncResponse.builder()
                .requestId(requestId)
                .build();
    }

    @POST
    @SneakyThrows
    @Path("/status")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Get status of workflow")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public WorkflowClientCallback initStatus(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                             @Valid @NotNull final WorkflowStatusRequest workflowStatusRequest) {

        authZService.authorizeOperationForTenant(
                authZService.getTenant(workflowStatusRequest.getOrganization(), workflowStatusRequest.getNamespace()),
                RESOURCE_WORKFLOW, OPERATION_WORKFLOW_INIT, serviceUserPrincipal);

        return workflowService.checkInitWorkflowStatus(workflowStatusRequest.getRequestId());
    }

    @POST
    @Path("/add-on")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Initiate add-on workflow for given user")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public WorkflowAddOnResponse initAddOn(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                           @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                           @Valid @NotNull final WorkflowAddOnRequest workflowAddOnRequest) {

        authZService.authorizeOperationForTenant(authZService.getTenant(workflowAddOnRequest.getProfileKey()
                .getOrganization(), workflowAddOnRequest.getProfileKey()
                .getNamespace()), RESOURCE_WORKFLOW, OPERATION_WORKFLOW_ADD_ON, serviceUserPrincipal);

        return workflowService.initAddOn(workflowAddOnRequest,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal));
    }

    @POST
    @Path("/latest")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Get latest workflow details for given entity and profile")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public LatestWorkflowResponse getLatestWorkflow(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                    @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                    @Valid @NotNull final LatestWorkflowRequest latestWorkflowRequest) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenant(latestWorkflowRequest.getOrganization(), latestWorkflowRequest.getNamespace()),
                RESOURCE_WORKFLOW, OPERATION_WORKFLOW_LATEST, serviceUserPrincipal);

        return workflowService.getLatestWorkflow(latestWorkflowRequest);
    }

    @POST
    @Path("/details")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Get requested details for given workflow")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public WorkflowDetailsResponse getDetails(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                              @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                              @Valid @NotNull final WorkflowDetailsRequest workflowDetailsRequest) {

        // Check if user has permission for WF details API

        authZService.authorizeOperationForTenant(
                authZService.getTenantFromWorkflow(workflowDetailsRequest.getWorkflowId()), RESOURCE_WORKFLOW,
                OPERATION_WORKFLOW_DETAILS, serviceUserPrincipal);

        // Check if user has permission for WF details for each required detail
        authorizeDetailsRequest(workflowDetailsRequest, serviceUserPrincipal);

        return detailsService.getDetails(workflowDetailsRequest);
    }

    @POST
    @Path("/latest/details")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Get requested details of latest workflow for profile")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public WorkflowDetailsResponse getDetailsOfLatestWorkflowOfAProfile(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                                        @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                                        @Valid @NotNull final LatestWorkflowDetailsRequest latestWorkflowDetailsRequest) {

        // Check if user has permission for WF details API
        authZService.authorizeOperationForTenant(authZService.getTenant(latestWorkflowDetailsRequest.getOrganization(),
                        latestWorkflowDetailsRequest.getNamespace()), RESOURCE_WORKFLOW, OPERATION_WORKFLOW_LATEST,
                serviceUserPrincipal);

        // Check if user has permission for WF details for each required detail
        Optional.ofNullable(latestWorkflowDetailsRequest.getRequiredDetails())
                .orElse(List.of())
                .forEach(requiredDetail -> authZService.authorizeOperationForTenant(
                        authZService.getTenant(latestWorkflowDetailsRequest.getOrganization(),
                                latestWorkflowDetailsRequest.getNamespace()), OPERATION_WORKFLOW_DETAILS,
                        requiredDetail.getType()
                                .name(), serviceUserPrincipal));

        return detailsService.getDetails(latestWorkflowDetailsRequest);
    }

    @POST
    @Path("/steps/details")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Get workflow steps for given workflow id")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public List<WorkflowStepsDetailsResponse> getWorkflowSteps(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                               @Parameter(hidden = true) @GandalfUserContext final UserDetails userDetails,
                                                               @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                               @Valid @NotNull final WorkflowStepsDetailsRequest workflowStepsDetailsRequest,
                                                               @QueryParam("fetchActionLevelDetails") @DefaultValue("false") final boolean fetchActionLevelDetails) {

        authZService.authorizeOperationForTenant(
                authZService.getTenantFromWorkflow(workflowStepsDetailsRequest.getWorkflowId()), RESOURCE_WORKFLOW,
                OPERATION_WORKFLOW_STEP_DETAILS, serviceUserPrincipal);

        return detailsService.getDetails(workflowStepsDetailsRequest, fetchActionLevelDetails);
    }

    @DELETE
    @Authorize
    @PermitAll
    @SneakyThrows
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/{workflowId}/step/{profileStepMappingId}/{screenMappingId}/skip/{intent}/{componentKitVersion}")
    @Operation(summary = "Skip Workflow Step")
    @AuthZ(gandalfPermission = "skipWorkflowStep", olympusPermission = OlympusPermissionNames.SKIP_WORKFLOW_STEP)
    public Response skipWorkflowStep(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                     @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                     @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken,
                                     @PathParam("intent") @NotEmpty final String intent,
                                     @PathParam("workflowId") @NotEmpty final String workflowId,
                                     @PathParam("componentKitVersion") final long componentKitVersion,
                                     @PathParam("profileStepMappingId") @NotEmpty final String profileStepMappingId,
                                     @PathParam("screenMappingId") @NotEmpty final String screenMappingId) {

        authZService.authorizeUserWithWorkflowId(workflowId, requestInfo, userDetails);

        authorizeAndValidateSession(workflowId, requestInfo, userDetails, sessionToken);

        publishMessageToSkipWorkflowStepActor(requestInfo, userDetails, intent, workflowId, componentKitVersion,
                profileStepMappingId, screenMappingId);

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.WORKFLOW_STEP_SKIP)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.WORKFLOW_STEP_SKIP)
                        .toMilliseconds())
                .build();
    }

    @POST
    @Path("/all")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Get all workflow IDs for entity and profileKey")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public Set<String> getAllWorkflowIds(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                         @Parameter(hidden = true) @GandalfUserContext final UserDetails userDetails,
                                         @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                         @Valid @NotNull final LatestWorkflowRequest workflowRequest) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenant(workflowRequest.getOrganization(), workflowRequest.getNamespace()),
                RESOURCE_WORKFLOW, OPERATION_WORKFLOW_ALL, serviceUserPrincipal);

        return workflowService.getAllWorkflowIds(workflowRequest);
    }

    @POST
    @Path("/filter")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Filter all workflow IDs for given timestamp range and profile parameters")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public Set<String> filterAllWorkflowIds(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                            @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                            @Valid @NotNull final FilterAllWorkflowIdsRequest workflowRequest) {

        authZService.authorizeOperationForTenant(
                authZService.getTenant(workflowRequest.getOrganization(), workflowRequest.getNamespace()),
                RESOURCE_WORKFLOW, OPERATION_WORKFLOW_FILTER, serviceUserPrincipal);

        return workflowService.filterAllWorkflowIds(workflowRequest)
                .getWorkflowInfoList()
                .stream()
                .map(WorkflowInfo::getWorkflowId)
                .collect(Collectors.toSet());
    }

    @DELETE
    @SneakyThrows
    @Path("/abort")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Abort given workflow")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void abort(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                      @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                      @Valid @NotNull final AbortWorkflowRequest abortWorkflowRequest) {

        authZService.authorizeOperationForTenant(
                authZService.getTenantFromWorkflow(abortWorkflowRequest.getWorkflowId()), RESOURCE_WORKFLOW,
                OPERATION_WORKFLOW_ABORT, serviceUserPrincipal);

        abortWorkflowActor.publish(AbortWorkflowMessage.builder()
                .abortWorkflowRequest(abortWorkflowRequest)
                .userDetails(UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal))
                .build());
    }

    @DELETE
    @SneakyThrows
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Invalidate this and the later steps")
    @Path("/{workflowId}/step/{profileStepMappingId}/invalidate")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void invalidate(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                           @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                           @PathParam("workflowId") @NotEmpty final String workflowId,
                           @PathParam("profileStepMappingId") @NotEmpty final String profileStepMappingId) {

        authZService.authorizeOperationForTenant(authZService.getTenantFromWorkflow(workflowId), RESOURCE_WORKFLOW,
                OPERATION_WORKFLOW_STEP_INVALIDATE, serviceUserPrincipal);

        workflowService.invalidateWorkflowFromProfileStepMappingId(workflowId, profileStepMappingId,
                UserDetailsUtils.getGandalfUserDetails(null, serviceUserPrincipal));
    }

    @GET
    @Authorize
    @PermitAll
    @SneakyThrows
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/{workflowId}/{intent}/{componentKitVersion}/template")
    @Operation(summary = "Fetch template for given workflowId, intent and componentKitVersion")
    @AuthZ(gandalfPermission = "getTemplate", olympusPermission = OlympusPermissionNames.GET_TEMPLATE)
    public ApiResponse<TemplateInitResponse> getTemplate(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                         @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken,
                                                         @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                                         @NotEmpty @PathParam("workflowId") final String workflowId,
                                                         @NotEmpty @PathParam("intent") final String intent,
                                                         @PathParam("componentKitVersion") final long componentKitVersion,
                                                         @QueryParam("showSummaryView") @DefaultValue("false") final boolean showSummaryView) {

        authZService.authorizeUserWithWorkflowId(workflowId, requestInfo, userDetails);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        final var templateInitResponse = workflowService.getTemplate(workflowId, intent, componentKitVersion,
                showSummaryView);

        return ApiResponse.<TemplateInitResponse>builder()
                .data(templateInitResponse)
                .success(true)
                .build();
    }


    @POST
    @Authorize
    @PermitAll
    @SneakyThrows
    @Path("/section/submit")
    @Operation(summary = "Section submit request")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AuthZ(gandalfPermission = "sectionSubmit", olympusPermission = OlympusPermissionNames.SECTION_SUBMIT)
    public Response sectionSubmit(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                  @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                  @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken,
                                  @Valid @NotNull final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest) {

        authZService.authorizeUserWithWorkflowId(shadowV2SectionSubmitRequest.getWorkflowId(), requestInfo,
                userDetails);

        sessionManagementService.validateSessionAndThrowException(shadowV2SectionSubmitRequest.getWorkflowId(),
                requestInfo, userDetails, sessionToken);

        sectionSubmitActor.publish(SectionSubmitMessage.builder()
                .requestInfo(requestInfo)
                .shadowV2SectionSubmitRequest(shadowV2SectionSubmitRequest)
                .userDetails(userDetails)
                .build());

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.SECTION_SUBMIT)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.SECTION_SUBMIT)
                        .toMilliseconds())
                .build();
    }

    @GET
    @Authorize
    @PermitAll
    @SneakyThrows
    @Path("/{workflowId}/summary/{intent}/{componentKitVersion}")
    @Operation(summary = "Fetch summary for given workflowId")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @AuthZ(gandalfPermission = "getSummary", olympusPermission = OlympusPermissionNames.GET_SUMMARY)
    public ApiResponse<SummaryViewResponse> getSummary(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                       @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken,
                                                       @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                                       @NotEmpty @PathParam("workflowId") final String workflowId,
                                                       @NotEmpty @PathParam("intent") final String intent,
                                                       @PathParam("componentKitVersion") final long componentKitVersion) {

        authZService.authorizeUserWithWorkflowId(workflowId, requestInfo, userDetails);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        final var summaryViewResponse = profileService.getSummaryView(workflowId, intent, componentKitVersion);

        return ApiResponse.<SummaryViewResponse>builder()
                .data(summaryViewResponse)
                .success(true)
                .build();
    }

    @DELETE
    @SneakyThrows
    @Path("/purge")
    @Operation(summary = "Purge given workflow")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void purgeWorkflow(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                              @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                              @Valid @NotNull final PurgeWorkflowRequest purgeWorkflowRequest) {

        // TODO : (Future Scope) Check if lock on workflow is required

        authZService.authorizeOperationForTenant(
                authZService.getTenantFromWorkflow(purgeWorkflowRequest.getWorkflowId()), RESOURCE_WORKFLOW,
                OPERATION_WORKFLOW_PURGE, serviceUserPrincipal);

        purgeWorkflowActor.publish(PurgeWorkflowMessage.builder()
                .purgeWorkflowRequest(purgeWorkflowRequest)
                .userDetails(UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal))
                .build());
    }

    @MonitoredFunction
    private void authorizeAndValidateSession(final String workflowId,
                                             final RequestInfo requestInfo,
                                             final UserDetails userDetails,
                                             final String sessionToken) {

        authZService.authorizeUserWithWorkflowId(workflowId, requestInfo, userDetails);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

    }

    @SneakyThrows
    @MonitoredFunction
    private void publishMessageToSkipWorkflowStepActor(final RequestInfo requestInfo,
                                                       final UserDetails userDetails,
                                                       final String intent,
                                                       final String workflowId,
                                                       final long componentKitVersion,
                                                       final String profileStepMappingId,
                                                       final String screenMappingId) {
        skipWorkflowStepActor.publish(SkipWorkflowStepMessage.builder()
                .intent(intent)
                .workflowId(workflowId)
                .userDetails(userDetails)
                .requestInfo(requestInfo)
                .apiVersion(ApiVersion.V1)
                .componentKitVersion(componentKitVersion)
                .profileStepMappingId(profileStepMappingId)
                .screenMappingId(screenMappingId)
                .build());
    }

    private void authorizeDetailsRequest(final WorkflowDetailsRequest workflowDetailsRequest,
                                         final ServiceUserPrincipal serviceUserPrincipal) {

        final var olympusTenant = authZService.getTenantFromWorkflow(workflowDetailsRequest.getWorkflowId());

        if (Objects.nonNull(workflowDetailsRequest.getRequiredDetails())) {

            workflowDetailsRequest.getRequiredDetails()
                    .forEach(requiredDetail -> authZService.authorizeOperationForTenant(olympusTenant,
                            OPERATION_WORKFLOW_DETAILS, requiredDetail.getType()
                                    .name(), serviceUserPrincipal));
        }
    }
}
