package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.models.response.GenericError;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.platform.sentinel.HurdleType;
import com.phonepe.platform.sentinel.SentinelClient;
import com.phonepe.platform.sentinel.SentinelClientError;
import com.phonepe.platform.sentinel.elements.AppIdElement;
import com.phonepe.platform.sentinel.elements.DeviceIdElement;
import com.phonepe.platform.sentinel.elements.HurdleElementType;
import com.phonepe.platform.sentinel.elements.PhoneNumberElement;
import com.phonepe.platform.sentinel.elements.RequestIdElement;
import com.phonepe.platform.sentinel.elements.UserIdElement;
import com.phonepe.platform.sentinel.hurdleinfo.ExternallyEvaluatedHurdleInfo;
import com.phonepe.platform.sentinel.hurdleinfo.data.HurdleInfoDataType;
import com.phonepe.platform.sentinel.hurdleinfo.data.users.OTPV4HurdleInfoData;
import com.phonepe.platform.sentinel.request.ExecuteHurdleRequest;
import com.phonepe.platform.sentinel.request.init.WorkflowInitRequest;
import com.phonepe.platform.sentinel.request.init.atomichurdlemeta.ExternallyEvaluatedHurdleMeta;
import com.phonepe.platform.sentinel.request.otp.MultiChannelOtpRequest;
import com.phonepe.platform.sentinel.request.otp.OtpRequestType;
import com.phonepe.platform.sentinel.response.InstanceState;
import com.phonepe.platform.sentinel.response.otp.OtpFetchResponse;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.OtpActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.OtpProviderType;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.requests.OtpVerificationRequest;
import com.phonepe.verified.kaizen.queue.actors.RevolverCallbackActor;
import com.phonepe.verified.kaizen.queue.messages.RevolverCallbackMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.OtpService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.aerospike.commands.OtpTokenDetailsStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.OtpTokenDetails;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.OtpTokenKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredOtpHurdleActionMetaData;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import javax.ws.rs.BadRequestException;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class OtpServiceImpl implements OtpService {

    public static final String SENTINEL_ERROR_CODE = "SEN1001";

    public static final String USER_OTP_BLOCKED_SENTINEL_ERROR_CODE = "SEN1100";

    public static final String OTP_LIMIT_EXHAUSTED_SENTINEL_ERROR_CODE = "SEN1101";

    private final KaizenConfig config;

    private final ActionService actionService;

    private final SentinelClient sentinelClient;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final EventIngestionCommand eventIngestionCommand;

    private final RevolverCallbackActor revolverCallbackActor;

    private final ActionMetadataServiceImpl actionMetadataService;

    private final OtpTokenDetailsStoreCommand otpTokenDetailsStoreCommand;

    private final DynamicUiResponseService dynamicUiResponseService;

    @Override
    public void triggerOtpHurdle(final String actionId) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedAction.getWorkflowStepId());

        final var otpActionContextOptional = triggerOtpAndGetOtpActionContext(storedAction, uiRequestContext);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        otpActionContextOptional.ifPresent(
                otpActionContext -> dynamicUiResponseService.sendResponseToUi(uiRequestContext,
                        otpActionContext.getOtpResponseMap(), storedWorkflowStep.getWorkflowId()));
    }

    @Override
    public void triggerResendOtp(final StoredAction storedAction) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedAction.getWorkflowStepId());

        final var otpTokenDetails = otpTokenDetailsStoreCommand.get(OtpTokenKey.builder()
                .actionId(storedAction.getActionId())
                .build());

        final var otpTokenResponse = fetchOtpTokenResponse(otpTokenDetails.getInstanceId(),
                storedWorkflow.getEntityId(), uiRequestContext.getRequestInfo());

        if (!StringUtils.isEmpty(otpTokenResponse.getCode())) {

            throw new BadRequestException(Response.status(Status.BAD_REQUEST)
                    .entity(GenericError.builder()
                            .code(Constants.SENTINEL_ERROR_CODE_RESPONSE.getOrDefault(otpTokenResponse.getCode(),
                                    Constants.SOMETHING_WENT_WRONG_CODE))
                            .message(Constants.SENTINEL_ERROR_MESSAGE_RESPONSE.getOrDefault(otpTokenResponse.getCode(),
                                    Constants.SOMETHING_WENT_WRONG_MESSAGE))
                            .build())
                    .build());
        }

        otpTokenDetailsStoreCommand.save(OtpTokenKey.builder()
                .actionId(storedAction.getActionId())
                .build(), OtpTokenDetails.builder()
                .instanceId(otpTokenDetails.getInstanceId())
                .key(otpTokenDetails.getKey())
                .token(otpTokenResponse.getData()
                        .getOtpToken())
                .success(true)
                .build());
    }

    @SneakyThrows
    private Optional<OtpActionContext> triggerOtpAndGetOtpActionContext(final StoredAction storedAction,
                                                                        final UiRequestContext uiRequestContext) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var otpActionContext = (OtpActionContext) actionService.extractStepActionContext(
                storedAction.getActionId(), profileStep.getProfileScreenConfig());

        final var requestOtpResponse = requestOtp(storedWorkflow, uiRequestContext, otpActionContext.getOtpProfile());

        if (requestOtpResponse.isSuccess()) {

            otpTokenDetailsStoreCommand.save(OtpTokenKey.builder()
                    .actionId(storedAction.getActionId())
                    .build(), requestOtpResponse);

            eventIngestionCommand.otpGenerationEvent(profile, profileStep, storedAction, storedWorkflow,
                    (ShadowV2UiRequestContext) uiRequestContext, null, OtpProviderType.SENTINEL,
                    EventType.OTP_GENERATION_SUCCEED);

            return Optional.of(otpActionContext);
        }

        // Storing Sentinel error code in token
        final var sentinelErrorCode = requestOtpResponse.getToken();

        final var auxiliaryStateMachineTransitionMap = new HashMap<>();

        eventIngestionCommand.otpGenerationEvent(profile, profileStep, storedAction, storedWorkflow,
                (ShadowV2UiRequestContext) uiRequestContext, sentinelErrorCode, OtpProviderType.SENTINEL,
                EventType.OTP_GENERATION_FAILED);

        if (USER_OTP_BLOCKED_SENTINEL_ERROR_CODE.equals(sentinelErrorCode)) {

            auxiliaryStateMachineTransitionMap.put(ActionFailureErrorCode.class,
                    ActionFailureErrorCode.OTP_GENERATION_BANNED);

            triggerOtpCompletionEvent(storedAction, Events.FAIL_OTP_GENERATION, auxiliaryStateMachineTransitionMap);

        } else {

            revolverCallbackActor.publish(RevolverCallbackMessage.builder()
                    .requestId(uiRequestContext.getRequestInfo()
                            .getRequestId())
                    .status(HttpStatus.SC_BAD_REQUEST)
                    .data(GenericError.builder()
                            .code(Constants.SENTINEL_ERROR_CODE_RESPONSE.getOrDefault(sentinelErrorCode,
                                    Constants.SOMETHING_WENT_WRONG_CODE))
                            .message(Constants.SENTINEL_ERROR_MESSAGE_RESPONSE.getOrDefault(sentinelErrorCode,
                                    Constants.SOMETHING_WENT_WRONG_MESSAGE))
                            .build())
                    .build());
        }
        return Optional.empty();
    }

    @Override
    @SneakyThrows
    public void verifyOtpHurdle(final OtpVerificationRequest otpVerificationRequest,
                                final String actionId,
                                final String intent,
                                final long componentKitVersion,
                                final RequestInfo requestInfo) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var otpTokenDetails = otpTokenDetailsStoreCommand.get(OtpTokenKey.builder()
                .actionId(actionId)
                .build());

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedAction.getWorkflowStepId());

        final var otpVerifyResponse = sentinelClient.getSentinel()
                .executeHurdles(uiRequestContext.getRequestInfo(), otpTokenDetails.getInstanceId(),
                        ExecuteHurdleRequest.builder()
                                .hurdleInfoSet(Set.of(ExternallyEvaluatedHurdleInfo.builder()
                                        .key(otpTokenDetails.getKey())
                                        .data(OTPV4HurdleInfoData.builder()
                                                .otp(otpVerificationRequest.getOtp())
                                                .token(otpTokenDetails.getToken())
                                                .userId(storedWorkflow.getEntityId())
                                                .otpReferenceId(uiRequestContext.getRequestInfo()
                                                        .getDeviceFingerprint())
                                                .build())
                                        .build()))
                                .build());

        final var sectionInputData = ((ShadowV2UiRequestContext) uiRequestContext).getSectionInputData();

        final var apiVersion = uiRequestContext.getApiVersion();

        final var shadowV2UiRequestContext = workflowStepService.getUiRequestContextAndSaveNewUiRequestContextWithExistingRequestDetails(
                storedWorkflowStep.getWorkflowStepId(), sectionInputData, requestInfo, componentKitVersion,
                storedWorkflow.getEntityId(), intent, apiVersion, storedWorkflow.getWorkflowId());

        if (InstanceState.SUCCESS.equals(otpVerifyResponse.getInstanceState())) {

            final var storedOTPHurdleActionMetaData = StoredOtpHurdleActionMetaData.builder()
                    .actionId(storedAction.getActionId())
                    .otpReferenceId(otpTokenDetails.getInstanceId())
                    .build();

            actionMetadataService.save(storedOTPHurdleActionMetaData);

            eventIngestionCommand.otpCompletionStatusEvent(profile, profileStep, storedAction, storedWorkflow,
                    shadowV2UiRequestContext, null, OtpProviderType.SENTINEL, EventType.OTP_VERIFICATION_SUCCEED,
                    otpTokenDetails.getInstanceId());

            triggerOtpCompletionEvent(storedAction, Events.OTP_HURDLE_COMPLETE, null);

        } else {
            // Storing Sentinel error code in token
            final var sentinelValidationResponse = otpVerifyResponse.getHurdleResponses()
                    .stream()
                    .filter(hurdleResponse -> hurdleResponse.getHurdleType()
                            .name()
                            .equalsIgnoreCase(HurdleType.EXTERNALLY_EVALUATED_TEXT))
                    .findFirst();

            final var sentinelErrorCode = sentinelValidationResponse.isPresent()
                                          ? sentinelValidationResponse.get()
                                                  .getHurdleValidationResp()
                                                  .getErrorCode()
                                          : SENTINEL_ERROR_CODE;

            final var auxiliaryStateMachineTransitionMap = new HashMap<>();

            eventIngestionCommand.otpCompletionStatusEvent(profile, profileStep, storedAction, storedWorkflow,
                    shadowV2UiRequestContext, sentinelErrorCode, OtpProviderType.SENTINEL,
                    EventType.OTP_VERIFICATION_FAILED, otpTokenDetails.getInstanceId());

            if (OTP_LIMIT_EXHAUSTED_SENTINEL_ERROR_CODE.equals(sentinelErrorCode)) {

                auxiliaryStateMachineTransitionMap.put(ActionFailureErrorCode.class,
                        ActionFailureErrorCode.OTP_ATTEMPTS_EXHAUSTED);

                triggerOtpCompletionEvent(storedAction, Events.FAIL_OTP_VERIFICATION,
                        auxiliaryStateMachineTransitionMap);

            } else {

                revolverCallbackActor.publish(RevolverCallbackMessage.builder()
                        .requestId(shadowV2UiRequestContext.getRequestInfo()
                                .getRequestId())
                        .status(HttpStatus.SC_BAD_REQUEST)
                        .data(GenericError.builder()
                                .code(Constants.SENTINEL_ERROR_CODE_RESPONSE.getOrDefault(sentinelErrorCode,
                                        Constants.SOMETHING_WENT_WRONG_CODE))
                                .message(Constants.SENTINEL_ERROR_MESSAGE_RESPONSE.getOrDefault(sentinelErrorCode,
                                        Constants.SOMETHING_WENT_WRONG_MESSAGE))
                                .build())
                        .build());
            }
        }
    }

    private void triggerOtpCompletionEvent(final StoredAction storedAction,
                                           final String otpCompletionEvent,
                                           final HashMap<Object, Object> auxiliaryStateMachineTransitionMap) {

        // todo{Ankit}:: Change PVCORE_SYSTEM_USER to UserId of caller
        actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                storedAction.getStateMachineVersion(), otpCompletionEvent, Constants.EMPTY_TRANSITION_CONTEXT,
                Constants.PVCORE_SYSTEM_USER, auxiliaryStateMachineTransitionMap);
    }

    private OtpTokenDetails requestOtp(final StoredWorkflow storedWorkflow,
                                       final UiRequestContext uiRequestContext,
                                       final String profile) {

        final var requestInfo = uiRequestContext.getRequestInfo();

        final var userIdHurdleElement = new UserIdElement(storedWorkflow.getEntityId());

        final var deviceIdHurdleElement = new DeviceIdElement(requestInfo.getDeviceFingerprint());

        final var phoneNumberHurdleElement = new PhoneNumberElement(storedWorkflow.getPhoneNumber());
        final var requestIdHurdleElement = new RequestIdElement(requestInfo.getRequestId());

        final var appIDHurdleElement = new AppIdElement(requestInfo.getMerchantAppId());
        final var hurdleElements = Set.of(userIdHurdleElement, deviceIdHurdleElement, phoneNumberHurdleElement,
                requestIdHurdleElement, appIDHurdleElement);

        final var sentinelWorkflowInitRequest = WorkflowInitRequest.builder()
                .workflowType(HurdleInfoDataType.OTP_V4.name())
                .workflowExpiryInSeconds((int) config.getSentinelWorkFlowExpiryDuration()
                        .toSeconds())
                .hurdleElements(hurdleElements)
                .hurdleMetas(Set.of(ExternallyEvaluatedHurdleMeta.builder()
                        .name("otpV4Hurdle")
                        .data(Map.of("otpProfile", profile, "HURDLE_ELEMENT_TYPES",
                                List.of(HurdleElementType.PHONE_NUMBER_TEXT)))
                        .build()))
                .build();

        final var sentinelWorkflowInitResponse = sentinelClient.getSentinel()
                .initWorkflow(uiRequestContext.getRequestInfo(), sentinelWorkflowInitRequest);

        if (sentinelWorkflowInitResponse.getInstanceResponseV2()
                .getErrorCode() != null || sentinelWorkflowInitResponse.getInstanceResponseV2()
                .getInstanceState()
                .equals(InstanceState.FAILED) || sentinelWorkflowInitResponse.getInstanceResponseV2()
                .getInstanceState()
                .equals(InstanceState.CANCELLED)) {

            return OtpTokenDetails.builder()
                    .success(false)
                    .token(sentinelWorkflowInitResponse.getInstanceResponseV2()
                            .getErrorCode())
                    .key("error while fetching otptoken")
                    .instanceId("error while fetching otptoken")
                    .build();
        }

        final var externallyEvaluatedResponses = sentinelWorkflowInitResponse.getInstanceResponseV2()
                .getHurdleResponses()
                .stream()
                .filter(hurdleResponse -> hurdleResponse.getHurdleType()
                        .equalsIgnoreCase(HurdleType.EXTERNALLY_EVALUATED_TEXT))
                .findFirst();

        if (externallyEvaluatedResponses.isEmpty()) {
            return OtpTokenDetails.builder()
                    .token(SENTINEL_ERROR_CODE)
                    .key("Error while triggering Sentinel Workflow Init.")
                    .success(false)
                    .instanceId("error while triggering Sentinel workflow init")
                    .build();
        }

        final var key = externallyEvaluatedResponses.get()
                .getKey();

        final var otpTokenResponse = fetchOtpTokenResponse(sentinelWorkflowInitResponse.getInstanceResponseV2()
                .getInstanceId(), storedWorkflow.getEntityId(), uiRequestContext.getRequestInfo());

        if (!StringUtils.isEmpty(otpTokenResponse.getCode())) {
            return OtpTokenDetails.builder()
                    .success(false)
                    .token(otpTokenResponse.getMessage())
                    .key(otpTokenResponse.getMessage())
                    .instanceId(otpTokenResponse.getMessage())
                    .build();
        }

        return OtpTokenDetails.builder()
                .key(key)
                .instanceId(sentinelWorkflowInitResponse.getInstanceResponseV2()
                        .getInstanceId())
                .token(otpTokenResponse.getData()
                        .getOtpToken())
                .success(true)
                .build();
    }

    private GenericResponse<OtpFetchResponse> fetchOtpTokenResponse(final String workflowInitInstanceId,
                                                                    final String userId,
                                                                    final RequestInfo requestInfo) {
        try {
            final var multiChannelOtpRequest = MultiChannelOtpRequest.builder()
                    .instanceId(workflowInitInstanceId)
                    .channels(Set.of(OtpRequestType.SMS))
                    .userId(userId)
                    .templateVariable(Map.of())
                    .build();

            return sentinelClient.getSentinel()
                    .getMultiChannelOtp(requestInfo, multiChannelOtpRequest);
        } catch (final SentinelClientError e) {
            log.error("Error while fetching otp token for instance id {} {}", workflowInitInstanceId, e);

            return GenericResponse.<OtpFetchResponse>builder()
                    .success(false)
                    .message(e.getMessage())
                    .code(SENTINEL_ERROR_CODE)
                    .build();
        }

    }
}
