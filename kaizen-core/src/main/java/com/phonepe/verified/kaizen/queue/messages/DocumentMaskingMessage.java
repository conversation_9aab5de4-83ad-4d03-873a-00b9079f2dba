package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DocumentMaskingMessage extends BaseMessage {

    @NonNull
    private final String actionId;

    @NonNull
    private final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel;

    @Builder
    @Jacksonized
    public DocumentMaskingMessage(final RequestInfo requestInfo,
                                  @NonNull final String actionId,
                                  @NonNull final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel) {

        super(ActorMessageType.DOCUMENT_MASKING, requestInfo);
        this.actionId = actionId;
        this.documentTypeIdentifierAndLabel = documentTypeIdentifierAndLabel;
    }
}
