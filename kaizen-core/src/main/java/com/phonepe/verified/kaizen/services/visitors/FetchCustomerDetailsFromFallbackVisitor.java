package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.requests.details.EntityDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetailVisitor;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredConsentDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredDocumentUploadDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredKeyValueDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredOtpDetails;
import com.phonepe.verified.kaizen.models.responses.details.Detail;
import java.util.Optional;
import lombok.AllArgsConstructor;

@Singleton
@AllArgsConstructor(onConstructor = @__(@Inject))
public class FetchCustomerDetailsFromFallbackVisitor implements
        RequiredDetailVisitor<Optional<? extends Detail>, EntityDetailsRequest> {

    @Override
    public Optional<? extends Detail> visit(final RequiredKeyValueDetail requiredKeyValueDetail,
                                            final EntityDetailsRequest data) {
        return Optional.empty();
    }

    @Override
    public Optional<? extends Detail> visit(final RequiredConsentDetail requiredConsentDetail,
                                            final EntityDetailsRequest data) {
        return Optional.empty();
    }

    @Override
    public Optional<? extends Detail> visit(final RequiredDocumentUploadDetail requiredDocumentUploadDetail,
                                            final EntityDetailsRequest data) {
        return Optional.empty();
    }

    @Override
    public Optional<? extends Detail> visit(final RequiredOtpDetails requiredOtpDetails,
                                            final EntityDetailsRequest data) {
        return Optional.empty();
    }
}
