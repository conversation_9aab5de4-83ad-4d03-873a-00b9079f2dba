package com.phonepe.verified.kaizen.storage.aerospike.keys;

import com.phonepe.verified.kaizen.models.requests.profiles.ProfileIdentifier;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProfileIdentifierKey implements AerospikeKey {


    @NonNull
    private ProfileIdentifier profileIdentifier;

    @Override
    public String getKey() {
        return profileIdentifier.toString();
    }
}
