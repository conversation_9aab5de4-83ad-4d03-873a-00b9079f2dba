package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.impl.ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.keys.ProfileIdSectionMappingIdKey;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.responses.ApiResponse;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.RevolverCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.SkipWorkflowStepMessage;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class SkipWorkflowStepActor extends BaseActor<SkipWorkflowStepMessage> {

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final RevolverCallbackActor revolverCallbackActor;

    private final ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache profileIdSectionMappingIdCache;


    @Inject
    protected SkipWorkflowStepActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                    final ConnectionRegistry connectionRegistry,
                                    final ObjectMapper mapper,
                                    final RetryStrategyFactory retryStrategyFactory,
                                    final ExceptionHandlingFactory exceptionHandlingFactory,
                                    final ProfileService profileService,
                                    final WorkflowService workflowService,
                                    final WorkflowStepService workflowStepService,
                                    final RevolverCallbackActor revolverCallbackActor,
                                    final ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache profileIdSectionMappingIdCache) {

        super(ActorType.SKIP_WORKFLOW_STEP, actorConfigMap.get(ActorType.SKIP_WORKFLOW_STEP), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, SkipWorkflowStepMessage.class);
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
        this.revolverCallbackActor = revolverCallbackActor;
        this.profileIdSectionMappingIdCache = profileIdSectionMappingIdCache;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final SkipWorkflowStepMessage skipWorkflowStepMessage) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(skipWorkflowStepMessage.getWorkflowId());

        final var profileStep = profileService.getProfileStep(storedWorkflow.getProfileId(),
                skipWorkflowStepMessage.getProfileStepMappingId());

        final var storedWorkFlowStepOptional = workflowStepService.getValidWorkflowStep(
                skipWorkflowStepMessage.getWorkflowId(), profileStep.getProfileStepId());

        if (storedWorkFlowStepOptional.isEmpty()) {

            revolverCallbackActor.publish(RevolverCallbackMessage.builder()
                    .requestId(skipWorkflowStepMessage.getRequestInfo()
                            .getRequestId())
                    .status(HttpStatus.SC_NOT_FOUND)
                    .data(ApiResponse.<Void>builder()
                            .success(false)
                            .build())
                    .build());

            return true;
        }

        final var profileStepStandardProfileScreen = profileIdSectionMappingIdCache.get(
                ProfileIdSectionMappingIdKey.builder()
                        .profileId(storedWorkflow.getProfileId())
                        .sectionMappingId(skipWorkflowStepMessage.getScreenMappingId())
                        .build());

        final var uiResponseConfig = BuildUtils.buildShadowV2UiRequestContext(storedWorkflow.getEntityId(),
                skipWorkflowStepMessage.getIntent(), skipWorkflowStepMessage.getApiVersion(),
                skipWorkflowStepMessage.getRequestInfo(), skipWorkflowStepMessage.getComponentKitVersion(),
                Constants.DUMMY_SECTION_INPUT_DATA, storedWorkFlowStepOptional.get().getWorkflowId());

        final var profileScreenConfig = profileStepStandardProfileScreen.getStandardProfileScreenConfig();

        if (profileScreenConfig.getSkipWorkflowStepConfig()
                .isCanBeSkippedByApiCall()) {
            final var storedWorkflowStep = storedWorkFlowStepOptional.get();

            workflowStepService.saveUiRequestContext(storedWorkflowStep.getWorkflowStepId(), uiResponseConfig);

            workflowStepService.skipWorkflowStep(storedWorkflowStep, profileScreenConfig,
                    skipWorkflowStepMessage.getUserDetails(), ActionType.SKIP_WORKFLOW_STEP,
                    TransitionEvent.SKIP_WORKFLOW_STEP, profileScreenConfig.getSkipWorkflowStepConfig()
                            .isInvalidateAllActions());

            return true;
        }

        revolverCallbackActor.publish(RevolverCallbackMessage.builder()
                .requestId(skipWorkflowStepMessage.getRequestInfo()
                        .getRequestId())
                .status(HttpStatus.SC_BAD_REQUEST)
                .data(ApiResponse.<Void>builder()
                        .success(false)
                        .build())
                .build());

        return true;
    }
}
