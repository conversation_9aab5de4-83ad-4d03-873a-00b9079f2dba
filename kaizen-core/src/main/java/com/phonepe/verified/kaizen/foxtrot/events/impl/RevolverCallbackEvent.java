package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class RevolverCallbackEvent extends BaseEvent {


    private final String revolverCallbackRequestId;

    private final boolean success;

    private final String errorMessage;

    @Builder
    public RevolverCallbackEvent(final String intent,
                                 final String entityId,
                                 final String namespace,
                                 final String workflowId,
                                 @NonNull final String groupingKey,
                                 final String organization,
                                 final String workflowType,
                                 final String workflowStepId,
                                 final EntityType entityType,
                                 final String workflowVersion,
                                 final String screenMappingId,
                                 final String actionMappingId,
                                 final long componentKitVersion,
                                 final String profileStepMappingId,
                                 final String revolverCallbackRequestId,
                                 final boolean success,
                                 final ProfileType profileType,
                                 final String addOnType,
                                 final String errorMessage) {
        super(EventType.REVOLVER_CALLBACK, intent, entityId, namespace, workflowId, profileType, addOnType, groupingKey,
                organization, workflowType, workflowStepId, entityType, workflowVersion, screenMappingId,
                actionMappingId, componentKitVersion, profileStepMappingId);

        this.revolverCallbackRequestId = revolverCallbackRequestId;
        this.success = success;
        this.errorMessage = errorMessage;
    }

}
