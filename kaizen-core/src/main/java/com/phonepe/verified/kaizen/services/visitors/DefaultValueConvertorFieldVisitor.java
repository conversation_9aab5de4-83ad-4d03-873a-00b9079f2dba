package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.shadow.page.field.FieldVisitor;
import com.phonepe.shadow.page.field.Value;
import com.phonepe.shadow.page.field.impl.AddressField;
import com.phonepe.shadow.page.field.impl.AddressListField;
import com.phonepe.shadow.page.field.impl.AlertBarField;
import com.phonepe.shadow.page.field.impl.ButtonWithActionField;
import com.phonepe.shadow.page.field.impl.ButtonWithEventRuleField;
import com.phonepe.shadow.page.field.impl.CameraWidgetField;
import com.phonepe.shadow.page.field.impl.CameraWidgetV2Field;
import com.phonepe.shadow.page.field.impl.CardCheckboxField;
import com.phonepe.shadow.page.field.impl.CardInfoBadgeField;
import com.phonepe.shadow.page.field.impl.CardWithActionField;
import com.phonepe.shadow.page.field.impl.CarouselField;
import com.phonepe.shadow.page.field.impl.CheckboxField;
import com.phonepe.shadow.page.field.impl.ChipSelectionField;
import com.phonepe.shadow.page.field.impl.CollapsibleListField;
import com.phonepe.shadow.page.field.impl.CrossSellField;
import com.phonepe.shadow.page.field.impl.CurrencyQuickSelectField;
import com.phonepe.shadow.page.field.impl.CustomListField;
import com.phonepe.shadow.page.field.impl.DateField;
import com.phonepe.shadow.page.field.impl.DateRangeField;
import com.phonepe.shadow.page.field.impl.DateV2Field;
import com.phonepe.shadow.page.field.impl.DescriptiveRadioListField;
import com.phonepe.shadow.page.field.impl.DocVerifyWidgetField;
import com.phonepe.shadow.page.field.impl.DocumentFormField;
import com.phonepe.shadow.page.field.impl.DocumentNumberField;
import com.phonepe.shadow.page.field.impl.DrawWidgetField;
import com.phonepe.shadow.page.field.impl.DropdownField;
import com.phonepe.shadow.page.field.impl.DropdownV2Field;
import com.phonepe.shadow.page.field.impl.DropdownWithIconField;
import com.phonepe.shadow.page.field.impl.ErrorField;
import com.phonepe.shadow.page.field.impl.ExtendedFormField;
import com.phonepe.shadow.page.field.impl.FormField;
import com.phonepe.shadow.page.field.impl.FormFieldWithPrefixAndAction;
import com.phonepe.shadow.page.field.impl.FormV2Field;
import com.phonepe.shadow.page.field.impl.FormWithButtonField;
import com.phonepe.shadow.page.field.impl.FullScreenSearchField;
import com.phonepe.shadow.page.field.impl.FullScreenSearchFieldV2;
import com.phonepe.shadow.page.field.impl.FullScreenTransientViewField;
import com.phonepe.shadow.page.field.impl.FullScreenWebViewWidgetField;
import com.phonepe.shadow.page.field.impl.GenericImageWidgetField;
import com.phonepe.shadow.page.field.impl.HorizontalCompactCardListField;
import com.phonepe.shadow.page.field.impl.IconActionWidgetField;
import com.phonepe.shadow.page.field.impl.IconDropdownField;
import com.phonepe.shadow.page.field.impl.IconField;
import com.phonepe.shadow.page.field.impl.IconV2Field;
import com.phonepe.shadow.page.field.impl.IconWithInfoField;
import com.phonepe.shadow.page.field.impl.ImageField;
import com.phonepe.shadow.page.field.impl.InfoLabelField;
import com.phonepe.shadow.page.field.impl.InfoListWidgetField;
import com.phonepe.shadow.page.field.impl.InfoWithSliderField;
import com.phonepe.shadow.page.field.impl.InformationBottomSheet;
import com.phonepe.shadow.page.field.impl.ItemSelectionField;
import com.phonepe.shadow.page.field.impl.ItemSelectionV2Field;
import com.phonepe.shadow.page.field.impl.ItemSelectionV3Field;
import com.phonepe.shadow.page.field.impl.JsonField;
import com.phonepe.shadow.page.field.impl.LabelField;
import com.phonepe.shadow.page.field.impl.LabelWithShowDetail;
import com.phonepe.shadow.page.field.impl.LabelWithShowDetailV2;
import com.phonepe.shadow.page.field.impl.LinearProgressBarField;
import com.phonepe.shadow.page.field.impl.LinkField;
import com.phonepe.shadow.page.field.impl.ListCheckboxField;
import com.phonepe.shadow.page.field.impl.ListWithActionField;
import com.phonepe.shadow.page.field.impl.LoadClientWidgetField;
import com.phonepe.shadow.page.field.impl.LottieWidgetField;
import com.phonepe.shadow.page.field.impl.MediaUploadWidgetField;
import com.phonepe.shadow.page.field.impl.MultiButtonField;
import com.phonepe.shadow.page.field.impl.MultiButtonV2Field;
import com.phonepe.shadow.page.field.impl.MultiButtonV3Field;
import com.phonepe.shadow.page.field.impl.MultiListSearchableCheckboxField;
import com.phonepe.shadow.page.field.impl.MultiPickerField;
import com.phonepe.shadow.page.field.impl.MultiSelectionDropdownField;
import com.phonepe.shadow.page.field.impl.MultiSelectionDropdownFieldV2;
import com.phonepe.shadow.page.field.impl.NotificationBarField;
import com.phonepe.shadow.page.field.impl.OtpField;
import com.phonepe.shadow.page.field.impl.PopupButtonField;
import com.phonepe.shadow.page.field.impl.PopupButtonV2Field;
import com.phonepe.shadow.page.field.impl.ProductListField;
import com.phonepe.shadow.page.field.impl.ProgressTimelineField;
import com.phonepe.shadow.page.field.impl.ProviderLogoField;
import com.phonepe.shadow.page.field.impl.QrWidgetField;
import com.phonepe.shadow.page.field.impl.QuickSelectField;
import com.phonepe.shadow.page.field.impl.RadioButtonField;
import com.phonepe.shadow.page.field.impl.RadioButtonV2Field;
import com.phonepe.shadow.page.field.impl.RichCheckBoxField;
import com.phonepe.shadow.page.field.impl.RichCheckboxWithMultiLinkField;
import com.phonepe.shadow.page.field.impl.RichLabelField;
import com.phonepe.shadow.page.field.impl.SearchField;
import com.phonepe.shadow.page.field.impl.SearchableListField;
import com.phonepe.shadow.page.field.impl.SelectionBottomSheet;
import com.phonepe.shadow.page.field.impl.SelectionInformationWithButtonField;
import com.phonepe.shadow.page.field.impl.SelectionWidgetWithLabel;
import com.phonepe.shadow.page.field.impl.SelectionWidgetWithLabelV2;
import com.phonepe.shadow.page.field.impl.SingleSegmentedSelectField;
import com.phonepe.shadow.page.field.impl.SingleSegmentedSelectV2Field;
import com.phonepe.shadow.page.field.impl.SliderField;
import com.phonepe.shadow.page.field.impl.StepperField;
import com.phonepe.shadow.page.field.impl.StreamingDescriptiveListField;
import com.phonepe.shadow.page.field.impl.StyleLabelWidgetField;
import com.phonepe.shadow.page.field.impl.TableWidgetField;
import com.phonepe.shadow.page.field.impl.TemplatizedJsonField;
import com.phonepe.shadow.page.field.impl.ToggleButtonField;
import com.phonepe.shadow.page.field.impl.TooltipConsentField;
import com.phonepe.shadow.page.field.impl.WebviewField;
import com.phonepe.shadow.page.field.impl.YesNoField;
import com.phonepe.shadow.page.field.impl.insurance.DisclaimerField;
import com.phonepe.shadow.page.field.impl.insurance.InformativeCardField;
import com.phonepe.shadow.page.field.impl.insurance.InsurancePlanField;
import com.phonepe.shadow.page.field.impl.insurance.InsurancePriceDetailsField;
import com.phonepe.shadow.page.field.impl.insurance.InsurancePriceDetailsV2Field;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewWidgetField;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.dropwizard.util.Strings;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class DefaultValueConvertorFieldVisitor implements FieldVisitor<Void> {

    private final Object defaultValueFromTemplate;

    private final JsonNode workflowContext;

    private final HandleBarsService handleBarsService;

    @Override
    public Void visit(final JsonField jsonField) {
        return null;
    }

    @Override
    public Void visit(final CheckboxField checkboxField) {
        return null;
    }

    @Override
    public Void visit(final DateField dateField) {

        dateField.setDefaultValue(getDateFromDefaultValueTemplate());
        return null;
    }

    @Override
    public Void visit(final DateV2Field dateV2Field) {

        dateV2Field.setDefaultValue(getDateFromDefaultValueTemplate());
        return null;
    }

    @Override
    public Void visit(final AddressListField addressListField) {
        addressListField.setDefaultValue((String) defaultValueFromTemplate);
        return null;
    }

    @Override
    public Void visit(final DropdownField dropdownField) {
        return null;
    }

    @Override
    public Void visit(final DropdownV2Field dropdownV2Field) {

        dropdownV2Field.setDefaultValue(getFieldValueFromValueListTemplate(dropdownV2Field.getValues()));
        return null;
    }

    @Override
    public Void visit(final DropdownWithIconField dropdownWithIconField) {
        return null;
    }

    @Override
    public Void visit(final FormField formField) {
        return null;
    }

    @Override
    public Void visit(final FormV2Field formV2Field) {
        return null;
    }

    @Override
    public Void visit(final OtpField otpField) {
        return null;
    }

    @Override
    public Void visit(final RadioButtonField radioButtonField) {
        return null;
    }

    @Override
    public Void visit(final CustomListField customListField) {
        return null;
    }

    @Override
    public Void visit(final WebviewField webviewField) {
        return null;
    }

    @Override
    public Void visit(final AddressField addressField) {
        return null;
    }

    @Override
    public Void visit(final LinkField linkField) {
        return null;
    }

    @Override
    public Void visit(final QuickSelectField quickSelectField) {
        return null;
    }

    @Override
    public Void visit(final ErrorField errorField) {
        return null;
    }

    @Override
    public Void visit(final MultiListSearchableCheckboxField multiListSearchableCheckboxField) {
        return null;
    }

    @Override
    public Void visit(final DateRangeField dateRangeField) {
        return null;
    }

    @Override
    public Void visit(final StepperField stepperField) {
        return null;
    }

    @Override
    public Void visit(final SliderField sliderField) {
        return null;
    }

    @Override
    public Void visit(final LabelField labelField) {
        return null;
    }

    @Override
    public Void visit(final RichLabelField richLabelField) {
        return null;
    }

    @Override
    public Void visit(final YesNoField yesNoField) {
        return null;
    }

    @Override
    public Void visit(final ListCheckboxField listCheckboxField) {
        return null;
    }

    @Override
    public Void visit(final CarouselField carouselField) {
        return null;
    }

    @Override
    public Void visit(final CollapsibleListField collapsibleListField) {
        return null;
    }

    @Override
    public Void visit(final DescriptiveRadioListField descriptiveRadioListField) {
        return null;
    }

    @Override
    public Void visit(final InsurancePlanField insurancePlanField) {
        return null;
    }

    @Override
    public Void visit(final ToggleButtonField toggleButtonField) {
        return null;
    }

    @Override
    public Void visit(final InsurancePriceDetailsField insurancePriceDetailsField) {
        return null;
    }

    @Override
    public Void visit(final InsurancePriceDetailsV2Field insurancePriceDetailsV2Field) {
        return null;
    }

    @Override
    public Void visit(final RichCheckBoxField richCheckBoxField) {
        return null;
    }

    @Override
    public Void visit(final DisclaimerField disclaimerField) {
        return null;
    }

    @Override
    public Void visit(final SelectionBottomSheet selectionBottomSheet) {
        return null;
    }

    @Override
    public Void visit(final InformationBottomSheet informationBottomSheet) {
        return null;
    }

    @Override
    public Void visit(final SelectionWidgetWithLabel selectionWidgetWithLabel) {
        return null;
    }

    @Override
    public Void visit(final SelectionWidgetWithLabelV2 selectionWidgetWithLabelV2) {
        return null;
    }

    @Override
    public Void visit(final FullScreenSearchField fullScreenSearchField) {
        return null;
    }

    @Override
    public Void visit(final TemplatizedJsonField templatizedJsonField) {
        return null;
    }

    @Override
    public Void visit(final MultiButtonField multiButtonField) {
        return null;
    }

    @Override
    public Void visit(final MultiButtonV2Field multiButtonV2Field) {
        return null;
    }

    @Override
    public Void visit(final SearchField searchField) {
        return null;
    }

    @Override
    public Void visit(final FormWithButtonField formWithButtonField) {
        return null;
    }

    @Override
    public Void visit(final ItemSelectionField itemSelectionField) {
        return null;
    }

    @Override
    public Void visit(final ItemSelectionV2Field itemSelectionV2Field) {
        return null;
    }

    @Override
    public Void visit(final FormFieldWithPrefixAndAction formFieldWithPrefixAndAction) {
        return null;
    }

    @Override
    public Void visit(final InfoLabelField infoLabelField) {
        return null;
    }

    @Override
    public Void visit(final CurrencyQuickSelectField currencyQuickSelectField) {

        currencyQuickSelectField.setDefaultValue(getFieldValueFromDefaultValueTemplate());
        return null;
    }

    @Override
    public Void visit(final RichCheckboxWithMultiLinkField richCheckboxWithMultiLinkField) {
        return null;
    }

    @Override
    public Void visit(final SelectionInformationWithButtonField selectionInformationWithButtonField) {
        return null;
    }

    @Override
    public Void visit(final SingleSegmentedSelectField singleSegmentedSelectField) {
        return null;
    }

    @Override
    public Void visit(final SingleSegmentedSelectV2Field singleSegmentedSelectV2Field) {
        return null;
    }


    @Override
    public Void visit(final InformativeCardField informativeCardField) {
        return null;
    }

    @Override
    public Void visit(final LabelWithShowDetail labelWithShowDetail) {
        return null;
    }

    @Override
    public Void visit(final LabelWithShowDetailV2 labelWithShowDetailV2) {
        return null;
    }

    @Override
    public Void visit(final MultiPickerField multiPickerField) {
        return null;
    }

    @Override
    public Void visit(final DocumentNumberField documentNumberField) {
        return null;
    }

    @Override
    public Void visit(final IconDropdownField iconDropdownField) {
        return null;
    }

    @Override
    public Void visit(final MultiButtonV3Field multiButtonV3Field) {

        multiButtonV3Field.setDefaultValue(List.of(getFieldValueFromValueListTemplate(multiButtonV3Field.getValues())));
        return null;
    }

    @Override
    public Void visit(final RadioButtonV2Field radioButtonV2Field) {
        return null;
    }

    @Override
    public Void visit(final CrossSellField crossSellField) {
        return null;
    }

    @Override
    public Void visit(final HorizontalCompactCardListField horizontalCompactCardListField) {
        return null;
    }

    @Override
    public Void visit(final IconField iconField) {
        return null;
    }

    @Override
    public Void visit(final IconV2Field iconV2Field) {
        return null;
    }

    @Override
    public Void visit(final ExtendedFormField extendedFormField) {
        return null;
    }

    @Override
    public Void visit(final SearchableListField searchableListField) {
        return null;
    }

    @Override
    public Void visit(final ProductListField productListField) {
        return null;
    }

    @Override
    public Void visit(final CardCheckboxField cardCheckboxField) {
        return null;
    }

    @Override
    public Void visit(final LinearProgressBarField linearProgressBarField) {
        return null;
    }

    @Override
    public Void visit(final PopupButtonField popupButtonField) {
        return null;
    }

    @Override
    public Void visit(final AlertBarField alertBarField) {
        return null;
    }

    @Override
    public Void visit(final CardInfoBadgeField cardInfoBadgeField) {
        return null;
    }

    @Override
    public Void visit(final DocVerifyWidgetField docVerifyWidgetField) {
        return null;
    }

    @Override
    public Void visit(final MediaUploadWidgetField mediaUploadWidgetField) {
        return null;
    }

    @Override
    public Void visit(final StreamingDescriptiveListField streamingDescriptiveListField) {
        return null;
    }

    @Override
    public Void visit(final ProgressTimelineField progressTimelineField) {
        return null;
    }

    @Override
    public Void visit(final MultiSelectionDropdownField multiSelectionDropdownField) {
        return null;
    }

    @Override
    public Void visit(final CameraWidgetField cameraWidgetField) {
        return null;
    }

    @Override
    public Void visit(final DrawWidgetField drawWidgetField) {
        return null;
    }

    @Override
    public Void visit(final GenericImageWidgetField genericImageWidgetField) {
        return null;
    }

    @Override
    public Void visit(final StyleLabelWidgetField styleLabelWidgetField) {
        return null;
    }

    @Override
    public Void visit(final ProviderLogoField providerLogoField) {
        return null;
    }

    @Override
    public Void visit(final TableWidgetField tableWidgetField) {
        return null;
    }

    @Override
    public Void visit(final ImageField imageField) {
        return null;
    }

    @Override
    public Void visit(final NotificationBarField notificationBarField) {
        return null;
    }

    @Override
    public Void visit(final IconActionWidgetField iconActionWidgetField) {
        return null;
    }

    @Override
    public Void visit(final PopupButtonV2Field popupButtonV2Field) {
        return null;
    }

    @Override
    public Void visit(final ButtonWithActionField buttonWithActionField) {
        return null;
    }

    @Override
    public Void visit(final LottieWidgetField lottieWidgetField) {
        return null;
    }

    @Override
    public Void visit(final IconWithInfoField iconWithInfoField) {
        return null;
    }

    @Override
    public Void visit(final LoadClientWidgetField loadClientWidgetField) {
        return null;
    }

    @Override
    public Void visit(final FullScreenWebViewWidgetField fullScreenWebViewWidgetField) {
        return null;
    }

    @Override
    public Void visit(final FullScreenSearchFieldV2 fullScreenSearchFieldV2) {

        fullScreenSearchFieldV2.setDefaultValue(getFieldValueFromDefaultValueTemplate());
        return null;
    }

    @Override
    public Void visit(final InfoWithSliderField infoWithSliderField) {
        return null;
    }

    @Override
    public Void visit(final CameraWidgetV2Field cameraWidgetV2Field) {
        return null;
    }

    @Override
    public Void visit(final FullScreenTransientViewField fullScreenTransientViewField) {
        return null;
    }

    @Override
    public Void visit(final ButtonWithEventRuleField buttonWithEventRuleField) {
        return null;
    }

    @Override
    public Void visit(final TooltipConsentField tooltipConsentField) {
        return null;
    }

    @Override
    public Void visit(final ChipSelectionField chipSelectionField) {
        return null;
    }

    @Override
    public Void visit(final MultiSelectionDropdownFieldV2 multiSelectionDropdownFieldV2) {
        return null;
    }

    @Override
    public Void visit(final ItemSelectionV3Field itemSelectionV3Field) {
        return null;
    }

    @Override
    public Void visit(final ListWithActionField listWithActionField) {
        return null;
    }

    @Override
    public Void visit(final QrWidgetField qrWidgetField) {
        return null;
    }

    @Override
    public Void visit(CardWithActionField cardWithActionField) {return null;}

    @Override
    public Void visit(final DocumentFormField documentFormField) {
        return null;
    }

    @Override
    public Void visit(final SummaryViewWidgetField summaryViewWidgetField) {
        return null;
    }

    @Override
    public Void visit(final InfoListWidgetField infoListWidgetField) {
        return null;
    }

    private Date getDateFromDefaultValueTemplate() {

        final var transformedDefaultValueFromTemplate = handleBarsService.transform((String) defaultValueFromTemplate,
                workflowContext);

        if (Strings.isNullOrEmpty(transformedDefaultValueFromTemplate)) {
            return null;
        }

        return new Date(Long.parseLong(transformedDefaultValueFromTemplate));
    }

    private Value getFieldValueFromDefaultValueTemplate() {

        final var serializedDefaultValueFromTemplate = MapperUtils.serializeToString(defaultValueFromTemplate);

        final var transformedDefaultValueFromTemplate = handleBarsService.transform(serializedDefaultValueFromTemplate,
                workflowContext);

        return MapperUtils.deserialize(transformedDefaultValueFromTemplate, Value.class);
    }

    private Value getFieldValueFromValueListTemplate(final List<Value> valueList) {

        final var serializedValueFromTemplate = MapperUtils.serializeToString(defaultValueFromTemplate);

        final var transformedValueFromTemplate = handleBarsService.transform(serializedValueFromTemplate,
                workflowContext);

        return valueList.stream()
                .filter(value -> value.getCode()
                        .equals(transformedValueFromTemplate))
                .findAny()
                .orElse(valueList.get(0));
    }
}
