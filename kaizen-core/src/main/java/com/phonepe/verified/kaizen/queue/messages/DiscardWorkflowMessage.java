package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DiscardWorkflowMessage extends BaseMessage {

    @NonNull
    private final String workflowId;

    @NonNull
    private final UserDetails userDetails;

    @Builder
    @Jacksonized
    public DiscardWorkflowMessage(final RequestInfo requestInfo,
                                  @NonNull final String workflowId,
                                  @NonNull final UserDetails userDetails) {

        super(ActorMessageType.DISCARD_WORKFLOW, requestInfo);
        this.workflowId = workflowId;
        this.userDetails = userDetails;
    }
}

