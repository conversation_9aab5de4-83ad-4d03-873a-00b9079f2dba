package com.phonepe.verified.kaizen.configs;

import io.dropwizard.util.Duration;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthZConfig {

    @NotEmpty
    private String appName;

    @NotEmpty
    private String roleName;

    @NotNull
    private Duration expiry;

    @NotNull
    private Duration ttl;

    @NotNull
    private Duration ttlWeb;
}