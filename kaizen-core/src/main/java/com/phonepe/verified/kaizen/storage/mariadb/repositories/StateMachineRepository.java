package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredStateMachineTransition;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredStateMachineTransition.Fields;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class StateMachineRepository extends CrudRepository<StoredStateMachineTransition> {


    private static final Set<ActionType> CURRENT_ENABLED_ACTION_TYPES = Arrays.stream(ActionType.values())
            .collect(Collectors.toSet());


    @Inject
    public StateMachineRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredStateMachineTransition.class), null);
    }

    public Optional<StoredStateMachineTransition> select(final ActionType actionType,
                                                         final String version) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredStateMachineTransition.class)
                .add(Restrictions.eq(Fields.actionType, actionType))
                .add(Restrictions.eq(Fields.version, version));

        return select(Constants.STATE_MACHINE_SHARD_KEY, detachedCriteria).stream()
                .findFirst();
    }

    public List<StoredStateMachineTransition> selectAll() {

        final var detachedCriteria = DetachedCriteria.forClass(StoredStateMachineTransition.class)
                .add(Restrictions.in(Fields.actionType, CURRENT_ENABLED_ACTION_TYPES));

        return select(Constants.STATE_MACHINE_SHARD_KEY, detachedCriteria);
    }

}
