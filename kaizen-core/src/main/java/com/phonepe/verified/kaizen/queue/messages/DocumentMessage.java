package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DocumentMessage extends BaseMessage {

    @NonNull
    private final String documentId;

    @NonNull
    private final String userId;

    @Builder
    @Jacksonized
    public DocumentMessage(final RequestInfo requestInfo,
                           @NonNull final String documentId,
                           @NonNull final String userId) {

        super(ActorMessageType.DOCUMENT, requestInfo);
        this.documentId = documentId;
        this.userId = userId;
    }
}
