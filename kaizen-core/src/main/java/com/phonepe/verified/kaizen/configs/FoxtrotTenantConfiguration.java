package com.phonepe.verified.kaizen.configs;

import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class FoxtrotTenantConfiguration {

    @NotEmpty
    private final String tenantTableName;

    @Default
    private final boolean baseTableIngestionEnabled = false;
}
