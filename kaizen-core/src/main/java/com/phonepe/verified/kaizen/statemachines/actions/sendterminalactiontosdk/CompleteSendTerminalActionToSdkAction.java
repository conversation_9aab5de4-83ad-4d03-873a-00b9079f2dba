package com.phonepe.verified.kaizen.statemachines.actions.sendterminalactiontosdk;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.actors.AutoRetryActionActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.HandleActionCompletionActor;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessActionCompletionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.SuccessStateBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import org.springframework.statemachine.StateContext;

@Singleton
@ActionKey("completeSendTerminalActionToSdkAction")
public class CompleteSendTerminalActionToSdkAction extends SuccessStateBaseAction {

    private final WorkflowStepService workflowStepService;

    private final DynamicUiResponseService dynamicUiResponseService;

    @Inject
    protected CompleteSendTerminalActionToSdkAction(final ActionService actionService,
                                                    final ActionRepository actionRepository,
                                                    final AutoRetryActionService autoRetryActionService,
                                                    final Provider<AutoRetryActionActor> autoRetryActionActorProvider,
                                                    final Provider<HandleActionCompletionActor> handleActionCompletionActorProvider,
                                                    final Provider<HandlePseudoSuccessActionCompletionActor> handlePseudoSuccessActionCompletionActorProvider,
                                                    final Provider<WorkflowContextStore> workflowContextStore,
                                                    final Provider<EventIngestionActor> eventIngestionActorProvider,
                                                    final WorkflowStepService workflowStepService,
                                                    final DynamicUiResponseService dynamicUiResponseService) {
        super(actionService, actionRepository, autoRetryActionService, autoRetryActionActorProvider,
                handleActionCompletionActorProvider, handlePseudoSuccessActionCompletionActorProvider,
                workflowContextStore, eventIngestionActorProvider);
        this.workflowStepService = workflowStepService;
        this.dynamicUiResponseService = dynamicUiResponseService;
    }

    @Override
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedAction.getWorkflowStepId());

        dynamicUiResponseService.sendResponseToUi(uiRequestContext, BuildUtils.buildTerminalActionResponseMap(),
                storedWorkflowStep.getWorkflowId());
    }
}
