package com.phonepe.verified.kaizen.statemachines.interceptors;

import com.phonepe.verified.kaizen.statemachines.StateMachineContextKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.support.StateMachineInterceptor;
import org.springframework.statemachine.support.StateMachineInterceptorAdapter;

@Slf4j
public class ErrorHandlingInterceptor<S, E> extends StateMachineInterceptorAdapter<S, E> implements
        StateMachineInterceptor<S, E> {

    @Override
    public Exception stateMachineError(final StateMachine<S, E> stateMachine,
                                       final Exception exception) {

        final var variables = stateMachine.getExtendedState()
                .getVariables();
        final var state = stateMachine.getState()
                .getId();
        final var targetState = variables.get(StateMachineContextKeys.TARGET_STATE);
        final var triggerEvent = variables.get(StateMachineContextKeys.TRIGGER_EVENT);

        log.error("Error occurred while processing event {} with transition from state {} to state {}", triggerEvent,
                state, targetState, exception);

        variables.put(StateMachineContextKeys.THROWN_EXCEPTION, exception);
        return exception;
    }
}
