package com.phonepe.verified.kaizen.utils;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.shadow.models.InfoType;
import com.phonepe.shadow.models.response.Action;
import com.phonepe.shadow.models.response.TemplateInitResponse;
import com.phonepe.shadow.models.response.actions.ButtonAction;
import com.phonepe.shadow.models.response.v2.TemplateInitV2Response;
import com.phonepe.shadow.page.field.ProgressDetail;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewItem;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewResponse;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewStatus;
import com.phonepe.verified.kaizen.caches.key.AddOnProfileCacheKey;
import com.phonepe.verified.kaizen.caches.key.PrimaryProfileCacheKey;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.WorkflowInitRequestContext;
import com.phonepe.verified.kaizen.models.configs.callback.ClientCallbackConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.Info;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl.TerminalActionShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.SummaryViewItemConfigV1;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.configs.template.impl.ShadowV2UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.data.keys.ProfileKey;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileVisitor;
import com.phonepe.verified.kaizen.models.requests.statemachines.AddStateMachineRegistryRequest;
import com.phonepe.verified.kaizen.models.requests.statemachines.StateMachineTransition;
import com.phonepe.verified.kaizen.models.requests.v2.ShadowV2SectionSubmitV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.LatestWorkflowDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.LatestWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowAddOnRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowInitRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowAddOnV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowInitV2Request;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.WorkflowClientCallback;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.visitors.CallbackWorkflowMessageBuilderProfileVisitor.CallbackWorkflowMessageBuilderProfileVisitorData;
import com.phonepe.verified.kaizen.services.visitors.ProfileBuilderStoredProfileVisitor;
import com.phonepe.verified.kaizen.services.visitors.StoredProfileBuilderProfileVisitor;
import com.phonepe.verified.kaizen.services.visitors.StoredProfileBuilderProfileVisitor.StoredProfileBuilderProfileVisitorMessage;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.AuditPrimaryKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.PrimaryKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredClientCallbackConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredProfileStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredStateMachineTransition;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredProfile;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@UtilityClass
public class BuildUtils {

    public StoredAction toStoredAction(final String workflowStepId,
                                       final String actionId,
                                       final ActionType actionType,
                                       final String stateMachineVersion,
                                       final String actionMappingId,
                                       final StateContext<String, String> stateContext,
                                       final String screenMappingId,
                                       final UserDetails userDetails) {
        return StoredAction.builder()
                .primaryKey(primaryKey())
                .workflowStepId(workflowStepId)
                .actionId(actionId)
                .actionType(actionType)
                .currentState(stateContext.getTarget()
                        .getId())
                .currentEvent(stateContext.getEvent())
                .stateMachineVersion(stateMachineVersion)
                .actionMappingId(actionMappingId)
                .screenMappingId(screenMappingId)
                .lastUpdatedBy(userDetails.getUserId())
                .lastUpdaterType(UpdaterType.valueOf(userDetails.getUserType()
                        .name()))
                .completed(false)
                .completionState(CompletionState.IN_PROGRESS)
                .build();
    }

    public List<StoredStateMachineTransition> toStoredStateMachineTransitionList(final AddStateMachineRegistryRequest addStateMachineRegistryRequest,
                                                                                 final String addedBy) {

        return addStateMachineRegistryRequest.getStateMachineTransitions()
                .stream()
                .map(stateMachineTransition -> BuildUtils.toStoredStateMachineTransition(stateMachineTransition,
                        addStateMachineRegistryRequest.getActionType(), addStateMachineRegistryRequest.getVersion(),
                        addedBy))
                .toList();
    }

    public StoredStateMachineTransition toStoredStateMachineTransition(final StateMachineTransition stateMachineTransition,
                                                                       final ActionType actionType,
                                                                       final String version,
                                                                       final String addedBy) {
        return StoredStateMachineTransition.builder()
                .actionType(actionType)
                .version(version)
                .lastUpdatedBy(addedBy)
                .source(stateMachineTransition.getSource())
                .target(stateMachineTransition.getTarget())
                .event(stateMachineTransition.getEvent())
                .actionKey(stateMachineTransition.getActionKey())
                .build();
    }

    public StoredWorkflow toStoredWorkflow(final WorkflowInitRequestContext request,
                                           final Profile profile,
                                           final String workflowId,
                                           final StateContext<TransitionState, TransitionEvent> stateContext,
                                           final UserDetails userDetails) {

        return StoredWorkflow.builder()
                .primaryKey(primaryKey())
                .workflowId(workflowId)
                .phoneNumber(request.getPhoneNumber())
                .emailId(request.getEmailId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .profileId(profile.getProfileId())
                .currentState(stateContext.getTarget()
                        .getId())
                .currentEvent(stateContext.getEvent())
                .lastUpdatedBy(userDetails.getUserId())
                .lastUpdaterType(UpdaterType.valueOf(userDetails.getUserType()
                        .name()))
                .callerFarmId(request.getCallerFarmId())
                .build();
    }

    public StoredWorkflowStep toStoredWorkflowStep(final String workflowId,
                                                   final ProfileStep profileStep,
                                                   final StateContext<TransitionState, TransitionEvent> stateContext,
                                                   final UserDetails userDetails,
                                                   final String workflowStepId) {
        return StoredWorkflowStep.builder()
                .primaryKey(primaryKey())
                .workflowStepId(workflowStepId)
                .workflowId(workflowId)
                .profileStepId(profileStep.getProfileStepId())
                .currentState(stateContext.getTarget()
                        .getId())
                .currentEvent(stateContext.getEvent())
                .lastUpdatedBy(userDetails.getUserId())
                .lastUpdaterType(UpdaterType.valueOf(userDetails.getUserType()
                        .name()))
                .build();
    }

    public StoredProfile toStoredProfile(final Profile profile,
                                         final String lastUpdatedBy,
                                         final String approvedBy) {

        final var profileId = IdUtils.generateId("P");

        return profile.accept(StoredProfileBuilderProfileVisitor.INSTANCE,
                StoredProfileBuilderProfileVisitorMessage.builder()
                        .lastUpdatedBy(lastUpdatedBy)
                        .profileId(profileId)
                        .approvedBy(approvedBy)
                        .build());
    }

    public StoredProfile toAlreadyCreatedStoredProfile(final Profile profile,
                                                       final long id,
                                                       final String lastUpdatedBy,
                                                       final String approvedBy) {

        return profile.accept(StoredProfileBuilderProfileVisitor.INSTANCE,
                StoredProfileBuilderProfileVisitorMessage.builder()
                        .lastUpdatedBy(lastUpdatedBy)
                        .profileId(profile.getProfileId())
                        .approvedBy(approvedBy)
                        .id(id)
                        .build());
    }

    public StoredProfileStep toStoredProfileStep(final String profileId,
                                                 final ProfileStep profileStep,
                                                 final String lastUpdatedBy) {

        final var profileStepId = IdUtils.generateId("PS");

        return StoredProfileStep.builder()
                .profileId(profileId)
                .profileStepId(profileStepId)
                .profileStepMappingId(profileStep.getProfileStepMappingId())
                .title(profileStep.getTitle())
                .executionRule(profileStep.getExecutionRule())
                .profileScreenConfig(profileStep.getProfileScreenConfig())
                .lastUpdatedBy(lastUpdatedBy)
                .build();
    }

    public StoredProfileStep toAlreadyCreatedStoredProfileStep(final String profileId,
                                                               final ProfileStep profileStep,
                                                               final long id,
                                                               final String lastUpdatedBy) {

        return StoredProfileStep.builder()
                .id(id)
                .profileId(profileId)
                .profileStepId(profileStep.getProfileStepId())
                .profileStepMappingId(profileStep.getProfileStepMappingId())
                .title(profileStep.getTitle())
                .executionRule(profileStep.getExecutionRule())
                .profileScreenConfig(profileStep.getProfileScreenConfig())
                .lastUpdatedBy(lastUpdatedBy)
                .build();
    }

    public Profile toProfile(final StoredProfile storedProfile) {

        return storedProfile.accept(ProfileBuilderStoredProfileVisitor.INSTANCE, null);
    }

    public ProfileStep toProfileStep(final StoredProfileStep storedProfileStep) {

        return ProfileStep.builder()
                .profileId(storedProfileStep.getProfileId())
                .profileStepId(storedProfileStep.getProfileStepId())
                .profileStepMappingId(storedProfileStep.getProfileStepMappingId())
                .title(storedProfileStep.getTitle())
                .executionRule(storedProfileStep.getExecutionRule())
                .profileScreenConfig(storedProfileStep.getProfileScreenConfig())
                .createdAt(storedProfileStep.getCreatedAt())
                .lastUpdatedAt(storedProfileStep.getLastUpdatedAt())
                .build();
    }

    public List<com.phonepe.shadow.models.Info> buildInfoList(final List<Info> infoList) {

        return infoList.stream()
                .map(BuildUtils::buildInfo)
                .collect(Collectors.toList());
    }

    public com.phonepe.shadow.models.Info buildInfo(final Info info) {
        return com.phonepe.shadow.models.Info.builder()
                .imageUrl(info.getImageUrl())
                .text(info.getText())
                .type(InfoType.valueOf(info.getType()
                        .name()))
                .build();
    }

    public UiRequestContext buildShadowV2UiRequestContext(final String userId,
                                                          final String intent,
                                                          final ApiVersion apiVersion,
                                                          final RequestInfo requestInfo,
                                                          final long componentKitVersion,
                                                          final SectionInputData sectionInputData, String workflowId) {

        return ShadowV2UiRequestContext.builder()
                .componentKitVersion(componentKitVersion)
                .sectionInputData(sectionInputData)
                .requestInfo(requestInfo)
                .apiVersion(apiVersion)
                .intent(intent)
                .userId(userId)
                .workflowId(workflowId)
                .build();
    }

    //for getting different templates for same profileId based on version
    public String getTemplateWorkflowType(final Profile profile) {
        //to do ravi review

        return profile.accept(new ProfileVisitor<String, Void>() {

            @Override
            public String visit(final AddOnProfile addOnProfile,
                                final Void data) {

                return String.join(":", addOnProfile.getOrganization(), addOnProfile.getNamespace(),
                        addOnProfile.getType(), ProfileType.ADD_ON.toString(), addOnProfile.getAddOnType(),
                        addOnProfile.getVersion());
            }

            @Override
            public String visit(final PrimaryProfile primaryProfile,
                                final Void data) {

                return String.join(":", primaryProfile.getOrganization(), primaryProfile.getNamespace(),
                        primaryProfile.getType(), primaryProfile.getVersion());
            }
        }, null);
    }

    public StoredClientCallbackConfig toStoredClientCallback(final ClientCallbackConfig clientCallbackConfig,
                                                             final String lastUpdatedBy) {

        return StoredClientCallbackConfig.builder()
                .profileId(clientCallbackConfig.getProfileId())
                .lastUpdatedBy(lastUpdatedBy)
                .callbackUrl(clientCallbackConfig.getCallbackUrl())
                .callbackService(clientCallbackConfig.getCallbackService())
                .build();
    }

    public ClientCallbackConfig toClientCallbackConfig(final StoredClientCallbackConfig storedClientCallbackConfig) {

        return ClientCallbackConfig.builder()
                .profileId(storedClientCallbackConfig.getProfileId())
                .lastUpdatedBy(storedClientCallbackConfig.getLastUpdatedBy())
                .callbackUrl(storedClientCallbackConfig.getCallbackUrl())
                .callbackService(storedClientCallbackConfig.getCallbackService())
                .build();
    }

    public StoredClientCallbackConfig toAlreadyCreatedStoredClientCallback(final ClientCallbackConfig clientCallbackConfig,
                                                                           final String lastUpdatedBy,
                                                                           final long id) {

        return StoredClientCallbackConfig.builder()
                .id(id)
                .profileId(clientCallbackConfig.getProfileId())
                .lastUpdatedBy(lastUpdatedBy)
                .callbackUrl(clientCallbackConfig.getCallbackUrl())
                .callbackService(clientCallbackConfig.getCallbackService())
                .build();
    }

    public PrimaryKey primaryKey() {

        final var now = LocalDate.now();
        final var partitionId = now.getYear() * 100 + now.getMonthValue();

        return PrimaryKey.builder()
                .partitionId(partitionId)
                .build();
    }

    public AuditPrimaryKey auditPrimaryKeyForUpdate() {

        final var now = LocalDate.now();
        final var partitionId = now.getYear() * 100 + now.getMonthValue();

        return AuditPrimaryKey.builder()
                .partitionId(partitionId)
                .rev(1)
                .build();
    }

    public WorkflowInitRequestContext buildContext(final WorkflowInitRequest request,
                                                   final String profileId) {
        return WorkflowInitRequestContext.builder()
                .entityId(request.getEntityId())
                .emailId(request.getEmailId())
                .entityType(request.getEntityType())
                .phoneNumber(request.getPhoneNumber())
                .callerFarmId(request.getCallerFarmId())
                .profileId(profileId)
                .build();
    }

    public WorkflowInitRequestContext buildContext(final WorkflowAddOnRequest request,
                                                   final String profileId) {
        return WorkflowInitRequestContext.builder()
                .entityId(request.getEntityId())
                .emailId(request.getEmailId())
                .entityType(request.getEntityType())
                .phoneNumber(request.getPhoneNumber())
                .callerFarmId(request.getCallerFarmId())
                .profileId(profileId)
                .build();
    }

    public WorkflowInitRequestContext buildContext(final WorkflowInitV2Request request,
                                                   final String profileId) {
        return WorkflowInitRequestContext.builder()
                .entityId(request.getEntityId())
                .emailId(request.getEmailId())
                .entityType(request.getEntityType())
                .phoneNumber(request.getPhoneNumber())
                .profileId(profileId)
                .userReferenceId(request.getUserReferenceId())
                .callerFarmId(request.getCallerFarmId())
                .build();
    }

    public WorkflowInitRequestContext buildContext(final WorkflowAddOnV2Request request,
                                                   final String profileId) {
        return WorkflowInitRequestContext.builder()
                .entityId(request.getEntityId())
                .emailId(request.getEmailId())
                .entityType(request.getEntityType())
                .phoneNumber(request.getPhoneNumber())
                .profileId(profileId)
                .userReferenceId(request.getUserReferenceId())
                .callerFarmId(request.getCallerFarmId())
                .build();
    }

    public static ShadowV2SectionSubmitRequest buildShadowV2SectionSubmitRequest(@NonNull final String workflowId,
                                                                                 @NonNull final String userId,
                                                                                 final ShadowV2SectionSubmitV2Request shadowV2SectionSubmitV2Request) {
        return ShadowV2SectionSubmitRequest.buildShadowV2SectionSubmitRequest()
                .componentKitVersion(shadowV2SectionSubmitV2Request.getComponentKitVersion())
                .sectionInputData(shadowV2SectionSubmitV2Request.getSectionInputData())
                .intent(shadowV2SectionSubmitV2Request.getIntent())
                .workflowId(workflowId)
                .userId(userId)
                .build();
    }

    public static TemplateInitV2Response getTemplateInitV2Response(final TemplateInitResponse templateInitResponse) {

        return TemplateInitV2Response.builder()
                .updatedTemplate(templateInitResponse.isUpdatedTemplate())
                .template(templateInitResponse.getTemplate())
                .action(templateInitResponse.getAction())
                .build();
    }

    public static String getIntent(final ShadowV2UiRequestContext shadowV2UiRequestContext) {

        return Objects.nonNull(shadowV2UiRequestContext)
               ? shadowV2UiRequestContext.getIntent()
               : null;

    }

    public static long getComponentKitVersion(final ShadowV2UiRequestContext shadowV2UiRequestContext) {

        return Objects.nonNull(shadowV2UiRequestContext)
               ? shadowV2UiRequestContext.getComponentKitVersion()
               : 0;
    }

    public static String getRequestId(final UiRequestContext uiRequestContext) {

        if (Objects.nonNull(uiRequestContext) && Objects.nonNull(uiRequestContext.getRequestInfo())) {

            return uiRequestContext.getRequestInfo()
                    .getRequestId();
        }
        return null;
    }

    public LatestWorkflowRequest toLatestWorkflowRequest(final LatestWorkflowDetailsRequest latestWorkflowDetailsRequest) {
        return LatestWorkflowRequest.builder()
                .entityId(latestWorkflowDetailsRequest.getEntityId())
                .entityType(latestWorkflowDetailsRequest.getEntityType())
                .namespace(latestWorkflowDetailsRequest.getNamespace())
                .organization(latestWorkflowDetailsRequest.getOrganization())
                .type(latestWorkflowDetailsRequest.getType())
                .build();
    }

    public static SummaryViewItem toSummaryViewItem(final SummaryViewItemConfigV1 summaryViewItemConfigV1,
                                                    final SummaryViewStatus summaryViewStatus,
                                                    final Action action,
                                                    final String identifierNumber) {

        final var failureDetail = SummaryViewStatus.FAILED.equals(summaryViewStatus)
                                  ? summaryViewItemConfigV1.getFailureDetail()
                                  : null;

        final var summaryViewButton = summaryViewItemConfigV1.getSummaryViewStatusButtonMap()
                .get(summaryViewStatus);

        return SummaryViewItem.builder()
                .title(summaryViewItemConfigV1.getTitle())
                .subtitle(summaryViewItemConfigV1.getSubtitle())
                .imageUrl(summaryViewItemConfigV1.getSummaryViewStatusImageMap()
                        .get(summaryViewStatus))
                .statusImageUrl(Objects.nonNull(summaryViewItemConfigV1.getSummaryViewStatusIconMap())
                                ? summaryViewItemConfigV1.getSummaryViewStatusIconMap()
                                        .getOrDefault(summaryViewStatus, null)
                                : null)
                .identifierNumber(identifierNumber)
                .failureDetail(failureDetail)
                .status(summaryViewStatus)
                .button(ButtonAction.builder()
                        .action(action)
                        .buttonStyle(summaryViewButton.getButtonStyle())
                        .buttonText(summaryViewButton.getButtonText())
                        .build())
                .build();
    }

    public static SummaryViewResponse toSummaryViewResponse(final String title,
                                                            final String subtitle,
                                                            final ProgressDetail progressDetail,
                                                            final List<SummaryViewItem> summaryViewItemList,
                                                            final BigDecimal progressPercentage) {

        return SummaryViewResponse.builder()
                .title(title)
                .subtitle(subtitle)
                .progressDetail(ProgressDetail.builder()
                        .type(progressDetail.getType())
                        .percentage(progressPercentage)
                        .build())
                .summaryItemList(summaryViewItemList)
                .build();
    }

    public static AddOnProfileCacheKey toAddOnProfileCacheKey(final String organization,
                                                              final String namespace,
                                                              final String type,
                                                              final String version) {
        return AddOnProfileCacheKey.builder()
                .organization(organization)
                .namespace(namespace)
                .type(type)
                .version(version)
                .build();
    }

    public static PrimaryProfileCacheKey toPrimaryProfileCacheKey(final String organization,
                                                                  final String namespace,
                                                                  final String type,
                                                                  final String version) {
        return PrimaryProfileCacheKey.builder()
                .organization(organization)
                .namespace(namespace)
                .type(type)
                .version(version)
                .build();
    }

    public static PrimaryProfileCacheKey toPrimaryProfileCacheKey(final ProfileKey profileKey) {

        return PrimaryProfileCacheKey.builder()
                .organization(profileKey.getOrganization())
                .namespace(profileKey.getNamespace())
                .type(profileKey.getType())
                .version(profileKey.getVersion())
                .build();
    }

    public static PrimaryProfileCacheKey toPrimaryProfileCacheKey(final WorkflowInitV2Request workflowInitV2Request) {

        return PrimaryProfileCacheKey.builder()
                .namespace(workflowInitV2Request.getProfileKey()
                        .getNamespace())
                .organization(workflowInitV2Request.getProfileKey()
                        .getOrganization())
                .type(workflowInitV2Request.getProfileKey()
                        .getType())
                .version(workflowInitV2Request.getProfileKey()
                        .getVersion())
                .build();
    }

    public static WorkflowClientCallback buildWorkflowClientCallback(final CallbackWorkflowMessageBuilderProfileVisitorData callbackWorkflowMessageBuilderProfileVisitorData,
                                                                     final String workflowType,
                                                                     final String version,
                                                                     final ProfileType profileType,
                                                                     final String addOnType) {
        return WorkflowClientCallback.builder()
                .entityId(callbackWorkflowMessageBuilderProfileVisitorData.getStoredWorkflow()
                        .getEntityId())
                .workflowId(callbackWorkflowMessageBuilderProfileVisitorData.getStoredWorkflow()
                        .getWorkflowId())
                .entityType(callbackWorkflowMessageBuilderProfileVisitorData.getStoredWorkflow()
                        .getEntityType())
                .workflowState(State.valueOf(callbackWorkflowMessageBuilderProfileVisitorData.getStoredWorkflow()
                        .getCurrentState()
                        .name()))
                .failureErrorCode(callbackWorkflowMessageBuilderProfileVisitorData.getFailureErrorCode())
                .failureReason(Objects.nonNull(callbackWorkflowMessageBuilderProfileVisitorData.getFailureErrorCode())
                               ? callbackWorkflowMessageBuilderProfileVisitorData.getFailureErrorCode()
                                       .getReason()
                               : null)
                .tag(callbackWorkflowMessageBuilderProfileVisitorData.getStoredWorkflow()
                        .getTag())
                .workflowType(workflowType)
                .version(version)
                .profileType(profileType)
                .addOnType(addOnType)
                .requestId(callbackWorkflowMessageBuilderProfileVisitorData.getRequestId())
                .accessToken(callbackWorkflowMessageBuilderProfileVisitorData.getAccessToken())
                .build();
    }

    public static Map<TemplateType, UiResponseConfig> buildTerminalActionResponseMap() {

        return Map.of(TemplateType.SHADOW_V2, ShadowV2UiResponseConfig.builder()
                .shadowV2ResponseConfig(TerminalActionShadowV2ResponseConfig.builder()
                        .build())
                .build());
    }
}
