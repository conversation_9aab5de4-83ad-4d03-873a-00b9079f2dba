package com.phonepe.verified.kaizen.resources.v2;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.authz.annotations.WorkflowId;
import com.phonepe.verified.kaizen.models.data.common.PincodeDetails;
import com.phonepe.verified.kaizen.services.PincodeAndStateMatchingService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v2/location")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Location Details V2", description = "Location related V2 APIs")
public class LocationV2Resource {

    private final WorkflowService workflowService;

    private final SessionManagementService sessionManagementService;

    private final PincodeAndStateMatchingService pincodeAndStateMatchingService;

    @GET
    @AuthZ
    @Path("/pincode/{pincode}")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Fetch details for pincode")
    public PincodeDetails getPincodeDetails(@NotEmpty @PathParam("pincode") final String pincode,
                                            @QueryParam("sources") final Set<String> sources,
                                            @Parameter(hidden = true) @WorkflowId final String workflowId,
                                            @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                            @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                            @Parameter(hidden = true) @HeaderParam(Headers.X_SESSION_TOKEN) final String sessionToken) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        sessionManagementService.validateSessionAndThrowException(storedWorkflow.getWorkflowId(), requestInfo,
                userDetails, sessionToken);

        return pincodeAndStateMatchingService.getPincodeDetailsFromPincodeWithException(pincode, sources);
    }
}
