package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.key.PrimaryProfileCacheKey;
import com.phonepe.verified.kaizen.clients.internal.CallbackClient;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.AndPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.ClientCallbackPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.OrPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.TagCalculationOnEntityDetailsPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.TagCalculationOnWorkflowContextPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.tag.OrderedWorkflowTagRule;
import com.phonepe.verified.kaizen.models.configs.tag.WorkflowTagConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.responses.workflow.ActionFailureResponse;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.messages.WorkflowClientCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowMessage;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.impl.DetailsServiceImpl;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import io.dropwizard.util.Strings;
import java.util.Comparator;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class PostCompletionActionConfigExecutor implements
        PostCompletionActionConfigVisitor<CompletionState, WorkflowMessage> {

    private final ProfileService profileService;

    private final CallbackClient callbackClient;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final DetailsServiceImpl detailsService;

    private final WorkflowRepository workflowRepository;

    private final WorkflowContextStore workflowContextStore;

    private final EventIngestionCommand eventIngestionCommand;

    @Override
    public CompletionState visit(final AndPostCompletionActionConfig andPostCompletionActionConfig,
                                 final WorkflowMessage workflowMessage) {

        final var leftPostCompletionActionState = andPostCompletionActionConfig.getLeft()
                .accept(this, workflowMessage);

        if (CompletionState.SUCCESS != leftPostCompletionActionState) {
            return leftPostCompletionActionState;
        }

        return andPostCompletionActionConfig.getRight()
                .accept(this, workflowMessage);
    }

    @Override
    public CompletionState visit(final OrPostCompletionActionConfig orPostCompletionActionConfig,
                                 final WorkflowMessage workflowMessage) {

        final var leftPostCompletionActionState = orPostCompletionActionConfig.getLeft()
                .accept(this, workflowMessage);

        if (CompletionState.FAILURE != leftPostCompletionActionState) {
            return leftPostCompletionActionState;
        }

        return orPostCompletionActionConfig.getRight()
                .accept(this, workflowMessage);
    }

    @Override
    @SneakyThrows
    public CompletionState visit(final ClientCallbackPostCompletionActionConfig clientCallbackPostCompletionActionConfig,
                                 final WorkflowMessage workflowMessage) {

        final var callbackMessage = WorkflowClientCallbackMessage.builder()
                .workflowId(workflowMessage.getWorkflowId())
                .failureErrorCode(workflowMessage.getFailureErrorCode())
                .build();

        if (Strings.isNullOrEmpty(clientCallbackPostCompletionActionConfig.getCallbackService())
                || Strings.isNullOrEmpty(clientCallbackPostCompletionActionConfig.getCallbackServiceUrl())) {
            callbackClient.sendCallback(callbackMessage);
        } else {
            callbackClient.sendCallback(callbackMessage, clientCallbackPostCompletionActionConfig.getCallbackService(),
                    clientCallbackPostCompletionActionConfig.getCallbackServiceUrl(),
                    clientCallbackPostCompletionActionConfig.getFarm());
        }

        return CompletionState.SUCCESS;
    }

    @Override
    public CompletionState visit(final TagCalculationOnWorkflowContextPostCompletionActionConfig tagCalculationOnWorkflowContextPostCompletionActionConfig,
                                 final WorkflowMessage workflowMessage) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowMessage.getWorkflowId());

        if (TransitionState.COMPLETION_STATES_TO_BE_TAGGED.contains(storedWorkflow.getCurrentState())) {

            final var profile = profileService.get(storedWorkflow.getProfileId(), false);

            final var workflowTagConfig = profile.accept(GetWorkflowTagConfigVisitor.INSTANCE, null);

            final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflow.getWorkflowId());

            calculateTagAndSaveStoredWorkflow(storedWorkflow, workflowTagConfig,
                    MapperUtils.convertToJsonNode(workflowContext));

            final var actionFailureReason = workflowService.fetchWorkflowActionFailure(storedWorkflow)
                    .map(ActionFailureResponse::getReason)
                    .orElse(null);

            eventIngestionCommand.workflowTagCalculationEvent(storedWorkflow, profile, actionFailureReason);
        }

        return CompletionState.SUCCESS;
    }

    @Override
    public CompletionState visit(final TagCalculationOnEntityDetailsPostCompletionActionConfig tagCalculationOnEntityDetailsPostCompletionActionConfig,
                                 final WorkflowMessage workflowMessage) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowMessage.getWorkflowId());

        if (TransitionState.COMPLETION_STATES_TO_BE_TAGGED.contains(storedWorkflow.getCurrentState())) {

            final var primaryProfile = getPrimaryProfile(storedWorkflow.getProfileId());

            final var workflowTagConfig = primaryProfile.accept(GetWorkflowTagConfigVisitor.INSTANCE, null);

            final var entityDetailsResponse = detailsService.getEntityDetailsFromCache(storedWorkflow.getWorkflowId());

            calculateTagAndSaveStoredWorkflow(storedWorkflow, workflowTagConfig,
                    MapperUtils.convertToJsonNode(entityDetailsResponse));

            final var actionFailureReason = workflowService.fetchWorkflowActionFailure(storedWorkflow)
                    .map(ActionFailureResponse::getReason)
                    .orElse(null);

            eventIngestionCommand.workflowTagCalculationEvent(storedWorkflow, primaryProfile, actionFailureReason);
        }

        return CompletionState.SUCCESS;
    }

    private Profile getPrimaryProfile(final String profileId) {

        final var profile = profileService.get(profileId, false);

        if (ProfileType.ADD_ON == profile.getProfileType()) {
            return profileService.getPrimaryProfile(PrimaryProfileCacheKey.builder()
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .type(profile.getType())
                    .version(profile.getVersion())
                    .build(), false);
        }
        return profile;

    }

    private void calculateTagAndSaveStoredWorkflow(final StoredWorkflow storedWorkflow,
                                                   final WorkflowTagConfig workflowTagConfig,
                                                   final JsonNode jsonNode) {

        final var tag = workflowTagConfig.getOrderedWorkflowTagRules()
                .stream()
                .sorted(Comparator.comparing(OrderedWorkflowTagRule::getOrder))
                .filter(tagRule -> hopeLangService.evaluate(tagRule.getEvaluationRule(), jsonNode))
                .findFirst()
                .map(OrderedWorkflowTagRule::getTag)
                .orElse(workflowTagConfig.getDefaultTag());

        storedWorkflow.setTag(tag);

        workflowRepository.save(storedWorkflow);
    }
}
