package com.phonepe.verified.kaizen.services;

import java.util.List;

public interface MakerCheckerService<T, J, K> {

    T approve(String requestId,
              String approvedBy);

    List<J> getAllChangeMetadata();

    List<T> getAllChanges();

    J getChangeMetadata(String requestId);

    T getStagedChange(String requestId);

    T reject(String requestId,
             String rejectedBy);

    T stage(K changedResource,
            String existingResourceId,
            String requester);
}
