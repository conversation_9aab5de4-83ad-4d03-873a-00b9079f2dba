package com.phonepe.verified.kaizen.resources;

import static com.phonepe.verified.kaizen.resources.WorkflowResource.OPERATION_WORKFLOW_DETAILS;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.requests.details.EntityDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetail;
import com.phonepe.verified.kaizen.models.responses.details.EntityDetailsResponse;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.DetailsService;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.PvcoreTenant;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/entity")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Entity", description = "Entity Related APIs")
public class EntityResource {

    public static final String OPERATION_DETAILS = "DETAILS";
    public static final String OPERATION_ALL_DETAILS = "ALL_DETAILS";
    public static final String RESOURCE_ENTITY = "ENTITY";

    private final AuthZService authZService;

    private final DetailsService detailsService;

    private final EventIngestionCommand eventIngestionCommand;

    @POST
    @Path("/details")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Get the latest details saved for the entity")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public EntityDetailsResponse getEntityDetails(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                  @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                  @Valid @NotNull final EntityDetailsRequest entityDetailsRequest) {

        // Check if user has permission for entity details API
        final var tenants = getTenantsFromProfileCriteria(entityDetailsRequest);

        tenants.forEach(tenant -> authZService.authorizeOperationForTenant(tenant, RESOURCE_ENTITY, OPERATION_DETAILS,
                serviceUserPrincipal));

        // Check if user has permission for WF details for each required detail
        tenants.forEach(tenant -> authorizeDetailsRequest(tenant, entityDetailsRequest.getRequiredDetails(),
                serviceUserPrincipal));

        eventIngestionCommand.ingestEntityDetailsRequest(entityDetailsRequest, serviceUserPrincipal.getName());

        return detailsService.getEntityDetails(entityDetailsRequest);
    }

    @POST
    @Path("/details/all")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Get all the details saved for the entity")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public EntityDetailsResponse getAllEntityDetails(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                     @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                     @Valid @NotNull final EntityDetailsRequest entityDetailsRequest) {

        // Check if user has permission for entity details API
        final var pvcoreTenants = getTenantsFromProfileCriteria(entityDetailsRequest);

        pvcoreTenants.forEach(olympusTenant -> authZService.authorizeOperationForTenant(olympusTenant, RESOURCE_ENTITY,
                OPERATION_ALL_DETAILS, serviceUserPrincipal));

        // Check if user has permission for WF details for each required detail
        pvcoreTenants.forEach(
                olympusTenant -> authorizeDetailsRequest(olympusTenant, entityDetailsRequest.getRequiredDetails(),
                        serviceUserPrincipal));

        eventIngestionCommand.ingestEntityDetailsRequest(entityDetailsRequest, serviceUserPrincipal.getName());

        return detailsService.getAllEntityDetails(entityDetailsRequest);
    }

    private List<PvcoreTenant> getTenantsFromProfileCriteria(final EntityDetailsRequest entityDetailsRequest) {

        return entityDetailsRequest.getProfileCriteria()
                .stream()
                .map(profileCriteria -> authZService.getTenant(profileCriteria.getOrganization(),
                        profileCriteria.getNamespace()))
                .toList();
    }

    private void authorizeDetailsRequest(final PvcoreTenant pvcoreTenant,
                                         final List<RequiredDetail> requiredDetails,
                                         final ServiceUserPrincipal serviceUserPrincipal) {

        if (Objects.nonNull(requiredDetails)) {
            requiredDetails.forEach(
                    requiredDetail -> authZService.authorizeOperationForTenant(pvcoreTenant, OPERATION_WORKFLOW_DETAILS,
                            requiredDetail.getType()
                                    .name(), serviceUserPrincipal));
        }
    }
}
