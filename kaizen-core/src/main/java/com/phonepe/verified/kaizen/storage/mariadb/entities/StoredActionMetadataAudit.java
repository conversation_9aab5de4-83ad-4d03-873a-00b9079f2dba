package com.phonepe.verified.kaizen.storage.mariadb.entities;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredConsentActionMetadataAudit;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadataAudit;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredKeyValueMetadataAudit;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredOtpHurdleActionMetaDataAudit;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Getter
@Setter
@Entity
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "action_detail_audit")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "action_metadata_type", discriminatorType = DiscriminatorType.STRING)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "actionMetadataType", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.KEY_VALUE, value = StoredKeyValueMetadataAudit.class),
        @JsonSubTypes.Type(name = Names.CONSENT, value = StoredConsentActionMetadataAudit.class),
        @JsonSubTypes.Type(name = Names.OTP_DETAILS, value = StoredOtpHurdleActionMetaDataAudit.class),
        @JsonSubTypes.Type(name = Names.DOCUMENT_UPLOAD, value = StoredDocumentUploadActionMetadataAudit.class)})
public abstract class StoredActionMetadataAudit implements Sharded, Serializable {

    @Serial
    private static final long serialVersionUID = 9033454798124827574L;

    @Id
    @EmbeddedId
    private AuditPrimaryKey primaryKey;

    @Column(name = "REVTYPE", columnDefinition = "tinyint", updatable = false, insertable = false)
    private int revType;

    @Column(name = "action_id", columnDefinition = "varchar(45)", nullable = false)
    private String actionId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "action_metadata_type", columnDefinition = "varchar(40)", nullable = false, insertable = false, updatable = false)
    private ActionMetadataType actionMetadataType;

    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3)", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "last_updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3)", nullable = false)
    private LocalDateTime lastUpdatedAt;

    @PrePersist
    void createdAt() {

        final var now = LocalDateTime.now();

        if (this.createdAt == null) {
            this.createdAt = now;
        }
        if (this.lastUpdatedAt == null) {
            this.lastUpdatedAt = now;
        }
    }

    @PreUpdate
    void updatedAt() {
        this.lastUpdatedAt = LocalDateTime.now();
    }

    @Override
    public String getShardingKey() {
        return actionId;
    }
}
