package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.drishti.models.responses.CallbackResponse;
import com.phonepe.verified.drishti.models.responses.DrishtiResponse;
import com.phonepe.verified.drishti.models.responses.masking.MaskingResponse;
import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.queue.actors.HandleTtlCallbackActor;
import com.phonepe.verified.kaizen.queue.actors.ProcessAutoRetryActionClockworkActor;
import com.phonepe.verified.kaizen.queue.actors.ProcessDocumentMaskingCallbackActor;
import com.phonepe.verified.kaizen.queue.actors.ScheduledWorkflowAbortCallbackActor;
import com.phonepe.verified.kaizen.queue.actors.ScheduledWorkflowAbortForStepCallbackActor;
import com.phonepe.verified.kaizen.queue.actors.WorkflowAutoAbortCallbackActor;
import com.phonepe.verified.kaizen.queue.actors.WorkflowAutoSkipCallbackActor;
import com.phonepe.verified.kaizen.queue.messages.HandleTtlCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.ProcessAutoRetryClockworkActionMessage;
import com.phonepe.verified.kaizen.queue.messages.ProcessDocumentMaskingCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.ScheduledWorkflowAbortCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.ScheduledWorkflowStepAbortCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowAutoAbortCallbackMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowAutoSkipCallbackMessage;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.UserDetailsUtils;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/internal/callback")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Internal Callback", description = "Internal Callback APIs")
public class InternalCallbackResource {

    private final HandleTtlCallbackActor handleTtlCallbackActor;

    private final WorkflowAutoSkipCallbackActor workflowAutoSkipCallbackActor;

    private final WorkflowAutoAbortCallbackActor workflowAutoAbortCallbackActor;

    private final ScheduledWorkflowAbortCallbackActor scheduledWorkflowAbortCallbackActor;

    private final ProcessDocumentMaskingCallbackActor processDocumentMaskingCallbackActor;

    private final ProcessAutoRetryActionClockworkActor processAutoRetryActionClockworkActor;

    private final ScheduledWorkflowAbortForStepCallbackActor scheduledWorkflowAbortForStepCallbackActor;

    @POST
    @SneakyThrows
    @RolesAllowed(OlympusPermissionNames.CLOCKWORK)
    @Path("/clockwork/dependency/action/{actionId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Callback from Clockwork for document verification")
    public void clockworkDocumentVerificationCallback(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                      @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                      final @NotEmpty @PathParam("actionId") String actionId) {

        final var processSourceVerificationMessage = HandleTtlCallbackMessage.builder()
                .actionId(actionId)
                .userDetails(UserDetailsUtils.getGandalfUserDetails(null, serviceUserPrincipal))
                .build();

        handleTtlCallbackActor.publish(processSourceVerificationMessage);
    }

    @POST
    @SneakyThrows
    @RolesAllowed(OlympusPermissionNames.CLOCKWORK)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/workflow/auto/abort/{workflowId}")
    @Operation(summary = "Callback for auto aborting WFs based on Profile config")
    public void workflowAutoAbortCallback(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                          @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                          final @NotEmpty @PathParam("workflowId") String workflowId,
                                          final @NotNull WorkflowAutoAbortConfig workflowAutoAbortConfig) {

        workflowAutoAbortCallbackActor.publish(WorkflowAutoAbortCallbackMessage.builder()
                .workflowId(workflowId)
                .workflowAutoAbortConfig(workflowAutoAbortConfig)
                .userDetails(UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal))
                .build());
    }

    @POST
    @SneakyThrows
    @RolesAllowed(OlympusPermissionNames.CLOCKWORK)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/workflow/auto/skip/{workflowId}")
    @Operation(summary = "Callback for auto skipping WFs based on Profile config")
    public void workflowAutoSkipCallback(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                         @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                         final @NotEmpty @PathParam("workflowId") String workflowId,
                                         final @NotNull WorkflowAutoSkipConfig workflowAutoSkipConfig) {

        workflowAutoSkipCallbackActor.publish(WorkflowAutoSkipCallbackMessage.builder()
                .workflowId(workflowId)
                .workflowAutoSkipConfig(workflowAutoSkipConfig)
                .userDetails(UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal))
                .build());
    }

    @POST
    @SneakyThrows
    @Path("/clockwork/workflow/abort/{actionId}")
    @RolesAllowed(OlympusPermissionNames.CLOCKWORK)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Callback from Clockwork for aborting stale kyc workflow")
    public void clockworkVideoKycWorkflowAbortCallback(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                       @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                       final @NotEmpty @PathParam("actionId") String actionId) {

        scheduledWorkflowAbortCallbackActor.publish(ScheduledWorkflowAbortCallbackMessage.builder()
                .actionId(actionId)
                .userDetails(UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal))
                .build());
    }

    @POST
    @SneakyThrows
    @Path("/clockwork/workflow/abort/step/{workflowStepId}")
    @RolesAllowed(OlympusPermissionNames.CLOCKWORK)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Callback from Clockwork for aborting stale kyc workflow stuck in given workflow step")
    public void clockworkWorkflowAbortCallbackForStep(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                      @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                      final @NotEmpty @PathParam("workflowStepId") String workflowStepId) {

        scheduledWorkflowAbortForStepCallbackActor.publish(ScheduledWorkflowStepAbortCallbackMessage.builder()
                .workflowStepId(workflowStepId)
                .userDetails(UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal))
                .build());
    }

    @POST
    @SneakyThrows
    @Path("/clockwork/auto-retry/action/{actionMappingId}/{workflowStepId}")
    @Operation(summary = "Trigger scheduled auto retriable action")
    @RolesAllowed(OlympusPermissionNames.CLOCKWORK)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    public void triggerAutoRetriableScheduledAction(@Parameter(hidden = true) @GandalfUserContext final UserDetails gandalfUserDetails,
                                                    @Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                    @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                    final @NotEmpty @PathParam("actionMappingId") String actionMappingId,
                                                    final @NotEmpty @PathParam("workflowStepId") String workflowStepId,
                                                    final String autoRetryActionDetails) {

        final var processAutoRetryClockworkActionMessage = ProcessAutoRetryClockworkActionMessage.builder()
                .actionMappingId(actionMappingId)
                .workflowStepId(workflowStepId)
                .encryptedBase64AutoRetryActionDetailsString(autoRetryActionDetails)
                .build();

        processAutoRetryActionClockworkActor.publish(processAutoRetryClockworkActionMessage);
    }

    @POST
    @SneakyThrows
    @Path("/drishti/document_masking/{actionId}")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @RolesAllowed(OlympusPermissionNames.DOCUMENT_MASKING_CALLBACK)
    @Operation(summary = "Callback from drishti for document masking")
    public void drishtiDocumentMaskingCallback(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                               @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                               final @NotEmpty @PathParam("actionId") String actionId,
                                               final @NotNull @Valid CallbackResponse<DrishtiResponse<MaskingResponse>> drishtiInternalCallbackResponse) {

        final var processDocumentMaskingCallbackMessage = ProcessDocumentMaskingCallbackMessage.builder()
                .actionId(actionId)
                .requestId(drishtiInternalCallbackResponse.getRequestId())
                .drishtiResponse(drishtiInternalCallbackResponse.getDrishtiResponse())
                .build();

        processDocumentMaskingCallbackActor.publish(processDocumentMaskingCallbackMessage);
    }
}
