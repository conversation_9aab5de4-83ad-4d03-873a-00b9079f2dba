package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.ProfileScreenStatus;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import java.util.Comparator;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class IdentifyProfileScreenToTrigger implements ProfileScreenVisitor<Optional<ProfileScreenStatus>> {

    private final String workflowStepId;

    private final String screenMappingId;

    private final ActionService actionService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowContextStore workflowContextStore;

    @Override
    public Optional<ProfileScreenStatus> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        final var actionCompletionStatus = standardProfileScreenConfig.getStepActionConfig()
                .accept(new ProfileScreenCompletionActionConfigVisitor(workflowStepId, actionService, workflowService,
                        hopeLangService, workflowContextStore), null);

        if (standardProfileScreenConfig.getScreenMappingId()
                .equals(screenMappingId)) {

            return Optional.of(ProfileScreenStatus.builder()
                    .profileScreenConfigToTrigger(standardProfileScreenConfig)
                    .completionState(actionCompletionStatus.getCompletionState())
                    .build());
        }

        if (CompletionState.SUCCESS_STATES.contains(actionCompletionStatus.getCompletionState())) {
            return Optional.empty();
        }

        throw KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_ACTION_NOT_COMPLETED,
                Map.of("screenMappingId", screenMappingId, "workflowStepId", workflowStepId));
    }

    @Override
    public Optional<ProfileScreenStatus> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        for (final var orderedProfileScreen : sortedOrderedProfileScreenList) {

            final var profileScreenStatus = orderedProfileScreen.getProfileScreenConfig()
                    .accept(this);

            if (profileScreenStatus.isPresent()) {
                return profileScreenStatus;
            }
        }

        throw KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                Map.of("screenMappingId", screenMappingId, "workflowStepId", workflowStepId));
    }
}
