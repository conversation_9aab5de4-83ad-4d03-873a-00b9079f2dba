package com.phonepe.verified.kaizen.statemachines;

import com.phonepe.verified.kaizen.models.data.keys.StateMachineRegistryKey;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.CompleteWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.CreateWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.InitialActionCompleteWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.InitialActionInProgressWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.MoveBackToPseudoSuccessWorkflowStepAndRetryAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.MoveToInProgressWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.PurgeWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflowstep.UpdateWorkflowStepAction;
import com.phonepe.verified.kaizen.statemachines.factories.WorkflowStepStateMachineModelFactory;
import com.phonepe.verified.kaizen.storage.aerospike.commands.TransitionLockCommand;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineModelConfigurer;

@Slf4j
public class WorkflowStepStateMachine extends BaseStateMachine<TransitionState, TransitionEvent> {

    private final CreateWorkflowStepAction createWorkflowStepAction;

    private final UpdateWorkflowStepAction updateWorkflowStepAction;

    private final CompleteWorkflowStepAction completeWorkflowStepAction;

    private final MoveToInProgressWorkflowStepAction moveToInprogressWorkflowStepAction;

    private final InitialActionCompleteWorkflowStepAction initialActionCompleteWorkflowStepAction;

    private final InitialActionInProgressWorkflowStepAction initialActionInProgressWorkflowStepAction;

    private final MoveBackToPseudoSuccessWorkflowStepAndRetryAction moveBackToPseudoSuccessWorkflowStepAndRetryAction;

    private final PurgeWorkflowStepAction purgeWorkflowStepAction;

    public WorkflowStepStateMachine(final TransitionLockCommand transitionLockCommand,
                                    final UpdateWorkflowStepAction updateWorkflowStepAction,
                                    final CreateWorkflowStepAction createWorkflowStepAction,
                                    final CompleteWorkflowStepAction completeWorkflowStepAction,
                                    final MoveToInProgressWorkflowStepAction moveToInprogressWorkflowStepAction,
                                    final InitialActionCompleteWorkflowStepAction initialActionCompleteWorkflowStepAction,
                                    final InitialActionInProgressWorkflowStepAction initialActionInProgressWorkflowStepAction,
                                    final MoveBackToPseudoSuccessWorkflowStepAndRetryAction moveBackToPseudoSuccessWorkflowStepAndRetryAction,
                                    final PurgeWorkflowStepAction purgeWorkflowStepAction) {
        super(transitionLockCommand);
        this.createWorkflowStepAction = createWorkflowStepAction;
        this.moveToInprogressWorkflowStepAction = moveToInprogressWorkflowStepAction;
        this.completeWorkflowStepAction = completeWorkflowStepAction;
        this.updateWorkflowStepAction = updateWorkflowStepAction;
        this.initialActionCompleteWorkflowStepAction = initialActionCompleteWorkflowStepAction;
        this.initialActionInProgressWorkflowStepAction = initialActionInProgressWorkflowStepAction;
        this.moveBackToPseudoSuccessWorkflowStepAndRetryAction = moveBackToPseudoSuccessWorkflowStepAndRetryAction;
        this.purgeWorkflowStepAction = purgeWorkflowStepAction;
    }

    @Override
    @SneakyThrows
    protected void configure(final StateMachineModelConfigurer<TransitionState, TransitionEvent> model) {
        model.withModel()
                .factory(new WorkflowStepStateMachineModelFactory(createWorkflowStepAction, updateWorkflowStepAction,
                        completeWorkflowStepAction, moveToInprogressWorkflowStepAction,
                        initialActionCompleteWorkflowStepAction, initialActionInProgressWorkflowStepAction,
                        moveBackToPseudoSuccessWorkflowStepAndRetryAction, purgeWorkflowStepAction));
    }

    @Override
    public StateMachineRegistryKey getStateMachineRegistryKey() {
        return null;
    }
}
