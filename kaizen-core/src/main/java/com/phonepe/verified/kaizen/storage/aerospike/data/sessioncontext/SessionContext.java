package com.phonepe.verified.kaizen.storage.aerospike.data.sessioncontext;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.data.EntityType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionContext {

    private String workflowId;

    private String profileId;

    private String phoneNumber;

    private String entityId;

    private EntityType entityType;

    private UserDetails userDetails;

    private RequestInfo requestInfo;

    private String sessionToken;

}
