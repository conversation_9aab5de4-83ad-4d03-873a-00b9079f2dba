package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.ConfirmationActionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.Events;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@SuppressWarnings("java:S3516")
@EqualsAndHashCode(callSuper = true)
public class ConfirmationActionActor extends BaseActor<ConfirmationActionMessage> {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    @Inject
    protected ConfirmationActionActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                      final ConnectionRegistry connectionRegistry,
                                      final ObjectMapper mapper,
                                      final RetryStrategyFactory retryStrategyFactory,
                                      final ExceptionHandlingFactory exceptionHandlingFactory,
                                      final ActionService actionService,
                                      final ProfileService profileService,
                                      final WorkflowService workflowService,
                                      final WorkflowStepService workflowStepService) {
        super(ActorType.CONFIRMATION_ACTION, actorConfigMap.get(ActorType.CONFIRMATION_ACTION), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, ConfirmationActionMessage.class);
        this.actionService = actionService;
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
    }

    @Override
    protected boolean handleMessage(final ConfirmationActionMessage confirmationActionMessage) {

        final var storedAction = actionService.validateAndGetAction(confirmationActionMessage.getActionId());

        if (!CompletionState.IN_PROGRESS.equals(storedAction.getCompletionState())) {
            return true;
        }

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        final var uiRequestContext = workflowStepService.getUiRequestContext(storedWorkflowStep.getWorkflowStepId());

        final var sectionInputData = Objects.nonNull(uiRequestContext)
                                     ? ((ShadowV2UiRequestContext) uiRequestContext).getSectionInputData()
                                     : null;

        final var apiVersion = Objects.nonNull(uiRequestContext)
                               ? uiRequestContext.getApiVersion()
                               : null;

        workflowStepService.getUiRequestContextAndSaveNewUiRequestContextWithExistingRequestDetails(
                storedWorkflowStep.getWorkflowStepId(), sectionInputData, confirmationActionMessage.getRequestInfo(),
                confirmationActionMessage.getComponentKitVersion(), storedWorkflow.getEntityId(),
                confirmationActionMessage.getIntent(), apiVersion, storedWorkflow.getWorkflowId());

        actionService.triggerEvent(storedAction.getActionId(), storedAction.getActionType(),
                storedAction.getStateMachineVersion(), Events.CONFIRMED, Constants.EMPTY_TRANSITION_CONTEXT,
                Constants.PVCORE_SYSTEM_USER, new HashMap<>());

        return true;
    }
}
