package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadataAudit;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Getter
@Setter
@Entity
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.DOCUMENT_UPLOAD)
public class StoredDocumentUploadActionMetadataAudit extends StoredActionMetadataAudit {

    private static final long serialVersionUID = 2185034016370184754L;

    @Column(name = "reference_id", columnDefinition = "varchar(45)")
    private String requestId;

    @Column(name = "global_reference", columnDefinition = "varchar(45)")
    private String documentId;

    @Column(name = "name", columnDefinition = "varchar(128)")
    @Enumerated(EnumType.STRING)
    private DocumentType documentType;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String documentLabel;

    @Builder
    public StoredDocumentUploadActionMetadataAudit(@NonNull final String actionId,
                                                   final int revType,
                                                   final LocalDateTime createdAt,
                                                   final LocalDateTime lastUpdatedAt,
                                                   final String requestId,
                                                   final String documentId,
                                                   final DocumentType documentType,
                                                   final String documentLabel) {
        super(BuildUtils.auditPrimaryKeyForUpdate(), revType, actionId, ActionMetadataType.DOCUMENT_UPLOAD, createdAt,
                lastUpdatedAt);
        this.requestId = requestId;
        this.documentId = documentId;
        this.documentType = documentType;
        this.documentLabel = documentLabel;
    }
}
