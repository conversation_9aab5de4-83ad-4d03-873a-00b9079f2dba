package com.phonepe.verified.kaizen.storage.mariadb.entities;

import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.With;
import lombok.experimental.FieldNameConstants;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@AuditTable(value = "workflow_step_audit")
@Table(name = "workflow_step", indexes = {
        @Index(name = "unq_idx_workflow_step_id", columnList = "workflow_step_id, partition_id", unique = true)})
public class StoredWorkflowStep implements Sharded, Serializable {

    @Id
    @EmbeddedId
    private PrimaryKey primaryKey;

    @Column(name = "workflow_step_id", columnDefinition = "varchar(64)", nullable = false)
    private String workflowStepId;

    @Column(name = "workflow_id", columnDefinition = "varchar(64)", nullable = false)
    private String workflowId;

    @Column(name = "profile_step_id", columnDefinition = "varchar(64)", nullable = false)
    private String profileStepId;

    @Column(name = "current_state", columnDefinition = "varchar(128)", nullable = false)
    @Enumerated(EnumType.STRING)
    private TransitionState currentState;

    @Column(name = "current_event", columnDefinition = "varchar(128)", nullable = false)
    @Enumerated(EnumType.STRING)
    private TransitionEvent currentEvent;

    @Column(name = "last_updated_by", columnDefinition = "varchar(128)", nullable = false)
    private String lastUpdatedBy;

    @Column(name = "last_updater_type", columnDefinition = "varchar(128)", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpdaterType lastUpdaterType;

    @With
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3)", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "last_updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3)", nullable = false)
    private LocalDateTime lastUpdatedAt;

    // TODO:: Once migration is done, add @CreationTimestamp and @UpdateTimestamp
    @PrePersist
    void createdAt() {

        final var now = LocalDateTime.now();

        if (this.createdAt == null) {
            this.createdAt = now;
        }
        if (this.lastUpdatedAt == null) {
            this.lastUpdatedAt = now;
        }
    }

    @PreUpdate
    void updatedAt() {
        this.lastUpdatedAt = LocalDateTime.now();
    }

    @Override
    public String getShardingKey() {
        return workflowStepId;
    }
}
