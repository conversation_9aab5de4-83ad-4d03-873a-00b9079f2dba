package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.services.impl.ProfileServiceImpl;

@Singleton
public class ProfileStepCache extends Cache<String, ProfileStep> {

    private final Provider<ProfileServiceImpl> profileService;

    @Inject
    public ProfileStepCache(final CaffeineCacheConfig caffeineCacheConfig,
                            final MetricRegistry metricRegistry,
                            final Provider<ProfileServiceImpl> profileService) {
        super(CacheName.PROFILE_STEP_CACHE, caffeineCacheConfig, metricRegistry);
        this.profileService = profileService;
    }

    @Override
    protected ProfileStep build(final String profileStepId) {
        return profileService.get()
                .getProfileStepFromDb(profileStepId);
    }
}
