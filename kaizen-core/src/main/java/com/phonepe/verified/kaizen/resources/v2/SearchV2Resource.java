package com.phonepe.verified.kaizen.resources.v2;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.request.SearchFieldRequest;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.authz.annotations.WorkflowId;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.data.common.MailboxApiName;
import com.phonepe.verified.kaizen.queue.actors.ProcessSearchClientDataActor;
import com.phonepe.verified.kaizen.queue.messages.SearchClientDataMessage;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v2/search")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Search V2", description = "Search related V2 APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class SearchV2Resource {

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final SessionManagementService sessionManagementService;

    private final ProcessSearchClientDataActor processSearchClientDataActor;

    @POST
    @AuthZ
    @SneakyThrows
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/client/data/{fieldId}/{intent}/{componentKitVersion}")
    @Operation(summary = "Search API call from PhonePe Verified SDK")
    public Response search(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                           @Parameter(hidden = true) @WorkflowId final String workflowId,
                           @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                           @Valid @NotNull final SearchFieldRequest searchFieldRequest,
                           @Parameter(hidden = true) @HeaderParam(Headers.X_SESSION_TOKEN) final String sessionToken,
                           @NotEmpty @PathParam("fieldId") final String fieldId,
                           @NotEmpty @PathParam("intent") final String intent,
                           @PathParam("componentKitVersion") final long componentKitVersion) {

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        processSearchClientDataActor.publish(SearchClientDataMessage.builder()
                .workflowId(workflowId)
                .fieldId(fieldId)
                .intent(intent)
                .componentKitVersion(componentKitVersion)
                .searchFieldRequest(searchFieldRequest)
                .requestInfo(requestInfo)
                .build());

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.SEARCH_CLIENT_DATA)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.SEARCH_CLIENT_DATA)
                        .toMilliseconds())
                .build();
    }
}
