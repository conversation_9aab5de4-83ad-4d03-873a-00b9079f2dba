package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.caches.key.SessionManagementConfigCacheKey;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredSessionManagementConfig;
import java.util.Optional;

@Singleton
public class SessionManagementConfigCache extends
        Cache<SessionManagementConfigCacheKey, Optional<StoredSessionManagementConfig>> {

    private final Provider<SessionManagementService> sessionManagementServiceProvider;

    @Inject
    public SessionManagementConfigCache(final CaffeineCacheConfig caffeineCacheConfig,
                                        final MetricRegistry metricRegistry,
                                        final Provider<SessionManagementService> sessionManagementServiceProvider) {
        super(CacheName.SESSION_MANAGEMENT_CONFIG_CACHE, caffeineCacheConfig, metricRegistry);
        this.sessionManagementServiceProvider = sessionManagementServiceProvider;
    }

    @Override
    protected Optional<StoredSessionManagementConfig> build(final SessionManagementConfigCacheKey sessionManagementConfigCacheKey) {
        return sessionManagementServiceProvider.get()
                .getSessionManagementConfigFromDb(sessionManagementConfigCacheKey.getProfileId(),
                        sessionManagementConfigCacheKey.getSourceType());
    }
}
