package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowAutoSkipCallbackMessage;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoSkipCallbackHandler;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoSkipCallbackHandler.WorkflowAutoSkipCallbackHandlerData;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import java.util.Objects;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class WorkflowAutoSkipCallbackActor extends BaseActor<WorkflowAutoSkipCallbackMessage> {


    private final WorkflowAutoSkipCallbackHandler workflowAutoSkipCallbackHandler;

    private final WorkflowService workflowService;

    private final ProfileService profileService;


    @Inject
    protected WorkflowAutoSkipCallbackActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                            final ConnectionRegistry connectionRegistry,
                                            final ObjectMapper mapper,
                                            final RetryStrategyFactory retryStrategyFactory,
                                            final ExceptionHandlingFactory exceptionHandlingFactory,
                                            final ProfileService profileService,
                                            final WorkflowService workflowService,
                                            final WorkflowAutoSkipCallbackHandler workflowAutoSkipCallbackHandler) {

        super(ActorType.WORKFLOW_AUTO_SKIP, actorConfigMap.get(ActorType.WORKFLOW_AUTO_SKIP), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, WorkflowAutoSkipCallbackMessage.class);

        this.workflowAutoSkipCallbackHandler = workflowAutoSkipCallbackHandler;
        this.workflowService = workflowService;
        this.profileService = profileService;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final WorkflowAutoSkipCallbackMessage workflowAutoSkipCallbackMessage) {

        // Ensure no retry config. We don't want to reschedule any faulty messages on Clockwork

        final var storedWorkflow = this.workflowService.validateAndGetWorkflow(
                workflowAutoSkipCallbackMessage.getWorkflowId());

        final var profile = this.profileService.get(storedWorkflow.getProfileId(), false);

        // If somehow the auto skip config is removed from the profile after clockwork callback is scheduled
        if (Objects.isNull(workflowAutoSkipCallbackMessage.getWorkflowAutoSkipConfig())) {
            log.warn(
                    "Received workflow auto abort clockwork callback for WF {} with PF {} without any auto abort config in profile. Ignoring callback.",
                    storedWorkflow.getWorkflowId(), profile.getProfileId());
            return true;
        }

        return workflowAutoSkipCallbackMessage.getWorkflowAutoSkipConfig()
                .accept(this.workflowAutoSkipCallbackHandler, WorkflowAutoSkipCallbackHandlerData.builder()
                        .storedWorkflow(storedWorkflow)
                        .userDetails(workflowAutoSkipCallbackMessage.getUserDetails())
                        .build());
    }
}
