package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.drishti.models.commons.documents.Document;
import com.phonepe.verified.drishti.models.commons.documents.DocumentType.DocumentTypeVisitor;
import com.phonepe.verified.drishti.models.requests.extraction.ExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.AadhaarExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.DrivingLicenseExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.PanExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.PassportExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.VehicleRegistrationCertificateExtractionContext;
import com.phonepe.verified.drishti.models.requests.extraction.impl.VoterExtractionContext;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DrishtiDocumentTypeToExtractionContextVisitor implements
        DocumentTypeVisitor<ExtractionContext, List<Document>> {

    public static final DrishtiDocumentTypeToExtractionContextVisitor INSTANCE = new DrishtiDocumentTypeToExtractionContextVisitor();

    @Override
    public ExtractionContext visitPan(final List<Document> documents) {

        return PanExtractionContext.builder()
                .document(documents.get(0))
                .build();
    }

    @Override
    public ExtractionContext visitVoter(final List<Document> documents) {

        final var voterExtractionContextBuilder = VoterExtractionContext.builder()
                .documentFront(documents.get(0));

        if (documents.size() >= 2) {
            voterExtractionContextBuilder.documentBack(documents.get(1));
        }

        return voterExtractionContextBuilder.build();
    }

    @Override
    public ExtractionContext visitDrivingLicense(final List<Document> documents) {

        final var drivingLicenseExtractionContextBuilder = DrivingLicenseExtractionContext.builder()
                .documentFront(documents.get(0));

        if (documents.size() >= 2) {
            drivingLicenseExtractionContextBuilder.documentBack(documents.get(1));
        }

        return drivingLicenseExtractionContextBuilder.build();
    }

    @Override
    public ExtractionContext visitAadhaar(final List<Document> documents) {

        final var aadhaarExtractionContextBuilder = AadhaarExtractionContext.builder()
                .documentFront(documents.get(0));

        if (documents.size() >= 2) {
            aadhaarExtractionContextBuilder.documentBack(documents.get(1));
        }

        return aadhaarExtractionContextBuilder.build();
    }

    @Override
    public ExtractionContext visitPassport(final List<Document> documents) {

        final var passportExtractionContextBuilder = PassportExtractionContext.builder()
                .documentFront(documents.get(0));

        if (documents.size() >= 2) {
            passportExtractionContextBuilder.documentBack(documents.get(1));
        }

        return passportExtractionContextBuilder.build();
    }

    @Override
    public ExtractionContext visitVehicleRegistrationCertificate(final List<Document> documents) {

        final var vehicleRegistrationCertificateExtractionContextBuilder = VehicleRegistrationCertificateExtractionContext.builder()
                .documentFront(documents.get(0));

        if (documents.size() >= 2) {
            vehicleRegistrationCertificateExtractionContextBuilder.documentBack(documents.get(1));
        }

        return vehicleRegistrationCertificateExtractionContextBuilder.build();
    }

    @Override
    public ExtractionContext visitGstCertificate(final List<Document> documents) {
        return null;
    }

    @Override
    public ExtractionContext visitSelfie(final List<Document> documents) {
        return null;
    }
}
