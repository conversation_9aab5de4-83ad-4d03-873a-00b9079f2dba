package com.phonepe.verified.kaizen.resources;

import com.phonepe.olympus.im.models.core.CoreConstants;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.security.SecuritySchemes;

@OpenAPIDefinition(security = {@SecurityRequirement(name = CoreConstants.BEARER)})
@SecuritySchemes(@SecurityScheme(name = CoreConstants.BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization"))
public interface OpenApiConfig {

}
