package com.phonepe.verified.kaizen.resources.v2;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.response.v2.TemplateInitV2Response;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewResponse;
import com.phonepe.verified.kaizen.authz.annotations.AuthZ;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.authz.annotations.WorkflowId;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.models.data.common.MailboxApiName;
import com.phonepe.verified.kaizen.models.requests.v2.ShadowV2SectionSubmitV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.AbortWorkflowRequestV2;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.FilterAllWorkflowIdsV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.ResumeWorkflowV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowAddOnV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowInitV2Request;
import com.phonepe.verified.kaizen.models.responses.ApiResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.AccessTokenResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowFilterResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowInitAsyncResponse;
import com.phonepe.verified.kaizen.queue.actors.AbortWorkflowV2Actor;
import com.phonepe.verified.kaizen.queue.actors.SectionSubmitV2Actor;
import com.phonepe.verified.kaizen.queue.actors.SkipWorkflowStepActor;
import com.phonepe.verified.kaizen.queue.messages.AbortWorkflowV2Message;
import com.phonepe.verified.kaizen.queue.messages.SectionSubmitV2Message;
import com.phonepe.verified.kaizen.queue.messages.SkipWorkflowStepMessage;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.Constants.OlympusPermissionNames;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.UserDetailsUtils;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v2/workflow")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__(@Inject))
@Tag(name = "Workflow V2", description = "Workflow V2 Related APIs")
public class WorkflowV2Resource {

    private static final String RESOURCE_WORKFLOW_V2 = "WORKFLOW_V2";

    private static final String OPERATION_WORKFLOW_V2_INIT = "INIT";

    private static final String OPERATION_WORKFLOW_V2_ADD_ON = "ADD_ON";

    private static final String OPERATION_WORKFLOW_V2_RESUME = "RESUME";

    private static final String OPERATION_WORKFLOW_V2_FILTER = "FILTER";

    private static final String OPERATION_WORKFLOW_V2_ABORT = "ABORT";


    private final AuthZService authZService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final SectionSubmitV2Actor sectionSubmitV2Actor;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final SessionManagementService sessionManagementService;

    private final SkipWorkflowStepActor skipWorkflowStepActor;

    private final AbortWorkflowV2Actor abortWorkflowV2Actor;

    @POST
    @Path("/init")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Initiate workflow v2 for given user")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public AccessTokenResponse init(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                    @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                    @Valid @NotNull final WorkflowInitV2Request workflowInitV2Request) {

        authZService.authorizeOperationForTenant(authZService.getTenant(workflowInitV2Request.getProfileKey()
                .getOrganization(), workflowInitV2Request.getProfileKey()
                .getNamespace()), RESOURCE_WORKFLOW_V2, OPERATION_WORKFLOW_V2_INIT, serviceUserPrincipal);

        return workflowService.initV2(workflowInitV2Request, requestInfo,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal));
    }

    @POST
    @SneakyThrows
    @Path("/init/async")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Initiate workflow v2 for given user in async")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public WorkflowInitAsyncResponse initAsync(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                               @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                               @Valid @NotNull final WorkflowInitV2Request workflowInitV2Request) {

        authZService.authorizeOperationForTenant(authZService.getTenant(workflowInitV2Request.getProfileKey()
                .getOrganization(), workflowInitV2Request.getProfileKey()
                .getNamespace()), RESOURCE_WORKFLOW_V2, OPERATION_WORKFLOW_V2_INIT, serviceUserPrincipal);

        final var requestId = workflowService.initAsyncV2(workflowInitV2Request,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal));

        return WorkflowInitAsyncResponse.builder()
                .requestId(requestId)
                .build();
    }

    @POST
    @Path("/filter")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Filter all workflow IDs for given timestamp range, profile parameters and workflow states")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public WorkflowFilterResponse filterAllWorkflowIds(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                                       @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                       @Valid @NotNull final FilterAllWorkflowIdsV2Request workflowRequest) {

        authZService.authorizeOperationForTenant(
                authZService.getTenant(workflowRequest.getOrganization(), workflowRequest.getNamespace()),
                RESOURCE_WORKFLOW_V2, OPERATION_WORKFLOW_V2_FILTER, serviceUserPrincipal);

        return workflowService.filterAllWorkflowIds(workflowRequest);
    }

    @DELETE
    @SneakyThrows
    @Path("/abort")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Abort given workflow")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void abort(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                      @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                      @Valid @NotNull final AbortWorkflowRequestV2 abortWorkflowRequest,
                      @QueryParam("forceAbortTerminalStates") @DefaultValue("false") final boolean forceAbortTerminalStates) {

        abortWorkflowRequest.getWorkflowIds()
                .forEach(workflowId -> authZService.authorizeOperationForTenant(
                        authZService.getTenantFromWorkflow(workflowId), RESOURCE_WORKFLOW_V2,
                        OPERATION_WORKFLOW_V2_ABORT, serviceUserPrincipal));

        abortWorkflowV2Actor.publish(AbortWorkflowV2Message.builder()
                .workflowIdsToAbort(abortWorkflowRequest.getWorkflowIds())
                .callerUserDetails(UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal))
                .forceAbortTerminalStates(forceAbortTerminalStates)
                .build());
    }


    @POST
    @Path("/add-on")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Initiate v2 add on workflow for given user")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public AccessTokenResponse addOn(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                     @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                     @Valid @NotNull final WorkflowAddOnV2Request workflowAddOnV2Request) {

        authZService.authorizeOperationForTenant(authZService.getTenant(workflowAddOnV2Request.getProfileKey()
                .getOrganization(), workflowAddOnV2Request.getProfileKey()
                .getNamespace()), RESOURCE_WORKFLOW_V2, OPERATION_WORKFLOW_V2_ADD_ON, serviceUserPrincipal);

        return workflowService.addOnV2(workflowAddOnV2Request, requestInfo,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal));
    }

    @PUT
    @Path("/resume")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Resume workflow V2 for given workflowId and userReferenceId")
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public AccessTokenResponse resume(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                      @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                      @Valid @NotNull final ResumeWorkflowV2Request resumeWorkflowV2Request) {

        authZService.authorizeOperationForTenant(
                authZService.getTenantFromWorkflow(resumeWorkflowV2Request.getWorkflowId()), RESOURCE_WORKFLOW_V2,
                OPERATION_WORKFLOW_V2_RESUME, serviceUserPrincipal);

        return workflowService.resumeV2(resumeWorkflowV2Request, requestInfo);
    }

    @GET
    @AuthZ
    @SneakyThrows
    @Path("/{intent}/{componentKitVersion}/template")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Fetch template for given v2 intent and componentKitVersion")
    public ApiResponse<TemplateInitV2Response> getTemplate(@NotEmpty @PathParam("intent") final String intent,
                                                           @Parameter(hidden = true) @WorkflowId final String workflowId,
                                                           @PathParam("componentKitVersion") final long componentKitVersion,
                                                           @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                                           @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                           @QueryParam("showSummaryView") @DefaultValue("false") final boolean showSummaryView,
                                                           @Parameter(hidden = true) @HeaderParam(Headers.X_SESSION_TOKEN) final String sessionToken) {

        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        final var templateInitV2Response = workflowService.getTemplateV2(storedWorkflow.getWorkflowId(), intent,
                componentKitVersion, showSummaryView);

        return ApiResponse.<TemplateInitV2Response>builder()
                .data(templateInitV2Response)
                .success(true)
                .build();
    }

    @POST
    @AuthZ
    @SneakyThrows
    @Path("/section/submit")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Operation(summary = "Section submit V2 request")
    public Response sectionSubmit(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                  @Parameter(hidden = true) @AuthZContext final UserDetails gandalfUserDetails,
                                  @Parameter(hidden = true) @WorkflowId final String workflowId,
                                  @Parameter(hidden = true) @HeaderParam(Headers.X_SESSION_TOKEN) final String sessionToken,
                                  @Valid @NotNull final ShadowV2SectionSubmitV2Request shadowV2SectionSubmitV2Request) {

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, gandalfUserDetails,
                sessionToken);

        sectionSubmitV2Actor.publish(SectionSubmitV2Message.builder()
                .shadowV2SectionSubmitV2Request(shadowV2SectionSubmitV2Request)
                .workflowId(workflowId)
                .requestInfo(requestInfo)
                .userDetails(gandalfUserDetails)
                .build());

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.SECTION_SUBMIT)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.SECTION_SUBMIT)
                        .toMilliseconds())
                .build();
    }

    @AuthZ
    @DELETE
    @SneakyThrows
    @Operation(summary = "Skip Workflow Step")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @Path("/step/{profileStepMappingId}/{screenMappingId}/skip/{intent}/{componentKitVersion}")
    public Response skipWorkflowStep(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                     @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                     @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken,
                                     @PathParam("intent") @NotEmpty final String intent,
                                     @Parameter(hidden = true) @WorkflowId final String workflowId,
                                     @PathParam("componentKitVersion") final long componentKitVersion,
                                     @PathParam("profileStepMappingId") @NotEmpty final String profileStepMappingId,
                                     @PathParam("screenMappingId") @NotEmpty final String screenMappingId) {

        sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails, sessionToken);

        skipWorkflowStepActor.publish(SkipWorkflowStepMessage.builder()
                .intent(intent)
                .workflowId(workflowId)
                .userDetails(userDetails)
                .requestInfo(requestInfo)
                .apiVersion(ApiVersion.V2)
                .componentKitVersion(componentKitVersion)
                .profileStepMappingId(profileStepMappingId)
                .screenMappingId(screenMappingId)
                .build());

        return Response.accepted()
                .header(Headers.X_RESPONSE_DYNAMIC_MAILBOX, true)
                .header(Headers.X_POLLING_TIME, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingTime(MailboxApiName.WORKFLOW_STEP_SKIP)
                        .toMilliseconds())
                .header(Headers.X_POLLING_FREQUENCY, appConfigProvider.getData()
                        .getMailboxConfig()
                        .getPollingFrequency(MailboxApiName.WORKFLOW_STEP_SKIP)
                        .toMilliseconds())
                .build();
    }

    @GET
    @SneakyThrows
    @Path("/summary/{intent}/{componentKitVersion}")
    @Operation(summary = "Fetch summary for given workflowId")
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @AuthZ(gandalfPermission = "getSummary", olympusPermission = OlympusPermissionNames.GET_SUMMARY)
    public ApiResponse<SummaryViewResponse> getSummary(@Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                                       @Parameter(hidden = true) @AuthZContext final UserDetails userDetails,
                                                       @Parameter(hidden = true) @HeaderParam(Constants.Headers.X_SESSION_TOKEN) final String sessionToken,
                                                       @Parameter(hidden = true) @WorkflowId final String workflowId,
                                                       @NotEmpty @PathParam("intent") final String intent,
                                                       @PathParam("componentKitVersion") final long componentKitVersion) {

        this.sessionManagementService.validateSessionAndThrowException(workflowId, requestInfo, userDetails,
                sessionToken);

        final var summaryViewResponse = this.profileService.getSummaryView(workflowId, intent, componentKitVersion);

        return ApiResponse.<SummaryViewResponse>builder()
                .data(summaryViewResponse)
                .success(true)
                .build();
    }
}