package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.common.CurrentProfileScreenNextProfileScreen;
import java.util.Comparator;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

/*
 * Assumption: This visitor assumes that any screen which is traversed by this visitor
 * and is before screenMappingId found is considered as a completed screen.
 * Assumption is made considering that visitor will be invoked as a part of handshake only
 * (Handshake is performed only when one of the screens is completed or failed)
 */
@RequiredArgsConstructor
public class ProfileScreenCompletionVisitor implements ProfileScreenVisitor<CurrentProfileScreenNextProfileScreen> {

    private final String screenMappingId;

    @Override
    public CurrentProfileScreenNextProfileScreen visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        if (screenMappingId.equals(standardProfileScreenConfig.getScreenMappingId())) {

            return CurrentProfileScreenNextProfileScreen.builder()
                    .mappingIdFound(true)
                    .nextProfileScreenConfig(null)
                    .currentProfileScreenConfig(standardProfileScreenConfig)
                    .build();
        }

        return CurrentProfileScreenNextProfileScreen.builder()
                .mappingIdFound(false)
                .nextProfileScreenConfig(null)
                .currentProfileScreenConfig(null)
                .build();
    }

    @Override
    public CurrentProfileScreenNextProfileScreen visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        final var iterator = sortedOrderedProfileScreenList.listIterator();

        while (iterator.hasNext()) {

            final var orderedProfileScreen = iterator.next();

            final var profileScreenCompletion = orderedProfileScreen.getProfileScreenConfig()
                    .accept(this);

            if (profileScreenCompletion.isMappingIdFound()) {

                if (Objects.isNull(profileScreenCompletion.getNextProfileScreenConfig())) {

                    return CurrentProfileScreenNextProfileScreen.builder()
                            .mappingIdFound(true)
                            .nextProfileScreenConfig(iterator.hasNext()
                                                     ? iterator.next()
                                                             .getProfileScreenConfig()
                                                     : null)
                            .currentProfileScreenConfig(profileScreenCompletion.getCurrentProfileScreenConfig())
                            .build();
                } else {

                    return profileScreenCompletion;
                }
            }
        }

        return CurrentProfileScreenNextProfileScreen.builder()
                .mappingIdFound(false)
                .nextProfileScreenConfig(null)
                .currentProfileScreenConfig(null)
                .build();
    }
}