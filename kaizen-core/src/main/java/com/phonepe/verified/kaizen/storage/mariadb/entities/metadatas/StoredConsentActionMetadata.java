package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.services.ActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.CONSENT)
public class StoredConsentActionMetadata extends StoredActionMetadata {

    @Column(name = "name", columnDefinition = "varchar(128)")
    private String consentGranted;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String consentDetailsUrl;

    @Column(name = "state", columnDefinition = "varchar(32)")
    private String language;

    @Column(name = "source_type", columnDefinition = "varchar(16)")
    private String sourceType;

    @Column(name = "source_version", columnDefinition = "varchar(16)")
    private String sourceVersion;

    @Column(name = "source_platform", columnDefinition = "varchar(16)")
    private String sourcePlatform;

    @Column(name = "approver_id", columnDefinition = "varchar(45)")
    private String consentGrantedBy;

    @Column(name = "reference_type", columnDefinition = "char(40)")
    private String consentType;

    @Builder

    public StoredConsentActionMetadata(@NonNull final String actionId,
                                       final LocalDateTime createdAt,
                                       final LocalDateTime lastUpdatedAt,
                                       final String consentGranted,
                                       final String consentDetailsUrl,
                                       final String language,
                                       final String sourceType,
                                       final String sourceVersion,
                                       final String sourcePlatform,
                                       final String consentGrantedBy,
                                       final String consentType) {
        super(BuildUtils.primaryKey(), actionId, ActionMetadataType.CONSENT, createdAt, lastUpdatedAt);
        this.consentGranted = consentGranted;
        this.consentDetailsUrl = consentDetailsUrl;
        this.language = language;
        this.sourceType = sourceType;
        this.sourceVersion = sourceVersion;
        this.sourcePlatform = sourcePlatform;
        this.consentGrantedBy = consentGrantedBy;
        this.consentType = consentType;
    }

    @Override
    public String getActionMetadataContextKey() {
        return String.join(":", Names.CONSENT, this.consentType);
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }

}
