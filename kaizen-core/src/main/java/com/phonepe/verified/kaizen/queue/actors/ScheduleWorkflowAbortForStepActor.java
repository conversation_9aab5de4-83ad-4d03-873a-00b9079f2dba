package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepMessage;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.utils.ClockworkUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ScheduleWorkflowAbortForStepActor extends BaseActor<WorkflowStepMessage> {

    private static final String CALLBACK_URI = "/v1/internal/callback/clockwork/workflow/abort/step/%s";

    private final ProfileService profileService;

    private final ClockworkClient clockworkClient;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final DataProvider<KaizenConfig> appConfigProvider;

    private final EventIngestionCommand eventIngestionCommand;

    @Inject
    protected ScheduleWorkflowAbortForStepActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                final ConnectionRegistry connectionRegistry,
                                                final ObjectMapper mapper,
                                                final RetryStrategyFactory retryStrategyFactory,
                                                final ExceptionHandlingFactory exceptionHandlingFactory,
                                                final ProfileService profileService,
                                                final ClockworkClient clockworkClient,
                                                final WorkflowService workflowService,
                                                final WorkflowStepService workflowStepService,
                                                final EventIngestionCommand eventIngestionCommand,
                                                final DataProvider<KaizenConfig> appConfigProvider) {
        super(ActorType.SCHEDULE_ABORT_FOR_WORKFLOW_STEP,
                actorConfigMap.get(ActorType.SCHEDULE_ABORT_FOR_WORKFLOW_STEP), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, WorkflowStepMessage.class);
        this.profileService = profileService;
        this.clockworkClient = clockworkClient;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
        this.appConfigProvider = appConfigProvider;
        this.eventIngestionCommand = eventIngestionCommand;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final WorkflowStepMessage workflowStepMessage) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(
                workflowStepMessage.getWorkflowStepId());

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var abortDuration = appConfigProvider.getData()
                .getAsyncWorkflowAbortDuration();

        final var schedulingRequest = ClockworkUtils.getActionMessageSchedulingRequest("{}", appConfigProvider.getData()
                        .getBaseUrl(), String.format(CALLBACK_URI, workflowStepMessage.getWorkflowStepId()),
                ClockworkUtils.getSchedulingDelayDate(abortDuration));

        try {

            final var clockworkSchedulingResponse = clockworkClient.schedule(schedulingRequest, Constants.APP_NAME);

            eventIngestionCommand.ingestScheduledAbortWorkflowEvent(storedWorkflow, profile,
                    EventType.ABORT_WORKFLOW_SCHEDULE_CREATED_ON_CLOCKWORK, null,
                    storedWorkflowStep.getWorkflowStepId(), profileStep.getProfileStepMappingId(),
                    Constants.ABORT_REASON_FOR_INCOMPLETE_WORKFLOW_STEP, abortDuration,
                    clockworkSchedulingResponse.getData()
                            .getJobId(), clockworkSchedulingResponse.isSuccess()
                                         ? null
                                         : clockworkSchedulingResponse.getErrorCode()
                                                 .name());

        } catch (final Exception e) {

            log.info("Unable to create abort workflow schedule for workflow: {}, workflowStep: {}",
                    storedWorkflow.getWorkflowId(), storedWorkflowStep.getWorkflowStepId());
        }

        return true;
    }
}
