package com.phonepe.verified.kaizen.storage.mariadb.entities;

import java.io.Serial;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

@Getter
@Setter
@Builder
@ToString
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class PrimaryKey implements Serializable {

    @Serial
    private static final long serialVersionUID = 387532198488282321L;

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) AUTO_INCREMENT", insertable = false, updatable = false, nullable = false)
    private long id;

    @Column(name = "partition_id", columnDefinition = "bigint(20)", nullable = false)
    private int partitionId;

    @Override
    public boolean equals(final Object o) {

        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        final PrimaryKey that = (PrimaryKey) o;

        if (id != that.id) {
            return false;
        }
        return partitionId == that.partitionId;
    }

    @Override
    public int hashCode() {
        int result = (int) (id ^ (id >>> 32));
        result = 31 * result + partitionId;
        return result;
    }
}
