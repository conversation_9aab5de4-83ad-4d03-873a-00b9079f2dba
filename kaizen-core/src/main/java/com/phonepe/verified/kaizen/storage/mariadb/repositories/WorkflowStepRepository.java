package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class WorkflowStepRepository extends CrudRepository<StoredWorkflowStep> {

    @Inject
    public WorkflowStepRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredWorkflowStep.class), null);
    }

    public Optional<StoredWorkflowStep> select(final String workflowStepId) {
        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflowStep.class)
                .add(Restrictions.eq(Fields.workflowStepId, workflowStepId));
        return select(workflowStepId, detachedCriteria).stream()
                .findFirst();
    }

    public List<StoredWorkflowStep> selectFromAllShards(final Set<String> workflowStepIds) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflowStep.class)
                .add(Restrictions.in(Fields.workflowStepId, workflowStepIds));
        return scatterGather(detachedCriteria);
    }

    public List<StoredWorkflowStep> selectAll(final String workflowId) {
        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflowStep.class)
                .add(Restrictions.eq(Fields.workflowId, workflowId));
        return select(workflowId, detachedCriteria);
    }

    public List<StoredWorkflowStep> selectAll(final Set<String> workflowIds) {
        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflowStep.class)
                .add(Restrictions.in(Fields.workflowId, workflowIds));
        return select(workflowIds.iterator()
                .next(), detachedCriteria);
    }

    public List<StoredWorkflowStep> selectAll(final String workflowId,
                                              final String profileStepId) {
        final var detachedCriteria = DetachedCriteria.forClass(StoredWorkflowStep.class)
                .add(Restrictions.eq(Fields.profileStepId, profileStepId))
                .add(Restrictions.eq(Fields.workflowId, workflowId));
        return select(workflowId, detachedCriteria);
    }
}