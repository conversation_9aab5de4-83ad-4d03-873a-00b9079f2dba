package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ProcessAutoRetryClockworkActionMessage extends BaseMessage {

    @NotNull
    private final String actionMappingId;

    @NotNull
    private final String workflowStepId;

    @NotNull
    private final String encryptedBase64AutoRetryActionDetailsString;

    @Builder
    @Jacksonized
    public ProcessAutoRetryClockworkActionMessage(final RequestInfo requestInfo,
                                                  @NotNull final String actionMappingId,
                                                  @NotNull final String workflowStepId,
                                                  @NotNull final String encryptedBase64AutoRetryActionDetailsString) {

        super(ActorMessageType.PROCESS_AUTO_RETRY_CLOCKWORK_ACTION, requestInfo);
        this.actionMappingId = actionMappingId;
        this.workflowStepId = workflowStepId;
        this.encryptedBase64AutoRetryActionDetailsString = encryptedBase64AutoRetryActionDetailsString;
    }
}
