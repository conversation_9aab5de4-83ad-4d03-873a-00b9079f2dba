package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.OtpService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class TriggerOtpHurdleActor extends BaseActor<StepActionMessage> {

    private final OtpService otpService;

    @Inject
    protected TriggerOtpHurdleActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                    final ConnectionRegistry connectionRegistry,
                                    final ObjectMapper mapper,
                                    final RetryStrategyFactory retryStrategyFactory,
                                    final ExceptionHandlingFactory exceptionHandlingFactory,
                                    final OtpService otpService) {
        super(ActorType.TRIGGER_OTP_HURDLE, actorConfigMap.get(ActorType.TRIGGER_OTP_HURDLE), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, StepActionMessage.class);
        this.otpService = otpService;
    }

    @Override
    protected boolean handleMessage(final StepActionMessage stepActionMessage) {

        otpService.triggerOtpHurdle(stepActionMessage.getActionId());
        return true;
    }
}
