package com.phonepe.verified.kaizen.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.ops.rmq.config.RMQServiceConfig;
import io.appform.dropwizard.actors.config.RMQConfig;

public class OncallOpsModule extends AbstractModule {

    @Provides
    @Singleton
    public RMQServiceConfig getRmqServiceConfig(final KaizenConfig config) {

        final RMQConfig rmqConfig = config.getRmqConfig();
        return new RMQServiceConfig() {
            @Override
            public String getHost() {
                return rmqConfig.getBrokers()
                        .get(0)
                        .getHost();
            }

            @Override
            public int getPort() {
                return config.getRmqOpsConfig()
                        .getRmqPort();
            }

            @Override
            public Boolean useSSL() {
                return config.getRmqOpsConfig()
                        .getRmqUseSsl();
            }

            @Override
            public String getUserName() {
                return rmqConfig.getUserName();
            }

            @Override
            public String getPassword() {
                return rmqConfig.getPassword();
            }

            @Override
            public String getVirtualHost() {
                return rmqConfig.getVirtualHost();
            }
        };
    }
}
