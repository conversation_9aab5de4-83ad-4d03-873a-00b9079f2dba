package com.phonepe.verified.kaizen.authz.filters;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.authz.annotations.AuthZContext;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Objects;
import java.util.function.Function;
import javax.inject.Inject;
import javax.inject.Singleton;
import javax.ws.rs.ext.Provider;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.model.Parameter.Source;
import org.glassfish.jersey.server.ContainerRequest;
import org.glassfish.jersey.server.internal.inject.AbstractValueParamProvider;
import org.glassfish.jersey.server.internal.inject.MultivaluedParameterExtractorProvider;
import org.glassfish.jersey.server.model.Parameter;

@Slf4j
@Provider
@Singleton
public class AuthZContextProvider extends AbstractValueParamProvider {

    @Inject
    public AuthZContextProvider(final javax.inject.Provider<MultivaluedParameterExtractorProvider> mpep) {
        super(mpep, Source.UNKNOWN);
    }

    @Override
    protected Function<ContainerRequest, ?> createValueProvider(final Parameter parameter) {

        if (parameter.getSourceAnnotation() instanceof AuthZContext) {

            final Class<?> classType = parameter.getRawType();

            if (Objects.nonNull(classType) && classType.equals(UserDetails.class)) {

                return containerRequest -> containerRequest.getProperty(Constants.PV_AUTH_Z_CONTEXT);
            }
            log.error(
                    "AuthZContextProvider annotation was not placed on correct object type; Injection might not work correctly!");
        }
        return null;
    }
}
