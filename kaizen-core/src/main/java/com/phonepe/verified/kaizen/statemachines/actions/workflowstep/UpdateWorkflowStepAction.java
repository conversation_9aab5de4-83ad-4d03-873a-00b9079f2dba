package com.phonepe.verified.kaizen.statemachines.actions.workflowstep;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class UpdateWorkflowStepAction extends UpdateWorkflowStepBaseAction {

    @Inject
    public UpdateWorkflowStepAction(final ProfileService profileService,
                                    final WorkflowService workflowService,
                                    final WorkflowStepService workflowStepService,
                                    final EventIngestionCommand eventIngestionCommand,
                                    final WorkflowStepRepository workflowStepRepository,
                                    final Provider<WorkflowContextStore> workflowContextStore) {
        super(profileService, workflowService, workflowStepService, eventIngestionCommand, workflowStepRepository,
                workflowContextStore);
    }
}
