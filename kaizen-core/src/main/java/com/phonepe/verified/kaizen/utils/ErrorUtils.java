package com.phonepe.verified.kaizen.utils;

import com.phonepe.models.response.GenericError;
import com.phonepe.platform.http.v2.executor.Consumer;
import com.phonepe.platform.http.v2.executor.ExtractedResponse;
import com.phonepe.platform.http.v2.executor.exception.HttpException;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@UtilityClass
public class ErrorUtils {

    public <T> Consumer<HttpException, T> exceptionConsumer() {

        return e -> {

            logErrorContext(e);

            throw getExceptionFromThrowableChain(e, KaizenException.class).orElseGet(
                    () -> KaizenException.propagate(KaizenResponseCode.INTERNAL_SERVER_ERROR, e));
        };
    }

    public void logErrorContext(final HttpException e) {

        final var executionContext = e.getExecutionContext();
        final var callerClass = executionContext.getCallerClass();
        final var command = executionContext.getCommand();

        final var logger = LoggerFactory.getLogger(callerClass);
        logger.error("Error while calling service: {} command: {} message: {}", callerClass.getSimpleName(), command,
                e.getMessage(), e);
    }

    public <T extends Exception> Optional<T> getExceptionFromThrowableChain(final Throwable throwable,
                                                                            final Class<T> exceptionClass) {

        return ExceptionUtils.getThrowableList(throwable)
                .stream()
                .filter(exceptionClass::isInstance)
                .map(exceptionClass::cast)
                .findFirst();
    }

    public <T> Consumer<ExtractedResponse, T> nonSuccessResponseHandler() {

        return extractedResponse -> {

            final var responseBody = extractedResponse.getBody();
            final var code = extractedResponse.getCode();
            final var responseContent = Objects.nonNull(responseBody)
                                        ? new String(responseBody, StandardCharsets.UTF_8)
                                        : null;

            final var executionContext = extractedResponse.getExecutionContext();
            final var callerClass = executionContext.getCallerClass();
            final var commandName = executionContext.getCommand();
            final var url = executionContext.getUrl();

            final var logger = LoggerFactory.getLogger(callerClass);

            handleErrorCodeSpecificFailure(code, responseContent, callerClass, commandName, url, logger);

            throw KaizenException.create(KaizenResponseCode.COMMUNICATION_ERROR,
                    Map.of(Constants.SERVICE_NAME, callerClass.getSimpleName(), Constants.COMMAND_NAME, commandName,
                            Constants.URL, url));
        };
    }

    public <T> Consumer<ExtractedResponse, T> nonSuccess4xxGracefulResponseHandler(final T defaultFailureResponse) {

        return extractedResponse -> {

            final var responseBody = extractedResponse.getBody();
            final var code = extractedResponse.getCode();
            final var responseContent = Objects.nonNull(responseBody)
                                        ? new String(responseBody, StandardCharsets.UTF_8)
                                        : null;

            final var executionContext = extractedResponse.getExecutionContext();
            final var callerClass = executionContext.getCallerClass();
            final var commandName = executionContext.getCommand();
            final var url = executionContext.getUrl();

            final var logger = LoggerFactory.getLogger(callerClass);

            if (code >= 400 && code < 500) {
                logger.info("Handling 4xx failure gracefully service {}, commandName {}, response {} and code {}",
                        callerClass.getSimpleName(), commandName, responseContent, code);
                return defaultFailureResponse;
            }

            handleErrorCodeSpecificFailure(code, responseContent, callerClass, commandName, url, logger);

            throw KaizenException.create(KaizenResponseCode.COMMUNICATION_ERROR,
                    Map.of(Constants.SERVICE_NAME, callerClass.getSimpleName(), Constants.COMMAND_NAME, commandName,
                            Constants.URL, url));
        };
    }

    private static void handleErrorCodeSpecificFailure(final int code,
                                                       final String responseContent,
                                                       final Class<?> callerClass,
                                                       final String commandName,
                                                       final String url,
                                                       final Logger logger) {
        logger.error("Error in call for service {}, commandName {}, response {} and code {}",
                callerClass.getSimpleName(), commandName, responseContent, code);

        if (Objects.nonNull(responseContent) && !responseContent.isEmpty()) {

            final var error = readGenericErrorIfPresent(responseContent);

            throw KaizenException.create(KaizenResponseCode.COMMUNICATION_ERROR,
                    Map.of(Constants.SERVICE_NAME, callerClass.getSimpleName(), Constants.COMMAND_NAME, commandName,
                            Constants.URL, url, Constants.RESPONSE, responseContent, Constants.CODE,
                            error.map(GenericError::getCode)
                                    .orElse(""), Constants.MESSAGE, error.map(GenericError::getMessage)
                                    .orElse("")));
        }
    }

    public Optional<GenericError> readGenericErrorIfPresent(final String response) {
        try {
            return Optional.ofNullable(MapperUtils.deserialize(response, GenericError.class));
        } catch (final Exception e) {
            return Optional.empty();
        }
    }
}
