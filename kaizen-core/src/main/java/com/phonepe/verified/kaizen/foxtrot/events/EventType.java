package com.phonepe.verified.kaizen.foxtrot.events;

import lombok.AllArgsConstructor;
import lombok.experimental.UtilityClass;

@AllArgsConstructor
public enum EventType {

    ACTION_UPDATE {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionUpdate(message);
        }
    },
    GET_TEMPLATE {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitGetTemplate(message);
        }
    },
    VERIFICATION_WORKFLOW_INIT {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_IN_PROGRESS {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_INITIAL_ACTION_IN_PROGRESS {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_READY {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_SUCCESS {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_PSEUDO_SUCCESS {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_FAILURE {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_SKIPPED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_AUTO_SKIPPED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_ABORT {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_DISCARD {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_PURGED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowTransition(message);
        }
    },
    WORKFLOW_STEP_INIT {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_IN_PROGRESS {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_SUCCESS {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_PSEUDO_SUCCESS {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_FAILURE {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_SKIPPED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_AUTO_SKIPPED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_INVALIDATED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_ABORT {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    WORKFLOW_STEP_PURGED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    ACTION_INVALIDATED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionInvalidated(message);
        }
    },
    WORKFLOW_STEP_DISCARD {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowStepTransition(message);
        }
    },
    SECTION_SUBMIT_RECEIVED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitSectionSubmitReceived(message);
        }
    },
    SECTION_SUBMIT_FAILED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitSectionSubmitCompletion(message);
        }
    },
    SECTION_SUBMIT_SUCCEED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitSectionSubmitCompletion(message);
        }
    },
    OTP_VERIFICATION_FAILED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitOtpVerificationFailed(message);
        }
    },
    OTP_VERIFICATION_SUCCEED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitOtpVerificationSucceed(message);
        }
    },
    OTP_GENERATION_FAILED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitOtpGenerationFailed(message);
        }
    },
    OTP_GENERATION_SUCCEED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitOtpGenerationSucceed(message);
        }
    },
    ACTION_INIT {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionInit(message);
        }
    },
    ABORT_ACTION {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionCompletion(message);
        }
    },
    DISCARD_ACTION {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionCompletion(message);
        }
    },
    CONSENT_ACTION_SUCCEED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionCompletion(message);
        }
    },
    DOCUMENT_UPLOAD_ACTION_SUCCEED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitDocumentUploadActionCompletion(message);
        }
    },
    PERSIST_KEY_VALUE_PAIR_ACTION_SUCCEED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionCompletion(message);
        }
    },
    PERSIST_KEY_VALUE_PAIR_V2_ACTION_SUCCEED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionCompletion(message);
        }
    },
    PERSIST_FORENSICS_ACTION_SUCCEED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionCompletion(message);
        }
    },
    CALLBACK_SENT_TO_CLIENT {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitCallbackSentToClient(message);
        }
    },
    CALLBACK_RECEIVED_FROM_DRISHTI {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitCallbackReceivedFromDrishti(message);
        }
    },
    REVOLVER_CALLBACK {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitRevolverCallback(message);
        }
    },
    ACTION_COMPLETION {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitActionCompletion(message);
        }
    },
    CLOCKWORK_CALLBACK_SCHEDULED {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitClockworkCallbackScheduled(message);
        }
    },
    ABORT_WORKFLOW_SCHEDULE_CREATED_ON_CLOCKWORK {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitAbortWorkflowScheduleCreatedOnClockwork(message);
        }
    },
    WORKFLOW_ABORT_TRIGGERED_ON_SCHEDULED_CALLBACK {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowAbortTriggeredOnScheduledCallback(message);
        }
    },
    ENTITY_DETAILS_FETCH {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitEntityDetailsFetch(message);
        }
    },
    WORKFLOW_ABORT_NOT_TRIGGERED_ON_SCHEDULED_CALLBACK {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitWorkflowAbortNotTriggeredOnScheduledCallback(message);
        }
    },
    WORKFLOW_INIT_ASYNC {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitAsyncWorkflowInit(message);
        }
    },
    KILL_SWITCH {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitKillSwitch(message);
        }
    },
    TAG_CALCULATION {
        @Override
        public <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                               final J message) {
            return visitor.visitTagCalculation(message);
        }
    };

    public abstract <T, J> T accept(final EventTypeVisitor<T, J> visitor,
                                    final J message);

    public interface EventTypeVisitor<T, J> {

        T visitActionUpdate(J message);

        T visitGetTemplate(J message);

        T visitWorkflowTransition(J message);

        T visitWorkflowStepTransition(J message);

        T visitActionInvalidated(J message);

        T visitSectionSubmitReceived(J message);

        T visitSectionSubmitCompletion(J message);

        T visitOtpVerificationFailed(J message);

        T visitOtpVerificationSucceed(J message);

        T visitOtpGenerationFailed(J message);

        T visitOtpGenerationSucceed(J message);

        T visitActionInit(J message);

        T visitActionCompletion(J message);

        T visitDocumentUploadActionCompletion(J message);

        T visitCallbackSentToClient(J message);

        T visitCallbackReceivedFromDrishti(J message);

        T visitRevolverCallback(J message);

        T visitClockworkCallbackScheduled(J message);

        T visitAbortWorkflowScheduleCreatedOnClockwork(J message);

        T visitWorkflowAbortTriggeredOnScheduledCallback(J message);

        T visitEntityDetailsFetch(J message);

        T visitWorkflowAbortNotTriggeredOnScheduledCallback(J message);

        T visitAsyncWorkflowInit(J message);

        T visitKillSwitch(J message);

        T visitTagCalculation(J message);
    }

    @UtilityClass
    public static class Names {

        public static final String ACTION_UPDATE = "ACTION_UPDATE";
        public static final String GET_TEMPLATE = "GET_TEMPLATE";
        public static final String VERIFICATION_WORKFLOW_INIT = "VERIFICATION_WORKFLOW_INIT";
        public static final String WORKFLOW_IN_PROGRESS = "WORKFLOW_IN_PROGRESS";
        public static final String WORKFLOW_INITIAL_ACTION_IN_PROGRESS = "WORKFLOW_INITIAL_ACTION_IN_PROGRESS";
        public static final String WORKFLOW_READY = "WORKFLOW_READY";
        public static final String WORKFLOW_SUCCESS = "WORKFLOW_SUCCESS";
        public static final String WORKFLOW_PSEUDO_SUCCESS = "WORKFLOW_PSEUDO_SUCCESS";
        public static final String WORKFLOW_FAILURE = "WORKFLOW_FAILURE";
        public static final String WORKFLOW_SKIPPED = "WORKFLOW_SKIPPED";
        public static final String WORKFLOW_AUTO_SKIPPED = "WORKFLOW_AUTO_SKIPPED";
        public static final String WORKFLOW_ABORT = "WORKFLOW_ABORT";
        public static final String WORKFLOW_DISCARD = "WORKFLOW_DISCARD";
        public static final String WORKFLOW_PURGED = "WORKFLOW_PURGED";
        public static final String WORKFLOW_STEP_INIT = "WORKFLOW_STEP_INIT";
        public static final String WORKFLOW_STEP_IN_PROGRESS = "WORKFLOW_STEP_IN_PROGRESS";
        public static final String WORKFLOW_STEP_SUCCESS = "WORKFLOW_STEP_SUCCESS";
        public static final String WORKFLOW_STEP_PSEUDO_SUCCESS = "WORKFLOW_STEP_PSEUDO_SUCCESS";
        public static final String WORKFLOW_STEP_FAILURE = "WORKFLOW_STEP_FAILURE";
        public static final String WORKFLOW_STEP_SKIPPED = "WORKFLOW_STEP_SKIPPED";
        public static final String WORKFLOW_STEP_AUTO_SKIPPED = "WORKFLOW_STEP_AUTO_SKIPPED";
        public static final String WORKFLOW_STEP_INVALIDATED = "WORKFLOW_STEP_INVALIDATED";
        public static final String WORKFLOW_STEP_ABORT = "WORKFLOW_STEP_ABORT";
        public static final String WORKFLOW_STEP_PURGED = "WORKFLOW_STEP_PURGED";
        public static final String ACTION_INVALIDATED = "ACTION_INVALIDATED";
        public static final String WORKFLOW_STEP_DISCARD = "WORKFLOW_STEP_DISCARD";
        public static final String SECTION_SUBMIT_RECEIVED = "SECTION_SUBMIT_RECEIVED";
        public static final String SECTION_SUBMIT_FAILED = "SECTION_SUBMIT_FAILED";
        public static final String SECTION_SUBMIT_SUCCEED = "SECTION_SUBMIT_SUCCEED";
        public static final String OTP_VERIFICATION_FAILED = "OTP_VERIFICATION_FAILED";
        public static final String OTP_VERIFICATION_SUCCEED = "OTP_VERIFICATION_SUCCEED";
        public static final String OTP_GENERATION_FAILED = "OTP_GENERATION_FAILED";
        public static final String OTP_GENERATION_SUCCEED = "OTP_GENERATION_SUCCEED";
        public static final String ACTION_INIT = "ACTION_INIT";
        public static final String ABORT_ACTION = "ABORT_ACTION";
        public static final String DISCARD_ACTION = "DISCARD_ACTION";
        public static final String CONSENT_ACTION_SUCCEED = "CONSENT_ACTION_SUCCEED";
        public static final String DOCUMENT_UPLOAD_ACTION_SUCCEED = "DOCUMENT_UPLOAD_ACTION_SUCCEED";
        public static final String PERSIST_KEY_VALUE_PAIR_ACTION_SUCCEED = "PERSIST_KEY_VALUE_PAIR_ACTION_SUCCEED";
        public static final String PERSIST_KEY_VALUE_PAIR_V2_ACTION_SUCCEED = "PERSIST_KEY_VALUE_PAIR_V2_ACTION_SUCCEED";
        public static final String PERSIST_FORENSICS_ACTION_SUCCEED = "PERSIST_FORENSICS_ACTION_SUCCEED";
        public static final String CALLBACK_SENT_TO_CLIENT = "CALLBACK_SENT_TO_CLIENT";
        public static final String CALLBACK_RECEIVED_FROM_DRISHTI = "CALLBACK_RECEIVED_FROM_DRISHTI";
        public static final String REVOLVER_CALLBACK = "REVOLVER_CALLBACK";
        public static final String ACTION_COMPLETION = "ACTION_COMPLETION";
        public static final String CLOCKWORK_CALLBACK_SCHEDULED = "CLOCKWORK_CALLBACK_SCHEDULED";
        public static final String ABORT_WORKFLOW_SCHEDULE_CREATED_ON_CLOCKWORK = "ABORT_WORKFLOW_SCHEDULE_CREATED_ON_CLOCKWORK";
        public static final String WORKFLOW_ABORT_TRIGGERED_ON_SCHEDULED_CALLBACK = "WORKFLOW_ABORT_TRIGGERED_ON_SCHEDULED_CALLBACK";
        public static final String ENTITY_DETAILS_FETCH = "ENTITY_DETAILS_FETCH";
        public static final String WORKFLOW_ABORT_NOT_TRIGGERED_ON_SCHEDULED_CALLBACK = "WORKFLOW_ABORT_NOT_TRIGGERED_ON_SCHEDULED_CALLBACK";
        public static final String WORKFLOW_INIT_ASYNC = "WORKFLOW_INIT_ASYNC";
        public static final String KILL_SWITCH = "KILL_SWITCH";
        public static final String TAG_CALCULATION = "TAG_CALCULATION";
    }
}
