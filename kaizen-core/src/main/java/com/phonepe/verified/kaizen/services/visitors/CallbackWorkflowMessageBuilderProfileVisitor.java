package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileVisitor;
import com.phonepe.verified.kaizen.models.responses.WorkflowClientCallback;
import com.phonepe.verified.kaizen.services.visitors.CallbackWorkflowMessageBuilderProfileVisitor.CallbackWorkflowMessageBuilderProfileVisitorData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CallbackWorkflowMessageBuilderProfileVisitor implements
        ProfileVisitor<WorkflowClientCallback, CallbackWorkflowMessageBuilderProfileVisitorData> {

    public static final CallbackWorkflowMessageBuilderProfileVisitor INSTANCE = new CallbackWorkflowMessageBuilderProfileVisitor();

    @Override
    public WorkflowClientCallback visit(final AddOnProfile addOnProfile,
                                        final CallbackWorkflowMessageBuilderProfileVisitorData callbackWorkflowMessageBuilderProfileVisitorData) {

        return BuildUtils.buildWorkflowClientCallback(callbackWorkflowMessageBuilderProfileVisitorData,
                addOnProfile.getType(), addOnProfile.getVersion(), ProfileType.ADD_ON, addOnProfile.getAddOnType());
    }

    @Override
    public WorkflowClientCallback visit(final PrimaryProfile primaryProfile,
                                        final CallbackWorkflowMessageBuilderProfileVisitorData callbackWorkflowMessageBuilderProfileVisitorData) {

        return BuildUtils.buildWorkflowClientCallback(callbackWorkflowMessageBuilderProfileVisitorData,
                primaryProfile.getType(), primaryProfile.getVersion(), ProfileType.PRIMARY, null);
    }

    @Getter
    @Builder
    @AllArgsConstructor
    public static class CallbackWorkflowMessageBuilderProfileVisitorData {

        private StoredWorkflow storedWorkflow;

        private ActionFailureErrorCode failureErrorCode;

        private String requestId;

        private String accessToken;

    }
}
