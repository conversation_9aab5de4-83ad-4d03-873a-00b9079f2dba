package com.phonepe.verified.kaizen.clients.internal;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.clockwork.model.ClockworkResponse;
import com.phonepe.platform.clockwork.model.SchedulingRequest;
import com.phonepe.platform.clockwork.model.SchedulingResponse;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import java.util.List;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class ClockworkClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    private final OlympusIMClient olympusIMClient;

    @Inject
    public ClockworkClient(final HttpClientRegistry httpClientRegistry,
                           final OlympusIMClient olympusIMClient) {

        this.olympusIMClient = olympusIMClient;
        this.httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(
                ClientIds.CLOCKWORK);
    }

    public ClockworkResponse<SchedulingResponse> schedule(final SchedulingRequest schedulingRequest,
                                                          final String clientId) {

        final var url = String.format("/jobs/%s/v2", clientId);

        final var authHeader = HttpClientUtils.generateHeaderPair(Headers.AUTHORIZATION,
                olympusIMClient.getSystemAuthHeader());

        return HttpClientUtils.executePost(httpExecutorBuilderFactory, "schedule", url,
                new SerializableHttpData(MediaType.APPLICATION_JSON, schedulingRequest), List.of(authHeader),
                new TypeReference<>() {
                }, getClass());
    }

}
