package com.phonepe.verified.kaizen.storage.aerospike.keys;

import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetail;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeKey;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@Builder
@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public class EntityDetailsKey implements AerospikeKey {

    @NotEmpty
    private String entityId;

    @NotNull
    private EntityType entityType;

    @NotEmpty
    private String organization;

    @NotEmpty
    private String namespace;

    @NotEmpty
    private String type;

    @Valid
    private List<RequiredDetail> requiredDetails;

    @Override
    public String getKey() {
        return this.toString();
    }
}
