package com.phonepe.verified.kaizen.services.impl;

import com.phonepe.platform.atlas.model.common.Place;
import com.phonepe.verified.kaizen.clients.internal.GeoLocationClient;
import com.phonepe.verified.kaizen.services.GeolocationService;
import java.util.Optional;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class GeolocationServiceImpl implements GeolocationService {

    private final GeoLocationClient geoLocationClient;

    @Override
    @SneakyThrows
    public Optional<Place> fetchGeolocationDetails(final String ipAddress) {
        final var geoLocationDetails = geoLocationClient.fetchLocationInfoViaIpAddress(ipAddress);

        if (!geoLocationDetails.isSuccess() || geoLocationDetails.getData() == null) {
            log.error("Geolocation details for ipAddress: {}, geolocationDetails: {}", ipAddress, geoLocationDetails);
            return Optional.empty();
        }

        return Optional.of(geoLocationDetails.getData()
                .getPlace());
    }
}
