package com.phonepe.verified.kaizen.foxtrot;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.eventingestion.model.Event;
import com.phonepe.platform.eventingestion.model.EventTTLBucket;
import com.phonepe.platform.killswitch.client.KillswitchDebugContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.verified.kaizen.clients.models.killswitch.KillSwitchContext;
import com.phonepe.verified.kaizen.configs.FoxtrotTenancyConfiguration;
import com.phonepe.verified.kaizen.configs.FoxtrotTenantConfiguration;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.foxtrot.events.impl.ActionCompletionEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.ActionInitEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.ActionUpdateEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.CallbackReceivedFromDrishtiEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.CallbackSentToClientEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.ClockworkSchedulingEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.DocumentUploadActionCompletionEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.EntityDetailsFetchEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.GetTemplateEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.KillSwitchEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.OtpGenerationEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.OtpVerificationEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.RevolverCallbackEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.ScheduledWorkflowAbortEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.SectionSubmitEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.SectionSubmitReceivedEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.WorkflowInitAsyncEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.WorkflowStepTransitionEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.WorkflowTagCalculationEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.WorkflowTransitionEvent;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.OtpProviderType;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.requests.details.EntityDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import io.dropwizard.lifecycle.Managed;
import io.dropwizard.util.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class EventIngestionCommand implements Managed {

    public static final String ERROR_WHILE_GENERATING_EVENTS_FOR_REQUEST_ID = "Error while generating Event: %s for requestId:%s";
    public static final String ERROR_WHILE_GENERATING_EVENTS_FOR_WORKFLOW_IDS = "Error while generating Event: %s for workflowId:%s";
    private static final String EVENT_INGESTION_ERROR_TEXT = "Error while generating Event: %s for workflowId:%s, actionId: %s";
    private final EventIngestorClient eventIngestorClient;
    private final FoxtrotTenancyConfiguration foxtrotTenancyConfiguration;

    @Setter
    private boolean enableEventIngestion;

    @Inject
    @SneakyThrows
    public EventIngestionCommand(final EventIngestorClient eventIngestorClient,
                                 final KaizenConfig kaizenConfig) {
        this.eventIngestorClient = eventIngestorClient;
        enableEventIngestion = kaizenConfig.isEnableEventIngestion();
        foxtrotTenancyConfiguration = kaizenConfig.getFoxtrotTenancyConfiguration();
    }

    public void actionCompletionEvent(final StoredAction storedAction,
                                      final StoredWorkflowStep storedWorkflowStep,
                                      final StoredWorkflow storedWorkflow,
                                      final Profile profile,
                                      final ProfileStep profileStep,
                                      final ShadowV2UiRequestContext shadowV2UiRequestContext,
                                      final EventIngestionMessage message) {
        try {
            final var actionUpdateEventIngestionMessage = message.getActionUpdateEventIngestionMessage();

            final var actionCompletionEvent = ActionCompletionEvent.builder()
                    .actionId(storedAction.getActionId())
                    .actionType(storedAction.getActionType())
                    .stateMachineVersion(storedAction.getStateMachineVersion())
                    .actionMappingId(storedAction.getActionMappingId())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .actionFailureErrorCode(storedAction.getFailureErrorCode())
                    .completionState(storedAction.getCompletionState())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityType(storedWorkflow.getEntityType())
                    .entityId(storedWorkflow.getEntityId())
                    .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .intent(BuildUtils.getIntent(shadowV2UiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(shadowV2UiRequestContext))
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .eventType(EventType.ACTION_COMPLETION)
                    .currentState(storedAction.getCurrentState())
                    .currentEvent(storedAction.getCurrentEvent())
                    .previousEvent(Objects.nonNull(actionUpdateEventIngestionMessage)
                                   ? actionUpdateEventIngestionMessage.getPreviousEvent()
                                   : null)
                    .previousState(Objects.nonNull(actionUpdateEventIngestionMessage)
                                   ? actionUpdateEventIngestionMessage.getPreviousState()
                                   : null)
                    .build();
            fireEvent(actionCompletionEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, EventType.ACTION_COMPLETION,
                    storedWorkflow.getWorkflowId(), storedAction.getActionId()), e);
        }
    }

    // to-do method to be removed along with action eventType cleanup
    // eventType has to be ACTION_COMPLETION only for consistency, in few actions we are sending different eventTypes
    public void actionCompletionEventWithDifferentEventType(final StoredAction storedAction,
                                                            final StoredWorkflowStep storedWorkflowStep,
                                                            final StoredWorkflow storedWorkflow,
                                                            final Profile profile,
                                                            final ProfileStep profileStep,
                                                            final ShadowV2UiRequestContext shadowV2UiRequestContext,
                                                            final EventType eventType) {
        try {
            final var actionCompletionEvent = ActionCompletionEvent.builder()
                    .actionId(storedAction.getActionId())
                    .actionType(storedAction.getActionType())
                    .stateMachineVersion(storedAction.getStateMachineVersion())
                    .actionMappingId(storedAction.getActionMappingId())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .actionFailureErrorCode(storedAction.getFailureErrorCode())
                    .completionState(storedAction.getCompletionState())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityType(storedWorkflow.getEntityType())
                    .entityId(storedWorkflow.getEntityId())
                    .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .intent(BuildUtils.getIntent(shadowV2UiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(shadowV2UiRequestContext))
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .eventType(eventType)
                    .currentState(storedAction.getCurrentState())
                    .currentEvent(storedAction.getCurrentEvent())
                    .build();
            fireEvent(actionCompletionEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, eventType, storedWorkflow.getWorkflowId(),
                    storedAction.getActionId()), e);
        }
    }

    public void documentUploadActionCompletionEvent(final StoredAction storedAction,
                                                    final StoredWorkflowStep storedWorkflowStep,
                                                    final StoredWorkflow storedWorkflow,
                                                    final Profile profile,
                                                    final ProfileStep profileStep,
                                                    final ShadowV2UiRequestContext shadowV2UiRequestContext,
                                                    final EventType eventType,
                                                    final Map<DocumentType, Long> documentTypeCountMap) {
        try {
            final var documentUploadActionCompletionEvent = DocumentUploadActionCompletionEvent.builder()
                    .actionId(storedAction.getActionId())
                    .actionType(storedAction.getActionType())
                    .stateMachineVersion(storedAction.getStateMachineVersion())
                    .actionMappingId(storedAction.getActionMappingId())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .actionFailureErrorCode(storedAction.getFailureErrorCode())
                    .completionState(storedAction.getCompletionState())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityType(storedWorkflow.getEntityType())
                    .entityId(storedWorkflow.getEntityId())
                    .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .intent(BuildUtils.getIntent(shadowV2UiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(shadowV2UiRequestContext))
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .eventType(eventType)
                    .documentTypeCountMap(documentTypeCountMap)
                    .build();
            fireEvent(documentUploadActionCompletionEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, eventType, storedWorkflow.getWorkflowId(),
                    storedAction.getActionId()), e);
        }
    }

    public void actionInitEvent(final StoredAction storedAction,
                                final StoredWorkflowStep storedWorkflowStep,
                                final StoredWorkflow storedWorkflow,
                                final Profile profile,
                                final ProfileStep profileStep,
                                final ShadowV2UiRequestContext shadowV2UiRequestContext,
                                final EventIngestionMessage message) {
        try {
            final var actionInitEventIngestionMessage = message.getActionInitEventIngestionMessage();

            final var actionCreateEntryEvent = ActionInitEvent.builder()
                    .actionId(storedAction.getActionId())
                    .actionType(storedAction.getActionType())
                    .actionMappingId(storedAction.getActionMappingId())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .stateMachineVersion(storedAction.getStateMachineVersion())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityType(storedWorkflow.getEntityType())
                    .entityId(storedWorkflow.getEntityId())
                    .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .organization(profile.getOrganization())
                    .namespace(profile.getNamespace())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .intent(BuildUtils.getIntent(shadowV2UiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(shadowV2UiRequestContext))
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .eventType(message.getEventType())
                    .isPseudoSuccess(Objects.nonNull(actionInitEventIngestionMessage)
                                     ? actionInitEventIngestionMessage.isPseudoSuccess()
                                     : null)
                    .neededActionMappingId(Objects.nonNull(actionInitEventIngestionMessage)
                                           ? actionInitEventIngestionMessage.getNeededActionMappingId()
                                           : null)
                    .build();
            fireEvent(actionCreateEntryEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, message.getEventType(), storedWorkflow.getWorkflowId(),
                    message.getActionId()), e);
        }
    }

    public void actionUpdateEvent(final StoredAction storedAction,
                                  final StoredWorkflowStep storedWorkflowStep,
                                  final StoredWorkflow storedWorkflow,
                                  final Profile profile,
                                  final ProfileStep profileStep,
                                  final ShadowV2UiRequestContext shadowV2UiRequestContext,
                                  final EventIngestionMessage message) {
        try {
            final var actionUpdateEventIngestionMessage = message.getActionUpdateEventIngestionMessage();

            final var actionUpdateEvent = ActionUpdateEvent.builder()
                    .actionId(storedAction.getActionId())
                    .actionType(storedAction.getActionType())
                    .actionMappingId(storedAction.getActionMappingId())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityType(storedWorkflow.getEntityType())
                    .entityId(storedWorkflow.getEntityId())
                    .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .organization(profile.getOrganization())
                    .namespace(profile.getNamespace())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .intent(BuildUtils.getIntent(shadowV2UiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(shadowV2UiRequestContext))
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .currentState(storedAction.getCurrentState())
                    .currentEvent(storedAction.getCurrentEvent())
                    .previousEvent(Objects.nonNull(actionUpdateEventIngestionMessage)
                                   ? actionUpdateEventIngestionMessage.getPreviousEvent()
                                   : null)
                    .previousState(Objects.nonNull(actionUpdateEventIngestionMessage)
                                   ? actionUpdateEventIngestionMessage.getPreviousState()
                                   : null)
                    .eventType(message.getEventType())
                    .build();
            fireEvent(actionUpdateEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, message.getEventType(), storedWorkflow.getWorkflowId(),
                    message.getActionId()), e);
        }
    }

    public void otpCompletionStatusEvent(final Profile profile,
                                         final ProfileStep profileStep,
                                         final StoredAction storedAction,
                                         final StoredWorkflow storedWorkflow,
                                         final ShadowV2UiRequestContext uiRequestContext,
                                         final String errorCode,
                                         final OtpProviderType otpProviderType,
                                         final EventType eventType,
                                         final String otpInstanceId) {
        try {
            final var otpCompletionEvent = OtpVerificationEvent.builder()
                    .actionId(storedAction.getActionId())
                    .currentEvent(storedAction.getCurrentEvent())
                    .actionMappingId(storedAction.getActionMappingId())
                    .actionType(storedAction.getActionType())
                    .currentState(storedAction.getCurrentState())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityType(storedWorkflow.getEntityType())
                    .entityId(storedWorkflow.getEntityId())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .namespace(profile.getNamespace())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .eventType(eventType)
                    .intent(BuildUtils.getIntent(uiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(uiRequestContext))
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .errorCode(errorCode)
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .otpProviderType(otpProviderType)
                    .otpInstanceId(otpInstanceId)
                    .build();
            fireEvent(otpCompletionEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, eventType, storedWorkflow.getWorkflowId(),
                    storedAction.getActionId()), e);
        }
    }

    public void otpGenerationEvent(final Profile profile,
                                   final ProfileStep profileStep,
                                   final StoredAction storedAction,
                                   final StoredWorkflow storedWorkflow,
                                   final ShadowV2UiRequestContext uiRequestContext,
                                   final String errorCode,
                                   final OtpProviderType otpProviderType,
                                   final EventType eventType) {
        try {
            final var otpCompletionEvent = OtpGenerationEvent.builder()
                    .actionId(storedAction.getActionId())
                    .currentEvent(storedAction.getCurrentEvent())
                    .actionMappingId(storedAction.getActionMappingId())
                    .actionType(storedAction.getActionType())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityType(storedWorkflow.getEntityType())
                    .entityId(storedWorkflow.getEntityId())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .namespace(profile.getNamespace())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .eventType(eventType)
                    .intent(BuildUtils.getIntent(uiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(uiRequestContext))
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .errorCode(errorCode)
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .otpProviderType(otpProviderType)
                    .build();
            fireEvent(otpCompletionEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, eventType, storedWorkflow.getWorkflowId(),
                    storedAction.getActionId()), e);
        }
    }

    public void workflowTransitionEvent(final StoredWorkflow storedWorkflow,
                                        final Profile profile,
                                        final TransitionState previousState,
                                        final TransitionEvent previousEvent,
                                        final EventType eventType,
                                        final String actionFailureReason) {
        try {
            final var workflowTransitionEvent = WorkflowTransitionEvent.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .eventType(eventType)
                    .currentEvent(storedWorkflow.getCurrentEvent())
                    .currentState(storedWorkflow.getCurrentState())
                    .previousEvent(previousEvent)
                    .previousState(previousState)
                    .tag(storedWorkflow.getTag())
                    .lastUpdatedBy(storedWorkflow.getLastUpdatedBy())
                    .updaterType(storedWorkflow.getLastUpdaterType())
                    .callerFarmId(storedWorkflow.getCallerFarmId())
                    .failureReason(actionFailureReason)
                    .build();
            fireEvent(workflowTransitionEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, eventType, storedWorkflow.getWorkflowId(), ""), e);
        }
    }

    public void workflowTagCalculationEvent(final StoredWorkflow storedWorkflow,
                                            final Profile profile,
                                            final String actionFailureReason) {
        try {
            final var workflowTagCalculationEvent = WorkflowTagCalculationEvent.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .currentEvent(storedWorkflow.getCurrentEvent())
                    .currentState(storedWorkflow.getCurrentState())
                    .tag(storedWorkflow.getTag())
                    .lastUpdatedBy(storedWorkflow.getLastUpdatedBy())
                    .updaterType(storedWorkflow.getLastUpdaterType())
                    .callerFarmId(storedWorkflow.getCallerFarmId())
                    .failureReason(actionFailureReason)
                    .build();
            fireEvent(workflowTagCalculationEvent);
        } catch (final Exception e) {
            log.error(
                    String.format(EVENT_INGESTION_ERROR_TEXT, EventType.TAG_CALCULATION, storedWorkflow.getWorkflowId(),
                            ""), e);
        }
    }

    public void workflowStepTransitionEvent(final StoredWorkflow storedWorkflow,
                                            final StoredWorkflowStep storedWorkflowStep,
                                            final Profile profile,
                                            final ProfileStep profileStep,
                                            final TransitionState previousState,
                                            final TransitionEvent previousEvent,
                                            final EventType eventType,
                                            final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest) {
        try {
            final var componentKitVersion = Objects.nonNull(shadowV2SectionSubmitRequest)
                                            ? shadowV2SectionSubmitRequest.getComponentKitVersion()
                                            : 0;

            final var intent = Objects.nonNull(shadowV2SectionSubmitRequest)
                               ? shadowV2SectionSubmitRequest.getIntent()
                               : null;

            final var workflowStepTransitionEvent = WorkflowStepTransitionEvent.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .groupingKey(storedWorkflowStep.getWorkflowId())
                    .eventType(eventType)
                    .currentEvent(storedWorkflowStep.getCurrentEvent())
                    .currentState(storedWorkflowStep.getCurrentState())
                    .previousEvent(previousEvent)
                    .previousState(previousState)
                    .title(profileStep.getTitle())
                    .profileStepId(profileStep.getProfileStepId())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .lastUpdatedBy(storedWorkflowStep.getLastUpdatedBy())
                    .updaterType(storedWorkflowStep.getLastUpdaterType())
                    .componentKitVersion(componentKitVersion)
                    .intent(intent)
                    .build();
            fireEvent(workflowStepTransitionEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, eventType, storedWorkflow.getWorkflowId(), ""), e);
        }
    }

    public void sectionSubmitEvent(final StoredWorkflow storedWorkflow,
                                   final Profile profile,
                                   final ProfileStep profileStep,
                                   final StoredAction storedAction,
                                   final ShadowV2UiRequestContext uiRequestContext,
                                   final EventType eventType) {
        try {

            final var requestId = BuildUtils.getRequestId(uiRequestContext);

            final var sectionSubmitEvent = SectionSubmitEvent.builder()
                    .actionId(storedAction.getActionId())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .requestId(requestId)
                    .workflowId(storedWorkflow.getWorkflowId())
                    .workflowType(profile.getType())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .workflowStepId(storedAction.getWorkflowStepId())
                    .failureErrorCode(storedAction.getFailureErrorCode())
                    .eventType(eventType)
                    .intent(BuildUtils.getIntent(uiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(uiRequestContext))
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .actionMappingId(storedAction.getActionMappingId())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .failureErrorCode(storedAction.getFailureErrorCode())
                    .build();
            fireEvent(sectionSubmitEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, eventType, storedWorkflow.getWorkflowId(),
                    storedAction.getActionId()), e);
        }
    }

    public void sectionSubmitReceivedEvent(final StoredWorkflow storedWorkflow,
                                           final Profile profile,
                                           final String intent,
                                           final long componentKitVersion,
                                           final SectionInputData sectionInputData,
                                           final UserDetails userDetails,
                                           final RequestInfo requestInfo) {

        try {
            final var sectionSubmitReceivedEvent = SectionSubmitReceivedEvent.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .requestId(requestInfo.getRequestId())
                    .screenMappingId(sectionInputData.getMappingId())
                    .intent(intent)
                    .componentKitVersion(componentKitVersion)
                    .updaterType(UpdaterType.valueOf(userDetails.getUserType()
                            .name()))
                    .lastUpdatedBy(userDetails.getUserId())
                    .build();

            fireEvent(sectionSubmitReceivedEvent);
        } catch (final Exception e) {
            log.error(String.format(ERROR_WHILE_GENERATING_EVENTS_FOR_WORKFLOW_IDS, EventType.SECTION_SUBMIT_RECEIVED,
                    storedWorkflow.getWorkflowId()), e);
        }

    }

    public void getTemplateEvent(final Profile profile,
                                 final StoredWorkflow storedWorkflow,
                                 final String intent,
                                 final long componentKitVersion) {
        try {
            final var getTemplateEvent = GetTemplateEvent.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .organization(profile.getOrganization())
                    .namespace(profile.getNamespace())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .intent(intent)
                    .componentKitVersion(componentKitVersion)
                    .build();
            fireEvent(getTemplateEvent);
        } catch (final Exception e) {
            log.error(String.format(ERROR_WHILE_GENERATING_EVENTS_FOR_WORKFLOW_IDS, EventType.GET_TEMPLATE,
                    storedWorkflow.getWorkflowId()), e);
        }
    }

    public <T extends BaseEvent> void fireEvent(final T event) {

        final var tenantConfigurationOptional = foxtrotTenancyConfiguration.fetchTenantConfiguration(
                event.getOrganization(), event.getNamespace());

        tenantConfigurationOptional.ifPresentOrElse(t -> fireEventBasedOnTenantConfiguration(event, t), () -> ingest(
                toIngestionEvent(event, UUID.randomUUID()
                        .toString(), foxtrotTenancyConfiguration.getBaseTableName())));
    }

    private <T extends BaseEvent> void fireEventBasedOnTenantConfiguration(final T event,
                                                                           final FoxtrotTenantConfiguration foxtrotTenantConfiguration) {

        final var commonId = UUID.randomUUID()
                .toString();

        ingest(toIngestionEvent(event, commonId, foxtrotTenantConfiguration.getTenantTableName()));

        if (foxtrotTenantConfiguration.isBaseTableIngestionEnabled()) {
            ingest(toIngestionEvent(event, commonId, foxtrotTenancyConfiguration.getBaseTableName()));
        }
    }

    private <T extends BaseEvent> Event<T> toIngestionEvent(final T event,
                                                            final String id,
                                                            final String tableName) {

        return Event.<T>builder()
                .app(tableName)
                .id(id)
                .eventType(event.getEventType()
                        .name())
                .groupingKey(event.getGroupingKey())
                .eventData(event)
                .time(new Date(System.currentTimeMillis()))
                .eventTTLBucket(EventTTLBucket.HIGH)
                .build();
    }

    public <T extends BaseEvent> void ingest(final Event<T> event) {

        if (null != event) {
            try {
                if (enableEventIngestion) {
                    eventIngestorClient.send(event);
                }
            } catch (final Exception e) {
                log.warn("Exception while asynchronously ingesting event:" + event, e);
            }
        }
    }

    @Override
    public void start() throws Exception {
        if (Objects.nonNull(eventIngestorClient)) {
            eventIngestorClient.start();
        }
    }

    @Override
    public void stop() throws Exception {
        if (Objects.nonNull(eventIngestorClient)) {
            eventIngestorClient.close();
        }
    }

    public void callbackSentToClientEvent(final Profile profile,
                                          final boolean success,
                                          final String errorMessage,
                                          final String callbackUrl,
                                          final String callbackService,
                                          final StoredWorkflow storedWorkflow) {

        try {
            final var callbackSentToClientEvent = CallbackSentToClientEvent.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .organization(profile.getOrganization())
                    .namespace(profile.getNamespace())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .callBackUrl(callbackUrl)
                    .callBackService(callbackService)
                    .success(success)
                    .errorMessage(errorMessage)
                    .build();
            fireEvent(callbackSentToClientEvent);
        } catch (final Exception e) {
            log.error(String.format(ERROR_WHILE_GENERATING_EVENTS_FOR_WORKFLOW_IDS, EventType.CALLBACK_SENT_TO_CLIENT,
                    storedWorkflow.getWorkflowId()), e);
        }
    }

    public void callbackReceivedFromDrishti(final StoredAction storedAction,
                                            final StoredWorkflowStep storedWorkflowStep,
                                            final StoredWorkflow storedWorkflow,
                                            final Profile profile,
                                            final ProfileStep profileStep,
                                            final ShadowV2UiRequestContext uiRequestContext,
                                            final EventIngestionMessage message) {

        try {
            final var callbackReceivedFromDrishtiMessage = message.getCallbackReceivedFromDrishtiMessage();

            final var callbackReceivedFromDrishtiEvent = CallbackReceivedFromDrishtiEvent.builder()
                    .actionId(storedAction.getActionId())
                    .actionType(storedAction.getActionType())
                    .actionMappingId(storedAction.getActionMappingId())
                    .screenMappingId(storedAction.getScreenMappingId())
                    .actionFailureErrorCode(storedAction.getFailureErrorCode())
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityType(storedWorkflow.getEntityType())
                    .entityId(storedWorkflow.getEntityId())
                    .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                    .profileStepMappingId(profileStep.getProfileStepMappingId())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .intent(BuildUtils.getIntent(uiRequestContext))
                    .componentKitVersion(BuildUtils.getComponentKitVersion(uiRequestContext))
                    .callbackReceivedFromClientSubEventType(callbackReceivedFromDrishtiMessage != null
                                                            ? callbackReceivedFromDrishtiMessage.getCallbackReceivedFromClientSubEventType()
                                                            : null)
                    .requestId(callbackReceivedFromDrishtiMessage != null
                               ? callbackReceivedFromDrishtiMessage.getRequestId()
                               : null)
                    .success(callbackReceivedFromDrishtiMessage != null
                             ? callbackReceivedFromDrishtiMessage.getSuccess()
                             : null)
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .actionMetadataType(callbackReceivedFromDrishtiMessage != null
                                        ? callbackReceivedFromDrishtiMessage.getActionMetadataType()
                                        : null)
                    .build();
            fireEvent(callbackReceivedFromDrishtiEvent);
        } catch (final Exception e) {
            log.error(String.format(EVENT_INGESTION_ERROR_TEXT, message.getEventType(), storedWorkflow.getWorkflowId(),
                    message.getActionId()), e);
        }
    }

    public void revolverCallbackEvent(final String revolverCallbackRequestId,
                                      final boolean success,
                                      final String errorMessage) {

        try {
            final var revolverCallbackEvent = RevolverCallbackEvent.builder()
                    .success(success)
                    .revolverCallbackRequestId(revolverCallbackRequestId)
                    .groupingKey(UUID.randomUUID()
                            .toString())
                    .errorMessage(errorMessage)
                    .build();

            fireEvent(revolverCallbackEvent);
        } catch (final Exception e) {
            log.error(String.format(ERROR_WHILE_GENERATING_EVENTS_FOR_REQUEST_ID, EventType.REVOLVER_CALLBACK,
                    revolverCallbackRequestId), e);
        }
    }

    public void ingestScheduledAbortWorkflowEvent(final StoredWorkflow storedWorkflow,
                                                  final Profile profile,
                                                  final EventType eventType,
                                                  final StoredAction storedAction,
                                                  final String workflowStepId,
                                                  final String profileStepMappingId,
                                                  final String abortReason,
                                                  final Duration abortDuration,
                                                  final String clockworkJobId,
                                                  final String failureReason) {

        ingestSystemWorkflowAbortEvent(storedWorkflow.getWorkflowId(), storedWorkflow.getEntityId(),
                storedWorkflow.getEntityType(), profile, eventType, storedAction, workflowStepId, profileStepMappingId,
                abortReason, abortDuration, clockworkJobId, failureReason);
    }

    public void ingestSystemWorkflowAbortEvent(final String workflowId,
                                               final String entityId,
                                               final EntityType entityType,
                                               final Profile profile,
                                               final EventType eventType,
                                               final StoredAction storedAction,
                                               final String workflowStepId,
                                               final String profileStepMappingId,
                                               final String abortReason,
                                               final Duration abortDuration,
                                               final String clockworkJobId,
                                               final String failureReason) {
        try {
            final var scheduledAbortWorkflowEventBuilder = ScheduledWorkflowAbortEvent.builder()
                    .workflowId(workflowId)
                    .workflowStepId(workflowStepId)
                    .profileStepMappingId(profileStepMappingId)
                    .entityId(entityId)
                    .entityType(entityType)
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .groupingKey(workflowId)
                    .eventType(eventType)
                    .abortReason(abortReason)
                    .abortDuration(abortDuration)
                    .clockworkJobId(clockworkJobId)
                    .failureReason(failureReason);

            if (Objects.nonNull(storedAction)) {
                scheduledAbortWorkflowEventBuilder.actionMappingId(storedAction.getActionMappingId())
                        .actionId(storedAction.getActionId());
            }

            fireEvent(scheduledAbortWorkflowEventBuilder.build());
        } catch (final Exception e) {
            log.error(String.format(ERROR_WHILE_GENERATING_EVENTS_FOR_WORKFLOW_IDS, eventType, workflowId), e);
        }
    }

    public void ingestClockworkRequestScheduled(final String clockworkJobId,
                                                final String callbackpath,
                                                final Duration callbackDuration,
                                                final boolean success,
                                                final String failureErrorCode,
                                                final String workflowId,
                                                final String actionMappingId) {
        try {
            final var event = ClockworkSchedulingEvent.builder()
                    .clockworkJobId(clockworkJobId)
                    .callbackPath(callbackpath)
                    .callbackDuration(callbackDuration)
                    .success(success)
                    .failureCode(failureErrorCode)
                    .groupingKey(workflowId)
                    .workflowId(workflowId)
                    .actionMappingId(actionMappingId)
                    .build();

            fireEvent(event);
        } catch (final Exception e) {
            log.error(String.format(ERROR_WHILE_GENERATING_EVENTS_FOR_WORKFLOW_IDS,
                    EventType.CLOCKWORK_CALLBACK_SCHEDULED, workflowId), e);
        }
    }

    public void ingestEntityDetailsRequest(final EntityDetailsRequest entityDetailsRequest,
                                           final String requester) {

        if (requester.equals(Constants.SOURCE_PVCORE)) {
            return;
        }

        final var requiredDetails = entityDetailsRequest.getRequiredDetails()
                .stream()
                .map(t -> t.getType()
                        .name())
                .collect(Collectors.joining("|"));

        final var requiredProfiles = entityDetailsRequest.getProfileCriteria()
                .stream()
                .map(t -> String.join(",", t.getOrganization(), t.getNamespace(), t.getType()))
                .collect(Collectors.joining("|"));

        try {
            final var entityDetailsEvent = EntityDetailsFetchEvent.builder()
                    .entityId(entityDetailsRequest.getEntityId())
                    .entityType(entityDetailsRequest.getEntityType())
                    .requester(requester)
                    .requiredDetails(requiredDetails)
                    .requiredProfiles(requiredProfiles)
                    .groupingKey(UUID.randomUUID()
                            .toString())
                    .build();

            fireEvent(entityDetailsEvent);
        } catch (final Exception e) {
            log.error("Error while generating event for EntityDetails", e);
        }
    }

    public void workflowInitAsyncEvent(final StoredWorkflow storedWorkflow,
                                       final Profile profile,
                                       final String requestId) {

        try {
            final var workflowTransitionEvent = WorkflowInitAsyncEvent.builder()
                    .workflowId(storedWorkflow.getWorkflowId())
                    .entityId(storedWorkflow.getEntityId())
                    .entityType(storedWorkflow.getEntityType())
                    .namespace(profile.getNamespace())
                    .organization(profile.getOrganization())
                    .workflowType(profile.getType())
                    .workflowVersion(profile.getVersion())
                    .profileType(profile.getProfileType())
                    .addOnType(profile.getAddOnType())
                    .groupingKey(storedWorkflow.getWorkflowId())
                    .requestId(requestId)
                    .build();
            fireEvent(workflowTransitionEvent);
        } catch (final Exception e) {
            log.error(String.format(ERROR_WHILE_GENERATING_EVENTS_FOR_WORKFLOW_IDS, EventType.WORKFLOW_INIT_ASYNC,
                    storedWorkflow.getWorkflowId()), e);
        }
    }

    public void ingestKillSwitchEvent(final KillswitchDebugContext killswitchDebugContext,
                                      final KillSwitchContext killSwitchContext) {

        try {
            final var workflowTransitionEvent = KillSwitchEvent.builder()
                    .id(killswitchDebugContext.getId())
                    .rule(killswitchDebugContext.getRule())
                    .reason(killswitchDebugContext.getReason())
                    .killSwitchContextType(killSwitchContext.getType())
                    .recommendedAction(killswitchDebugContext.getRecommendedAction())
                    .workflowId(killSwitchContext.getWorkflowId())
                    .entityId(killSwitchContext.getEntityId())
                    .entityType(killSwitchContext.getEntityType())
                    .organization(killSwitchContext.getOrganization())
                    .namespace(killSwitchContext.getNamespace())
                    .workflowType(killSwitchContext.getWorkflowType())
                    .workflowVersion(killSwitchContext.getProfileVersion())
                    .profileType(killSwitchContext.getProfileType())
                    .intent(killSwitchContext.getIntent())
                    .componentKitVersion(killSwitchContext.getComponentKitVersion())
                    .groupingKey(killSwitchContext.getWorkflowId())
                    .build();
            fireEvent(workflowTransitionEvent);
        } catch (final Exception e) {
            log.error(String.format(ERROR_WHILE_GENERATING_EVENTS_FOR_WORKFLOW_IDS, EventType.WORKFLOW_INIT_ASYNC,
                    killSwitchContext.getWorkflowId()), e);
        }
    }
}
