package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfig;
import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.abort.impl.NoValidStepSubmissionPresentWorkflowAutoAbortConfig;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.SchedulingService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.WorkflowAutoAbortCallbackHandler.WorkflowAutoAbortCallbackHandlerData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.utils.Constants;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SuppressWarnings("java:S3516")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class WorkflowAutoAbortCallbackHandler implements
        WorkflowAutoAbortConfigVisitor<Boolean, WorkflowAutoAbortCallbackHandlerData> {

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final SchedulingService schedulingService;

    private final EventIngestionCommand eventIngestionCommand;

    @Override
    public Boolean visit(final NoValidStepSubmissionPresentWorkflowAutoAbortConfig config,
                         final WorkflowAutoAbortCallbackHandlerData data) {

        /*
        We need to handle 3 scenarios:
        1. User has not done any section submit at all (no WS present. WF is IN_PROGRESS)
        2. User has done some submission, but all WS are in a terminal failure state (WF can be in multiple states).
        3. User is in the middle of a submission (WS and WF both IN_PROGRESS).

        In case of 1, we can blind abort.
        In case of both 2 & 3, we need to check the timestamp of the last updated WS.
            If timestamp + delay < current timestamp, then abort the workflow.
            Otherwise, schedule another callback.
         */
        final var storedWorkflow = data.storedWorkflow();
        final var userDetails = data.userDetails();

        final var workflowState = storedWorkflow.getCurrentState();

        if (!config.getWorkflowStatesToConsider()
                .contains(State.valueOf(workflowState.name()))) {
            return true;
        }

        // Note that this can contain initial action step also.
        final var validWorkflowSteps = workflowStepService.getValidWorkflowStepsFromWorkflowId(
                storedWorkflow.getWorkflowId());

        if (checkStepsForLastUpdatedTimeAndScheduleCallback(config, data, storedWorkflow, validWorkflowSteps)) {
            return true;
        }

        if (handlePseudoSuccessWorkflowStepState(validWorkflowSteps, data, storedWorkflow, config)) {
            return true;
        }

        // Returns true even for empty list i.e. no WS
        final var areAllValidStepsInTerminalFailureState = validWorkflowSteps.stream()
                .map(StoredWorkflowStep::getCurrentState)
                .allMatch(transitionState -> config.getWorkflowStepStatesToConsider()
                        .contains(State.valueOf(transitionState.name())));

        // If any WS is not in a terminal failure state OR in progress i.e. at least one success, then stop scheduling aborts.
        if (!areAllValidStepsInTerminalFailureState) {
            return true;
        }

        workflowService.abort(List.of(storedWorkflow.getWorkflowId()), false, userDetails);

        eventIngestionCommand.ingestScheduledAbortWorkflowEvent(storedWorkflow, data.profile(),
                EventType.WORKFLOW_ABORT_TRIGGERED_ON_SCHEDULED_CALLBACK, null, null, config.getType()
                        .name(), config.getAbortReason(), null, null, null);

        return true;
    }


    private boolean handlePseudoSuccessWorkflowStepState(final List<StoredWorkflowStep> validWorkflowSteps,
                                                         final WorkflowAutoAbortCallbackHandlerData data,
                                                         final StoredWorkflow storedWorkflow,
                                                         final NoValidStepSubmissionPresentWorkflowAutoAbortConfig config) {

        final var anyStepInPseudoSuccess = validWorkflowSteps.stream()
                .map(StoredWorkflowStep::getCurrentState)
                .anyMatch(TransitionState.PSEUDO_SUCCESS::equals);

        if (anyStepInPseudoSuccess && config.isRescheduleIfStepInPseudoSuccess()) {

            log.debug("Rescheduling Auto Abort callback for WF {} after {}", storedWorkflow.getWorkflowId(),
                    config.getAbortAfter());

            schedulingService.scheduleClockworkCallback(config, Constants.AUTO_ABORT_CALLBACK_PATH_FORMAT.formatted(
                    data.storedWorkflow()
                            .getWorkflowId()), config.getAbortAfter(), data.storedWorkflow()
                    .getWorkflowId(), config.getType()
                    .name());

            return true;
        }

        return false;
    }

    public boolean checkStepsForLastUpdatedTimeAndScheduleCallback(final WorkflowAutoAbortConfig config,
                                                                   final WorkflowAutoAbortCallbackHandlerData data,
                                                                   final StoredWorkflow storedWorkflow,
                                                                   final List<StoredWorkflowStep> validWorkflowSteps) {

        // Keep default of WF last updated at to handle Case 1 explained above
        final var lastUpdatedTimestamp = validWorkflowSteps.stream()
                .map(StoredWorkflowStep::getLastUpdatedAt)
                .max(Comparator.naturalOrder())
                .orElse(storedWorkflow.getLastUpdatedAt());

        // If last updated at + Delay > Current timestamp, then we need to schedule a new callback and wait for that
        if (lastUpdatedTimestamp.plusSeconds(config.getAbortAfter()
                        .toSeconds())
                .isAfter(LocalDateTime.now())) {

            log.debug("Rescheduling Auto Abort callback for WF {} after {}", storedWorkflow.getWorkflowId(),
                    config.getAbortAfter());
            schedulingService.scheduleClockworkCallback(config, Constants.AUTO_ABORT_CALLBACK_PATH_FORMAT.formatted(
                    data.storedWorkflow()
                            .getWorkflowId()), config.getAbortAfter(), data.storedWorkflow()
                    .getWorkflowId(), config.getType()
                    .name());

            return true;
        }

        return false;
    }


    @Builder
    @Jacksonized
    public record WorkflowAutoAbortCallbackHandlerData(UserDetails userDetails, StoredWorkflow storedWorkflow,
                                                       Profile profile) {

    }
}
