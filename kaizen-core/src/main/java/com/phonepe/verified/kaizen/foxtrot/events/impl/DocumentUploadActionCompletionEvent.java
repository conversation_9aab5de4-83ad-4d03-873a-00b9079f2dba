package com.phonepe.verified.kaizen.foxtrot.events.impl;

import com.phonepe.verified.kaizen.foxtrot.events.BaseEvent;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentUploadActionCompletionEvent extends BaseEvent {

    private Map<DocumentType, Long> documentTypeCountMap;

    private String actionId;

    private ActionType actionType;

    private String stateMachineVersion;

    private CompletionState completionState;

    private ActionFailureErrorCode actionFailureErrorCode;

    @Builder
    public DocumentUploadActionCompletionEvent(final EventType eventType,
                                               final String intent,
                                               final String entityId,
                                               final String namespace,
                                               final String workflowId,
                                               @NonNull final String groupingKey,
                                               final String organization,
                                               final String workflowType,
                                               final String workflowStepId,
                                               final EntityType entityType,
                                               final String workflowVersion,
                                               final String screenMappingId,
                                               final String actionMappingId,
                                               final long componentKitVersion,
                                               final String profileStepMappingId,
                                               final Map<DocumentType, Long> documentTypeCountMap,
                                               final String actionId,
                                               final ActionType actionType,
                                               final String stateMachineVersion,
                                               final CompletionState completionState,
                                               final ProfileType profileType,
                                               final String addOnType,
                                               final ActionFailureErrorCode actionFailureErrorCode) {
        super(eventType, intent, entityId, namespace, workflowId, profileType, addOnType, groupingKey, organization,
                workflowType, workflowStepId, entityType, workflowVersion, screenMappingId, actionMappingId,
                componentKitVersion, profileStepMappingId);
        this.documentTypeCountMap = documentTypeCountMap;
        this.actionId = actionId;
        this.actionType = actionType;
        this.stateMachineVersion = stateMachineVersion;
        this.completionState = completionState;
        this.actionFailureErrorCode = actionFailureErrorCode;
    }
}
