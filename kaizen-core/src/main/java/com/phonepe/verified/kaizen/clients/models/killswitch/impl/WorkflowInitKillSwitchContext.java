package com.phonepe.verified.kaizen.clients.models.killswitch.impl;

import com.phonepe.verified.kaizen.clients.models.killswitch.KillSwitchContext;
import com.phonepe.verified.kaizen.clients.models.killswitch.KillSwitchContextType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
public class WorkflowInitKillSwitchContext extends KillSwitchContext {

    @Builder
    public WorkflowInitKillSwitchContext(final String organization,
                                         final String namespace,
                                         final ProfileType profileType,
                                         final String profileVersion,
                                         final String workflowType,
                                         final String workflowId,
                                         final EntityType entityType,
                                         final String entityId,
                                         final String intent,
                                         final long componentKitVersion) {
        super(KillSwitchContextType.WORKFLOW_INIT, organization, namespace, profileType, profileVersion, workflowType,
                workflowId, entityType, entityId, intent, componentKitVersion);
    }

    protected WorkflowInitKillSwitchContext() {
        super(KillSwitchContextType.WORKFLOW_INIT);
    }
}
