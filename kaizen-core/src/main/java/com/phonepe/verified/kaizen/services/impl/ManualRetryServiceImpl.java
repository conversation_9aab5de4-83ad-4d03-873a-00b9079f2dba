package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ManualRetryService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigForGivenAction;
import com.phonepe.verified.kaizen.services.visitors.ManualRetryConfigExecutor;
import com.phonepe.verified.kaizen.services.visitors.StandardProfileScreenConfigFromProfileStepVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ManualRetryServiceImpl implements ManualRetryService {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final ManualRetryConfigExecutor manualRetryConfigExecutor;

    @Override
    public void triggerManualRetry(final String workflowId,
                                   final String workflowStepId,
                                   final String actionId) {

        if (!checkIfManualRetryIsAvailable(workflowId, workflowStepId, actionId)) {
            throw new KaizenException(KaizenResponseCode.MANUAL_RETRY_OF_ACTION_NOT_ALLOWED,
                    "Manual Retry not enabled for the action", Map.of(StoredAction.Fields.actionId, actionId));
        }
        workflowService.moveBackToPseudoSuccessOnManualRetryEvent(workflowId, workflowStepId, actionId);
    }

    private boolean checkIfManualRetryIsAvailable(final String workflowId,
                                                  final String workflowStepId,
                                                  final String actionId) {
        final var storedWorkflow = workflowService.validateAndGetWorkflow(workflowId);
        if (!TransitionState.FAILURE.equals(storedWorkflow.getCurrentState())) {
            throw new KaizenException(KaizenResponseCode.MANUAL_RETRY_OF_ACTION_NOT_ALLOWED,
                    "Workflow not in failure state, hence cannot be manually retried.",
                    Map.of(StoredWorkflow.Fields.workflowId, workflowId));
        }

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(workflowStepId);

        if (!storedWorkflowStep.getCurrentState()
                .equals(TransitionState.FAILURE)) {
            throw new KaizenException(KaizenResponseCode.MANUAL_RETRY_OF_ACTION_NOT_ALLOWED,
                    "WorkflowStep not in failure state, hence cannot be manually retried.",
                    Map.of(Fields.workflowStepId, storedWorkflowStep.getWorkflowStepId()));
        }

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var standardProfileScreenConfig = profileStep.getProfileScreenConfig()
                .accept(new StandardProfileScreenConfigFromProfileStepVisitor(storedAction.getScreenMappingId()))
                .orElseThrow(() -> new KaizenException(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                        "Profile screen not found for the given screen mapping id.",
                        Map.of(StoredAction.Fields.screenMappingId, storedAction.getScreenMappingId())));

        final var standardStepActionConfig = standardProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                .orElseThrow(() -> new KaizenException(KaizenResponseCode.MANUAL_RETRY_OF_ACTION_NOT_ALLOWED,
                        "Action Id not found", Map.of(StoredAction.Fields.actionId, actionId)));

        return standardStepActionConfig.getRetryConfig()
                .accept(manualRetryConfigExecutor, null);
    }

    @Override
    public boolean isActionManuallyRetriable(final String actionId) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(storedAction.getWorkflowStepId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var standardProfileScreenConfig = profileStep.getProfileScreenConfig()
                .accept(new StandardProfileScreenConfigFromProfileStepVisitor(storedAction.getScreenMappingId()))
                .orElse(null);

        if (standardProfileScreenConfig == null) {
            return false;
        }

        final var standardStepActionConfig = standardProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                .orElse(null);

        if (standardStepActionConfig == null) {
            return false;
        }

        return standardStepActionConfig.getRetryConfig()
                .accept(manualRetryConfigExecutor, null);
    }
}
