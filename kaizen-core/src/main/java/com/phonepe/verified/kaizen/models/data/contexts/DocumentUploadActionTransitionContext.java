package com.phonepe.verified.kaizen.models.data.contexts;

import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.models.data.contexts.visitor.TransitionContextTypeVisitor;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentUploadActionTransitionContext extends TransitionContext {

    @Valid
    @NotEmpty
    private List<@NotNull DocumentTypeIdentifierAndLabel> documents;

    public DocumentUploadActionTransitionContext() {
        super(ContextType.DOCUMENT_UPLOAD_ACTION_TRANSITION_CONTEXT);
    }

    @Builder
    public DocumentUploadActionTransitionContext(final List<DocumentTypeIdentifierAndLabel> documents) {
        this();
        this.documents = documents;
    }

    @Override
    public <T, J> T accept(final TransitionContextTypeVisitor<T, J> transitionContextTypeVisitor,
                           final J data) {
        return transitionContextTypeVisitor.visit(this, data);
    }
}
