package com.phonepe.verified.kaizen.services;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.shadow.models.response.TemplateInitResponse;
import com.phonepe.shadow.models.response.v2.TemplateInitV2Response;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.requests.v2.ShadowV2SectionSubmitV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.AbortWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.FilterAllWorkflowIdsRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.LatestWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.PurgeWorkflowRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowAddOnRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.WorkflowInitRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.FilterAllWorkflowIdsV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.LatestWorkflowRequestV2;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.ResumeWorkflowV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowAddOnV2Request;
import com.phonepe.verified.kaizen.models.requests.workflow.v2.WorkflowInitV2Request;
import com.phonepe.verified.kaizen.models.responses.WorkflowClientCallback;
import com.phonepe.verified.kaizen.models.responses.workflow.AccessTokenResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.ActionFailureResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.LatestWorkflowResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowAddOnResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowFilterResponse;
import com.phonepe.verified.kaizen.models.responses.workflow.WorkflowInitResponse;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface WorkflowService {

    WorkflowInitResponse init(WorkflowInitRequest request,
                              UserDetails userDetails);

    WorkflowAddOnResponse initAddOn(WorkflowAddOnRequest request,
                                    UserDetails userDetails);

    String initAsync(final WorkflowInitRequest workflowInitRequest,
                     final UserDetails userDetails);

    String initAsyncV2(final WorkflowInitV2Request workflowInitV2Request,
                       final UserDetails userDetails);

    void processInitAsyncRequest(final WorkflowInitRequest request,
                                 final String requestId,
                                 final String workflowId,
                                 final UserDetails userDetails);

    void processInitAsyncV2Request(final WorkflowInitV2Request workflowInitV2Request,
                                   final String requestId,
                                   final String workflowId,
                                   final UserDetails userDetails);

    void sectionSubmit(final RequestInfo requestInfo,
                       final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                       final UserDetails userDetails);

    void sectionSubmitV2(final String workflowId,
                         final RequestInfo requestInfo,
                         final UserDetails userDetails,
                         final ShadowV2SectionSubmitV2Request shadowV2SectionSubmitV2Request);

    TemplateInitResponse getTemplate(final String workflowId,
                                     final String intent,
                                     final long componentKitVersion,
                                     boolean showSummaryView);

    TemplateInitV2Response getTemplateV2(final String workflowId,
                                         final String intent,
                                         final long componentKitVersion,
                                         boolean showSummaryView);

    TemplateInitResponse generateTemplateInitResponseForResumeConfig(final String workflowId,
                                                                     final String intent,
                                                                     final long componentKitVersion);

    TemplateInitResponse generateTemplateInitResponseForSummaryConfig(final String workflowId,
                                                                      final String intent,
                                                                      final long componentKitVersion,
                                                                      final String summaryViewScreenMappingId);


    StoredWorkflow validateAndGetWorkflow(final String workflowId);

    StoredWorkflow validateAndGetStoredWorkflowFromWorkflowStepId(final String workflowStepId);

    List<StoredWorkflow> getStoredWorkflowFromAllShards(final Set<String> workflowStepId);

    void handleWorkflowStepCompletion(String completedWorkflowStepId,
                                      String completedActionId,
                                      StandardProfileScreenConfig currentProfileScreenConfig,
                                      final boolean retryable);

    LatestWorkflowResponse getLatestWorkflow(final LatestWorkflowRequest latestWorkflowRequest);

    LatestWorkflowResponse getLatestWorkflow(final LatestWorkflowRequest latestWorkflowRequest,
                                             final Optional<TransitionState> transitionState);

    List<StoredWorkflow> getAllWorkflows(LatestWorkflowRequest latestWorkflowRequest);

    List<StoredWorkflow> getAllWorkflows(final LatestWorkflowRequestV2 latestWorkflowRequest);

    void triggerWorkflowCompletionEvent(final TransitionEvent transitionEvent,
                                        final StoredWorkflow storedWorkflow,
                                        final StoredWorkflowStep storedWorkflowStep);

    void autoSkipWorkflow(final StoredWorkflow workflowToAutoSkip,
                          final UserDetails userDetails,
                          final List<StoredWorkflowStep> workflowStepsToAutoSkip);

    void abort(AbortWorkflowRequest abortWorkflowRequest,
               UserDetails userDetails);

    void abort(final List<String> workflowIds,
               final boolean forceAbortTerminalStates,
               final UserDetails userDetails);

    void discard(String workflowId,
                 UserDetails userDetails);

    void triggerWorkflowCompletionEvent(TransitionEvent transitionEvent,
                                        StoredWorkflow storedWorkflow,
                                        StoredWorkflowStep storedWorkflowStep,
                                        String actionId);

    void moveBackToPseudoSuccessOnManualRetryEvent(final String workflowId,
                                                   final String workflowStepId,
                                                   final String actionMappingId);

    Optional<ActionFailureResponse> fetchWorkflowActionFailure(final StoredWorkflow storedWorkflow);

    Set<String> getAllWorkflowIds(LatestWorkflowRequest workflowRequest);

    WorkflowFilterResponse filterAllWorkflowIds(FilterAllWorkflowIdsRequest filterAllWorkflowIdsRequest);

    WorkflowFilterResponse filterAllWorkflowIds(FilterAllWorkflowIdsV2Request filterAllWorkflowIdsRequest);

    List<StoredWorkflow> getAllWorkflowsOfSameEntityAndProfileFromWorkflowId(String workflowId);

    AccessTokenResponse initV2(final WorkflowInitV2Request verificationInitRequest,
                               final RequestInfo requestInfo,
                               final UserDetails userDetails);

    AccessTokenResponse addOnV2(final WorkflowAddOnV2Request verificationInitRequest,
                                final RequestInfo requestInfo,
                                final UserDetails userDetails);

    AccessTokenResponse resumeV2(final ResumeWorkflowV2Request resumeWorkflowV2Request,
                                 final RequestInfo requestInfo);

    void invalidateWorkflowFromProfileStepMappingId(final String workflowId,
                                                    final String profileStepMappingId,
                                                    final UserDetails userDetails);

    List<StoredWorkflow> getAllWorkflowsFromEntityId(final EntityType entityType,
                                                     final String entityId,
                                                     final String organization,
                                                     final String namespace);

    List<StoredWorkflow> getAllWorkflowsFromEntityId(final EntityType entityType,
                                                     final String entityId,
                                                     final String organization,
                                                     final String namespace,
                                                     final String type);

    void handleInitialActionWorkflowStepCompletion(final StoredWorkflowStep storedWorkflowStep,
                                                   final ActionFailureErrorCode actionFailureErrorCode);

    WorkflowClientCallback checkInitWorkflowStatus(final String requestId);

    void purge(final PurgeWorkflowRequest purgeWorkflowRequest,
               final UserDetails userDetails);
}
