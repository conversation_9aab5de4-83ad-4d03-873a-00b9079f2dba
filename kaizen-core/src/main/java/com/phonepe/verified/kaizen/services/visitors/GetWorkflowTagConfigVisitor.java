package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.tag.WorkflowTagConfig;
import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileVisitor;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GetWorkflowTagConfigVisitor implements ProfileVisitor<WorkflowTagConfig, Void> {

    public static final GetWorkflowTagConfigVisitor INSTANCE = new GetWorkflowTagConfigVisitor();

    @Override
    public WorkflowTagConfig visit(final AddOnProfile addOnProfile,
                                   final Void data) {
        return null;
    }

    @Override
    public WorkflowTagConfig visit(final PrimaryProfile primaryProfile,
                                   final Void data) {
        return primaryProfile.getWorkflowTagConfig();
    }
}
