package com.phonepe.verified.kaizen.configs;

import io.dropwizard.util.Duration;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClockworkDelayConfig {

    @NotEmpty
    private Map<String, Duration> commandNameClockworkDelayMap;

    @NotNull
    private Duration defaultClockworkDelay;

    public Duration getClockworkDelay(@NonNull final String commandName) {
        return commandNameClockworkDelayMap.getOrDefault(commandName, defaultClockworkDelay);
    }

}
