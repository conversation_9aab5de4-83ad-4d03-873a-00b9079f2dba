package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import java.util.List;
import java.util.Set;

import io.appform.dropwizard.sharding.DBShardingBundleBase;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class ActionMetadataRepository extends CrudRepository<StoredActionMetadata> {

    @Inject
    public ActionMetadataRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredActionMetadata.class), null);
    }

    public List<StoredActionMetadata> select(final String actionId) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredActionMetadata.class)
                .add(Restrictions.eq(Fields.actionId, actionId));
        return select(actionId, detachedCriteria);
    }

    public List<StoredActionMetadata> select(final Set<String> actionIds) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredActionMetadata.class)
                .add(Restrictions.in(Fields.actionId, actionIds));
        return select(actionIds.iterator()
                .next(), detachedCriteria);
    }

    public List<StoredActionMetadata> selectDocumentUploadMetadata(final String documentId) {

        // As detachedCriteria class type is mentioned as subclass StoredDocumentUploadActionMetadata
        // DetachedCriteria automatically adds condition on actionMetadataType with value of DOCUMENT_UPLOAD
        // Because StoredDocumentUploadActionMetadata is Hibernate Polymorphed class
        final var detachedCriteria = DetachedCriteria.forClass(StoredDocumentUploadActionMetadata.class)
                .add(Restrictions.eq(StoredDocumentUploadActionMetadata.Fields.documentId, documentId));
        return select(documentId, detachedCriteria);
    }

    public List<StoredActionMetadata> select(final String actionId,
                                             final ActionMetadataType actionMetadataType) {

        final var detachedCriteria = DetachedCriteria.forClass(StoredActionMetadata.class)
                .add(Restrictions.eq(Fields.actionId, actionId))
                .add(Restrictions.eq(Fields.actionMetadataType, actionMetadataType));
        return select(actionId, detachedCriteria);
    }

    public <T> List<StoredActionMetadata> selectFromAllShards(final Class<T> clazz,
                                                              final String queryParam,
                                                              final String queryValue) {

        final var detachedCriteria = DetachedCriteria.forClass(clazz)
                .add(Restrictions.in(queryParam, queryValue));

        return scatterGather(detachedCriteria);
    }
}
