package com.phonepe.verified.kaizen.utils;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.platform.http.v2.client.ClientFactory;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.BaseHttpData;
import com.phonepe.platform.http.v2.executor.Consumer;
import com.phonepe.platform.http.v2.executor.ExtractedResponse;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.event.CallbackHandler;
import com.phonepe.platform.http.v2.executor.exception.HttpException;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import okhttp3.Headers;
import okhttp3.Request;
import org.jetbrains.annotations.NotNull;

@UtilityClass
public class HttpClientUtils {

    @SneakyThrows
    public HttpExecutorBuilderFactory getHttpExecutorBuilderFactory(final HttpConfiguration httpConfiguration,
                                                                    final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                                                                    final ObjectMapper mapper,
                                                                    final MetricRegistry metricRegistry) {

        final var client = ClientFactory.newHttpClientBuilder()
                .withMetricRegistry(metricRegistry)
                .withConfiguration(httpConfiguration)
                .build();



        return HttpExecutorBuilderFactory.builder()
                .mapper(mapper)
                .client(client)
                .endpointProviderSupplier(
                        () -> serviceEndpointProviderFactory.provider(httpConfiguration.getClientId()))
                .build();
    }

    public <T> T executeGet(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                            final String command,
                            final String url,
                            final List<HeaderPair> headers,
                            final Class<T> responseClass,
                            final Class<?> callerClass) {

        return executeGet(httpExecutorBuilderFactory, command, url, headers, responseClass,
                ErrorUtils.nonSuccessResponseHandler(), callerClass);
    }

    public <T> T executeGet(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                            final String command,
                            final String url,
                            final List<HeaderPair> headers,
                            final TypeReference<T> responseTypeReference,
                            final Class<?> callerClass) {

        return executeGet(httpExecutorBuilderFactory, command, url, headers, responseTypeReference,
                ErrorUtils.nonSuccessResponseHandler(), callerClass);
    }

    public <T> T executeGet(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                            final String command,
                            final String url,
                            final List<HeaderPair> headers,
                            final Class<T> responseClass,
                            final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                            final Class<?> callerClass) {

        return httpExecutorBuilderFactory.<T>httpGetExecutorBuilder()
                .command(command)
                .url(url)
                .headers(headers)
                .responseType(responseClass)
                .nonSuccessResponseConsumer(nonSuccessResponseHandler)
                .exceptionConsumer(ErrorUtils.exceptionConsumer())
                .callerClass(callerClass)
                .requestInfo(RequestInfoUtils.getOrBuildEmptyRequestInfoWithEnvField())
                .build()
                .executeTracked();
    }

    public <T> T executeGet(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                            final String command,
                            final String url,
                            final List<HeaderPair> headers,
                            final TypeReference<T> responseTypeReference,
                            final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                            final Class<?> callerClass) {

        return httpExecutorBuilderFactory.<T>httpGetExecutorBuilder()
                .command(command)
                .url(url)
                .headers(headers)
                .responseTypeReference(responseTypeReference)
                .nonSuccessResponseConsumer(nonSuccessResponseHandler)
                .exceptionConsumer(ErrorUtils.exceptionConsumer())
                .callerClass(callerClass)
                .requestInfo(RequestInfoUtils.getOrBuildEmptyRequestInfoWithEnvField())
                .build()
                .executeTracked();
    }

    public <T> T executeGet(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                            final String command,
                            final String url,
                            final List<HeaderPair> headers,
                            final TypeReference<T> responseTypeReference,
                            final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                            final CallbackHandler callbackHandler,
                            final Class<?> callerClass) {

        return httpExecutorBuilderFactory.<T>httpGetExecutorBuilder()
                .command(command)
                .url(url)
                .headers(headers)
                .responseTypeReference(responseTypeReference)
                .nonSuccessResponseConsumer(nonSuccessResponseHandler)
                .callbackHandler(callbackHandler)
                .exceptionConsumer(ErrorUtils.exceptionConsumer())
                .callerClass(callerClass)
                .requestInfo(RequestInfoUtils.getOrBuildEmptyRequestInfoWithEnvField())
                .build()
                .executeTracked();
    }

    public <T> T executeDelete(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                               final String command,
                               final String url,
                               final BaseHttpData requestHttpData,
                               final List<HeaderPair> headers,
                               final Class<T> responseClass,
                               final Class<?> callerClass) {

        return executeDelete(httpExecutorBuilderFactory, command, url, requestHttpData, headers, responseClass,
                ErrorUtils.nonSuccessResponseHandler(), callerClass);
    }

    public <T> T executeDelete(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                               final String command,
                               final String url,
                               final BaseHttpData requestHttpData,
                               final List<HeaderPair> headers,
                               final Class<T> responseClass,
                               final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                               final Class<?> callerClass) {

        return httpExecutorBuilderFactory.<T>httpDeleteExecutorBuilder()
                .command(command)
                .url(url)
                .httpData(requestHttpData)
                .headers(headers)
                .responseType(responseClass)
                .nonSuccessResponseConsumer(nonSuccessResponseHandler)
                .exceptionConsumer(ErrorUtils.exceptionConsumer())
                .callerClass(callerClass)
                .requestInfo(RequestInfoUtils.getOrBuildEmptyRequestInfoWithEnvField())
                .build()
                .executeTracked();
    }

    public <T> T executePost(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                             final String command,
                             final String url,
                             final BaseHttpData requestHttpData,
                             final List<HeaderPair> headers,
                             final Class<T> responseClass,
                             final Class<?> callerClass) {

        return executePost(httpExecutorBuilderFactory, command, url, requestHttpData, headers, responseClass,
                ErrorUtils.nonSuccessResponseHandler(), callerClass);
    }

    public <T> T executePost(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                             final String command,
                             final String url,
                             final BaseHttpData requestHttpData,
                             final List<HeaderPair> headers,
                             final Class<T> responseClass,
                             final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                             final Class<?> callerClass) {

        return executePost(httpExecutorBuilderFactory, command, url, requestHttpData, headers, responseClass,
                nonSuccessResponseHandler, ErrorUtils.exceptionConsumer(), callerClass);
    }

    public <T> T executePost(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                             final String command,
                             final String url,
                             final BaseHttpData requestHttpData,
                             final List<HeaderPair> headers,
                             final TypeReference<T> responseTypeReference,
                             final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                             final Class<?> callerClass) {

        return executePost(httpExecutorBuilderFactory, command, url, requestHttpData, headers, responseTypeReference,
                nonSuccessResponseHandler, ErrorUtils.exceptionConsumer(), callerClass);
    }

    public <T> T executePost(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                             final String command,
                             final String url,
                             final BaseHttpData requestHttpData,
                             final List<HeaderPair> headers,
                             final Class<T> responseClass,
                             final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                             final Consumer<HttpException, T> exceptionConsumer,
                             final Class<?> callerClass) {

        return httpExecutorBuilderFactory.<T>httpPostExecutorBuilder()
                .command(command)
                .url(url)
                .headers(headers)
                .httpData(requestHttpData)
                .responseType(responseClass)
                .nonSuccessResponseConsumer(nonSuccessResponseHandler)
                .exceptionConsumer(exceptionConsumer)
                .callerClass(callerClass)
                .requestInfo(RequestInfoUtils.getOrBuildEmptyRequestInfoWithEnvField())
                .build()
                .executeTracked();
    }

    @NotNull
    public CallbackHandler getHeadersCallbackHandler(final Headers.Builder headersBuilder) {

        return new CallbackHandler() {

            @Override
            public void beforeExecute(final Request request) {
                // NOOP
            }

            @Override
            public void afterExecute(final Request request,
                                     final ExtractedResponse extractedResponse) {
                if (extractedResponse.getHeaders() != null) {
                    headersBuilder.addAll(extractedResponse.getHeaders());
                }
            }
        };
    }

    public <T> T executePost(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                             final String command,
                             final String url,
                             final BaseHttpData requestHttpData,
                             final List<HeaderPair> headers,
                             final CallbackHandler callbackHandler,
                             final Class<T> responseClass,
                             final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                             final Consumer<HttpException, T> exceptionConsumer,
                             final Class<?> callerClass) {

        return httpExecutorBuilderFactory.<T>httpPostExecutorBuilder()
                .command(command)
                .url(url)
                .headers(headers)
                .httpData(requestHttpData)
                .responseType(responseClass)
                .nonSuccessResponseConsumer(nonSuccessResponseHandler)
                .exceptionConsumer(exceptionConsumer)
                .callbackHandler(callbackHandler)
                .callerClass(callerClass)
                .requestInfo(RequestInfoUtils.getOrBuildEmptyRequestInfoWithEnvField())
                .build()
                .executeTracked();
    }

    public <T> T executePost(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                             final String command,
                             final String url,
                             final BaseHttpData requestHttpData,
                             final List<HeaderPair> headers,
                             final TypeReference<T> responseTypeReference,
                             final Class<?> callerClass) {

        return executePost(httpExecutorBuilderFactory, command, url, requestHttpData, headers, responseTypeReference,
                ErrorUtils.nonSuccessResponseHandler(), ErrorUtils.exceptionConsumer(), callerClass);
    }

    public <T> T executePost(final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
                             final String command,
                             final String url,
                             final BaseHttpData requestHttpData,
                             final List<HeaderPair> headers,
                             final TypeReference<T> responseTypeReference,
                             final Consumer<ExtractedResponse, T> nonSuccessResponseHandler,
                             final Consumer<HttpException, T> exceptionConsumer,
                             final Class<?> callerClass) {

        return httpExecutorBuilderFactory.<T>httpPostExecutorBuilder()
                .command(command)
                .url(url)
                .headers(headers)
                .httpData(requestHttpData)
                .responseTypeReference(responseTypeReference)
                .nonSuccessResponseConsumer(nonSuccessResponseHandler)
                .exceptionConsumer(exceptionConsumer)
                .callerClass(callerClass)
                .requestInfo(RequestInfoUtils.getOrBuildEmptyRequestInfoWithEnvField())
                .build()
                .executeTracked();
    }

    public static HeaderPair generateHeaderPair(final String name,
                                                final String value) {
        return HeaderPair.builder()
                .name(name)
                .value(value)
                .build();
    }

    public static List<HeaderPair> generateHeaderPair(final Map<String, String> headers) {

        if (Objects.isNull(headers) || headers.isEmpty()) {
            return List.of();
        }
        return headers.entrySet()
                .stream()
                .map(es -> HeaderPair.builder()
                        .name(es.getKey())
                        .value(es.getValue())
                        .build())
                .toList();
    }
}
