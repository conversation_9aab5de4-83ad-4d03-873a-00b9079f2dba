package com.phonepe.verified.kaizen.configs;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class FoxtrotTenancyConfiguration {

    @NotNull
    private final Map<@NotEmpty String, Map<@NotEmpty String, @Valid FoxtrotTenantConfiguration>> organizationNamespaceToFoxtrotTenantConfigurationMapping;

    @NotEmpty
    private final String baseTableName;

    public Optional<FoxtrotTenantConfiguration> fetchTenantConfiguration(final String organization,
                                                                         final String namespace) {

        if (Objects.isNull(organization) || Objects.isNull(namespace)) {
            return Optional.empty();
        }

        return Optional.ofNullable(
                organizationNamespaceToFoxtrotTenantConfigurationMapping.getOrDefault(organization, Map.of())
                        .get(namespace));
    }
}
