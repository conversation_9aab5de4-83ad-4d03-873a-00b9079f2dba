package com.phonepe.verified.kaizen.storage.mariadb.entities;

import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.converters.ProfileScreenConfigConverter;
import com.phonepe.verified.kaizen.utils.Constants;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Builder
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@SuppressWarnings("java:S1948")
@AuditTable(value = "profile_step_audit")
@Table(name = "profile_step", indexes = {
        @Index(name = "unq_idx_profile_step_id", columnList = "profile_step_id", unique = true)})
public class StoredProfileStep implements Serializable, Sharded {

    @Serial
    private static final long serialVersionUID = -8585048958154432190L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) AUTO_INCREMENT", insertable = false, updatable = false, nullable = false)
    private long id;

    @NotEmpty
    @Column(name = "profile_id", columnDefinition = "varchar(64)", nullable = false)
    private String profileId;

    @NotEmpty
    @Column(name = "profile_step_id", columnDefinition = "varchar(64)", nullable = false)
    private String profileStepId;

    @NotEmpty
    @Column(name = "profile_step_mapping_id", columnDefinition = "varchar(128)", nullable = false)
    private String profileStepMappingId;

    @Column(name = "title", columnDefinition = "varchar(128)", nullable = false)
    private String title;

    @Column(name = "execution_rule", columnDefinition = "text", nullable = false)
    private String executionRule;

    @Convert(converter = ProfileScreenConfigConverter.class)
    @Column(name = "screen_config", columnDefinition = "mediumtext", nullable = false)
    private ProfileScreenConfig profileScreenConfig;

    @Column(name = "last_updated_by", columnDefinition = "varchar(128)", nullable = false)
    private String lastUpdatedBy;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3)", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "last_updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3)", nullable = false)
    private LocalDateTime lastUpdatedAt;


    @Override
    public String getShardingKey() {
        return Constants.PROFILE_SHARD_KEY;
    }
}
