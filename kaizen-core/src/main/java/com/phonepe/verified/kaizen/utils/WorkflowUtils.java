package com.phonepe.verified.kaizen.utils;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow.Fields;
import java.util.Map;
import lombok.experimental.UtilityClass;

@UtilityClass
public class WorkflowUtils {

    public void validateEntityAgainstWorkflow(final StoredWorkflow storedWorkflow,
                                              final String entityId,
                                              final EntityType entityType) {
        if (!storedWorkflow.getEntityId()
                .equals(entityId) || storedWorkflow.getEntityType() != entityType) {
            throw KaizenException.create(KaizenResponseCode.WORKFLOW_NOT_FOUND,
                    Map.ofEntries(Map.entry(Constants.MESSAGE, "Unable to find workflow for given inputs"),
                            Map.entry(Fields.entityId, entityId), Map.entry(Fields.entityType, entityType),
                            Map.entry(Fields.workflowId, storedWorkflow.getWorkflowId())));
        }
    }

}
