package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TriggerWorkflowStepMessage extends BaseMessage {

    @NonNull
    private final String workflowStepId;

    @NonNull
    private final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest;

    @NonNull
    private final UserDetails userDetails;

    @Builder
    @Jacksonized
    public TriggerWorkflowStepMessage(final RequestInfo requestInfo,
                                      @NonNull final String workflowStepId,
                                      @NonNull final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                      @NonNull final UserDetails userDetails) {

        super(ActorMessageType.TRIGGER_WORKFLOW_STEP, requestInfo);
        this.workflowStepId = workflowStepId;
        this.shadowV2SectionSubmitRequest = shadowV2SectionSubmitRequest;
        this.userDetails = userDetails;
    }
}
