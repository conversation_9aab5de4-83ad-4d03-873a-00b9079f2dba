package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AbortWorkflowV2Message extends BaseMessage {

    @NotEmpty
    private final List<@NotEmpty String> workflowIdsToAbort;

    @NotEmpty
    private final UserDetails callerUserDetails;

    private final boolean forceAbortTerminalStates;

    @Builder
    @Jacksonized
    public AbortWorkflowV2Message(final RequestInfo requestInfo,
                                  @NotEmpty final List<@NotEmpty String> workflowIdsToAbort,
                                  @NotEmpty final UserDetails callerUserDetails,
                                  final boolean forceAbortTerminalStates) {

        super(ActorMessageType.ABORT_WORKFLOW_V2, requestInfo);
        this.workflowIdsToAbort = workflowIdsToAbort;
        this.callerUserDetails = callerUserDetails;
        this.forceAbortTerminalStates = forceAbortTerminalStates;
    }
}
