package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.services.impl.ProfileServiceImpl;

@Singleton
public class ProfileCache extends Cache<String, Profile> {

    private final Provider<ProfileServiceImpl> profileService;

    @Inject
    public ProfileCache(final CaffeineCacheConfig caffeineCacheConfig,
                        final MetricRegistry metricRegistry,
                        final Provider<ProfileServiceImpl> profileService) {
        super(CacheName.PROFILE_CACHE, caffeineCacheConfig, metricRegistry);
        this.profileService = profileService;
    }

    @Override
    protected Profile build(final String profileId) {
        return profileService.get()
                .getProfileFromDb(profileId);
    }
}
