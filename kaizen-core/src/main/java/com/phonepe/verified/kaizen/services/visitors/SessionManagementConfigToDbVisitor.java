package com.phonepe.verified.kaizen.services.visitors;


import com.phonepe.verified.kaizen.models.configs.session.SessionManagementVisitor;
import com.phonepe.verified.kaizen.models.configs.session.impl.ClientManagedSessionConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredClientManagedSession;
import com.phonepe.verified.kaizen.storage.mariadb.entities.session.StoredSessionManagementConfig;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SessionManagementConfigToDbVisitor implements
        SessionManagementVisitor<StoredSessionManagementConfig, String> {

    public static final SessionManagementConfigToDbVisitor INSTANCE = new SessionManagementConfigToDbVisitor();

    @Override
    public StoredSessionManagementConfig visit(final ClientManagedSessionConfig clientManagedSessionConfig,
                                               final String userId) {
        return StoredClientManagedSession.builder()
                .disabled(clientManagedSessionConfig.isDisabled())
                .profileId(clientManagedSessionConfig.getProfileId())
                .validateToken(clientManagedSessionConfig.isValidateToken())
                .validateTimer(clientManagedSessionConfig.isValidateTimer())
                .principalBuilder(clientManagedSessionConfig.getPrincipalBuilder())
                .sourceType(clientManagedSessionConfig.getSourceType())
                .lastUpdatedBy(userId)
                .build();
    }
}
