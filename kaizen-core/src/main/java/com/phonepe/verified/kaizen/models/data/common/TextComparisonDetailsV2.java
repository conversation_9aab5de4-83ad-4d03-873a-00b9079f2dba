package com.phonepe.verified.kaizen.models.data.common;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextComparisonDetailsV2 {

    private BigDecimal score;

    private BigDecimal tokenThreshold;

    private boolean matching;

    private String textOne;

    private String textTwo;

    private String referenceId;

    private String nameMatchVersion;

    private String requestId;
}