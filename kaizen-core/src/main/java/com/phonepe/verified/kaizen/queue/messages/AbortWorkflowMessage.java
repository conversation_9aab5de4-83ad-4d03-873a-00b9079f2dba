package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.workflow.AbortWorkflowRequest;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AbortWorkflowMessage extends BaseMessage {

    @NonNull
    private final AbortWorkflowRequest abortWorkflowRequest;

    @NonNull
    private final UserDetails userDetails;

    @Builder
    @Jacksonized
    public AbortWorkflowMessage(@NonNull final AbortWorkflowRequest abortWorkflowRequest,
                                @NonNull final UserDetails userDetails,
                                final RequestInfo requestInfo) {

        super(ActorMessageType.ABORT_WORKFLOW, requestInfo);
        this.abortWorkflowRequest = abortWorkflowRequest;
        this.userDetails = userDetails;
    }
}