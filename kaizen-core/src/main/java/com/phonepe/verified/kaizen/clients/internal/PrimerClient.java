package com.phonepe.verified.kaizen.clients.internal;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.EmptyHttpData;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.primer.GenerateTokenV2Request;
import com.phonepe.verified.kaizen.models.primer.PrimerTokenResponseV2;
import com.phonepe.verified.kaizen.models.primer.RefreshTokenV2Request;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.utils.Constants.ClientIds;
import com.phonepe.verified.kaizen.utils.Constants.Headers;
import com.phonepe.verified.kaizen.utils.HttpClientUtils;
import io.dropwizard.util.Strings;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@SuppressWarnings("java:S1192")
public class PrimerClient {

    private static final Retryer<PrimerTokenResponseV2> tokenGenRetryer = RetryerBuilder.<PrimerTokenResponseV2>newBuilder()
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .retryIfException()
            .build();

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    @Inject
    public PrimerClient(final HttpClientRegistry httpClientRegistry) {
        this.httpExecutorBuilderFactory = httpClientRegistry.getHttpExecutorBuilderFactoryForService(ClientIds.PRIMER);
    }

    public PrimerTokenResponseV2 generateTokenV2(final String appName,
                                                 final String userReferenceId,
                                                 final String actxHeaderString,
                                                 final GenerateTokenV2Request generateTokenRequest) {

        try {

            return tokenGenRetryer.call(() -> {
                try {

                    final var url = Strings.isNullOrEmpty(actxHeaderString)
                                    ? String.format("/v2/generate/%s", appName)
                                    : String.format("/v2/actx/generate/%s", appName);

                    final var actxHeader = HeaderPair.builder()
                            .name(Headers.ACTX)
                            .value(actxHeaderString)
                            .build();

                    final var tokenIdHeader = HeaderPair.builder()
                            .name(Headers.X_TOKEN_ID)
                            .value(userReferenceId)
                            .build();

                    final var headers = Strings.isNullOrEmpty(actxHeaderString)
                                        ? List.of(tokenIdHeader)
                                        : List.of(actxHeader, tokenIdHeader);

                    return HttpClientUtils.executePost(httpExecutorBuilderFactory, "generateTokenV2", url,
                            new SerializableHttpData(MediaType.APPLICATION_JSON, generateTokenRequest), headers,
                            PrimerTokenResponseV2.class, getClass());

                } catch (final HystrixRuntimeException e) {

                    log.error("Exception while refreshing primer token ", e);
                    throw (Exception) e.getCause();
                }
            });
        } catch (final Exception e) {

            log.error("Exception while creating primer token ", e);
            throw KaizenException.propagate(e);
        }
    }


    public PrimerTokenResponseV2 refreshTokenV2(final String appName,
                                                final String authToken,
                                                final String userReferenceId,
                                                final String actxHeaderString,
                                                final RefreshTokenV2Request refreshTokenRequest) {
        try {
            return tokenGenRetryer.call(() -> {
                try {

                    final var url = Strings.isNullOrEmpty(actxHeaderString)
                                    ? String.format("/v2/refresh/%s/%s", appName, userReferenceId)
                                    : String.format("/v2/actx/refresh/%s/%s", appName, userReferenceId);

                    final var actxHeader = HeaderPair.builder()
                            .name(Headers.ACTX)
                            .value(actxHeaderString)
                            .build();

                    final var authTokenHeader = HeaderPair.builder()
                            .name(Headers.X_AUTH_TOKEN)
                            .value(authToken)
                            .build();

                    final List<HeaderPair> headers = Strings.isNullOrEmpty(actxHeaderString)
                                                     ? List.of(authTokenHeader)
                                                     : List.of(authTokenHeader, actxHeader);

                    return HttpClientUtils.executePost(httpExecutorBuilderFactory, "refreshToken", url,
                            new SerializableHttpData(MediaType.APPLICATION_JSON, refreshTokenRequest), headers,
                            PrimerTokenResponseV2.class, getClass());
                } catch (final HystrixRuntimeException e) {

                    log.error("Exception while refreshing primer token ", e);
                    throw (Exception) e.getCause();
                }
            });
        } catch (final Exception e) {

            log.error("Exception while refreshing primer token ", e);
            throw KaizenException.create(Strings.isNullOrEmpty(actxHeaderString)
                                         ? KaizenResponseCode.REFRESH_TOKEN_EXPIRED
                                         : KaizenResponseCode.REFRESH_TOKEN_EXPIRED_FOR_WEB, Map.of());
        }
    }

    public void clearTokenV2(final String appName,
                             final String authToken) {
        try {

            final var url = String.format("/v2/clearDynamic/%s", appName);

            final var authTokenHeader = HeaderPair.builder()
                    .name(Headers.X_AUTH_TOKEN)
                    .value(authToken)
                    .build();

            HttpClientUtils.executeDelete(httpExecutorBuilderFactory, "clearToken", url, new EmptyHttpData(),
                    List.of(authTokenHeader), String.class, getClass());

        } catch (final Exception e) {

            log.error("Exception while clearing primer token ", e);
            throw KaizenException.propagate(e);
        }
    }
}