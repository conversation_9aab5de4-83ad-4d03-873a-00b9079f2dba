package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import java.util.Comparator;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ProfileStepCompletionStatusVisitor implements ProfileScreenVisitor<CompletionState> {

    private final String workflowStepId;

    private final ActionService actionService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowContextStore workflowContextStore;

    @Override
    public CompletionState visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        return standardProfileScreenConfig.getStepActionConfig()
                .accept(new ProfileScreenCompletionActionConfigVisitor(workflowStepId, actionService, workflowService,
                        hopeLangService, workflowContextStore), null)
                .getCompletionState();
    }

    @Override
    public CompletionState visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        var isPseudoSuccessFound = false;

        for (final var orderedProfileScreen : sortedOrderedProfileScreenList) {

            final var completionState = orderedProfileScreen.getProfileScreenConfig()
                    .accept(this);

            isPseudoSuccessFound = CompletionState.PSEUDO_SUCCESS == completionState;

            if (CompletionState.FAILURE == completionState) {

                return completionState;
            }
        }

        return isPseudoSuccessFound
               ? CompletionState.PSEUDO_SUCCESS
               : CompletionState.SUCCESS;
    }
}
