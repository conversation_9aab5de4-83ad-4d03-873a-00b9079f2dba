package com.phonepe.verified.kaizen.storage.aerospike.commands;

import com.aerospike.client.IAerospikeClient;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeCommand;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeSet;
import com.phonepe.verified.kaizen.storage.aerospike.keys.AutoRetryActionKey;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class AutoRetryActionDetailsCommand extends AerospikeCommand<AutoRetryActionKey, String> {

    @Inject
    public AutoRetryActionDetailsCommand(final IAerospikeClient aerospikeClient,
                                         final AerospikeConfig aerospikeConfig) {
        super(aerospikeClient, aerospikeConfig, AerospikeSet.AUTO_RETRY_ACTION, String.class);
    }
}
