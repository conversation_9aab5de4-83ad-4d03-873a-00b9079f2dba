package com.phonepe.verified.kaizen.storage.aerospike.keys;

import com.phonepe.verified.kaizen.storage.aerospike.AerospikeKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Session<PERSON><PERSON> implements AerospikeKey {

    @NonNull
    private String principal;

    @Override
    public String getKey() {
        return principal;
    }
}
