package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.DocumentMaskingMessage;
import com.phonepe.verified.kaizen.services.DocumentMaskingService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class DocumentMaskingActor extends BaseActor<DocumentMaskingMessage> {

    private final DocumentMaskingService documentMaskingService;

    @Inject
    protected DocumentMaskingActor(final ObjectMapper mapper,
                                   final ConnectionRegistry connectionRegistry,
                                   final RetryStrategyFactory retryStrategyFactory,
                                   final Map<ActorType, ActorConfig> actorConfigMap,
                                   final ExceptionHandlingFactory exceptionHandlingFactory,
                                   final DocumentMaskingService documentMaskingService) {

        super(ActorType.DOCUMENT_MASK_ACTION, actorConfigMap.get(ActorType.DOCUMENT_MASK_ACTION), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, DocumentMaskingMessage.class);
        this.documentMaskingService = documentMaskingService;
    }

    @Override
    @SneakyThrows
    protected boolean handleMessage(final DocumentMaskingMessage documentMaskingMessage) {

        return documentMaskingService.mask(documentMaskingMessage.getActionId(),
                documentMaskingMessage.getDocumentTypeIdentifierAndLabel());
    }
}
