package com.phonepe.verified.kaizen.statemachines;

import com.phonepe.verified.kaizen.models.data.keys.StateMachineRegistryKey;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.CompleteWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.CreateWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.InitialActionInProgressWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.PurgeWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.actions.workflow.UpdateWorkflowAction;
import com.phonepe.verified.kaizen.statemachines.factories.WorkflowStateMachineModelFactory;
import com.phonepe.verified.kaizen.storage.aerospike.commands.TransitionLockCommand;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineModelConfigurer;

@Slf4j
public class WorkflowStateMachine extends BaseStateMachine<TransitionState, TransitionEvent> {

    private final CreateWorkflowAction createWorkflowAction;

    private final UpdateWorkflowAction updateWorkflowAction;

    private final CompleteWorkflowAction completeWorkflowAction;

    private final InitialActionInProgressWorkflowAction initialActionInProgressWorkflowAction;

    private final PurgeWorkflowAction purgeWorkflowAction;

    public WorkflowStateMachine(final TransitionLockCommand transitionLockCommand,
                                final CreateWorkflowAction createWorkflowAction,
                                final UpdateWorkflowAction updateWorkflowAction,
                                final CompleteWorkflowAction completeWorkflowAction,
                                final InitialActionInProgressWorkflowAction initialActionInProgressWorkflowAction,
                                final PurgeWorkflowAction purgeWorkflowAction) {
        super(transitionLockCommand);
        this.createWorkflowAction = createWorkflowAction;
        this.updateWorkflowAction = updateWorkflowAction;
        this.completeWorkflowAction = completeWorkflowAction;
        this.initialActionInProgressWorkflowAction = initialActionInProgressWorkflowAction;
        this.purgeWorkflowAction = purgeWorkflowAction;
    }

    @Override
    @SneakyThrows
    protected void configure(final StateMachineModelConfigurer<TransitionState, TransitionEvent> model) {
        model.withModel()
                .factory(new WorkflowStateMachineModelFactory(createWorkflowAction, updateWorkflowAction,
                        completeWorkflowAction, initialActionInProgressWorkflowAction, purgeWorkflowAction));
    }

    @Override
    public StateMachineRegistryKey getStateMachineRegistryKey() {
        return null;
    }
}
