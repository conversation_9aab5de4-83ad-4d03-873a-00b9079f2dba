package com.phonepe.verified.kaizen.models.data.contexts;

import com.phonepe.verified.kaizen.models.data.contexts.visitor.TransitionContextTypeVisitor;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class KeyValuePairsActionTransitionContext extends TransitionContext {

    private Map<String, String> keyValuePairs;

    public KeyValuePairsActionTransitionContext() {
        super(ContextType.KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT);
    }

    @Builder
    public KeyValuePairsActionTransitionContext(final Map<String, String> keyValuePairs) {
        super(ContextType.KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT);
        this.keyValuePairs = keyValuePairs;
    }

    @Override
    public <T, J> T accept(final TransitionContextTypeVisitor<T, J> transitionContextTypeVisitor,
                           final J data) {
        return transitionContextTypeVisitor.visit(this, data);
    }
}
