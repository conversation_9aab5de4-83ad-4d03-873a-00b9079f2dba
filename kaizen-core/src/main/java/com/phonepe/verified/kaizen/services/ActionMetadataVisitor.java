package com.phonepe.verified.kaizen.services;

import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredConsentActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredKeyValueMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredOtpHurdleActionMetaData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredWaitForConditionMetadata;

public interface ActionMetadataVisitor<T, J> {

    T visit(StoredDocumentUploadActionMetadata documentUploadActionMetadata,
            J data);

    T visit(StoredKeyValueMetadata keyValuePairsActionMetadata,
            J data);

    T visit(StoredConsentActionMetadata consentActionMetadata,
            J data);

    T visit(StoredWaitForConditionMetadata storedWaitForConditionMetadata,
            J data);

    T visit(StoredOtpHurdleActionMetaData storedCkycMetaData,
            J data);

    T visit(StoredDocumentUploadWithMetaDataActionMetadata storedDocumentUploadWithMetaDataActionMetadata, J data);
}
