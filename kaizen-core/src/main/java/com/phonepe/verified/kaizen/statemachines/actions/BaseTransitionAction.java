package com.phonepe.verified.kaizen.statemachines.actions;

import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;

public abstract class BaseTransitionAction<S, E> implements Action<S, E> {

    protected abstract void performTransition(final StateContext<S, E> stateContext);

    @Override
    public void execute(final StateContext<S, E> stateContext) {
        try {
            this.performTransition(stateContext);
        } catch (Exception e) {
            try {
                stateContext.getStateMachine()
                        .setStateMachineError(e);
            } catch (Exception ex) {
                // NOOP
            }
        }
    }
}
