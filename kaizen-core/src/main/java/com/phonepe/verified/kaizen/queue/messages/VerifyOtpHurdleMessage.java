package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.OtpVerificationRequest;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class VerifyOtpHurdleMessage extends BaseMessage {

    @NonNull
    private final OtpVerificationRequest otpVerificationRequest;

    @NonNull
    private final String intent;

    @NonNull
    private final String actionId;

    private final long componentKitVersion;

    @Builder
    @Jacksonized
    public VerifyOtpHurdleMessage(@NonNull final RequestInfo requestInfo,
                                  @NonNull final OtpVerificationRequest otpVerificationRequest,
                                  @NonNull final String intent,
                                  @NonNull final String actionId,
                                  final long componentKitVersion) {

        super(ActorMessageType.VERIFY_OTP_HURDLE, requestInfo);
        this.otpVerificationRequest = otpVerificationRequest;
        this.intent = intent;
        this.actionId = actionId;
        this.componentKitVersion = componentKitVersion;
    }
}
