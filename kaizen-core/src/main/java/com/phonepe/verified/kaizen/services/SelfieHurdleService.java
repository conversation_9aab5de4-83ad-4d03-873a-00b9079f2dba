package com.phonepe.verified.kaizen.services;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.SelfieDocumentIdSubmitRequest;

public interface SelfieHurdleService {

    void triggerSelfieHurdle(final String actionId);

    void submitSelfieDocumentId(final String actionId,
                                final String intent,
                                final RequestInfo requestInfo,
                                final long componentKitVersion,
                                final UserDetails userDetails,
                                final SelfieDocumentIdSubmitRequest selfieDocumentIdSubmitRequest);
}
