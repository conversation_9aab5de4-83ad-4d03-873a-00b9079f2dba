package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.workflow.PurgeWorkflowRequest;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PurgeWorkflowMessage extends BaseMessage {

    @NonNull
    private final PurgeWorkflowRequest purgeWorkflowRequest;

    @NonNull
    private final UserDetails userDetails;

    @Builder
    @Jacksonized
    public PurgeWorkflowMessage(final RequestInfo requestInfo,
                                @NonNull final PurgeWorkflowRequest purgeWorkflowRequest,
                                @NonNull final UserDetails userDetails) {

        super(ActorMessageType.PURGE_WORKFLOW, requestInfo);
        this.purgeWorkflowRequest = purgeWorkflowRequest;
        this.userDetails = userDetails;
    }
}
