package com.phonepe.verified.kaizen.queue;

public enum ActorType {

    MOVE_TO_IN_PROGRESS_WORKFLOW_STEP,
    TRIGGER_WORKFLOW_STEP,
    REVOLVER_CALLBACK,
    CLIENT_WORKFLOW_CALLBACK,
    <PERSON><PERSON><PERSON><PERSON>_ACTION_COMPLETION,
    <PERSON><PERSON><PERSON><PERSON>_WORKFLOW_STEP_COMPLETION,
    SECTION_SUBMIT,
    SECTION_SUBMIT_V2,
    TRIGGER_OTP_HURDLE,
    PROCESS_SELFIE_DOCUMENT_ID_SUBMIT,
    VERIFY_OTP_HURDLE,
    ACTION_EXECUTOR,
    TEXT_COMPARISON_PROCESSOR,
    DELETE_DOCSTORE_FILE,
    <PERSON><PERSON><PERSON><PERSON>_TTL_CALLBACK,
    HANDLE_PSEUDO_SUCCESS_ACTION_COMPLETION,
    HANDLE_PSEUDO_SUCCESS_WORKFLOW_STEP_COMPLETION,
    HAN<PERSON><PERSON>_PSEUDO_SUCCESS_WORKFLOW_STEP_COMPLETION_V2,
    EVENT_INGESTION,
    <PERSON><PERSON><PERSON>_WORKFLOW,
    ABORT_WORKFLOW_V2,
    <PERSON><PERSON><PERSON>_WORKFLOW,
    PROCESS_OCR_DATA_DRISHTI_CALLBACK,
    PROCESS_DRISHTI_OCR_ACTION,
    HAWKEYE_NOTIFICATION,
    CONFIRMATION_ACTION,
    SKIP_WORKFLOW_STEP,
    WORKFLOW_AUTO_ABORT,
    WORKFLOW_AUTO_SKIP,
    SCHEDULE_WORKFLOW_ABORT,
    SCHEDULE_ABORT_FOR_WORKFLOW_STEP,
    SCHEDULED_WORKFLOW_ABORT_CALLBACK,
    SCHEDULED_ABORT_CALLBACK_FOR_WORKFLOW_STEP,
    POST_COMPLETION_ACTION_CONFIG_PROCESSOR,
    AUTO_RETRY_ACTION,
    AUTO_RETRY_ACTION_CLOCKWORK_CALLBACK,
    DOCUMENT_MASK_ACTION,
    PROCESS_DOCUMENT_MASKING_DRISHTI_CALLBACK,
    PROCESS_SEARCH_CLIENT_DATA,
    WORKFLOW_INIT,
    OCR_ACTION_V2,
    PERSIST_FORENSICS,
    PROCESS_OCR_ACTION_V2_CALLBACK,
    WORKFLOW_INIT_V2,
    HANDLE_INITIAL_ACTION_WORKFLOW_STEP_COMPLETION,
    VERIFY_OTP_HURDLE_V2,
    TRIGGER_OTP_HURDLE_V2,
    TRIGGER_SMS_CONSENT_HURDLE,
    ACKNOWLEDGE_SMS_CONSENT,
    PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL,
    DOCUMENT_PREFILL
}