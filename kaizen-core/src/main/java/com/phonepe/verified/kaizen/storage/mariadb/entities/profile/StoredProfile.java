package com.phonepe.verified.kaizen.storage.mariadb.entities.profile;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType.Names;
import com.phonepe.verified.kaizen.models.configs.summary.config.SummaryViewConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.Sharded;
import com.phonepe.verified.kaizen.storage.mariadb.entities.converters.GetTemplateConfigConverter;
import com.phonepe.verified.kaizen.storage.mariadb.entities.converters.PostCompletionActionConfigConverter;
import com.phonepe.verified.kaizen.storage.mariadb.entities.converters.PostWorkflowCreationActionConfigConverter;
import com.phonepe.verified.kaizen.storage.mariadb.entities.converters.SummaryViewConfigConverter;
import com.phonepe.verified.kaizen.utils.Constants;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@AuditTable(value = "profile_audit")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "profile_type", discriminatorType = DiscriminatorType.INTEGER)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "profileType", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.ADD_ON, value = StoredAddOnProfile.class),
        @JsonSubTypes.Type(name = Names.PRIMARY, value = StoredPrimaryProfile.class)})
@Table(name = "profile", indexes = {@Index(name = "unq_idx_profile_id", columnList = "profile_id", unique = true)})
public abstract class StoredProfile implements Serializable, Sharded {

    @Serial
    private static final long serialVersionUID = 1964300645240956233L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) AUTO_INCREMENT", insertable = false, updatable = false, nullable = false)
    private long id;

    @NotEmpty
    @Column(name = "profile_id", columnDefinition = "varchar(64)", nullable = false)
    private String profileId;

    @NotEmpty
    @Column(name = "organization", columnDefinition = "varchar(128)", nullable = false)
    private String organization;

    @NotEmpty
    @Column(name = "namespace", columnDefinition = "varchar(128)", nullable = false)
    private String namespace;

    @NotEmpty
    @Column(name = "type", columnDefinition = "varchar(128)", nullable = false)
    private String type;

    @Column(name = "version", columnDefinition = "varchar(32)", nullable = false)
    private String version;

    @Convert(converter = SummaryViewConfigConverter.class)
    @Column(name = "summary_view_config", columnDefinition = "text")
    private SummaryViewConfig summaryViewConfig;

    @Convert(converter = GetTemplateConfigConverter.class)
    @Column(name = "get_template_config", columnDefinition = "text")
    private GetTemplateConfig getTemplateConfig;

    @Convert(converter = PostCompletionActionConfigConverter.class)
    @Column(name = "post_completion_action_config", columnDefinition = "text")
    private PostCompletionActionConfig postCompletionActionConfig;

    @Nullable
    @Convert(converter = PostWorkflowCreationActionConfigConverter.class)
    @Column(name = "post_workflow_creation_action_config", columnDefinition = "text")
    private List<PostWorkflowCreationActionConfig> postWorkflowCreationActionConfigs;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "profile_type", columnDefinition = "TINYINT UNSIGNED DEFAULT 0", insertable = false, updatable = false, nullable = false)
    private ProfileType profileType;

    @Column(name = "last_updated_by", columnDefinition = "varchar(128)", nullable = false)
    private String lastUpdatedBy;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3)", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "last_updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3)", nullable = false)
    private LocalDateTime lastUpdatedAt;

    @Column(name = "approved_by", columnDefinition = "varchar(128)", nullable = true)
    private String approvedBy;

    @Override
    public String getShardingKey() {
        return Constants.PROFILE_SHARD_KEY;
    }


    public abstract <T, J> T accept(StoredProfileVisitor<T, J> visitor,
                                    J data);
}
