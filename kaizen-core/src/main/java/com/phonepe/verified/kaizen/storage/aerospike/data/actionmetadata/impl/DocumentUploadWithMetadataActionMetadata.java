package com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.ActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.ActionMetadataVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentUploadWithMetadataActionMetadata extends ActionMetadata {
    private List<DocumentTypeIdentifierAndLabel> documents;

    @Builder
    public DocumentUploadWithMetadataActionMetadata(final String actionId,
                                        final List<DocumentTypeIdentifierAndLabel> documents) {
        super(actionId, ActionMetadataType.DOCUMENT_UPLOAD_WITH_METADATA);
        this.documents = documents;
    }

    public DocumentUploadWithMetadataActionMetadata() {
        super(ActionMetadataType.DOCUMENT_UPLOAD_WITH_METADATA);
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }
}
