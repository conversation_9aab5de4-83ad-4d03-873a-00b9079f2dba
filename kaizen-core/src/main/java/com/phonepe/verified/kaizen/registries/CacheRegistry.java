package com.phonepe.verified.kaizen.registries;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.caches.impl.AddOnProfileCache;
import com.phonepe.verified.kaizen.caches.impl.ClientCallbackCache;
import com.phonepe.verified.kaizen.caches.impl.PrimaryProfileCache;
import com.phonepe.verified.kaizen.caches.impl.ProfileCache;
import com.phonepe.verified.kaizen.caches.impl.ProfileIdProfileStepsCache;
import com.phonepe.verified.kaizen.caches.impl.ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache;
import com.phonepe.verified.kaizen.caches.impl.ProfileStepCache;
import com.phonepe.verified.kaizen.caches.impl.SessionManagementConfigCache;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.util.Map;
import java.util.Objects;

@Singleton
public class CacheRegistry {

    private final Map<CacheName, Cache<?, ?>> cacheMap;

    @Inject
    public CacheRegistry(final ProfileCache profileCache,
                         final ProfileStepCache profileStepCache,
                         final PrimaryProfileCache primaryProfileCache,
                         final AddOnProfileCache addOnProfileCache,
                         final ProfileIdProfileStepsCache profileIdProfileStepsCache,
                         final ProfileIdSectionMappingIdToProfileStepStandardProfileScreenCache profileIdSectionMappingIdToProfileStepStandardProfileScreenCache,
                         final SessionManagementConfigCache sessionManagementConfigCache,
                         final ClientCallbackCache clientCallbackCache) {

        this.cacheMap = Map.ofEntries(Map.entry(profileCache.getCacheName(), profileCache),
                Map.entry(profileStepCache.getCacheName(), profileStepCache),
                Map.entry(primaryProfileCache.getCacheName(), primaryProfileCache),
                Map.entry(addOnProfileCache.getCacheName(), addOnProfileCache),
                Map.entry(profileIdProfileStepsCache.getCacheName(), profileIdProfileStepsCache),
                Map.entry(profileIdSectionMappingIdToProfileStepStandardProfileScreenCache.getCacheName(),
                        profileIdSectionMappingIdToProfileStepStandardProfileScreenCache),
                Map.entry(sessionManagementConfigCache.getCacheName(), sessionManagementConfigCache),
                Map.entry(clientCallbackCache.getCacheName(), clientCallbackCache));
    }

    public void invalidateCache(final CacheName cacheName) {

        final Cache<?, ?> cache = this.cacheMap.get(cacheName);

        if (Objects.isNull(cache)) {
            throw KaizenException.create(KaizenResponseCode.CACHE_NOT_FOUND, Map.of("cacheName", cacheName));
        }

        cache.invalidateAll();
    }
}
