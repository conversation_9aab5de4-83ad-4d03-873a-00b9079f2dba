package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowInitMessage;
import com.phonepe.verified.kaizen.services.WorkflowService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class WorkflowInitActor extends BaseActor<WorkflowInitMessage> {

    private final WorkflowService workflowService;

    @Inject
    protected WorkflowInitActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                final ConnectionRegistry connectionRegistry,
                                final ObjectMapper mapper,
                                final RetryStrategyFactory retryStrategyFactory,
                                final ExceptionHandlingFactory exceptionHandlingFactory,
                                final WorkflowService workflowService) {

        super(ActorType.WORKFLOW_INIT, actorConfigMap.get(ActorType.WORKFLOW_INIT), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, WorkflowInitMessage.class);
        this.workflowService = workflowService;
    }

    // NO RETRY
    @Override
    protected boolean handleMessage(final WorkflowInitMessage workflowInitMessage) {

        workflowService.processInitAsyncRequest(workflowInitMessage.getWorkflowInitRequest(),
                workflowInitMessage.getRequestId(), workflowInitMessage.getWorkflowId(),
                workflowInitMessage.getUserDetails());
        return true;
    }
}
