package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WorkflowStepCompletionMessage extends BaseMessage {

    @NonNull
    private final String workflowStepId;

    @NonNull
    private final String completedActionId;

    @NonNull
    private final StandardProfileScreenConfig currentProfileScreenConfig;

    private final boolean retryable;

    @Builder
    @Jacksonized
    public WorkflowStepCompletionMessage(final RequestInfo requestInfo,
                                         @NonNull final String workflowStepId,
                                         @NonNull final String completedActionId,
                                         @NonNull final StandardProfileScreenConfig currentProfileScreenConfig,
                                         final boolean retryable) {

        super(ActorMessageType.WORKFLOW_STEP_COMPLETION, requestInfo);
        this.workflowStepId = workflowStepId;
        this.completedActionId = completedActionId;
        this.currentProfileScreenConfig = currentProfileScreenConfig;
        this.retryable = retryable;
    }
}
