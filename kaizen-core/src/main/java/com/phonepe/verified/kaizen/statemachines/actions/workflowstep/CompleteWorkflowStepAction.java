package com.phonepe.verified.kaizen.statemachines.actions.workflowstep;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.actors.HandlePseudoSuccessWorkflowStepCompletionV2Actor;
import com.phonepe.verified.kaizen.queue.actors.HandleWorkflowStepCompletionActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepCompletionMessage;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepV2Message;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class CompleteWorkflowStepAction extends UpdateWorkflowStepBaseAction {

    private final Provider<HandleWorkflowStepCompletionActor> triggerWorkflowStepCompletionActorProvider;

    private final Provider<HandlePseudoSuccessWorkflowStepCompletionV2Actor> handlePseudoSuccessWorkflowStepCompletionV2ActorProvider;

    @Inject
    public CompleteWorkflowStepAction(final ProfileService profileService,
                                      final WorkflowService workflowService,
                                      final WorkflowStepService workflowStepService,
                                      final EventIngestionCommand eventIngestionCommand,
                                      final WorkflowStepRepository workflowStepRepository,
                                      final Provider<WorkflowContextStore> workflowContextStore,
                                      final Provider<HandleWorkflowStepCompletionActor> triggerWorkflowStepCompletionActorProvider,
                                      final Provider<HandlePseudoSuccessWorkflowStepCompletionV2Actor> handlePseudoSuccessWorkflowStepCompletionV2ActorProvider) {
        super(profileService, workflowService, workflowStepService, eventIngestionCommand, workflowStepRepository,
                workflowContextStore);
        this.triggerWorkflowStepCompletionActorProvider = triggerWorkflowStepCompletionActorProvider;
        this.handlePseudoSuccessWorkflowStepCompletionV2ActorProvider = handlePseudoSuccessWorkflowStepCompletionV2ActorProvider;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final TransitionState previousState,
                                  final StoredWorkflowStep storedWorkflowStep,
                                  final StateContext<TransitionState, TransitionEvent> stateContext) {

        if (TransitionState.PSEUDO_SUCCESS == previousState) {

            final var actionId = stateContext.getExtendedState()
                    .get(StoredAction.Fields.actionId, String.class);

            handlePseudoSuccessWorkflowStepCompletionV2ActorProvider.get()
                    .publish(WorkflowStepV2Message.builder()
                            .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                            .actionId(actionId)
                            .build());

            return;
        }

        final var currentProfileScreenConfig = stateContext.getExtendedState()
                .get(StandardProfileScreenConfig.class, StandardProfileScreenConfig.class);

        final var completedActionId = stateContext.getExtendedState()
                .get(Fields.actionId, String.class);

        final var isRetryable = stateContext.getExtendedState()
                .get(Constants.IS_RETRYABLE, Boolean.class);

        triggerWorkflowStepCompletionActorProvider.get()
                .publish(WorkflowStepCompletionMessage.builder()
                        .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                        .completedActionId(completedActionId)
                        .currentProfileScreenConfig(currentProfileScreenConfig)
                        .retryable(isRetryable)
                        .build());
    }
}
