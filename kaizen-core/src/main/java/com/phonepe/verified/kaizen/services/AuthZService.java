package com.phonepe.verified.kaizen.services;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.utils.PvcoreTenant;
import javax.annotation.Nullable;

public interface AuthZService {

    @Nullable
    PvcoreTenant getTenant(final String organization,
                           final String namespace);

    @Nullable
    PvcoreTenant getTenantFromWorkflow(final String workflowId);

    @Nullable
    PvcoreTenant getTenantFromProfile(final String profileId);

    @Nullable
    PvcoreTenant getTenantFromAction(final String actionId);

    void authorizeOperationForTenant(final PvcoreTenant pvcoreTenant,
                                     final String resource,
                                     final String operation,
                                     final ServiceUserPrincipal serviceUserPrincipal);

    void authorizeOperationForTenantOrDefault(final PvcoreTenant pvcoreTenant,
                                              final String resource,
                                              final String operation,
                                              final ServiceUserPrincipal serviceUserPrincipal);

    void authorizeUserWithActionId(final String actionId,
                                   final RequestInfo requestInfo,
                                   final UserDetails userDetails);

    void authorizeUserWithWorkflowStepId(final String workflowStepId,
                                         final RequestInfo requestInfo,
                                         final UserDetails userDetails);

    void authorizeUserWithWorkflowId(final String workflowId,
                                     final RequestInfo requestInfo,
                                     final UserDetails userDetails);

    void authorizeUserWithEntityId(final String entityId,
                                   final RequestInfo requestInfo,
                                   final UserDetails userDetails);

    void authorizeActionIdBelongsToWorkflowId(final String actionId,
                                              final String workflowId);
}
