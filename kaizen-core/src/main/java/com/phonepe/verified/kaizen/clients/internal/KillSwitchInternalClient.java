package com.phonepe.verified.kaizen.clients.internal;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.platform.killswitch.client.KillswitchClient;
import com.phonepe.platform.killswitch.client.KillswitchDebugContext;
import com.phonepe.platform.killswitch.client.KillswitchEvaluationResult;
import com.phonepe.platform.killswitch.client.KillswitchRecommendedAction;
import com.phonepe.platform.killswitch.common.RecommendedAction;
import com.phonepe.verified.kaizen.clients.models.killswitch.KillSwitchContext;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import io.appform.core.hystrix.CommandFactory;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class KillSwitchInternalClient {

    private final KillswitchClient client;

    private final EventIngestionCommand ingestionCommand;

    @Inject
    public KillSwitchInternalClient(final KillswitchClient killswitchClient,
                                    final EventIngestionCommand ingestionCommand) {

        this.client = killswitchClient;
        this.ingestionCommand = ingestionCommand;
    }

    public KillswitchRecommendedAction evaluate(final KillSwitchContext killSwitchContext) {

        try {
            final var killswitchEvaluationResult = CommandFactory.<KillswitchEvaluationResult>create(
                            KillSwitchInternalClient.class.getSimpleName(), "evaluate")
                    .executor(() -> client.evaluate(killSwitchContext))
                    .execute();
            final var recommendedAction = killswitchEvaluationResult.getRecommendedAction();

            if (recommendedAction == KillswitchRecommendedAction.BLOCK) {
                logAndIngestEvent(killSwitchContext, killswitchEvaluationResult);
            }

            return recommendedAction;

        } catch (final Exception e) {

            log.error(String.format("Error while fetching killSwitch for reference:%s", killSwitchContext.getType()),
                    e);
            return KillswitchRecommendedAction.ALLOW;
        }
    }

    public void evaluateAndThrow(final KillSwitchContext killSwitchContext) {

        final KillswitchRecommendedAction recommendedAction = evaluate(killSwitchContext);

        if (recommendedAction == KillswitchRecommendedAction.BLOCK) {
            throw KaizenException.create(KaizenResponseCode.KILLSWITCH_ENGAGED,
                    Map.of("workflowId", killSwitchContext.getWorkflowId(), "contextType", killSwitchContext.getType()
                            .name()));
        }
    }

    private void logAndIngestEvent(final KillSwitchContext killSwitchContext,
                                   final KillswitchEvaluationResult killswitchEvaluationResult) {
        final var debugContextOptional = killswitchEvaluationResult.getFailedRules()
                .stream()
                .filter(d -> d.getRecommendedAction()
                        .equals(RecommendedAction.BLOCK))
                .findFirst();

        log.info("Downtime found:{} for context:{}, workflowId: {}",
                debugContextOptional.map(KillswitchDebugContext::getId)
                        .orElse(null), killSwitchContext.getType(), killSwitchContext.getWorkflowId());

        debugContextOptional.ifPresent(
                killswitchDebugContext -> ingestionCommand.ingestKillSwitchEvent(killswitchDebugContext,
                        killSwitchContext));
    }

}
