package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Provider;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.retry.autoretry.AutoRetryConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.retry.autoretry.CountLimitedAutoRetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.autoretry.CountLimitedWithFixedWaitAutoRetryConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.AutoRetryConfigExecutor;
import com.phonepe.verified.kaizen.services.visitors.AutoRetryConfigExecutor.AutoRetryConfigExecutorData;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigForGivenAction;
import com.phonepe.verified.kaizen.services.visitors.StandardProfileScreenConfigFromProfileStepVisitor;
import com.phonepe.verified.kaizen.storage.aerospike.commands.AutoRetryActionDetailsCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.AutoRetryActionDetails;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.impl.ShadowV2UiRequestContext;
import com.phonepe.verified.kaizen.storage.aerospike.keys.AutoRetryActionKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.utils.CipherUtils;
import com.phonepe.verified.kaizen.utils.ClockworkUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class AutoRetryActionServiceImpl implements AutoRetryActionService {

    public final Map<Integer, byte[]> aesCipherKeyConfig;
    public final Map.Entry<Integer, byte[]> latestAesCipherKey;
    private final ActionService actionService;
    private final ProfileService profileService;
    private final DataProvider<KaizenConfig> appConfigProvider;
    private final AutoRetryConfigExecutor autoRetryConfigExecutor;
    private final Provider<WorkflowStepService> workflowStepServiceProvider;
    private final ClockworkClient clockworkClient;
    private final AutoRetryActionDetailsCommand autoRetryActionDetailsCommand;

    @Inject
    public AutoRetryActionServiceImpl(final ActionService actionService,
                                      final ProfileService profileService,
                                      final DataProvider<KaizenConfig> appConfigProvider,
                                      final AutoRetryConfigExecutor autoRetryConfigExecutor,
                                      final Provider<WorkflowStepService> workflowStepServiceProvider,
                                      final ClockworkClient clockworkClient,
                                      final AutoRetryActionDetailsCommand autoRetryActionDetailsCommand,
                                      @Named(value = "aesCipherKeyConfig") final Map<Integer, byte[]> aesCipherKeyConfig,
                                      @Named(value = "latestAesCipherKey") final Entry<Integer, byte[]> latestAesCipherKey) {

        this.actionService = actionService;
        this.profileService = profileService;
        this.appConfigProvider = appConfigProvider;
        this.autoRetryConfigExecutor = autoRetryConfigExecutor;
        this.workflowStepServiceProvider = workflowStepServiceProvider;
        this.clockworkClient = clockworkClient;
        this.autoRetryActionDetailsCommand = autoRetryActionDetailsCommand;
        this.aesCipherKeyConfig = aesCipherKeyConfig;
        this.latestAesCipherKey = latestAesCipherKey;
    }

    @Override
    public boolean checkIfAutoRetryAvailable(final String actionId) {
        final var storedAction = actionService.validateAndGetAction(actionId);

        final var workflowStepId = storedAction.getWorkflowStepId();

        final var storedWorkflowStep = workflowStepServiceProvider.get()
                .validateAndGetWorkflowStep(workflowStepId);

        final var standardStepActionConfig = getStandardStepActionConfig(storedAction, storedWorkflowStep);

        final var actionList = actionService.getActions(storedAction.getWorkflowStepId(),
                standardStepActionConfig.getActionMappingId());

        final var autoRetryConfig = standardStepActionConfig.getAutoRetryConfig();

        if (Objects.isNull(autoRetryConfig)) {
            return false;
        }

        final var failedActions = actionList.stream()
                .filter(action -> CompletionState.FAILURE == action.getCompletionState())
                .toList();

        return autoRetryConfig.accept(autoRetryConfigExecutor, AutoRetryConfigExecutorData.builder()
                .failedActionCount(failedActions.size())
                .build());
    }

    @Override
    public void triggerAutoRetryAction(final String actionId) {

        final var storedAction = actionService.validateAndGetAction(actionId);

        final var workflowStepId = storedAction.getWorkflowStepId();
        final var storedWorkflowStep = workflowStepServiceProvider.get()
                .validateAndGetWorkflowStep(workflowStepId);
        final var standardStepActionConfig = getStandardStepActionConfig(storedAction, storedWorkflowStep);

        final var autoRetryActionDetailsFromCache = getAutoRetryActionDetailsFromCache(
                standardStepActionConfig.getActionMappingId(), storedWorkflowStep.getWorkflowStepId());

        if (Objects.isNull(autoRetryActionDetailsFromCache)) {
            saveAutoRetryActionDetails(storedAction, storedWorkflowStep, standardStepActionConfig);
        }

        final var cachedAutoRetryActionDetails = getAutoRetryActionDetailsFromCache(
                standardStepActionConfig.getActionMappingId(), storedWorkflowStep.getWorkflowStepId());

        final var autoRetryConfig = standardStepActionConfig.getAutoRetryConfig();

        autoRetryConfig.accept(new AutoRetryConfigVisitor<Void, Void>() {
            @Override
            public Void visit(final CountLimitedAutoRetryConfig countLimitedAutoRetryConfig,
                              final Void data) {

                final var autoRetryActionDetails = decryptAutoRetryActionDetails(cachedAutoRetryActionDetails);

                triggerActionCreation(autoRetryActionDetails);
                return null;
            }

            @Override
            public Void visit(final CountLimitedWithFixedWaitAutoRetryConfig countLimitedWithFixedWaitAutoRetryConfig,
                              final Void data) {

                final var schedulingRequest = ClockworkUtils.getActionMessageSchedulingRequest(
                        cachedAutoRetryActionDetails, appConfigProvider.getData()
                                .getBaseUrl(), String.format("/v1/internal/callback/clockwork/auto-retry/action/%s/%s",
                                storedAction.getActionMappingId(), storedWorkflowStep.getWorkflowStepId()),
                        ClockworkUtils.getSchedulingDelayDate(
                                ((CountLimitedWithFixedWaitAutoRetryConfig) autoRetryConfig).getWaitTime()));

                final var clockworkSchedulingResponse = clockworkClient.schedule(schedulingRequest, Constants.APP_NAME);

                log.info(
                        "Auto retry action Request sent to clockwork with jobId: {}, actionMappingId: {}, workflowStepId: {}",
                        clockworkSchedulingResponse.getData()
                                .getJobId(), storedAction.getActionMappingId(), storedWorkflowStep.getWorkflowStepId());
                return null;
            }
        }, null);
    }

    @Override
    public AutoRetryActionDetails decryptAutoRetryActionDetails(final String autoRetryActionDetails) {
        final var decryptedAutoRetryActionString = CipherUtils.aesDecrypt(Base64.getDecoder()
                .decode(autoRetryActionDetails.getBytes(StandardCharsets.UTF_8)), aesCipherKeyConfig);

        return MapperUtils.deserialize(decryptedAutoRetryActionString, AutoRetryActionDetails.class);
    }

    @Override
    public void triggerActionCreation(final AutoRetryActionDetails autoRetryAction) {
        log.info("Trigger auto retry action for actionMappingId: {} and workflowStepId: {}",
                autoRetryAction.getActionMappingId(), autoRetryAction.getWorkflowStepId());

        actionService.createAction(autoRetryAction.getWorkflowStepId(), autoRetryAction.getActionType(),
                autoRetryAction.getStateMachineVersion(), autoRetryAction.getActionMappingId(),
                autoRetryAction.getTransitionContext(), autoRetryAction.getScreenMappingId(),
                autoRetryAction.getUserDetails(), autoRetryAction.getStepActionContext(),
                autoRetryAction.getTtlConfig(), autoRetryAction.getDependencyConfig());
    }

    private String getAutoRetryActionDetailsFromCache(final String actionMappingId,
                                                      final String workflowStepId) {
        return autoRetryActionDetailsCommand.get(AutoRetryActionKey.builder()
                .actionMappingId(actionMappingId)
                .workflowStepId(workflowStepId)
                .build());
    }

    private void saveAutoRetryActionDetails(final StoredAction storedAction,
                                            final StoredWorkflowStep storedWorkflowStep,
                                            final StandardStepActionConfig standardStepActionConfig) {

        autoRetryActionDetailsCommand.save(AutoRetryActionKey.builder()
                .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                .actionMappingId(standardStepActionConfig.getActionMappingId())
                .build(), encryptAutoRetryActionDetails(storedAction, storedWorkflowStep, standardStepActionConfig));
    }

    private String encryptAutoRetryActionDetails(final StoredAction storedAction,
                                                 final StoredWorkflowStep storedWorkflowStep,
                                                 final StandardStepActionConfig standardStepActionConfig) {
        final var uiRequestContext = workflowStepServiceProvider.get()
                .getUiRequestContext(storedWorkflowStep.getWorkflowStepId());

        final var shadowV2UiRequestContext = Objects.nonNull(uiRequestContext)
                                             ? (ShadowV2UiRequestContext) uiRequestContext
                                             : null;
        final var transitionContext = actionService.buildTransitionContext(
                standardStepActionConfig.getTransitionContextTemplate(),
                standardStepActionConfig.getTransitionContextTemplateAsString(), shadowV2UiRequestContext);

        final var autoRetryActionValueBytes = MapperUtils.serializeToBytes(AutoRetryActionDetails.builder()
                .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                .actionType(standardStepActionConfig.getActionType())
                .stateMachineVersion(standardStepActionConfig.getStateMachineVersion())
                .actionMappingId(standardStepActionConfig.getActionMappingId())
                .transitionContext(transitionContext)
                .screenMappingId(storedAction.getScreenMappingId())
                .userDetails(Constants.PVCORE_SYSTEM_USER)
                .stepActionContext(standardStepActionConfig.getStepActionContext())
                .ttlConfig(standardStepActionConfig.getTtlConfig())
                .dependencyConfig(standardStepActionConfig.getDependencyConfig())
                .uiRequestContext(uiRequestContext)
                .build());

        return new String(Base64.getEncoder()
                .encode(CipherUtils.aesEncrypt(autoRetryActionValueBytes, latestAesCipherKey)), StandardCharsets.UTF_8);
    }

    private StandardStepActionConfig getStandardStepActionConfig(final StoredAction storedAction,
                                                                 final StoredWorkflowStep storedWorkflowStep) {
        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        final var standardProfileScreenConfig = profileStep.getProfileScreenConfig()
                .accept(new StandardProfileScreenConfigFromProfileStepVisitor(storedAction.getScreenMappingId()))
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND,
                        Map.of(StoredAction.Fields.screenMappingId, storedAction.getScreenMappingId())));

        return standardProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND, Map.of()));
    }

}
