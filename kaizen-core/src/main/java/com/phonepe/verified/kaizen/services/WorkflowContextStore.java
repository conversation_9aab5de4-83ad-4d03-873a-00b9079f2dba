package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext.WorkflowContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import java.util.List;

public interface WorkflowContextStore {

    WorkflowContext getWorkflowContext(String workflowId);

    void createAndPersistWorkflowContext(StoredWorkflow storedWorkflow);

    void updateWorkflowContext(StoredWorkflow storedWorkflow);

    void updateWorkflowContext(String workflowId);

    void addWorkflowStepContext(StoredWorkflowStep storedWorkflowStep);

    void updateActionContextInWorkflowContext(StoredAction storedAction);

    void updateActionContextInWorkflowContextIfPresent(StoredAction storedAction);

    void addActionInWorkflowContext(StoredAction storedAction);

    void deleteActionFromWorkflowContext(final StoredAction storedAction);

    void updateWorkflowStepContext(StoredWorkflowStep storedWorkflowStep);

    WorkflowContext buildWorkflowContextWithoutDbCall(final StoredWorkflow storedWorkflow,
                                                      final List<StoredWorkflowStep> storedWorkflowSteps,
                                                      final List<StoredAction> storedActions,
                                                      final List<StoredActionMetadata> storedActionMetadataList);

    WorkflowContext getWorkflowContextWithInvalidatedActions(String workflowId);

    WorkflowContext buildWorkflowContextWithInvalidatedActions(String workflowId);
}
