package com.phonepe.verified.kaizen.services;

import com.phonepe.shadow.page.field.impl.summary.SummaryViewResponse;
import com.phonepe.verified.kaizen.caches.key.AddOnProfileCacheKey;
import com.phonepe.verified.kaizen.caches.key.PrimaryProfileCacheKey;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.keys.ProfileKey;
import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileCriteria;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ProfileService {

    Profile create(Profile profile,
                   String lastUpdatedBy,
                   String approvedBy);

    Profile get(String profileId,
                boolean fetchProfileSteps);

    List<Profile> get(final String organization,
                      final String namespace,
                      final String type,
                      final boolean fetchProfileSteps);

    List<Profile> get(final List<ProfileCriteria> profileCriteria,
                      boolean fetchProfileSteps);

    List<Profile> get(final String organization,
                      final String namespace,
                      final boolean fetchProfileSteps);

    List<Profile> getAll(boolean fetchProfileSteps);

    Set<String> getAllOrganisations(final ProfileType profileType);

    List<StandardProfileScreenConfig> getAllStandardProfileScreenConfigs(final Profile profile);

    Set<String> getNamespaces(final String organization,
                              final ProfileType profileType);

    PrimaryProfile getPrimaryProfile(PrimaryProfileCacheKey primaryProfileCacheKey,
                                     boolean fetchProfileSteps);

    ProfileStep getProfileStep(final String profileStepId);

    ProfileStep getProfileStep(final String profileId,
                               final String profileStepMappingId);

    Optional<ProfileStep> getProfileStepFromGivenActionMappingId(final Profile profile,
                                                                 final String actionMappingId);

    StandardProfileScreenConfig getStandardProfileScreenConfigForGivenActionMappingId(final Profile profile,
                                                                                      final String actionMappingId);

    StandardStepActionConfig getStandardStepActionConfigForGivenActionMappingId(final Profile profile,
                                                                                final String actionMappingId);

    SummaryViewResponse getSummaryView(final String workflowId,
                                       final String intent,
                                       final long componentKitVersion);

    Set<String> getTypes(final String organization,
                         final String namespace,
                         final ProfileType profileType);

    AddOnProfile getAddOnProfile(AddOnProfileCacheKey addOnProfileCacheKey,
                                 String addOnType,
                                 boolean fetchProfileSteps);

    List<AddOnProfile> getAddOnProfiles(AddOnProfileCacheKey addOnProfileCacheKey,
                                        boolean fetchProfileSteps);

    Set<String> getAddOnTypes(final String organization,
                              final String namespace,
                              final String type,
                              final String version);

    Optional<AddOnProfile> getValidAddOnProfileForEntity(String entityId,
                                                         EntityType entityType,
                                                         ProfileKey profileKey,
                                                         boolean fetchProfileSteps);

    Set<String> getVersions(final String organization,
                            final String namespace,
                            final String type,
                            final ProfileType profileType);

    Profile update(Profile profile,
                   String lastUpdatedBy,
                   String approvedBy);

    void validateProfileActionTypeExists(Profile profile);

    void validateProfileAlreadyExists(Profile profile);

    void validateProfileEvaluationAndExecutionRules(Profile profile);

    void validateProfileIdentifierUnmodified(Profile updatedProfile,
                                             Profile profileAlreadyStored);

    void validateUniqueActionMappingIds(List<StandardProfileScreenConfig> standardProfileScreenConfigs);

    void validateUniqueProfileStepMappingIds(Profile profile);

    void validateUniqueScreenMappingIdsAndActionMappingIds(Profile profile);
}
