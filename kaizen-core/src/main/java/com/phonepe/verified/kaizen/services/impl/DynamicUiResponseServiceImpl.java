package com.phonepe.verified.kaizen.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.shadow.services.TemplateServiceV2;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.contexts.AuxiliaryWorkflowContext;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.actors.RevolverCallbackActor;
import com.phonepe.verified.kaizen.queue.messages.EventIngestionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DynamicUiResponseService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.ExpressionEvaluator;
import com.phonepe.verified.kaizen.services.visitors.GetFirstStandardProfileScreenConfig;
import com.phonepe.verified.kaizen.services.visitors.GetStandardActionConfigForGivenAction;
import com.phonepe.verified.kaizen.services.visitors.TriggerUiResponseVisitor;
import com.phonepe.verified.kaizen.storage.aerospike.commands.RespondedToRevolverAgainstRequestIdCommand;
import com.phonepe.verified.kaizen.storage.aerospike.commands.RespondedToRevolverAgainstRequestIdLockCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.uirequestcontext.UiRequestContext;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction.Fields;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class DynamicUiResponseServiceImpl implements DynamicUiResponseService {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final TemplateServiceV2 templateServiceV2;

    private final HandleBarsService handleBarsService;

    private final ExpressionEvaluator expressionEvaluator;

    private final WorkflowContextStore workflowContextStore;

    private final EventIngestionActor eventIngestionActor;

    private final RevolverCallbackActor revolverCallbackActor;

    private final GetFirstStandardProfileScreenConfig getFirstStandardProfileScreenConfig;

    private final RespondedToRevolverAgainstRequestIdCommand respondedToRevolverAgainstRequestIdCommand;

    private final RespondedToRevolverAgainstRequestIdLockCommand respondedToRevolverAgainstRequestIdLockCommand;


    @Override
    @SneakyThrows
    public void sendSuccessResponseToUi(final String completedActionId,
                                        final UiRequestContext uiRequestContext,
                                        final StandardProfileScreenConfig currentStandardProfileScreenConfig,
                                        final ProfileScreenConfig nextProfileScreenConfig) {

        final var nextStandardProfileScreenConfig = nextProfileScreenConfig.accept(getFirstStandardProfileScreenConfig)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND, Map.of()));

        final var storedAction = actionService.validateAndGetAction(completedActionId);

        final var storedWorkflowStep = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        eventIngestionActor.publish(EventIngestionMessage.builder()
                .eventType(EventType.SECTION_SUBMIT_SUCCEED)
                .actionId(storedAction.getActionId())
                .build());

        currentStandardProfileScreenConfig.getSuccessResponseConfig()
                .get(uiRequestContext.getTemplateType())
                .accept(new TriggerUiResponseVisitor(nextStandardProfileScreenConfig.getScreenMappingId(),
                        currentStandardProfileScreenConfig.getScreenMappingId(), profileService, workflowService,
                        hopeLangService, uiRequestContext, handleBarsService, templateServiceV2, false,
                        expressionEvaluator, workflowContextStore, revolverCallbackActor, null,
                        storedWorkflowStep.getWorkflowId(), respondedToRevolverAgainstRequestIdCommand,
                        respondedToRevolverAgainstRequestIdLockCommand));
    }

    @Override
    @SneakyThrows
    public void sendSkipWorkflowStepResponseToUi(final String completedActionId,
                                                 final UiRequestContext uiRequestContext,
                                                 final StandardProfileScreenConfig currentStandardProfileScreenConfig,
                                                 final ProfileScreenConfig nextProfileScreenConfig) {

        final var nextStandardProfileScreenConfig = nextProfileScreenConfig.accept(getFirstStandardProfileScreenConfig)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND, Map.of()));

        final var storedAction = actionService.validateAndGetAction(completedActionId);

        eventIngestionActor.publish(EventIngestionMessage.builder()
                .eventType(EventType.SECTION_SUBMIT_SUCCEED)
                .actionId(storedAction.getActionId())
                .build());

        final var storedWorkflowStep = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        currentStandardProfileScreenConfig.getSkipWorkflowStepConfig()
                .getSkipWorkflowStepResponseConfig()
                .get(uiRequestContext.getTemplateType())
                .accept(new TriggerUiResponseVisitor(nextStandardProfileScreenConfig.getScreenMappingId(),
                        currentStandardProfileScreenConfig.getScreenMappingId(), profileService, workflowService,
                        hopeLangService, uiRequestContext, handleBarsService, templateServiceV2, false,
                        expressionEvaluator, workflowContextStore, revolverCallbackActor, null,
                        storedWorkflowStep.getWorkflowId(), respondedToRevolverAgainstRequestIdCommand,
                        respondedToRevolverAgainstRequestIdLockCommand));
    }

    //make other methods call  sendResponseToUi to return ui response
    @Override
    public void sendResponseToUi(final UiRequestContext uiRequestContext,
                                 final Map<TemplateType, UiResponseConfig> uiResponseConfigMap,
                                 final String workflowId) {

        this.sendResponseToUi(uiRequestContext, uiResponseConfigMap, null, workflowId);
    }

    @Override
    public void sendResponseToUi(final UiRequestContext uiRequestContext,
                                 final Map<TemplateType, UiResponseConfig> uiResponseConfigMap,
                                 final AuxiliaryWorkflowContext auxiliaryWorkflowContext,
                                 final String workflowId) {

        uiResponseConfigMap.get(uiRequestContext.getTemplateType())
                .accept(new TriggerUiResponseVisitor(null, null, profileService, workflowService, hopeLangService,
                        uiRequestContext, handleBarsService, templateServiceV2, false, expressionEvaluator,
                        workflowContextStore, revolverCallbackActor, auxiliaryWorkflowContext, workflowId,
                        respondedToRevolverAgainstRequestIdCommand, respondedToRevolverAgainstRequestIdLockCommand));
    }

    @Override
    public void sendSkipWorkflowResponseToUi(final UiRequestContext uiRequestContext,
                                             final StandardProfileScreenConfig standardProfileScreenConfig,
                                             final String workflowId) {

        standardProfileScreenConfig.getSkipWorkflowConfig()
                .getSkipWorkflowResponseConfig()
                .get(uiRequestContext.getTemplateType())
                .accept(new TriggerUiResponseVisitor(null, standardProfileScreenConfig.getScreenMappingId(),
                        profileService, workflowService, hopeLangService, uiRequestContext, handleBarsService,
                        templateServiceV2, false, expressionEvaluator, workflowContextStore, revolverCallbackActor,
                        null, workflowId, respondedToRevolverAgainstRequestIdCommand,
                        respondedToRevolverAgainstRequestIdLockCommand));
    }


    @Override
    //merge all methods with sendUiResponse
    public void sendWorkflowCompletionResponseToUi(final UiRequestContext uiRequestContext,
                                                   final StandardProfileScreenConfig standardProfileScreenConfig,
                                                   final String workflowId) {

        standardProfileScreenConfig.getSuccessResponseConfig()
                .get(uiRequestContext.getTemplateType())
                .accept(new TriggerUiResponseVisitor(null, standardProfileScreenConfig.getScreenMappingId(),
                        profileService, workflowService, hopeLangService, uiRequestContext, handleBarsService,
                        templateServiceV2, false, expressionEvaluator, workflowContextStore, revolverCallbackActor,
                        null, workflowId, respondedToRevolverAgainstRequestIdCommand,
                        respondedToRevolverAgainstRequestIdLockCommand));
    }

    @Override
    public void sendFailureResponseToUi(final String completedActionId,
                                        final UiRequestContext uiRequestContext,
                                        final StandardProfileScreenConfig currentProfileScreenConfig,
                                        final ProfileScreenConfig nextProfileScreenConfig,
                                        final boolean retryNotAllowedOrRetryExhausted) {

        final var storedAction = actionService.validateAndGetAction(completedActionId);

        final var storedWorkflowStep = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        final var standardStepActionConfig = currentProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, storedAction.getActionMappingId())
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of(Fields.actionId, completedActionId, Fields.workflowStepId,
                                storedAction.getWorkflowStepId())));

        sendFailureResponseToUi(storedAction, uiRequestContext, currentProfileScreenConfig, nextProfileScreenConfig,
                standardStepActionConfig, retryNotAllowedOrRetryExhausted, storedWorkflowStep.getWorkflowId());
    }

    @Override
    public void sendFailureResponseToUi(final String actionMappingId,
                                        final String workflowId,
                                        final ActionFailureErrorCode actionFailureErrorCode,
                                        final UiRequestContext uiRequestContext,
                                        final StandardProfileScreenConfig currentProfileScreenConfig,
                                        final AuxiliaryWorkflowContext auxiliaryWorkflowContext,
                                        final boolean retryNotAllowedOrRetryExhausted) {

        // Useful when the actionId is not yet created
        final var standardStepActionConfig = currentProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, actionMappingId)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.ACTION_NOT_FOUND,
                        Map.of("actionMappingId", actionMappingId, "workflowId", workflowId)));

        sendFailureResponseToUi(workflowId, actionFailureErrorCode, uiRequestContext, currentProfileScreenConfig, null,
                standardStepActionConfig, auxiliaryWorkflowContext, retryNotAllowedOrRetryExhausted);
    }

    @Override
    @SneakyThrows
    public void sendFailureResponseToUi(final StoredAction storedAction,
                                        final UiRequestContext uiRequestContext,
                                        final StandardProfileScreenConfig currentProfileScreenConfig,
                                        final ProfileScreenConfig nextProfileScreenConfig,
                                        final StandardStepActionConfig standardStepActionConfig,
                                        final boolean retryNotAllowedOrRetryExhausted,
                                        final String workflowId) {

        // todo{Ankit}:: Need to handle edge case where config is missing for failure error code
        eventIngestionActor.publish(EventIngestionMessage.builder()
                .eventType(EventType.SECTION_SUBMIT_FAILED)
                .actionId(storedAction.getActionId())
                .build());

        sendFailureResponseToUi(workflowId, storedAction.getFailureErrorCode(), uiRequestContext,
                currentProfileScreenConfig, nextProfileScreenConfig, standardStepActionConfig, null,
                retryNotAllowedOrRetryExhausted);
    }

    @Override
    @SneakyThrows
    public void sendFailureResponseToUi(final String workflowId,
                                        final ActionFailureErrorCode actionFailureErrorCode,
                                        final UiRequestContext uiRequestContext,
                                        final StandardProfileScreenConfig currentProfileScreenConfig,
                                        final ProfileScreenConfig nextProfileScreenConfig,
                                        final StandardStepActionConfig standardStepActionConfig,
                                        final AuxiliaryWorkflowContext auxiliaryWorkflowContext,
                                        final boolean retryNotAllowedOrRetryExhausted) {

        // Useful when we have to send failure response to UI before action has been created or without section submit
        sendResponseToUi(workflowId, actionFailureErrorCode, uiRequestContext, currentProfileScreenConfig,
                nextProfileScreenConfig, standardStepActionConfig.getFailureResponseConfig(), auxiliaryWorkflowContext,
                retryNotAllowedOrRetryExhausted);
    }

    @Override
    @SneakyThrows
    public void sendResponseToUi(final String workflowId,
                                 final ActionFailureErrorCode actionFailureErrorCode,
                                 final UiRequestContext uiRequestContext,
                                 final StandardProfileScreenConfig currentProfileScreenConfig,
                                 final ProfileScreenConfig nextProfileScreenConfig,
                                 final Map<ActionFailureErrorCode, Map<TemplateType, UiResponseConfig>> uiResponseConfig,
                                 final AuxiliaryWorkflowContext auxiliaryWorkflowContext,
                                 final boolean retryNotAllowedOrRetryExhausted) {

        final var templateConfig = uiResponseConfig.getOrDefault(actionFailureErrorCode, Map.of())
                .get(uiRequestContext.getTemplateType());

        final var effectiveNextScreenMappingId = getEffectiveNextScreenMappingId(nextProfileScreenConfig);

        templateConfig.accept(new TriggerUiResponseVisitor(effectiveNextScreenMappingId,
                currentProfileScreenConfig.getScreenMappingId(), profileService, workflowService, hopeLangService,
                uiRequestContext, handleBarsService, templateServiceV2, retryNotAllowedOrRetryExhausted,
                expressionEvaluator, workflowContextStore, revolverCallbackActor, auxiliaryWorkflowContext, workflowId,
                respondedToRevolverAgainstRequestIdCommand, respondedToRevolverAgainstRequestIdLockCommand));
    }

    private String getEffectiveNextScreenMappingId(final ProfileScreenConfig nextProfileScreenConfig) {

        if (Objects.isNull(nextProfileScreenConfig)) {
            return null;
        }

        return nextProfileScreenConfig.accept(getFirstStandardProfileScreenConfig)
                .orElseThrow(() -> KaizenException.create(KaizenResponseCode.PROFILE_SCREEN_NOT_FOUND, Map.of()))
                .getScreenMappingId();
    }
}
