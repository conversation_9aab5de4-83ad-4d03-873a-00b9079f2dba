package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import com.phonepe.verified.kaizen.models.configs.action.impl.AndStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.EvaluatedStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.OrStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import java.util.List;
import java.util.stream.Stream;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GetStandardStepActionConfigForGivenActionMappingIdVisitor implements
        StepActionVisitor<List<StandardStepActionConfig>, String> {

    public static final GetStandardStepActionConfigForGivenActionMappingIdVisitor INSTANCE = new GetStandardStepActionConfigForGivenActionMappingIdVisitor();

    @Override
    public @NotNull List<StandardStepActionConfig> visit(final StandardStepActionConfig standardStepActionConfig,
                                                         final String actionMappingId) {

        return standardStepActionConfig.getActionMappingId()
                       .equals(actionMappingId)
               ? List.of(standardStepActionConfig)
               : List.of();
    }

    @Override
    public @NotNull List<StandardStepActionConfig> visit(final AndStepActionConfig andStepActionConfig,
                                                         final String actionMappingId) {

        final var storedActionListFromLeftAction = andStepActionConfig.getLeft()
                .accept(this, actionMappingId);

        final var storedActionListFromRightAction = andStepActionConfig.getRight()
                .accept(this, actionMappingId);

        return Stream.concat(storedActionListFromLeftAction.stream(), storedActionListFromRightAction.stream())
                .toList();
    }

    @Override
    public List<StandardStepActionConfig> visit(final OrStepActionConfig orStepActionConfig,
                                                final String actionMappingId) {

        final var storedActionListFromLeftAction = orStepActionConfig.getLeft()
                .accept(this, actionMappingId);

        final var storedActionListFromRightAction = orStepActionConfig.getRight()
                .accept(this, actionMappingId);

        return Stream.concat(storedActionListFromLeftAction.stream(), storedActionListFromRightAction.stream())
                .toList();
    }

    @Override
    public @NotNull List<StandardStepActionConfig> visit(final EvaluatedStepActionConfig evaluatedStepActionConfig,
                                                         final String actionMappingId) {

        return evaluatedStepActionConfig.getConfig()
                .accept(this, actionMappingId);
    }
}
