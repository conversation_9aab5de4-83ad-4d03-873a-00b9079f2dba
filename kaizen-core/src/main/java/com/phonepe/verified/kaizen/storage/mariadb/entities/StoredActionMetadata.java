package com.phonepe.verified.kaizen.storage.mariadb.entities;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.services.ActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredConsentActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredKeyValueMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredOtpHurdleActionMetaData;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredWaitForConditionMetadata;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "action_detail")
@AuditTable(value = "action_detail_audit")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "action_metadata_type", discriminatorType = DiscriminatorType.STRING)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "actionMetadataType", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.DOCUMENT_UPLOAD_WITH_METADATA, value = StoredDocumentUploadWithMetaDataActionMetadata.class),
        @JsonSubTypes.Type(name = Names.DOCUMENT_UPLOAD, value = StoredDocumentUploadActionMetadata.class),
        @JsonSubTypes.Type(name = Names.KEY_VALUE, value = StoredKeyValueMetadata.class),
        @JsonSubTypes.Type(name = Names.CONSENT, value = StoredConsentActionMetadata.class),
        @JsonSubTypes.Type(name = Names.WAIT_FOR_CONDITION, value = StoredWaitForConditionMetadata.class),
        @JsonSubTypes.Type(name = Names.OTP_DETAILS, value = StoredOtpHurdleActionMetaData.class)})
public abstract class StoredActionMetadata implements Sharded, Serializable {

    @Serial
    private static final long serialVersionUID = 9033454798124827574L;
    @Id
    @EmbeddedId
    private PrimaryKey primaryKey;

    @Column(name = "action_id", columnDefinition = "varchar(45)", nullable = false)
    private String actionId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "action_metadata_type", columnDefinition = "varchar(40)", nullable = false, insertable = false, updatable = false)
    private ActionMetadataType actionMetadataType;

    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3)", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "last_updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3)", nullable = false)
    private LocalDateTime lastUpdatedAt;

    // TODO:: Once migration is done, add @CreationTimestamp and @UpdateTimestamp
    @PrePersist
    void createdAt() {

        final var now = LocalDateTime.now();

        if (this.createdAt == null) {
            this.createdAt = now;
        }
        if (this.lastUpdatedAt == null) {
            this.lastUpdatedAt = now;
        }
    }

    @PreUpdate
    void updatedAt() {
        this.lastUpdatedAt = LocalDateTime.now();
    }

    @Override
    public String getShardingKey() {
        return actionId;
    }

    public abstract String getActionMetadataContextKey();

    public abstract <T, J> T accept(ActionMetadataVisitor<T, J> actionMetadataVisitor,
                                    J data);

}
