package com.phonepe.verified.kaizen.statemachines.actions.commons;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.UpdateStateBaseAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@ActionKey(value = "updateStateAction")
public class UpdateStateAction extends UpdateStateBaseAction {

    @Inject
    public UpdateStateAction(final ActionService actionService,
                             final ActionRepository actionRepository,
                             final Provider<WorkflowContextStore> workflowContextStore,
                             final Provider<EventIngestionActor> eventIngestionActorProvider) {
        super(actionService, actionRepository, workflowContextStore, eventIngestionActorProvider);
    }
}
