package com.phonepe.verified.kaizen.storage.mariadb.entities;

import com.phonepe.verified.kaizen.utils.Constants;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Builder
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@FieldNameConstants
@NoArgsConstructor
@AllArgsConstructor
@AuditTable(value = "client_callback_config_audit")
@Table(name = "client_callback_config", indexes = {
        @Index(name = "unq_idx_profile_id", columnList = "profile_id", unique = true)})
public class StoredClientCallbackConfig implements Sharded {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) AUTO_INCREMENT", insertable = false, updatable = false, nullable = false)
    private long id;

    @Column(name = "profile_id", columnDefinition = "varchar(64)", nullable = false)
    private String profileId;

    @Column(name = "callback_service", columnDefinition = "varchar(128)", nullable = false)
    private String callbackService;

    @Column(name = "callback_url", columnDefinition = "varchar(256)", nullable = false)
    private String callbackUrl;

    @Column(name = "last_updated_by", columnDefinition = "varchar(128)", nullable = false)
    private String lastUpdatedBy;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3)", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "last_updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3)", nullable = false)
    private LocalDateTime lastUpdatedAt;

    @Override
    public String getShardingKey() {
        return Constants.PROFILE_SHARD_KEY;
    }
}


