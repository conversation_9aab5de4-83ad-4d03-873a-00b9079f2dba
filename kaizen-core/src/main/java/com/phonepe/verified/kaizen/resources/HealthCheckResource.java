package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/")
@Tag(name = "Health Check APIs")
@AllArgsConstructor(onConstructor = @__(@Inject))
public class HealthCheckResource {

    @GET
    @AccessAllowed
    @Produces(MediaType.TEXT_PLAIN)
    @ApiKillerMeta(tags = {OperationTypeConstants.READ})
    @Operation(summary = "Health Check resource for upstream nginx boxes", hidden = true)
    public String healthCheck() {
        return Constants.APP_NAME;
    }
}
