package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import java.util.Comparator;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GetAllStandardScreenConfigsVisitor implements ProfileScreenVisitor<List<StandardProfileScreenConfig>> {

    public static final GetAllStandardScreenConfigsVisitor INSTANCE = new GetAllStandardScreenConfigsVisitor();

    @Override
    public List<StandardProfileScreenConfig> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {
        return List.of(standardProfileScreenConfig);
    }

    @Override
    public List<StandardProfileScreenConfig> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        return sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .flatMap(orderedProfileScreen -> orderedProfileScreen.getProfileScreenConfig()
                        .accept(this)
                        .stream())
                .toList();
    }
}
