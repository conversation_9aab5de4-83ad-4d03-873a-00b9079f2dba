package com.phonepe.verified.kaizen.utils;

import com.phonepe.platform.requestinfo.bundle.core.RequestThreadData;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import java.util.Objects;
import java.util.Optional;
import lombok.experimental.UtilityClass;

@UtilityClass
public class RequestInfoUtils {

    public Optional<RequestInfo> getRequestInfo() {

        return Optional.ofNullable(RequestThreadData.get()
                .getRequestInfo());
    }

    public void saveRequestInfoInThread(final RequestInfo requestInfo) {

        RequestThreadData.clear();

        RequestThreadData.get()
                .setRequestInfo(requestInfo);
    }

    public void clearRequestInfo() {

        RequestThreadData.clear();
    }

    public RequestInfo getOrBuildEmptyRequestInfoWithEnvField() {

        final var requestInfoFromMDCOptional = Optional.ofNullable(RequestThreadData.get()
                .getRequestInfo());

        // This is populated by request info bundle itself.
        // Can be null if client is called in async manner
        // i.e. not directly from resource (or HK resource).

        // Case may also exist when request info is non-null
        // but was not received with feature env header.
        // If feature env is disabled, this is fine.
        if (requestInfoFromMDCOptional.isPresent() && (Objects.nonNull(requestInfoFromMDCOptional.get()
                .getRequestEnv()) || !FeatureEnvironmentUtils.isFeatureEnvApplicable())) {
            return requestInfoFromMDCOptional.get();
        }

        // Fetch feature environment name. If present, make empty RequestInfo context with it.
        // Otherwise, return null since HttpExecutor ignores null RequestInfo

        return updateRequestInfoWithEnv(requestInfoFromMDCOptional.orElse(RequestInfo.builder()
                .build()));
    }

    private RequestInfo updateRequestInfoWithEnv(final RequestInfo requestInfo) {

        FeatureEnvironmentUtils.getFeatureEnvironmentName()
                .ifPresent(requestInfo::setRequestEnv);

        return requestInfo;
    }
}
