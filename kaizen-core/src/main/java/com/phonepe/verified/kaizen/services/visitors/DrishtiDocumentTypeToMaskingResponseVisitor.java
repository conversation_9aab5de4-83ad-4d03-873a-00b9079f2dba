package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.drishti.models.commons.documents.DocumentMaskingIdentifier;
import com.phonepe.verified.drishti.models.responses.masking.MaskingResponseVisitor;
import com.phonepe.verified.drishti.models.responses.masking.impl.AadhaarMaskingResponse;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DrishtiDocumentTypeToMaskingResponseVisitor implements
        MaskingResponseVisitor<List<DocumentMaskingIdentifier>, Void> {

    public static final DrishtiDocumentTypeToMaskingResponseVisitor INSTANCE = new DrishtiDocumentTypeToMaskingResponseVisitor();

    @Override
    public List<DocumentMaskingIdentifier> visitAadhaar(final AadhaarMaskingResponse aadhaarMaskingResponse,
                                                        final Void data) {

        return Objects.isNull(aadhaarMaskingResponse.getDocumentBack())
               ? List.of(aadhaarMaskingResponse.getDocumentFront())
               : List.of(aadhaarMaskingResponse.getDocumentFront(), aadhaarMaskingResponse.getDocumentBack());
    }
}
