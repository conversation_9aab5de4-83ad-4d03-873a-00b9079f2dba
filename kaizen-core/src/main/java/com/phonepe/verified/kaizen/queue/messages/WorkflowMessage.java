package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.annotation.Nullable;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WorkflowMessage extends BaseMessage {

    @NonNull
    private final String workflowId;

    @Nullable
    private final ActionFailureErrorCode failureErrorCode;

    @Builder
    @Jacksonized
    public WorkflowMessage(final RequestInfo requestInfo,
                           @NonNull final String workflowId,
                           @Nullable final ActionFailureErrorCode failureErrorCode) {

        super(ActorMessageType.WORKFLOW, requestInfo);
        this.workflowId = workflowId;
        this.failureErrorCode = failureErrorCode;
    }
}
