package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.requests.v2.ShadowV2SectionSubmitV2Request;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SectionSubmitV2Message extends BaseMessage {

    @NotEmpty
    private final String workflowId;

    @NonNull
    private final UserDetails userDetails;

    @NonNull
    private final ShadowV2SectionSubmitV2Request shadowV2SectionSubmitV2Request;

    @Builder
    @Jacksonized
    public SectionSubmitV2Message(@NonNull final RequestInfo requestInfo,
                                  @NotEmpty final String workflowId,
                                  @NonNull final UserDetails userDetails,
                                  @NonNull final ShadowV2SectionSubmitV2Request shadowV2SectionSubmitV2Request) {

        super(ActorMessageType.SECTION_SUBMIT_V2, requestInfo);
        this.workflowId = workflowId;
        this.userDetails = userDetails;
        this.shadowV2SectionSubmitV2Request = shadowV2SectionSubmitV2Request;
    }
}