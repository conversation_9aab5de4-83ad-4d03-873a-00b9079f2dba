package com.phonepe.verified.kaizen.registries;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.BaseTransitionAction;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;

@Slf4j
@Singleton
public class SpringActionRegistry {

    private final Map<String, BaseTransitionAction<String, String>> springActionRegistryMap;

    @Inject
    public SpringActionRegistry(final Injector injector) {
        this.springActionRegistryMap = initializeActionRegistryMap(injector);
        logActionRegistry();
    }

    private Map<String, BaseTransitionAction<String, String>> initializeActionRegistryMap(final Injector injector) {

        final var reflections = new Reflections(Constants.SM_ACTION_PACKAGE);

        validateDuplicateActionKeys(reflections);

        return reflections.getTypesAnnotatedWith(ActionKey.class)
                .stream()
                .filter(BaseTransitionAction.class::isAssignableFrom)
                .collect(Collectors.toMap(c -> c.getAnnotation(ActionKey.class)
                        .value(), c -> (BaseTransitionAction<String, String>) injector.getInstance(c)));
    }

    private void validateDuplicateActionKeys(final Reflections reflections) {

        final var totalActionSize = reflections.getTypesAnnotatedWith(ActionKey.class)
                .size();
        final var actionKeySet = reflections.getTypesAnnotatedWith(ActionKey.class)
                .stream()
                .map(c -> c.getAnnotation(ActionKey.class)
                        .value())
                .collect(Collectors.toSet());

        if (actionKeySet.size() != totalActionSize) {
            throw KaizenException.create(KaizenResponseCode.DUPLICATE_ACTION_KEY_ERROR, Map.of());
        }
    }

    public BaseTransitionAction<String, String> getAction(final String actionKey) {
        if (springActionRegistryMap.containsKey(actionKey)) {
            return springActionRegistryMap.get(actionKey);
        } else {
            throw KaizenException.create(KaizenResponseCode.STATE_MACHINE_ACTION_NOT_FOUND,
                    Map.of("actionKey", actionKey));
        }
    }

    public Set<String> getAllSpringActions() {
        return springActionRegistryMap.keySet();
    }

    public boolean doesNotExist(final String actionKey) {
        return !springActionRegistryMap.containsKey(actionKey);
    }

    private void logActionRegistry() {

        final var builder = new StringBuilder("Registered Actions:").append(System.lineSeparator())
                .append(System.lineSeparator());

        springActionRegistryMap.forEach((k, v) -> builder.append("\t")
                .append(k)
                .append("\t => ")
                .append("(")
                .append(v.getClass()
                        .getName())
                .append(")")
                .append(System.lineSeparator()));

        log.info(builder.toString());
    }

}
