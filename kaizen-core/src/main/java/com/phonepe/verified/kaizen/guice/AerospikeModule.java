package com.phonepe.verified.kaizen.guice;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.IAerospikeClient;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.phonepe.platform.aerospike.bundle.AerospikeBundle;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class AerospikeModule extends AbstractModule {

    private final AerospikeBundle<? extends KaizenConfig> aerospikeBundle;

    @Provides
    public IAerospikeClient aerospikeClient() {
        return aerospikeBundle.getAerospikeClient();
    }

    @Provides
    public AerospikeClient getAerospikeClient() {
        return (AerospikeClient) aerospikeBundle.getAerospikeClient();
    }

    @Provides
    public AerospikeConfig aerospikeConfig(final KaizenConfig config) {
        return config.getAerospikeConfig();
    }
}
