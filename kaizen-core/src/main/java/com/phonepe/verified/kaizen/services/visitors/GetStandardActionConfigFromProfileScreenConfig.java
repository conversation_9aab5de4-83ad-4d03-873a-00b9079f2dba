package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import java.util.Comparator;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class GetStandardActionConfigFromProfileScreenConfig implements
        ProfileScreenVisitor<Optional<StandardStepActionConfig>> {

    private final String actionMappingId;

    @Override
    public Optional<StandardStepActionConfig> visit(final StandardProfileScreenConfig standardProfileScreenConfig) {

        return standardProfileScreenConfig.getStepActionConfig()
                .accept(GetStandardActionConfigForGivenAction.INSTANCE, actionMappingId);
    }

    @Override
    public Optional<StandardStepActionConfig> visit(final SequentialProfileScreenConfig sequentialProfileScreenConfig) {

        final var sortedOrderedProfileScreenList = sequentialProfileScreenConfig.getOrderedProfileScreenList()
                .stream()
                .sorted(Comparator.comparing(OrderedProfileScreen::getOrder))
                .toList();

        for (final var orderedProfileScreen : sortedOrderedProfileScreenList) {

            final var standardStepActionConfig = orderedProfileScreen.getProfileScreenConfig()
                    .accept(this);

            if (standardStepActionConfig.isPresent()) {
                return standardStepActionConfig;
            }
        }

        return Optional.empty();
    }
}
