package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import com.phonepe.verified.kaizen.models.configs.action.impl.AndStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.EvaluatedStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.OrStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GetStandardActionConfigForGivenAction implements
        StepActionVisitor<Optional<StandardStepActionConfig>, String> {

    public static final GetStandardActionConfigForGivenAction INSTANCE = new GetStandardActionConfigForGivenAction();

    @Override
    public Optional<StandardStepActionConfig> visit(final StandardStepActionConfig standardStepActionConfig,
                                                    final String actionMappingId) {

        if (standardStepActionConfig.getActionMappingId()
                .equals(actionMappingId)) {

            return Optional.of(standardStepActionConfig);
        }
        return Optional.empty();
    }

    @Override
    public Optional<StandardStepActionConfig> visit(final AndStepActionConfig andStepActionConfig,
                                                    final String actionMappingId) {

        final var leftStepActionConfig = andStepActionConfig.getLeft()
                .accept(this, actionMappingId);

        if (leftStepActionConfig.isPresent()) {
            return leftStepActionConfig;
        }

        return andStepActionConfig.getRight()
                .accept(this, actionMappingId);
    }

    @Override
    public Optional<StandardStepActionConfig> visit(final OrStepActionConfig orStepActionConfig,
                                                    final String actionMappingId) {

        final var leftStepActionConfig = orStepActionConfig.getLeft()
                .accept(this, actionMappingId);

        if (leftStepActionConfig.isPresent()) {
            return leftStepActionConfig;
        }

        return orStepActionConfig.getRight()
                .accept(this, actionMappingId);
    }

    @Override
    public Optional<StandardStepActionConfig> visit(final EvaluatedStepActionConfig evaluatedStepActionConfig,
                                                    final String actionMappingId) {

        // Not checking execution rule here because the task of this visitor is
        // to find action config for a given action mapping id and search for
        // action mapping id doesn't warrant for DB calls and rule evaluations.
        // ===
        // If there is a requirement for running evaluation rule, then new Visitor
        // should be added and this visitor and its usages should npt be modified
        return evaluatedStepActionConfig.getConfig()
                .accept(this, actionMappingId);
    }
}
