package com.phonepe.verified.kaizen.storage.mariadb.repositories;

import com.google.inject.Inject;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStepAudit;
import io.appform.dropwizard.sharding.DBShardingBundleBase;

public class WorkflowStepAuditRepository extends CrudRepository<StoredWorkflowStepAudit> {

    @Inject
    public WorkflowStepAuditRepository(final DBShardingBundleBase<? extends KaizenConfig> dbShardingBundle) {
        super(dbShardingBundle.createRelatedObjectDao(StoredWorkflowStepAudit.class), null);
    }
}
