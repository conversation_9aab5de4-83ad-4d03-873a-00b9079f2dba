package com.phonepe.verified.kaizen.storage.aerospike.data.workflowcontext;

import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.zeus.models.Farm;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowContext {

    private String workflowId;

    private String profileId;

    private String phoneNumber;

    private String email;

    private String entityId;

    private EntityType entityType;

    private TransitionState currentState;

    private TransitionEvent currentEvent;

    private String lastUpdatedBy;

    private UpdaterType updaterType;

    private String actorId; //agentId

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;

    private String tag;

    private Farm callerFarmId;

    //key profileStepMappingId
    @Builder.Default
    private Map<String, WorkflowStepContext> workflowStepMap = new HashMap<>();
}
