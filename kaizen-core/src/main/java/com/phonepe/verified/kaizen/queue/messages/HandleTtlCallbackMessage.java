package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class HandleTtlCallbackMessage extends BaseMessage {

    @NonNull
    private final String actionId;

    @NonNull
    private final UserDetails userDetails;

    @Builder
    @Jacksonized
    public HandleTtlCallbackMessage(final RequestInfo requestInfo,
                                    @NonNull final String actionId,
                                    @NonNull final UserDetails userDetails) {

        super(ActorMessageType.HANDLE_TTL_CALLBACK, requestInfo);
        this.actionId = actionId;
        this.userDetails = userDetails;
    }
}
