package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import com.phonepe.verified.kaizen.models.configs.action.impl.AndStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.EvaluatedStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.OrStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor
public class ActionToTriggerFirstTime implements StepActionVisitor<StandardStepActionConfig, Void> {

    public static final ActionToTriggerFirstTime INSTANCE = new ActionToTriggerFirstTime();

    @Override
    public StandardStepActionConfig visit(final StandardStepActionConfig standardStepActionConfig,
                                          final Void data) {

        return standardStepActionConfig;
    }

    @Override
    public StandardStepActionConfig visit(final AndStepActionConfig andStepActionConfig,
                                          final Void data) {

        return andStepActionConfig.getLeft()
                .accept(ActionToTriggerFirstTime.INSTANCE, null);
    }

    @Override
    public StandardStepActionConfig visit(final OrStepActionConfig orStepActionConfig,
                                          final Void data) {

        return orStepActionConfig.getLeft()
                .accept(ActionToTriggerFirstTime.INSTANCE, null);
    }

    @Override
    public StandardStepActionConfig visit(final EvaluatedStepActionConfig evaluatedStepActionConfig,
                                          final Void data) {

        // This visitor returns leftmost action for a given profile step.
        // Evaluated action cannot be leftmost action because leftmost action should
        // always process the input and create base for next actions.
        return evaluatedStepActionConfig.getConfig()
                .accept(ActionToTriggerFirstTime.INSTANCE, null);
    }
}
