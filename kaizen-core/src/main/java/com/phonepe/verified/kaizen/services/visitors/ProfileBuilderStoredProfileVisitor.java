package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.PrimaryProfile;
import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredAddOnProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredPrimaryProfile;
import com.phonepe.verified.kaizen.storage.mariadb.entities.profile.StoredProfileVisitor;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProfileBuilderStoredProfileVisitor implements StoredProfileVisitor<Profile, Void> {

    public static final ProfileBuilderStoredProfileVisitor INSTANCE = new ProfileBuilderStoredProfileVisitor();


    @Override
    public Profile visit(final StoredPrimaryProfile storedPrimaryProfile,
                         final Void data) {

        return PrimaryProfile.builder()
                .profileId(storedPrimaryProfile.getProfileId())
                .organization(storedPrimaryProfile.getOrganization())
                .namespace(storedPrimaryProfile.getNamespace())
                .type(storedPrimaryProfile.getType())
                .version(storedPrimaryProfile.getVersion())
                .createdAt(storedPrimaryProfile.getCreatedAt())
                .lastUpdatedAt(storedPrimaryProfile.getLastUpdatedAt())
                .workflowTagConfig(storedPrimaryProfile.getWorkflowTagConfig())
                .summaryViewConfig(storedPrimaryProfile.getSummaryViewConfig())
                .getTemplateConfig(storedPrimaryProfile.getGetTemplateConfig())
                .postCompletionActionConfig(storedPrimaryProfile.getPostCompletionActionConfig())
                .postWorkflowCreationActionConfigs(storedPrimaryProfile.getPostWorkflowCreationActionConfigs())
                .build();
    }

    @Override
    public Profile visit(final StoredAddOnProfile storedAddOnProfile,
                         final Void data) {

        return AddOnProfile.builder()
                .profileId(storedAddOnProfile.getProfileId())
                .organization(storedAddOnProfile.getOrganization())
                .namespace(storedAddOnProfile.getNamespace())
                .type(storedAddOnProfile.getType())
                .version(storedAddOnProfile.getVersion())
                .createdAt(storedAddOnProfile.getCreatedAt())
                .lastUpdatedAt(storedAddOnProfile.getLastUpdatedAt())
                .summaryViewConfig(storedAddOnProfile.getSummaryViewConfig())
                .getTemplateConfig(storedAddOnProfile.getGetTemplateConfig())
                .postCompletionActionConfig(storedAddOnProfile.getPostCompletionActionConfig())
                .postWorkflowCreationActionConfigs(storedAddOnProfile.getPostWorkflowCreationActionConfigs())
                .rule(storedAddOnProfile.getRule())
                .priority(storedAddOnProfile.getPriority())
                .addOnType(storedAddOnProfile.getAddOnType())
                .build();
    }
}
