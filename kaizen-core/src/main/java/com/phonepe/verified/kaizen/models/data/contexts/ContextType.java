package com.phonepe.verified.kaizen.models.data.contexts;

import lombok.experimental.UtilityClass;

public enum ContextType {

    EMPTY_TRANSITION_CONTEXT,
    CONSENT_TRANSITION_CONTEXT,
    DOCUMENT_UPLOAD_ACTION_TRANSITION_CONTEXT,
    KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT,
    PERSIST_KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT,
    ;

    @UtilityClass
    public static final class Names {

        public static final String EMPTY_TRANSITION_CONTEXT = "EMPTY_TRANSITION_CONTEXT";
        public static final String CONSENT_TRANSITION_CONTEXT = "CONSENT_TRANSITION_CONTEXT";
        public static final String DOCUMENT_UPLOAD_ACTION_TRANSITION_CONTEXT = "DOCUMENT_UPLOAD_ACTION_TRANSITION_CONTEXT";
        public static final String KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT = "KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT";
        public static final String PERSIST_KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT = "PERSIST_KEY_VALUE_PAIRS_ACTION_TRANSITION_CONTEXT";
    }
}
