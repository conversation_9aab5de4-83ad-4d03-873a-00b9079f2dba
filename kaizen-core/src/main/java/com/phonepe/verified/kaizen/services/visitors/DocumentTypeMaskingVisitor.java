package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.DocumentMaskingActor;
import com.phonepe.verified.kaizen.queue.messages.DocumentMaskingMessage;
import com.phonepe.verified.kaizen.services.visitors.DocumentTypeMaskingVisitor.DocumentTypeMaskingVisitorData;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class DocumentTypeMaskingVisitor extends DocumentTypeBaseVisitor<Boolean, DocumentTypeMaskingVisitorData> {

    private final Provider<DocumentMaskingActor> documentMaskingActorProvider;

    @Override
    @SneakyThrows
    public Boolean visitAadhaar(final DocumentTypeMaskingVisitorData data) {

        if (data.getDocumentTypeIdentifierAndLabel()
                .getDocuments()
                .size() > 2) {

            throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_MASKING_OPERATION,
                    Map.of("numberOfImagesToBeMasked", data.getDocumentTypeIdentifierAndLabel()
                            .getDocuments()
                            .size()));
        }

        documentMaskingActorProvider.get()
                .publish(DocumentMaskingMessage.builder()
                        .actionId(data.getActionId())
                        .documentTypeIdentifierAndLabel(data.getDocumentTypeIdentifierAndLabel())
                        .build());

        return Boolean.TRUE;
    }

    @Data
    @Builder
    public static class DocumentTypeMaskingVisitorData {

        @NotEmpty
        final String actionId;

        @NonNull
        final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel;
    }
}
