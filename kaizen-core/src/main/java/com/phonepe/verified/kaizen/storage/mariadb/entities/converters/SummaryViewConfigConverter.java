package com.phonepe.verified.kaizen.storage.mariadb.entities.converters;

import com.phonepe.verified.kaizen.models.configs.summary.config.SummaryViewConfig;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.util.Objects;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class SummaryViewConfigConverter implements AttributeConverter<SummaryViewConfig, String> {

    @Override
    public String convertToDatabaseColumn(final SummaryViewConfig summaryViewConfig) {
        return Objects.isNull(summaryViewConfig)
               ? null
               : MapperUtils.serializeToString(summaryViewConfig);
    }

    @Override
    public SummaryViewConfig convertToEntityAttribute(final String text) {
        return Objects.isNull(text)
               ? null
               : MapperUtils.deserialize(text, SummaryViewConfig.class);
    }
}
