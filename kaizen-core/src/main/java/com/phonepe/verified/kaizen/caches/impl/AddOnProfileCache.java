package com.phonepe.verified.kaizen.caches.impl;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.caches.Cache;
import com.phonepe.verified.kaizen.caches.CacheName;
import com.phonepe.verified.kaizen.caches.key.AddOnProfileCacheKey;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import com.phonepe.verified.kaizen.models.requests.profiles.AddOnProfile;
import com.phonepe.verified.kaizen.services.impl.ProfileServiceImpl;
import java.util.List;

@Singleton
public class AddOnProfileCache extends Cache<AddOnProfileCacheKey, List<AddOnProfile>> {

    private final Provider<ProfileServiceImpl> profileService;

    @Inject
    public AddOnProfileCache(final CaffeineCacheConfig caffeineCacheConfig,
                             final MetricRegistry metricRegistry,
                             final Provider<ProfileServiceImpl> profileService) {
        super(CacheName.ADD_ON_PROFILE_CACHE, caffeineCacheConfig, metricRegistry);
        this.profileService = profileService;
    }

    @Override
    protected List<AddOnProfile> build(final AddOnProfileCacheKey addOnProfileCacheKey) {
        return profileService.get()
                .getAddOnProfilesFromDb(addOnProfileCacheKey);
    }
}
