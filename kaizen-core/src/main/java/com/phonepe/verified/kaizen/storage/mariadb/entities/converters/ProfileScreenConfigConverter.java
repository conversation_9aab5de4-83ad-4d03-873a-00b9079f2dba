package com.phonepe.verified.kaizen.storage.mariadb.entities.converters;

import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class ProfileScreenConfigConverter implements AttributeConverter<ProfileScreenConfig, String> {

    @Override
    public String convertToDatabaseColumn(final ProfileScreenConfig profileScreenConfig) {
        return MapperUtils.serializeToString(profileScreenConfig);
    }

    @Override
    public ProfileScreenConfig convertToEntityAttribute(final String text) {
        return MapperUtils.deserialize(text, ProfileScreenConfig.class);
    }
}
