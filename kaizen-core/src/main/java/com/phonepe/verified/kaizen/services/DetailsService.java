package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.models.requests.details.EntityDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetail;
import com.phonepe.verified.kaizen.models.requests.details.WorkflowDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.WorkflowStepsDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.workflow.LatestWorkflowDetailsRequest;
import com.phonepe.verified.kaizen.models.responses.details.EntityDetailsResponse;
import com.phonepe.verified.kaizen.models.responses.details.WorkflowDetailsResponse;
import com.phonepe.verified.kaizen.models.responses.details.WorkflowStepsDetailsResponse;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import java.util.List;

public interface DetailsService {

    WorkflowDetailsResponse getDetails(final WorkflowDetailsRequest workflowDetailsRequest);

    EntityDetailsResponse getEntityDetails(final EntityDetailsRequest entityDetailsRequest);

    EntityDetailsResponse getAllEntityDetails(final EntityDetailsRequest entityDetailsRequest);

    List<WorkflowStepsDetailsResponse> getDetails(final WorkflowStepsDetailsRequest workflowStepsDetailsRequest,
                                                  final boolean fetchActionLevelDetails);

    WorkflowDetailsResponse getDetails(final LatestWorkflowDetailsRequest workflowStepsDetailsRequest);

    List<WorkflowDetailsResponse> getWorkflowDetailsResponse(final List<StoredWorkflow> storedWorkflows,
                                                             final List<RequiredDetail> requiredDetails);
}
