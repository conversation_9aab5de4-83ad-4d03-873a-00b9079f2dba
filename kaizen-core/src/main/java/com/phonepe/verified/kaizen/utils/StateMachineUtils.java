package com.phonepe.verified.kaizen.utils;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredStateMachineTransition;
import com.phonepe.verified.kaizen.utils.StateMachineConstants.States;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.experimental.UtilityClass;
import org.apache.commons.math3.util.Pair;

@UtilityClass
public class StateMachineUtils {

    public boolean validateStateMachineTransitions(final List<StoredStateMachineTransition> storedStateMachineTransitions) {

        final var transitionPairs = getTransitionPairs(storedStateMachineTransitions);
        final var allStates = getAllStates(storedStateMachineTransitions);
        final var initialStates = getInitialStates(transitionPairs, allStates);
        final var terminalStates = getTerminalStates(transitionPairs, allStates);

        return initialStates.size() == 1 && !terminalStates.isEmpty();
    }

    public boolean validateInitialState(final List<StoredStateMachineTransition> storedStateMachineTransitions) {
        final var transitionPairs = getTransitionPairs(storedStateMachineTransitions);
        final var allStates = getAllStates(storedStateMachineTransitions);
        final var initialStates = getInitialStates(transitionPairs, allStates);

        return initialStates.size() == 1;
    }

    public boolean validateTerminalStates(final List<StoredStateMachineTransition> storedStateMachineTransitions) {
        final var transitionPairs = getTransitionPairs(storedStateMachineTransitions);
        final var allStates = getAllStates(storedStateMachineTransitions);
        final var terminalStates = getTerminalStates(transitionPairs, allStates);

        return !terminalStates.isEmpty();
    }

    public boolean validatePseudoSuccessState(final List<StoredStateMachineTransition> storedStateMachineTransitions) {

        final var pseudoSuccessTransitions = storedStateMachineTransitions.stream()
                .filter(transition -> !(States.PSEUDO_SUCCESS.equals(transition.getTarget()) && transition.getTarget()
                        .equals(transition.getSource())))
                .filter(transition -> States.PSEUDO_SUCCESS.equals(transition.getTarget()))
                .map(transition -> new Pair<>(transition.getSource(), transition.getTarget()))
                .collect(Collectors.toSet());

        return pseudoSuccessTransitions.isEmpty() || pseudoSuccessTransitions.size() == 1;
    }

    public String getInitialState(final List<StoredStateMachineTransition> storedStateMachineTransitions) {
        final var transitionPairs = getTransitionPairs(storedStateMachineTransitions);
        final var allStates = getAllStates(storedStateMachineTransitions);
        final var initialStates = getInitialStates(transitionPairs, allStates);

        if (initialStates.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.INVALID_STATE_MACHINE_INITIAL_STATE, Map.of());
        }

        return initialStates.iterator()
                .next();
    }

    public Set<String> getTerminalStates(final List<StoredStateMachineTransition> storedStateMachineTransitions) {
        final var transitionPairs = getTransitionPairs(storedStateMachineTransitions);
        final var allStates = getAllStates(storedStateMachineTransitions);
        return getTerminalStates(transitionPairs, allStates);
    }

    public Set<String> getAllStates(final List<StoredStateMachineTransition> storedStateMachineTransitions) {
        return storedStateMachineTransitions.stream()
                .flatMap(storedStateMachineTransition -> Stream.of(storedStateMachineTransition.getSource(),
                        storedStateMachineTransition.getTarget()))
                .collect(Collectors.toSet());
    }

    private List<Pair<String, String>> getTransitionPairs(final List<StoredStateMachineTransition> storedStateMachineTransitions) {

        return storedStateMachineTransitions.stream()
                .map(smt -> new Pair<>(smt.getSource(), smt.getTarget()))
                .toList();
    }

    private Set<String> getInitialStates(final List<Pair<String, String>> transitionPairs,
                                         final Set<String> allStates) {

        final var inDegreeExistsStates = transitionPairs.stream()
                .filter(tp -> !tp.getFirst()
                        .equals(tp.getSecond()))
                .map(Pair::getSecond)
                .collect(Collectors.toSet());

        return allStates.stream()
                .filter(s -> !inDegreeExistsStates.contains(s))
                .collect(Collectors.toSet());

    }

    private Set<String> getTerminalStates(final List<Pair<String, String>> transitionPairs,
                                          final Set<String> allStates) {

        final var outDegreeExistsStates = transitionPairs.stream()
                .filter(tp -> !tp.getFirst()
                        .equals(tp.getSecond()))
                .map(Pair::getFirst)
                .collect(Collectors.toSet());

        return allStates.stream()
                .filter(s -> !outDegreeExistsStates.contains(s))
                .collect(Collectors.toSet());

    }

}
