package com.phonepe.verified.kaizen.services.impl;

import com.phonepe.verified.drishti.models.commons.documents.impl.Base64EncodedDocument;
import com.phonepe.verified.drishti.models.requests.extraction.ExtractionRequest;
import com.phonepe.verified.drishti.models.requests.facematch.FaceMatchRequest;
import com.phonepe.verified.drishti.models.requests.liveness.LivenessCheckRequest;
import com.phonepe.verified.drishti.models.requests.masking.MaskingRequest;
import com.phonepe.verified.drishti.models.responses.DrishtiAsyncResponse;
import com.phonepe.verified.drishti.models.responses.DrishtiResponse;
import com.phonepe.verified.drishti.models.responses.extraction.ExtractionResponse;
import com.phonepe.verified.drishti.models.responses.facematch.FaceMatchResponse;
import com.phonepe.verified.drishti.models.responses.liveness.LivenessCheckResponse;
import com.phonepe.verified.drishti.models.responses.masking.MaskingResponse;
import com.phonepe.verified.kaizen.clients.internal.DrishtiClient;
import com.phonepe.verified.kaizen.models.Document;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.DocumentTypeIdentifierAndLabel;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.DrishtiService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.visitors.DocumentExtractionContextVisitor;
import com.phonepe.verified.kaizen.services.visitors.DrishtiDocumentTypeToMaskingContextVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.IdUtils;
import java.util.List;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@SuppressWarnings({"java:S116", "java:S1170"})
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class DrishtiServiceImpl implements DrishtiService {

    public static final String LIVENESS_CHECK_CALLBACK_URI = "/v1/internal/callback/drishti/livenessCheck/%s";

    public static final String FACE_MATCH_CALLBACK_URI = "/v1/internal/callback/drishti/faceMatch/%s";

    public static final String DOCUMENT_MASKING_CALLBACK_URI = "/v1/internal/callback/drishti/document_masking/%s";

    private final DrishtiClient drishtiClient;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final DocumentService documentService;

    private final String OCR_CALLBACK_URL = "/v1/internal/callback/drishti/ocr/%s";

    @Override
    public DrishtiAsyncResponse livenessCheck(final String documentId,
                                              final StoredAction storedAction) {

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());
        final var clientRequestId = IdUtils.generateId(Constants.PVC_ID_PREFIX);
        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final String selfieBase64EncodedImage = documentService.getBase64EncodedImage(documentId);

        final var selfie = getBase64EncodedDocument(selfieBase64EncodedImage);

        final var livenessCheckRequest = LivenessCheckRequest.builder()
                .clientRequestId(clientRequestId)
                .namespace(profile.getNamespace())
                .organization(profile.getOrganization())
                .selfie(selfie)
                .callbackPath(String.format(LIVENESS_CHECK_CALLBACK_URI, storedAction.getActionId()))
                .callbackServiceName(Constants.APP_NAME)
                .build();

        return drishtiClient.checkLivenesss(livenessCheckRequest);
    }

    @Override
    public DrishtiResponse<LivenessCheckResponse> getLivenessCheckDrishtiResponse(final String requestId) {

        return drishtiClient.fetchLivenessCheckStatus(requestId);
    }

    @Override
    public DrishtiAsyncResponse faceMatch(final String selfieDocumentId,
                                          final String referenceDocumentId,
                                          final StoredAction storedAction) {

        final var selfieDocument = documentService.getBase64EncodedImage(selfieDocumentId);

        final var referenceDocument = documentService.getBase64EncodedImage(referenceDocumentId);

        return faceMatchWithImages(selfieDocument, referenceDocument, storedAction);
    }

    @Override
    public DrishtiAsyncResponse faceMatchWithImages(final String selfieDocument,
                                                    final String referenceDocument,
                                                    final StoredAction storedAction) {

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());

        final var clientRequestId = IdUtils.generateId(Constants.PVC_ID_PREFIX);

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var selfieBase64EncodedDocument = getBase64EncodedDocument(selfieDocument);

        final var referenceBase64EncodedDocument = getBase64EncodedDocument(referenceDocument);

        final var faceMatchRequest = FaceMatchRequest.builder()
                .clientRequestId(clientRequestId)
                .namespace(profile.getNamespace())
                .organization(profile.getOrganization())
                .selfie(selfieBase64EncodedDocument)
                .identityProofReferenceDocument(referenceBase64EncodedDocument)
                .callbackPath(String.format(FACE_MATCH_CALLBACK_URI, storedAction.getActionId()))
                .callbackServiceName(Constants.APP_NAME)
                .build();

        return drishtiClient.matchFace(faceMatchRequest);
    }

    @Override
    public DrishtiResponse<FaceMatchResponse> getFaceMatchDrishtiResponse(final String requestId) {

        return drishtiClient.fetchFaceMatchStatus(requestId);
    }

    @Override
    public DrishtiAsyncResponse maskDocument(final StoredAction storedAction,
                                             final DocumentTypeIdentifierAndLabel documentTypeIdentifierAndLabel) {

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());
        final var clientRequestId = IdUtils.generateId(Constants.PVC_ID_PREFIX);
        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var drishtiDocumentType = drishtiClient.pvDocumentTypeToDrishtiDocumentType(
                documentTypeIdentifierAndLabel.getDocumentType());

        final var maskingContext = drishtiDocumentType.accept(
                new DrishtiDocumentTypeToMaskingContextVisitor(documentService),
                documentTypeIdentifierAndLabel.getDocuments());

        final var maskingRequest = MaskingRequest.builder()
                .clientRequestId(clientRequestId)
                .namespace(profile.getNamespace())
                .maskingContext(maskingContext)
                .organization(profile.getOrganization())
                .callbackServiceName(Constants.PVCORE_FARM_ID_TEXT)
                .callbackPath(String.format(DOCUMENT_MASKING_CALLBACK_URI, storedAction.getActionId()))
                .build();

        return drishtiClient.maskDocument(maskingRequest);
    }

    @Override
    public DrishtiResponse<MaskingResponse> fetchMaskedDocumentStatus(final String requestId) {

        return drishtiClient.fetchMaskingStatus(requestId);
    }

    @Override
    public byte[] downloadDocument(final String documentId) {

        return drishtiClient.downloadDocumentFromDrishti(documentId);
    }

    private Base64EncodedDocument getBase64EncodedDocument(final String unmaskedDocumentBase64EncodedImage) {

        return Base64EncodedDocument.builder()
                .content(unmaskedDocumentBase64EncodedImage)
                .build();
    }

    @Override
    public DrishtiAsyncResponse submitOcrRequest(final DocumentType documentType,
                                                 final List<Document> documentList,
                                                 final StoredAction storedAction) {
        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(
                storedAction.getWorkflowStepId());
        final var clientRequestId = IdUtils.generateId(Constants.PVC_ID_PREFIX);
        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var extractionContext = documentType.accept(DocumentExtractionContextVisitor.INSTANCE, documentList);

        final var ocrRequest = ExtractionRequest.builder()
                .clientRequestId(clientRequestId)
                .namespace(profile.getNamespace())
                .organization(profile.getOrganization())
                .extractionContext(extractionContext)
                .callbackPath(String.format(OCR_CALLBACK_URL, storedAction.getActionId()))
                .callbackServiceName(Constants.PVCORE_FARM_ID_TEXT)
                .build();

        return drishtiClient.submitExtractionRequest(ocrRequest);

    }

    @Override
    public DrishtiResponse<ExtractionResponse> getDocumentOcrResponse(final String requestId) {
        return drishtiClient.fetchDocumentOcrResponse(requestId);
    }
}
