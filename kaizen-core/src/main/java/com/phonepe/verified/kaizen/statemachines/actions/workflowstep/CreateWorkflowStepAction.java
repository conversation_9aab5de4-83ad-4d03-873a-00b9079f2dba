package com.phonepe.verified.kaizen.statemachines.actions.workflowstep;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileStep;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.queue.actors.MoveToInProgressWorkflowStepActor;
import com.phonepe.verified.kaizen.queue.messages.MoveToInProgressWorkflowStepMessage;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.statemachines.actions.BaseTransitionAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import com.phonepe.verified.kaizen.utils.Constants;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class CreateWorkflowStepAction extends BaseTransitionAction<TransitionState, TransitionEvent> {

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final EventIngestionCommand eventIngestionCommand;

    private final WorkflowStepRepository workflowStepRepository;

    private final Provider<WorkflowContextStore> workflowContextStore;

    private final Provider<MoveToInProgressWorkflowStepActor> moveToInProgressWorkflowStepActorProvider;

    @Override
    protected void performTransition(final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var shadowV2SectionSubmitRequest = stateContext.getExtendedState()
                .get(ShadowV2SectionSubmitRequest.class, ShadowV2SectionSubmitRequest.class);

        final var workflowId = stateContext.getExtendedState()
                .get(Fields.workflowId, String.class);

        final var workflowStepId = stateContext.getExtendedState()
                .get(Fields.workflowStepId, String.class);

        final var profileStep = stateContext.getExtendedState()
                .get(ProfileStep.class, ProfileStep.class);

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        final var shouldMoveToInProgress = stateContext.getExtendedState()
                .get(Constants.SHOULD_MOVE_TO_IN_PROGRESS, Boolean.class);

        Objects.requireNonNull(workflowId);
        Objects.requireNonNull(profileStep);
        Objects.requireNonNull(userDetails);
        Objects.requireNonNull(shouldMoveToInProgress);

        final var storedWorkflowStep = BuildUtils.toStoredWorkflowStep(workflowId, profileStep, stateContext,
                userDetails, workflowStepId);

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        workflowStepRepository.save(storedWorkflowStep);

        workflowContextStore.get()
                .addWorkflowStepContext(storedWorkflowStep);

        eventIngestionCommand.workflowStepTransitionEvent(storedWorkflow, storedWorkflowStep, profile, profileStep,
                null, null, EventType.WORKFLOW_STEP_INIT, shadowV2SectionSubmitRequest);

        postTransition(storedWorkflowStep, shadowV2SectionSubmitRequest, shouldMoveToInProgress);
    }

    @SneakyThrows
    protected void postTransition(final StoredWorkflowStep storedWorkflowStep,
                                  final ShadowV2SectionSubmitRequest shadowV2SectionSubmitRequest,
                                  final boolean shouldMoveToInProgress) {

        if (shouldMoveToInProgress) {
            moveToInProgressWorkflowStepActorProvider.get()
                    .publish(MoveToInProgressWorkflowStepMessage.builder()
                            .workflowStepId(storedWorkflowStep.getWorkflowStepId())
                            .shadowV2SectionSubmitRequest(shadowV2SectionSubmitRequest)
                            .userDetails(Constants.PVCORE_SYSTEM_USER)
                            .build());
        }
    }
}
