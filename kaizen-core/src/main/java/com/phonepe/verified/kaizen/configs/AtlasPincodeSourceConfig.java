package com.phonepe.verified.kaizen.configs;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AtlasPincodeSourceConfig {

    @NotEmpty
    private String source;

    @Min(0)
    private long priority;
}
