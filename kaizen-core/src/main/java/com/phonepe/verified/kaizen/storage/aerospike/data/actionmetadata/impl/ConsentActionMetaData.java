package com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.ActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.ActionMetadataVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ConsentActionMetaData extends ActionMetadata {

    private String language;

    @Builder
    public ConsentActionMetaData(final String actionId,
                                 final String language) {
        super(actionId, ActionMetadataType.CONSENT);
        this.language = language;
    }

    public ConsentActionMetaData() {
        super(ActionMetadataType.CONSENT);
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }
}
