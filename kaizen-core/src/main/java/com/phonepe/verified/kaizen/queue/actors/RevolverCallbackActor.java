package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.clients.internal.RevolverClient;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.RevolverCallbackMessage;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class RevolverCallbackActor extends BaseActor<RevolverCallbackMessage> {

    private final RevolverClient revolverClient;

    private final EventIngestionCommand eventIngestionCommand;

    @Inject
    protected RevolverCallbackActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                    final ConnectionRegistry connectionRegistry,
                                    final ObjectMapper mapper,
                                    final RetryStrategyFactory retryStrategyFactory,
                                    final ExceptionHandlingFactory exceptionHandlingFactory,
                                    final RevolverClient revolverClient,
                                    final EventIngestionCommand eventIngestionCommand) {
        super(ActorType.REVOLVER_CALLBACK, actorConfigMap.get(ActorType.REVOLVER_CALLBACK), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, RevolverCallbackMessage.class);
        this.revolverClient = revolverClient;
        this.eventIngestionCommand = eventIngestionCommand;
    }

    @Override
    protected boolean handleMessage(final RevolverCallbackMessage revolverCallbackMessage) {

        log.info("Calling revolver with callback of requestId:{}", revolverCallbackMessage.getRequestId());

        final var result = revolverClient.revolverCallback(revolverCallbackMessage);

        if (result.isEmpty()) {

            log.info("revolver callback failed for requestId:{} ", revolverCallbackMessage.getRequestId());
            eventIngestionCommand.revolverCallbackEvent(revolverCallbackMessage.getRequestId(), false, null);
            throw KaizenException.create(KaizenResponseCode.REVOLVER_CALLBACK_FAILED,
                    Map.of("requestId", revolverCallbackMessage.getRequestId()));

        } else {
            eventIngestionCommand.revolverCallbackEvent(revolverCallbackMessage.getRequestId(), true, null);
            return true;
        }
    }
}
