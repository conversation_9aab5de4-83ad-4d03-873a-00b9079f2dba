package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.EntityType.EntityTypeVisitor;
import com.phonepe.verified.kaizen.models.requests.details.EntityDetailsRequest;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetailVisitor;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.models.responses.details.Detail;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;

@Singleton
@AllArgsConstructor(onConstructor = @__(@Inject))
public class RequiredDetailFallbackVisitorForEntityTypeVisitor implements
        EntityTypeVisitor<RequiredDetailVisitor<Optional<? extends Detail>, EntityDetailsRequest>> {

    private FetchMerchantDetailsFromFallbackVisitor fetchMerchantDetailsFromFallbackVisitor;

    private FetchCustomerDetailsFromFallbackVisitor fetchCustomerDetailsFromFallbackVisitor;

    @Override
    public RequiredDetailVisitor<Optional<? extends Detail>, EntityDetailsRequest> visitMerchant() {
        return fetchMerchantDetailsFromFallbackVisitor;
    }

    @Override
    public RequiredDetailVisitor<Optional<? extends Detail>, EntityDetailsRequest> visitCustomer() {
        return fetchCustomerDetailsFromFallbackVisitor;
    }

    @Override
    public RequiredDetailVisitor<Optional<? extends Detail>, EntityDetailsRequest> visitAgent() {
        throw KaizenException.create(KaizenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }
}
