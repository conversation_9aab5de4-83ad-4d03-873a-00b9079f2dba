package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.WorkflowStepMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.utils.Constants;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Comparator;
import java.util.Map;
import java.util.Set;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class HandleInitialActionWorkflowStepCompletionActor extends BaseActor<WorkflowStepMessage> {

    private final ActionService actionService;

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    @Inject
    protected HandleInitialActionWorkflowStepCompletionActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                                             final ConnectionRegistry connectionRegistry,
                                                             final ObjectMapper mapper,
                                                             final RetryStrategyFactory retryStrategyFactory,
                                                             final ExceptionHandlingFactory exceptionHandlingFactory,
                                                             final ActionService actionService,
                                                             final ProfileService profileService,
                                                             final WorkflowService workflowService,
                                                             final WorkflowStepService workflowStepService) {

        super(ActorType.HANDLE_INITIAL_ACTION_WORKFLOW_STEP_COMPLETION,
                actorConfigMap.get(ActorType.HANDLE_INITIAL_ACTION_WORKFLOW_STEP_COMPLETION), connectionRegistry,
                mapper, retryStrategyFactory, exceptionHandlingFactory, WorkflowStepMessage.class);

        this.actionService = actionService;
        this.profileService = profileService;
        this.workflowService = workflowService;
        this.workflowStepService = workflowStepService;
    }

    @Override
    protected boolean handleMessage(final WorkflowStepMessage message) {

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(message.getWorkflowStepId());

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        if (!Constants.INITIAL_ACTION_STEP.equals(profileStep.getProfileStepMappingId())) {
            throw KaizenException.create(KaizenResponseCode.INITIAL_ACTION_PROFILE_STEP_NOT_FOUND,
                    Map.of(Fields.workflowStepId, message.getWorkflowStepId()));
        }

        final var initialActions = actionService.getActions(Set.of(message.getWorkflowStepId()));

        final var actionFailureErrorCode = initialActions.stream()
                .max(Comparator.comparing(StoredAction::getLastUpdatedAt))
                .map(StoredAction::getFailureErrorCode)
                .orElse(null);

        workflowService.handleInitialActionWorkflowStepCompletion(storedWorkflowStep, actionFailureErrorCode);

        return true;
    }
}
