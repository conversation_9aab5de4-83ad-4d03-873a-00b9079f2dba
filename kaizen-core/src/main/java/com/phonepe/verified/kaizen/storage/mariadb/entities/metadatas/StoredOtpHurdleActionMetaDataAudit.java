package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadataAudit;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Getter
@Setter
@Entity
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.OTP_DETAILS)
public class StoredOtpHurdleActionMetaDataAudit extends StoredActionMetadataAudit {

    private static final long serialVersionUID = 3655619358124757018L;

    @Column(name = "name", columnDefinition = "varchar(128)")
    private String otpReferenceId;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String otp;

    @Builder
    public StoredOtpHurdleActionMetaDataAudit(@NonNull final String actionId,
                                              final int revType,
                                              final LocalDateTime created,
                                              final LocalDateTime updated,
                                              final String otpReferenceId,
                                              final String otp) {
        super(BuildUtils.auditPrimaryKeyForUpdate(), revType, actionId, ActionMetadataType.OTP_DETAILS, created,
                updated);
        this.otpReferenceId = otpReferenceId;
        this.otp = otp;
    }
}
