package com.phonepe.verified.kaizen.statemachines.actions.documentuploadv2;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.contexts.DocumentUploadActionTransitionContext;
import com.phonepe.verified.kaizen.models.data.contexts.TransitionContext;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import com.phonepe.verified.kaizen.queue.actors.ActionExecutorActor;
import com.phonepe.verified.kaizen.queue.actors.EventIngestionActor;
import com.phonepe.verified.kaizen.queue.messages.ActionExecutionMessage;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.DocumentService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.services.visitors.DocumentTypeMaskingVisitor;
import com.phonepe.verified.kaizen.services.visitors.DocumentTypeMaskingVisitor.DocumentTypeMaskingVisitorData;
import com.phonepe.verified.kaizen.services.visitors.GetDependentActionMappingIdDependencyConfigVisitor;
import com.phonepe.verified.kaizen.statemachines.actions.ActionKey;
import com.phonepe.verified.kaizen.statemachines.actions.CreateEntryBaseAction;
import com.phonepe.verified.kaizen.storage.aerospike.commands.ActionMetadataStoreCommand;
import com.phonepe.verified.kaizen.storage.aerospike.data.actionmetadata.impl.DocumentUploadActionMetadata;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ActionMetadataStoreKey;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.utils.Constants;
import com.phonepe.verified.kaizen.utils.StateMachineConstants;
import io.dropwizard.setup.Environment;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.validation.Validator;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@ActionKey(value = "createDocumentUploadActionV2")
public class CreateDocumentUploadActionV2 extends CreateEntryBaseAction {

    private final Validator validator;

    private final DocumentService documentService;

    private final ActionMetadataStoreCommand actionMetadataStoreCommand;

    private final DocumentTypeMaskingVisitor documentTypeMaskingVisitor;

    private final Provider<ActionExecutorActor> actionExecutorActorProvider;

    @Inject
    public CreateDocumentUploadActionV2(final ActionService actionService,
                                        final WorkflowService workflowService,
                                        final ClockworkClient clockworkClient,
                                        final ActionRepository actionRepository,
                                        final WorkflowStepService workflowStepService,
                                        final DataProvider<KaizenConfig> appConfigDataProvider,
                                        final Provider<WorkflowContextStore> workflowContextStore,
                                        final Provider<ActionExecutorActor> actionExecutorActorProvider,
                                        final ActionMetadataStoreCommand actionMetadataStoreCommand,
                                        final Provider<EventIngestionActor> eventIngestionActorProvider,
                                        final DocumentService documentService,
                                        final Environment environment,
                                        final GetDependentActionMappingIdDependencyConfigVisitor getDependentActionMappingIdDependencyConfigVisitor,
                                        final DocumentTypeMaskingVisitor documentTypeMaskingVisitor,
                                        final Provider<ActionExecutorActor> actionExecutorActorProvider1) {
        super(actionService, workflowService, clockworkClient, actionRepository, workflowStepService,
                appConfigDataProvider, workflowContextStore, eventIngestionActorProvider, actionExecutorActorProvider,
                getDependentActionMappingIdDependencyConfigVisitor);
        this.actionMetadataStoreCommand = actionMetadataStoreCommand;
        this.documentService = documentService;
        this.validator = environment.getValidator();
        this.documentTypeMaskingVisitor = documentTypeMaskingVisitor;
        this.actionExecutorActorProvider = actionExecutorActorProvider1;
    }

    @Override
    protected void preTransition(final StateContext<String, String> stateContext) {

        final var documentUploadActionTransitionContext = stateContext.getExtendedState()
                .get(TransitionContext.class, DocumentUploadActionTransitionContext.class);

        Objects.requireNonNull(documentUploadActionTransitionContext);
        validateDocumentContext(documentUploadActionTransitionContext);
        documentService.validateAllDocumentsPresentOnDocStore(documentUploadActionTransitionContext);
    }

    @Override
    @SneakyThrows
    protected void transition(final StoredAction storedAction,
                              final StateContext<String, String> stateContext) {

        final var documentUploadActionTransitionContext = stateContext.getExtendedState()
                .get(TransitionContext.class, DocumentUploadActionTransitionContext.class);

        final var documentActionMetadata = DocumentUploadActionMetadata.builder()
                .documents(documentUploadActionTransitionContext.getDocuments())
                .build();

        actionMetadataStoreCommand.save(ActionMetadataStoreKey.builder()
                .actionId(storedAction.getActionId())
                .build(), documentActionMetadata);
    }

    @Override
    @SneakyThrows
    protected void postTransition(final StoredAction storedAction,
                                  final StateContext<String, String> stateContext) {

        final var actionId = storedAction.getActionId();

        final var documentUploadActionTransitionContext = stateContext.getExtendedState()
                .get(TransitionContext.class, DocumentUploadActionTransitionContext.class);

        final var documentMaskingAppliedList = applyMaskingByDocumentType(actionId,
                documentUploadActionTransitionContext);

        //If masking is not applied for any document, we can directly move to success transition
        if (!documentMaskingAppliedList.contains(Boolean.TRUE)) {
            actionExecutorActorProvider.get()
                    .publish(ActionExecutionMessage.builder()
                            .actionId(storedAction.getActionId())
                            .eventToTrigger(StateMachineConstants.Events.DOCUMENT_UPLOAD_SUCCEEDED)
                            .userDetails(Constants.PVCORE_SYSTEM_USER)
                            .build());
        }
    }

    private void validateDocumentContext(final DocumentUploadActionTransitionContext documentUploadActionTransitionContext) {

        final var constraintViolations = validator.validate(documentUploadActionTransitionContext);

        if (!constraintViolations.isEmpty()) {
            throw KaizenException.create(KaizenResponseCode.INVALID_DOCUMENT_UPLOAD_TRANSITION_CONTEXT,
                    Map.of("constraintViolations", constraintViolations));
        }
    }

    private List<Boolean> applyMaskingByDocumentType(final String actionId,
                                                     final DocumentUploadActionTransitionContext documentUploadActionTransitionContext) {

        return documentUploadActionTransitionContext.getDocuments()
                .stream()
                .map(documentTypeIdentifierAndLabel -> documentTypeIdentifierAndLabel.getDocumentType()
                        .accept(documentTypeMaskingVisitor, DocumentTypeMaskingVisitorData.builder()
                                .actionId(actionId)
                                .documentTypeIdentifierAndLabel(documentTypeIdentifierAndLabel)
                                .build()))
                .filter(Objects::nonNull)
                .toList();
    }
}
