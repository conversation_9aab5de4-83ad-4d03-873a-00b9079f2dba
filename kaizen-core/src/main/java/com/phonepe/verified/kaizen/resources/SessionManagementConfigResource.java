package com.phonepe.verified.kaizen.resources;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.authz.resolvers.override.KaizenAuthZNoOpOverrideAccessResolver;
import com.phonepe.verified.kaizen.models.configs.session.SessionManagementConfig;
import com.phonepe.verified.kaizen.services.AuthZService;
import com.phonepe.verified.kaizen.services.SessionManagementService;
import com.phonepe.verified.kaizen.utils.OperationTypeConstants;
import com.phonepe.verified.kaizen.utils.UserDetailsUtils;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@Path("/v1/session/config")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Session Management Config", description = "Session Management Config related APIs")
@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class SessionManagementConfigResource {

    private static final String RESOURCE_SESSION_MANAGEMENT_CONFIG = "SESSION_MANAGEMENT_CONFIG";

    private static final String OPERATION_SESSION_MANAGEMENT_CONFIG_GET = "GET";
    private static final String OPERATION_SESSION_MANAGEMENT_CONFIG_CREATE = "CREATE";
    private static final String OPERATION_SESSION_MANAGEMENT_CONFIG_UPDATE = "UPDATE";
    private static final String OPERATION_SESSION_MANAGEMENT_CONFIG_ENABLE = "ENABLE";
    private static final String OPERATION_SESSION_MANAGEMENT_CONFIG_DISABLE = "DISABLE";

    private final SessionManagementService sessionManagementService;

    private final AuthZService authZService;

    @GET
    @SneakyThrows
    @Path("/{profileId}/{sourceType}")
    @Operation(summary = "Get a session config")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public SessionManagementConfig getConfig(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                                             @PathParam("profileId") @NotEmpty final String profileId,
                                             @PathParam("sourceType") @NotEmpty final String sourceType) {

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenantFromProfile(profileId),
                RESOURCE_SESSION_MANAGEMENT_CONFIG, OPERATION_SESSION_MANAGEMENT_CONFIG_GET, serviceUserPrincipal);

        return sessionManagementService.getSessionConfig(profileId, sourceType);
    }

    @POST
    @SneakyThrows
    @Operation(summary = "Create a session config")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void createConfig(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                             @Valid @NotNull final SessionManagementConfig sessionManagementConfig) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenantFromProfile(sessionManagementConfig.getProfileId()),
                RESOURCE_SESSION_MANAGEMENT_CONFIG, OPERATION_SESSION_MANAGEMENT_CONFIG_CREATE, serviceUserPrincipal);

        sessionManagementService.createSessionConfig(sessionManagementConfig,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                        .getUserId());
    }


    @PUT
    @SneakyThrows
    @Operation(summary = "Update a session config")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void updateConfig(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                             @Valid @NotNull final SessionManagementConfig sessionManagementConfig) {

        authZService.authorizeOperationForTenantOrDefault(
                authZService.getTenantFromProfile(sessionManagementConfig.getProfileId()),
                RESOURCE_SESSION_MANAGEMENT_CONFIG, OPERATION_SESSION_MANAGEMENT_CONFIG_UPDATE, serviceUserPrincipal);

        sessionManagementService.updateSessionConfig(sessionManagementConfig,
                UserDetailsUtils.getGandalfUserDetails(serviceUserPrincipal)
                        .getUserId());
    }


    @PUT
    @SneakyThrows
    @Path("/{profileId}/{sourceType}/enable")
    @Operation(summary = "Enable a session config")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void enableConfig(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                             @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                             @PathParam("profileId") @NotEmpty final String profileId,
                             @PathParam("sourceType") @NotEmpty final String sourceType) {

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenantFromProfile(profileId),
                RESOURCE_SESSION_MANAGEMENT_CONFIG, OPERATION_SESSION_MANAGEMENT_CONFIG_ENABLE, serviceUserPrincipal);

        sessionManagementService.toggleDisabled(profileId, sourceType, false);
    }

    @PUT
    @SneakyThrows
    @Path("/{profileId}/{sourceType}/disable")
    @Operation(summary = "Disable a session config")
    @ApiKillerMeta(tags = {OperationTypeConstants.WRITE})
    @AccessAllowed(overrideAccessResolver = KaizenAuthZNoOpOverrideAccessResolver.class)
    public void disableConfig(@Parameter(hidden = true) @Auth final ServiceUserPrincipal serviceUserPrincipal,
                              @Parameter(hidden = true) @RequestContext final RequestInfo requestInfo,
                              @PathParam("profileId") @NotEmpty final String profileId,
                              @PathParam("sourceType") @NotEmpty final String sourceType) {

        authZService.authorizeOperationForTenantOrDefault(authZService.getTenantFromProfile(profileId),
                RESOURCE_SESSION_MANAGEMENT_CONFIG, OPERATION_SESSION_MANAGEMENT_CONFIG_DISABLE, serviceUserPrincipal);

        sessionManagementService.toggleDisabled(profileId, sourceType, true);
    }
}
