package com.phonepe.verified.kaizen.statemachines.actions.workflowstep;

import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.foxtrot.events.EventType;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.requests.ShadowV2SectionSubmitRequest;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.statemachines.actions.BaseTransitionAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep.Fields;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
@AllArgsConstructor
public abstract class UpdateWorkflowStepBaseAction extends BaseTransitionAction<TransitionState, TransitionEvent> {

    private final ProfileService profileService;

    private final WorkflowService workflowService;

    private final WorkflowStepService workflowStepService;

    private final EventIngestionCommand eventIngestionCommand;

    private final WorkflowStepRepository workflowStepRepository;

    private final Provider<WorkflowContextStore> workflowContextStore;

    @Override
    protected void performTransition(final StateContext<TransitionState, TransitionEvent> stateContext) {

        final var workflowStepId = stateContext.getExtendedState()
                .get(Fields.workflowStepId, String.class);

        final var userDetails = stateContext.getExtendedState()
                .get(UserDetails.class, UserDetails.class);

        final var shadowV2SectionSubmitRequest = stateContext.getExtendedState()
                .get(ShadowV2SectionSubmitRequest.class, ShadowV2SectionSubmitRequest.class);

        Objects.requireNonNull(workflowStepId);
        Objects.requireNonNull(userDetails);

        final var storedWorkflowStep = workflowStepService.validateAndGetWorkflowStep(workflowStepId);

        final var storedWorkflow = workflowService.validateAndGetWorkflow(storedWorkflowStep.getWorkflowId());

        final var profile = profileService.get(storedWorkflow.getProfileId(), false);

        final var profileStep = profileService.getProfileStep(storedWorkflowStep.getProfileStepId());

        preTransition(storedWorkflowStep, stateContext);

        final var previousState = storedWorkflowStep.getCurrentState();
        final var previousEvent = storedWorkflowStep.getCurrentEvent();

        storedWorkflowStep.setCurrentState(stateContext.getTarget()
                .getId());
        storedWorkflowStep.setCurrentEvent(stateContext.getEvent());
        storedWorkflowStep.setLastUpdatedBy(userDetails.getUserId());
        storedWorkflowStep.setLastUpdaterType(UpdaterType.valueOf(userDetails.getUserType()
                .name()));

        workflowStepRepository.save(storedWorkflowStep, savedWorkflowStep -> {
            transition(storedWorkflowStep, stateContext);
            workflowContextStore.get()
                    .updateWorkflowStepContext(storedWorkflowStep);
            return savedWorkflowStep;
        });

        eventIngestionCommand.workflowStepTransitionEvent(storedWorkflow, storedWorkflowStep, profile, profileStep,
                previousState, previousEvent, getEventType(storedWorkflowStep.getCurrentState()),
                shadowV2SectionSubmitRequest);

        postTransition(previousState, storedWorkflowStep, stateContext);
    }

    private EventType getEventType(final TransitionState currentState) {

        return switch (currentState) {
            case SUCCESS -> EventType.WORKFLOW_STEP_SUCCESS;
            case PSEUDO_SUCCESS -> EventType.WORKFLOW_STEP_PSEUDO_SUCCESS;
            case READY, IN_PROGRESS -> EventType.WORKFLOW_STEP_IN_PROGRESS;
            case FAILURE -> EventType.WORKFLOW_STEP_FAILURE;
            case CREATED -> EventType.WORKFLOW_STEP_INIT;
            case ABORTED -> EventType.WORKFLOW_STEP_ABORT;
            case SKIPPED -> EventType.WORKFLOW_STEP_SKIPPED;
            case AUTO_SKIPPED -> EventType.WORKFLOW_STEP_AUTO_SKIPPED;
            case DISCARDED -> EventType.WORKFLOW_STEP_DISCARD;
            case INVALIDATED -> EventType.WORKFLOW_STEP_INVALIDATED;
            case INITIAL_ACTION_IN_PROGRESS -> EventType.WORKFLOW_INITIAL_ACTION_IN_PROGRESS;
            case PURGED -> EventType.WORKFLOW_STEP_PURGED;
        };
    }

    protected void preTransition(final StoredWorkflowStep storedWorkflowStep,
                                 final StateContext<TransitionState, TransitionEvent> stateContext) {
        // NOOP
    }

    protected void transition(final StoredWorkflowStep storedWorkflowStep,
                              final StateContext<TransitionState, TransitionEvent> stateContext) {
        // NOOP
    }

    protected void postTransition(final TransitionState previousState,
                                  final StoredWorkflowStep storedWorkflow,
                                  final StateContext<TransitionState, TransitionEvent> stateContext) {
        // NOOP
    }

}
