package com.phonepe.verified.kaizen.foxtrot.events;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.foxtrot.events.EventType.Names;
import com.phonepe.verified.kaizen.foxtrot.events.impl.ActionCompletionEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.ActionUpdateEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.CallbackReceivedFromDrishtiEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.CallbackSentToClientEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.GetTemplateEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.KillSwitchEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.OtpGenerationEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.OtpVerificationEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.RevolverCallbackEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.ScheduledWorkflowAbortEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.SectionSubmitEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.WorkflowStepTransitionEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.WorkflowTagCalculationEvent;
import com.phonepe.verified.kaizen.foxtrot.events.impl.WorkflowTransitionEvent;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.utils.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "eventType", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.ACTION_UPDATE, value = ActionUpdateEvent.class),
        @JsonSubTypes.Type(name = Names.GET_TEMPLATE, value = GetTemplateEvent.class),
        @JsonSubTypes.Type(name = Names.VERIFICATION_WORKFLOW_INIT, value = WorkflowTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.KILL_SWITCH, value = KillSwitchEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_ABORT, value = WorkflowTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_FAILURE, value = WorkflowTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_SKIPPED, value = WorkflowTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_PSEUDO_SUCCESS, value = WorkflowTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_SUCCESS, value = WorkflowTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_IN_PROGRESS, value = WorkflowTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_INIT, value = WorkflowStepTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_ABORT, value = WorkflowStepTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_FAILURE, value = WorkflowStepTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_IN_PROGRESS, value = WorkflowStepTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_PSEUDO_SUCCESS, value = WorkflowStepTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_SKIPPED, value = WorkflowStepTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_STEP_SUCCESS, value = WorkflowStepTransitionEvent.class),
        @JsonSubTypes.Type(name = Names.ACTION_INVALIDATED, value = ActionCompletionEvent.class),
        @JsonSubTypes.Type(name = Names.SECTION_SUBMIT_FAILED, value = SectionSubmitEvent.class),
        @JsonSubTypes.Type(name = Names.SECTION_SUBMIT_SUCCEED, value = SectionSubmitEvent.class),
        @JsonSubTypes.Type(name = Names.SECTION_SUBMIT_RECEIVED, value = SectionSubmitEvent.class),
        @JsonSubTypes.Type(name = Names.OTP_VERIFICATION_FAILED, value = OtpVerificationEvent.class),
        @JsonSubTypes.Type(name = Names.OTP_VERIFICATION_SUCCEED, value = OtpVerificationEvent.class),
        @JsonSubTypes.Type(name = Names.OTP_GENERATION_SUCCEED, value = OtpGenerationEvent.class),
        @JsonSubTypes.Type(name = Names.OTP_GENERATION_FAILED, value = OtpGenerationEvent.class),
        @JsonSubTypes.Type(name = Names.CONSENT_ACTION_SUCCEED, value = ActionCompletionEvent.class),
        @JsonSubTypes.Type(name = Names.DOCUMENT_UPLOAD_ACTION_SUCCEED, value = ActionCompletionEvent.class),
        @JsonSubTypes.Type(name = Names.PERSIST_KEY_VALUE_PAIR_ACTION_SUCCEED, value = ActionCompletionEvent.class),
        @JsonSubTypes.Type(name = Names.ABORT_ACTION, value = ActionCompletionEvent.class),
        @JsonSubTypes.Type(name = Names.CALLBACK_SENT_TO_CLIENT, value = CallbackSentToClientEvent.class),
        @JsonSubTypes.Type(name = Names.CALLBACK_RECEIVED_FROM_DRISHTI, value = CallbackReceivedFromDrishtiEvent.class),
        @JsonSubTypes.Type(name = Names.REVOLVER_CALLBACK, value = RevolverCallbackEvent.class),
        @JsonSubTypes.Type(name = Names.ABORT_WORKFLOW_SCHEDULE_CREATED_ON_CLOCKWORK, value = ScheduledWorkflowAbortEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_ABORT_TRIGGERED_ON_SCHEDULED_CALLBACK, value = ScheduledWorkflowAbortEvent.class),
        @JsonSubTypes.Type(name = Names.WORKFLOW_ABORT_NOT_TRIGGERED_ON_SCHEDULED_CALLBACK, value = ScheduledWorkflowAbortEvent.class),
        @JsonSubTypes.Type(name = Names.ACTION_COMPLETION, value = ActionCompletionEvent.class),
        @JsonSubTypes.Type(name = Names.TAG_CALCULATION, value = WorkflowTagCalculationEvent.class)})
public abstract class BaseEvent {

    private ProfileType profileType;

    private String addOnType;

    private String intent;

    private String entityId;

    private String namespace;

    private String workflowId;

    @JsonIgnore
    private String groupingKey;

    private String organization;

    @JsonIgnore
    private EventType eventType;

    private String workflowType;

    private String workflowStepId;

    private EntityType entityType;

    private String workflowVersion;

    private String screenMappingId;

    private String actionMappingId;

    private long componentKitVersion;

    private String profileStepMappingId;

    protected BaseEvent(final EventType eventType,
                        final String intent,
                        final String entityId,
                        final String namespace,
                        final String workflowId,
                        final ProfileType profileType,
                        final String addOnType,
                        final String groupingKey,
                        final String organization,
                        final String workflowType,
                        final String workflowStepId,
                        final EntityType entityType,
                        final String workflowVersion,
                        final String screenMappingId,
                        final String actionMappingId,
                        final long componentKitVersion,
                        final String profileStepMappingId) {
        this.intent = intent;
        this.entityId = entityId;
        this.namespace = namespace;
        this.workflowId = workflowId;
        this.profileType = profileType;
        this.addOnType = addOnType;
        this.groupingKey = groupingKey;
        this.organization = organization;
        this.eventType = eventType;
        this.workflowType = workflowType;
        this.workflowStepId = workflowStepId;
        this.entityType = entityType;
        this.workflowVersion = workflowVersion;
        this.screenMappingId = screenMappingId;
        this.actionMappingId = actionMappingId;
        this.componentKitVersion = componentKitVersion;
        this.profileStepMappingId = profileStepMappingId;
    }

    public String getMesosHostName() {

        return Constants.MESOS_HOSTNAME;
    }

    public String getMesosTaskId() {

        return Constants.MESOS_TASK_ID;
    }

}



