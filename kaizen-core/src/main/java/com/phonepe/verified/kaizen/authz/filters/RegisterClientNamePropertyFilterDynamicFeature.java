package com.phonepe.verified.kaizen.authz.filters;

import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.annotations.AccessesAllowed;
import io.dropwizard.primer.auth.annotation.Authorize;
import java.lang.annotation.Annotation;
import java.util.Objects;
import java.util.Set;
import javax.annotation.security.DenyAll;
import javax.annotation.security.PermitAll;
import javax.annotation.security.RolesAllowed;
import javax.ws.rs.Path;
import javax.ws.rs.container.DynamicFeature;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.FeatureContext;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.server.model.ResourceMethodInvoker;

@Slf4j
public class RegisterClientNamePropertyFilterDynamicFeature implements DynamicFeature {

    private static final Set<Class<? extends Annotation>> ANNOTATIONS_TO_REGISTER_FILTER_ON = Set.of(PermitAll.class,
            DenyAll.class, Authorize.class, RolesAllowed.class, AccessAllowed.class, AccessesAllowed.class);

    @Override
    public void configure(final ResourceInfo resourceInfo,
                          final FeatureContext featureContext) {

        final var isValidAnnotationPresentOnMethod = ANNOTATIONS_TO_REGISTER_FILTER_ON.stream()
                .anyMatch(t -> Objects.nonNull(resourceInfo.getResourceMethod()
                        .getAnnotation(t)));

        if (isValidAnnotationPresentOnMethod) {

            if (resourceInfo instanceof final ResourceMethodInvoker resourceMethodInvoker) {

                final var methodPath = resourceMethodInvoker.getResourceMethod()
                        .getAnnotation(Path.class);

                // Technically this can also be null. Ideally should never be since it means misconfigured resource.
                final var parentPath = resourceMethodInvoker.getResourceMethod()
                        .getDeclaringClass()
                        .getAnnotation(Path.class);

                log.debug("Registered Client Name logging on {}", String.format("%s%s", parentPath.value(),
                        methodPath == null
                        ? ""
                        : methodPath.value()));
            }

            featureContext.register(ClientNamePropertyFilter.class);
        }
    }
}
