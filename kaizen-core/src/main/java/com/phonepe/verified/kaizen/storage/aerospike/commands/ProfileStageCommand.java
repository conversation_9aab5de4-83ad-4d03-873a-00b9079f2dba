package com.phonepe.verified.kaizen.storage.aerospike.commands;

import com.aerospike.client.IAerospikeClient;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import com.phonepe.verified.kaizen.models.data.profile.ProfileStageData;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeCommand;
import com.phonepe.verified.kaizen.storage.aerospike.AerospikeSet;
import com.phonepe.verified.kaizen.storage.aerospike.keys.ProfileStageKey;
import javax.inject.Inject;
import javax.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class ProfileStageCommand extends AerospikeCommand<ProfileStageKey, ProfileStageData> {

    @Inject
    public ProfileStageCommand(IAerospikeClient aerospikeClient,
                               AerospikeConfig aerospikeConfig) {
        super(aerospikeClient, aerospikeConfig, AerospikeSet.PROFILE_STAGE, ProfileStageData.class);
    }
}
