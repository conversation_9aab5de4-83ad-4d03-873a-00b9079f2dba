package com.phonepe.verified.kaizen.services.visitors;

import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import com.phonepe.verified.kaizen.models.configs.action.impl.AndStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.EvaluatedStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.OrStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.StandardStepActionConfigCompletionTimestamp;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor
public class IdentifyNextActionToTriggerActionConfigVisitor implements
        StepActionVisitor<StandardStepActionConfigCompletionTimestamp, LocalDateTime> {

    private final String workflowStepId;

    private final ActionService actionService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowContextStore workflowContextStore;

    @Override
    public StandardStepActionConfigCompletionTimestamp visit(final StandardStepActionConfig standardStepActionConfig,
                                                             final LocalDateTime actionCompletionTimestamp) {

        final var storedAction = actionService.getLatestAction(workflowStepId,
                standardStepActionConfig.getActionMappingId());

        // if storedAction is not present in DB, return the current StepActionConfig
        if (storedAction.isEmpty() || CompletionState.INVALIDATED == storedAction.get()
                .getCompletionState() || (Objects.nonNull(actionCompletionTimestamp) && storedAction.get()
                .getCreatedAt()
                .isBefore(actionCompletionTimestamp))) {

            return StandardStepActionConfigCompletionTimestamp.builder()
                    .actionCompletionTimestamp(null)
                    .standardStepActionConfig(standardStepActionConfig)
                    .completionStateOfAction(CompletionState.NOT_STARTED)
                    .build();
        }

        return StandardStepActionConfigCompletionTimestamp.builder()
                .actionCompletionTimestamp(storedAction.get()
                        .getCreatedAt())
                .standardStepActionConfig(null)
                .completionStateOfAction(storedAction.get()
                        .getCompletionState())
                .build();
    }

    @Override
    public StandardStepActionConfigCompletionTimestamp visit(final AndStepActionConfig andStepActionConfig,
                                                             final LocalDateTime actionCompletionTimestamp) {

        final var leftStandardStepActionConfig = andStepActionConfig.getLeft()
                .accept(this, actionCompletionTimestamp);

        // if leftStandardStepActionConfig is present, return it since we found the action to trigger next
        if (Objects.nonNull(leftStandardStepActionConfig.getStandardStepActionConfig())) {
            return leftStandardStepActionConfig;
        }

        return andStepActionConfig.getRight()
                .accept(this, leftStandardStepActionConfig.getActionCompletionTimestamp());
    }

    @Override
    public StandardStepActionConfigCompletionTimestamp visit(final OrStepActionConfig orStepActionConfig,
                                                             final LocalDateTime actionCompletionTimestamp) {

        final var leftStandardStepActionConfig = orStepActionConfig.getLeft()
                .accept(this, actionCompletionTimestamp);

        // if leftStandardStepActionConfig is present, return it since we found the action to trigger next
        if (Objects.nonNull(leftStandardStepActionConfig.getStandardStepActionConfig())) {
            return leftStandardStepActionConfig;
        }

        if (CompletionState.FAILURE != leftStandardStepActionConfig.getCompletionStateOfAction()) {
            return leftStandardStepActionConfig;
        }

        return orStepActionConfig.getRight()
                .accept(this, leftStandardStepActionConfig.getActionCompletionTimestamp());
    }

    @Override
    public StandardStepActionConfigCompletionTimestamp visit(final EvaluatedStepActionConfig evaluatedStepActionConfig,
                                                             final LocalDateTime actionCompletionTimestamp) {

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(workflowStepId);

        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflow.getWorkflowId());

        final var workflowContextJsonNode = MapperUtils.convertToJsonNode(workflowContext);

        if (hopeLangService.evaluate(evaluatedStepActionConfig.getEvaluationRule(), workflowContextJsonNode)) {

            return evaluatedStepActionConfig.getConfig()
                    .accept(this, actionCompletionTimestamp);
        }

        return StandardStepActionConfigCompletionTimestamp.builder()
                .standardStepActionConfig(null)
                .actionCompletionTimestamp(actionCompletionTimestamp)
                .build();
    }
}
