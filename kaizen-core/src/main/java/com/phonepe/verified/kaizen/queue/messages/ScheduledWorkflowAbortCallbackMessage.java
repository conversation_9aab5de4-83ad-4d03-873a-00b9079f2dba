package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ScheduledWorkflowAbortCallbackMessage extends BaseMessage {

    @NotEmpty
    private final String actionId;

    @NotNull
    private final UserDetails userDetails;

    @Builder
    @Jacksonized
    public ScheduledWorkflowAbortCallbackMessage(final RequestInfo requestInfo,
                                                 @NotEmpty final String actionId,
                                                 @NotNull final UserDetails userDetails) {

        super(ActorMessageType.SCHEDULED_WORKFLOW_ABORT_CALLBACK, requestInfo);
        this.actionId = actionId;
        this.userDetails = userDetails;
    }
}
