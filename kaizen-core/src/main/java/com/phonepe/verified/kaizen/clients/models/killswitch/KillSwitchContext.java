package com.phonepe.verified.kaizen.clients.models.killswitch;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.clients.models.killswitch.impl.WorkflowInitKillSwitchContext;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.EntityType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(name = KillSwitchContextType.WORKFLOW_INIT_TEXT, value = WorkflowInitKillSwitchContext.class)})
public abstract class KillSwitchContext {

    private KillSwitchContextType type;

    private String organization;

    private String namespace;

    private ProfileType profileType;

    private String profileVersion;

    private String workflowType;

    private String workflowId;

    private EntityType entityType;

    private String entityId;

    private String intent;

    private long componentKitVersion;


    public KillSwitchContext(final KillSwitchContextType type,
                             final String organization,
                             final String namespace,
                             final ProfileType profileType,
                             final String profileVersion,
                             final String workflowType,
                             @NonNull final String workflowId,
                             final EntityType entityType,
                             final String entityId,
                             final String intent,
                             final long componentKitVersion) {
        this.type = type;
        this.organization = organization;
        this.namespace = namespace;
        this.profileType = profileType;
        this.profileVersion = profileVersion;
        this.workflowType = workflowType;
        this.workflowId = workflowId;
        this.entityType = entityType;
        this.entityId = entityId;
        this.intent = intent;
        this.componentKitVersion = componentKitVersion;
    }

    protected KillSwitchContext(final KillSwitchContextType type) {
        this.type = type;
    }
}
