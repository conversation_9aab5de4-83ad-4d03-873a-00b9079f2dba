package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.ApiVersion;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SkipWorkflowStepMessage extends BaseMessage {

    @NonNull
    private final String intent;

    @NonNull
    private final String workflowId;

    @NonNull
    private final ApiVersion apiVersion;

    @NonNull
    private final String screenMappingId;

    @NonNull
    private final UserDetails userDetails;

    private final long componentKitVersion;

    @NonNull
    private final String profileStepMappingId;

    @Builder
    @Jacksonized
    public SkipWorkflowStepMessage(@NonNull final RequestInfo requestInfo,
                                   @NonNull final String intent,
                                   @NonNull final String workflowId,
                                   @NonNull final ApiVersion apiVersion,
                                   @NonNull final String screenMappingId,
                                   @NonNull final UserDetails userDetails,
                                   final long componentKitVersion,
                                   @NonNull final String profileStepMappingId) {

        super(ActorMessageType.SKIP_WORKFLOW_STEP, requestInfo);
        this.intent = intent;
        this.workflowId = workflowId;
        this.apiVersion = apiVersion;
        this.screenMappingId = screenMappingId;
        this.userDetails = userDetails;
        this.componentKitVersion = componentKitVersion;
        this.profileStepMappingId = profileStepMappingId;
    }
}
