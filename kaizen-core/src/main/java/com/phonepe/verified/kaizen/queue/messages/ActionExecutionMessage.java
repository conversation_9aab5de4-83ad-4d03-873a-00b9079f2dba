package com.phonepe.verified.kaizen.queue.messages;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.queue.ActorMessageType;
import com.phonepe.verified.kaizen.queue.BaseMessage;
import java.util.Optional;
import javax.annotation.Nullable;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ActionExecutionMessage extends BaseMessage {

    @NonNull
    private final String actionId;

    @NonNull
    private final String eventToTrigger;

    @NonNull
    private final UserDetails userDetails;

    @Nullable
    private final ActionFailureErrorCode actionFailureErrorCode;

    @Builder
    @Jacksonized
    public ActionExecutionMessage(final RequestInfo requestInfo,
                                  @NonNull final String actionId,
                                  @NonNull final String eventToTrigger,
                                  @NonNull final UserDetails userDetails,
                                  @Nullable final ActionFailureErrorCode actionFailureErrorCode) {

        super(ActorMessageType.ACTION_EXECUTION, requestInfo);
        this.actionId = actionId;
        this.eventToTrigger = eventToTrigger;
        this.userDetails = userDetails;
        this.actionFailureErrorCode = actionFailureErrorCode;
    }

    public Optional<ActionFailureErrorCode> getActionFailureErrorCode() {
        return Optional.ofNullable(actionFailureErrorCode);
    }
}
