package com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.Names;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.services.ActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(Names.DOCUMENT_UPLOAD)
public class StoredDocumentUploadActionMetadata extends StoredActionMetadata {

    private static final long serialVersionUID = 9045020083523483463L;

    @Column(name = "reference_id", columnDefinition = "varchar(45)")
    private String requestId;

    @Column(name = "global_reference", columnDefinition = "varchar(45)")
    private String documentId;

    @Column(name = "name", columnDefinition = "varchar(128)")
    @Enumerated(EnumType.STRING)
    private DocumentType documentType;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String documentLabel;

    @Builder
    public StoredDocumentUploadActionMetadata(@NonNull final String actionId,
                                              final LocalDateTime createdAt,
                                              final LocalDateTime lastUpdatedAt,
                                              final String requestId,
                                              final String documentId,
                                              final DocumentType documentType,
                                              final String documentLabel) {
        super(BuildUtils.primaryKey(), actionId, ActionMetadataType.DOCUMENT_UPLOAD, createdAt, lastUpdatedAt);
        this.requestId = requestId;
        this.documentId = documentId;
        this.documentType = documentType;
        this.documentLabel = documentLabel;
    }

    @Override
    public String getActionMetadataContextKey() {
        return String.join(":", Names.DOCUMENT_UPLOAD, this.documentType.name(), this.documentLabel);
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }

}
