package com.phonepe.verified.kaizen.services.visitors;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.shadow.page.field.rules.AndExpression;
import com.phonepe.shadow.page.field.rules.AtleastExpression;
import com.phonepe.shadow.page.field.rules.AtmostExpression;
import com.phonepe.shadow.page.field.rules.ContainsAtLeastExpression;
import com.phonepe.shadow.page.field.rules.EqualsExpression;
import com.phonepe.shadow.page.field.rules.EventEqualsExpression;
import com.phonepe.shadow.page.field.rules.ExpressionVisitor;
import com.phonepe.shadow.page.field.rules.GreaterThanExpression;
import com.phonepe.shadow.page.field.rules.HandleBarsRuleExpression;
import com.phonepe.shadow.page.field.rules.HopeRuleExpression;
import com.phonepe.shadow.page.field.rules.LessThanExpression;
import com.phonepe.shadow.page.field.rules.NotContainsExpression;
import com.phonepe.shadow.page.field.rules.NotEqualsExpression;
import com.phonepe.shadow.page.field.rules.RegexExpression;
import com.phonepe.verified.kaizen.handlebars.HandleBarsService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ExpressionEvaluator implements ExpressionVisitor<Boolean, JsonNode> {

    private final HopeLangService hopeLangService;

    private final HandleBarsService handleBarsService;

    @Override
    public Boolean visit(final EqualsExpression equalsExpression,
                         final JsonNode context) {
        log.warn("EqualsExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final NotEqualsExpression notEqualsExpression,
                         final JsonNode context) {
        log.warn("NotEqualsExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final AndExpression andExpression,
                         final JsonNode context) {
        log.warn("AndExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final LessThanExpression lessThanExpression,
                         final JsonNode context) {
        log.warn("LessThanExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final GreaterThanExpression greaterThanExpression,
                         final JsonNode context) {
        log.warn("GreaterThanExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final AtleastExpression atleastExpression,
                         final JsonNode context) {
        log.warn("AtleastExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final AtmostExpression atmostExpression,
                         final JsonNode context) {
        log.warn("AtmostExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final EventEqualsExpression eventEqualsExpression,
                         final JsonNode context) {
        log.warn("EventEqualsExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final ContainsAtLeastExpression containsAtLeastExpression,
                         final JsonNode context) {
        log.warn("ContainsAtLeastExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final NotContainsExpression notContainsExpression,
                         final JsonNode context) {
        log.warn("NotContainsExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final HopeRuleExpression hopeRuleExpression,
                         final JsonNode context) {
        return hopeLangService.evaluate(hopeRuleExpression.getRule(), context);
    }

    @Override
    public Boolean visit(final RegexExpression regexExpression,
                         final JsonNode jsonNode) {
        log.warn("NotContainsExpression is currently not supported");
        return Boolean.FALSE;
    }

    @Override
    public Boolean visit(final HandleBarsRuleExpression handleBarsRuleExpression,
                         final JsonNode context) {

        final var transformString = handleBarsService.transform(handleBarsRuleExpression.getRule(), context);

        return Boolean.parseBoolean(transformString);
    }
}
