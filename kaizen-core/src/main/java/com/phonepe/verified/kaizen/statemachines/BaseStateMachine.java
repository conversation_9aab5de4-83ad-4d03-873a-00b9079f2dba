package com.phonepe.verified.kaizen.statemachines;

import com.phonepe.verified.kaizen.models.data.keys.StateMachineRegistryKey;
import com.phonepe.verified.kaizen.statemachines.interceptors.ErrorHandlingInterceptor;
import com.phonepe.verified.kaizen.statemachines.interceptors.TransitionLockingInterceptor;
import com.phonepe.verified.kaizen.statemachines.listeners.EventNotAcceptedListener;
import com.phonepe.verified.kaizen.storage.aerospike.commands.TransitionLockCommand;
import io.dropwizard.lifecycle.Managed;
import java.util.Map;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.access.StateMachineAccess;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.statemachine.config.StateMachineBuilder.Builder;
import org.springframework.statemachine.config.builders.StateMachineModelConfigurer;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.statemachine.support.StateMachineInterceptor;

public abstract class BaseStateMachine<S, E> implements Managed {

    protected final Builder<S, E> stateMachineBuilder;

    protected final StateMachineInterceptor<S, E> transitionLockingInterceptor;

    protected final StateMachineInterceptor<S, E> errorHandlingInterceptor;

    protected final StateMachineListener<S, E> eventNotAcceptedListener;

    protected BaseStateMachine(final TransitionLockCommand transitionLockCommand) {
        this.stateMachineBuilder = StateMachineBuilder.builder();
        this.transitionLockingInterceptor = new TransitionLockingInterceptor<>(transitionLockCommand);
        this.errorHandlingInterceptor = new ErrorHandlingInterceptor<>();
        this.eventNotAcceptedListener = new EventNotAcceptedListener<>();
    }

    public StateMachine<S, E> sendEvent(final S currentState,
                                        final E event,
                                        final Map<Object, Object> transitionContext) {

        final var stateMachine = stateMachineBuilder.build();
        stateMachine.addStateListener(eventNotAcceptedListener);

        final var variables = stateMachine.getExtendedState()
                .getVariables();
        variables.putAll(transitionContext);
        variables.put(StateMachineContextKeys.TRIGGER_EVENT, event);

        stateMachine.getStateMachineAccessor()
                .doWithAllRegions(sma -> {
                    sma.resetStateMachine(new DefaultStateMachineContext<>(currentState, null, null, null));
                    sma.addStateMachineInterceptor(transitionLockingInterceptor);
                    sma.addStateMachineInterceptor(errorHandlingInterceptor);
                    configure(sma);
                });
        stateMachine.start();

        stateMachine.sendEvent(event);

        if (stateMachine.hasStateMachineError()) {
            final var throwable = stateMachine.getExtendedState()
                    .get(StateMachineContextKeys.THROWN_EXCEPTION, Throwable.class);
            throw (RuntimeException) throwable;
        }

        return stateMachine;
    }

    public StateMachine<S, E> internalApiGetStateMachine() {
        return stateMachineBuilder.build();
    }

    protected void configure(final StateMachineAccess<S, E> stateMachineAccess) {
        // NOOP
    }

    protected abstract void configure(final StateMachineModelConfigurer<S, E> model);

    public abstract StateMachineRegistryKey getStateMachineRegistryKey();

    @Override
    public void start() {
        configure(stateMachineBuilder.configureModel());
    }

    @Override
    public void stop() {
        // NOOP
    }
}
