package com.phonepe.verified.kaizen.queue.actors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.phonepe.verified.kaizen.queue.BaseActor;
import com.phonepe.verified.kaizen.queue.messages.StepActionMessage;
import com.phonepe.verified.kaizen.services.AutoRetryActionService;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@SuppressWarnings("java:S3516")
@EqualsAndHashCode(callSuper = true)
public class AutoRetryActionActor extends BaseActor<StepActionMessage> {

    private final AutoRetryActionService autoRetryActionService;

    @Inject
    protected AutoRetryActionActor(final Map<ActorType, ActorConfig> actorConfigMap,
                                   final ConnectionRegistry connectionRegistry,
                                   final ObjectMapper mapper,
                                   final RetryStrategyFactory retryStrategyFactory,
                                   final ExceptionHandlingFactory exceptionHandlingFactory,
                                   final AutoRetryActionService autoRetryActionService) {
        super(ActorType.AUTO_RETRY_ACTION, actorConfigMap.get(ActorType.AUTO_RETRY_ACTION), connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, StepActionMessage.class);
        this.autoRetryActionService = autoRetryActionService;
    }

    @Override
    protected boolean handleMessage(final StepActionMessage stepActionMessage) {
        autoRetryActionService.triggerAutoRetryAction(stepActionMessage.getActionId());
        return true;
    }
}
