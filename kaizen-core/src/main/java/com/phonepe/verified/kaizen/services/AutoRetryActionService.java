package com.phonepe.verified.kaizen.services;

import com.phonepe.verified.kaizen.storage.aerospike.data.AutoRetryActionDetails;

public interface AutoRetryActionService {

    boolean checkIfAutoRetryAvailable(final String actionId);

    void triggerAutoRetryAction(final String actionId);

    AutoRetryActionDetails decryptAutoRetryActionDetails(final String autoRetryActionDetails);

    void triggerActionCreation(final AutoRetryActionDetails autoRetryAction);
}
