package com.phonepe.verified.kaizen.hopelangfunctions;

import io.appform.hope.core.Value;
import io.appform.hope.core.functions.FunctionImplementation;
import io.appform.hope.core.functions.HopeFunction;
import io.appform.hope.core.utils.Converters;
import io.appform.hope.core.values.NumericValue;
import io.appform.hope.core.visitors.Evaluator;
import java.time.LocalDate;
import java.time.Period;
import lombok.AllArgsConstructor;

@AllArgsConstructor
@FunctionImplementation("computeAgeFromDob")
public class ComputeAgeFromDob extends HopeFunction<NumericValue> {

    private final Value yearOfBirthVal;
    private final Value monthOfBirthYearVal;
    private final Value dayOfBirthMonthVal;

    @Override
    public NumericValue apply(final Evaluator.EvaluationContext evaluationContext) {

        final var year = Converters.numericValue(evaluationContext, yearOfBirthVal, LocalDate.now()
                .getYear());
        final var month = Converters.numericValue(evaluationContext, monthOfBirthYearVal, LocalDate.now()
                .getMonthValue());
        final var day = Converters.numericValue(evaluationContext, dayOfBirthMonthVal, LocalDate.now()
                .getDayOfMonth());

        final var dob = LocalDate.of(year.intValue(), month.intValue(), day.intValue());

        final var age = Period.between(dob, LocalDate.now())
                .getYears();

        return new NumericValue(age);
    }
}
