package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType.ActionMetadataTypeVisitor;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import java.util.Map;

public abstract class ActionMetadataTypeBaseVisitor<T> implements ActionMetadataTypeVisitor<T> {

    @Override
    public T visitDocumentUpload() {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitKeyValue() {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitConsent() {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitWaitForConditionAction() {
        return throwUnsupportedOperation();
    }

    @Override
    public T visitOtpDetails() {
        return throwUnsupportedOperation();
    }

    private T throwUnsupportedOperation() {
        throw KaizenException.create(<PERSON>zenResponseCode.UNSUPPORTED_OPERATION, Map.of());
    }
}
