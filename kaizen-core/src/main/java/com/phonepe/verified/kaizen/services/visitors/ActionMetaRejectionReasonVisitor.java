package com.phonepe.verified.kaizen.services.visitors;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ActionMetaRejectionReasonVisitor extends ActionMetadataDefaultVisitor<String, String> {

    public static final ActionMetaRejectionReasonVisitor INSTANCE = new ActionMetaRejectionReasonVisitor();

    @Override
    public String defaultOperation(final String defaultValue) {
        return defaultValue;
    }
}
