package com.phonepe.verified.kaizen.services.visitors;

import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import com.phonepe.verified.kaizen.models.configs.action.impl.AndStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.EvaluatedStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.OrStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import com.phonepe.verified.kaizen.models.data.common.ActionCompletionStatus;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.HopeLangService;
import com.phonepe.verified.kaizen.services.WorkflowContextStore;
import com.phonepe.verified.kaizen.services.WorkflowService;
import com.phonepe.verified.kaizen.utils.MapperUtils;
import java.time.LocalDateTime;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ProfileScreenCompletionActionConfigVisitor implements
        StepActionVisitor<ActionCompletionStatus, LocalDateTime> {

    private final String workflowStepId;

    private final ActionService actionService;

    private final WorkflowService workflowService;

    private final HopeLangService hopeLangService;

    private final WorkflowContextStore workflowContextStore;

    @Override
    public @NotNull ActionCompletionStatus visit(final StandardStepActionConfig standardStepActionConfig,
                                                 final LocalDateTime actionCreationTimestamp) {

        final var action = actionService.getLatestAction(workflowStepId, standardStepActionConfig.getActionMappingId());

        if (action.isEmpty() || (Objects.nonNull(actionCreationTimestamp) && action.get()
                .getCreatedAt()
                .isBefore(actionCreationTimestamp))) {

            return ActionCompletionStatus.builder()
                    .completionState(CompletionState.NOT_STARTED)
                    .actionCreationTimestamp(null)
                    .build();
        }

        return ActionCompletionStatus.builder()
                .completionState(action.get()
                        .getCompletionState())
                .actionCreationTimestamp(action.get()
                        .getCreatedAt())
                .build();
    }

    @Override
    public @NotNull ActionCompletionStatus visit(final AndStepActionConfig andStepActionConfig,
                                                 final LocalDateTime actionCreationTimestamp) {

        final var leftActionCompletionStatus = andStepActionConfig.getLeft()
                .accept(this, actionCreationTimestamp);

        // If status is SUCCESS or PSEUDO_SUCCESS, we traverse the right tree
        if (!CompletionState.SUCCESS_STATES.contains(leftActionCompletionStatus.getCompletionState())) {
            return leftActionCompletionStatus;
        }

        final var rightActionCompletionStatus = andStepActionConfig.getRight()
                .accept(this, leftActionCompletionStatus.getActionCreationTimestamp());

        if (leftActionCompletionStatus.getCompletionState() == CompletionState.PSEUDO_SUCCESS
                && CompletionState.SUCCESS_STATES.contains(rightActionCompletionStatus.getCompletionState())) {

            return rightActionCompletionStatus.withCompletionState(CompletionState.PSEUDO_SUCCESS);
        }

        return rightActionCompletionStatus;
    }

    @Override
    public ActionCompletionStatus visit(final OrStepActionConfig orStepActionConfig,
                                        final LocalDateTime actionCreationTimestamp) {

        final var leftActionCompletionStatus = orStepActionConfig.getLeft()
                .accept(this, actionCreationTimestamp);

        // If status is SUCCESS or PSEUDO_SUCCESS, we return leftActionCompletionStatus
        if (CompletionState.FAILURE != leftActionCompletionStatus.getCompletionState()) {
            return leftActionCompletionStatus;
        }

        return orStepActionConfig.getRight()
                .accept(this, leftActionCompletionStatus.getActionCreationTimestamp());
    }

    @Override
    public @NotNull ActionCompletionStatus visit(final EvaluatedStepActionConfig evaluatedStepActionConfig,
                                                 final LocalDateTime actionCreationTimestamp) {

        final var storedWorkflow = workflowService.validateAndGetStoredWorkflowFromWorkflowStepId(workflowStepId);

        final var workflowContext = workflowContextStore.getWorkflowContext(storedWorkflow.getWorkflowId());

        final var workflowContextJsonNode = MapperUtils.convertToJsonNode(workflowContext);

        if (hopeLangService.evaluate(evaluatedStepActionConfig.getEvaluationRule(), workflowContextJsonNode)) {

            return evaluatedStepActionConfig.getConfig()
                    .accept(this, actionCreationTimestamp);
        }

        return ActionCompletionStatus.builder()
                .actionCreationTimestamp(actionCreationTimestamp)
                .completionState(CompletionState.SUCCESS)
                .build();
    }
}
