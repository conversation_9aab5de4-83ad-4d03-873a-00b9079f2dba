package com.phonepe.verified.kaizen.handlebars;

import com.github.jknack.handlebars.Context;
import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.JsonNodeValueResolver;
import com.github.jknack.handlebars.Template;
import com.github.jknack.handlebars.ValueResolver;
import com.github.jknack.handlebars.context.FieldValueResolver;
import com.github.jknack.handlebars.context.JavaBeanValueResolver;
import com.github.jknack.handlebars.context.MapValueResolver;
import com.github.jknack.handlebars.context.MethodValueResolver;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.verified.kaizen.exceptions.KaizenException;
import com.phonepe.verified.kaizen.models.responses.KaizenResponseCode;
import io.dropwizard.util.Strings;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import javax.annotation.Nullable;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class HandleBarsService {

    private static final ValueResolver[] NOTIFY_VALUE_RESOLVERS = {JavaBeanValueResolver.INSTANCE,
            FieldValueResolver.INSTANCE, MapValueResolver.INSTANCE, MethodValueResolver.INSTANCE,
            JsonNodeValueResolver.INSTANCE};

    private final Handlebars handlebars;

    private final ConcurrentMap<String, Template> compiledTemplates;

    @Inject
    public HandleBarsService(final HandleBarsHelperRegistry handleBarsHelperRegistry) {

        handlebars = handleBarsHelperRegistry.getHandlebars();
        compiledTemplates = new ConcurrentHashMap<>();
    }

    @Nullable
    public String transform(final String template,
                            final Object data) {

        try {
            if (Strings.isNullOrEmpty(template)) {
                return null;
            }

            final var compiledTemplate = compiledTemplates.computeIfAbsent(template, this::getCompiledTemplate);

            return compiledTemplate.apply(Context.newBuilder(data)
                    .resolver(NOTIFY_VALUE_RESOLVERS)
                    .build());

        } catch (final Exception e) {

            throw KaizenException.propagate(KaizenResponseCode.TRANSFORMATION_ERROR, Objects.nonNull(e.getCause())
                                                                                     ? e.getCause()
                                                                                     : e);
        }
    }

    @SneakyThrows
    private Template getCompiledTemplate(final String template) {
        return handlebars.compileInline(template);
    }
}