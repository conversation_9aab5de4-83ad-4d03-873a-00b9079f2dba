package com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas;

import com.phonepe.stratos.kaizen.models.data.ActionStatus;
import com.phonepe.verified.kaizen.models.data.ActionMetadataType;
import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.services.ActionMetadataVisitor;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.utils.BuildUtils;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(ActionMetadataType.Names.DOCUMENT_UPLOAD_WITH_METADATA)
public class StoredDocumentUploadWithMetaDataActionMetadata extends StoredActionMetadata {

    private static final long serialVersionUID = 9045020083523483463L;
    @Column(name = "reference_id", columnDefinition = "varchar(45)")
    private String requestId;

    @Column(name = "global_reference", columnDefinition = "varchar(45)")
    private String documentId;

    @Column(name = "name", columnDefinition = "varchar(128)")
    @Enumerated(EnumType.STRING)
    private DocumentType documentType;

    @Column(name = "value", columnDefinition = "varchar(128)")
    private String documentLabel;

    @Column(name = "status", columnDefinition = "varchar(128)")
    @Enumerated(EnumType.STRING)
    private ActionStatus status;

    @Lob
    @Column(name = "metadata")
    private byte[] metadata;

    @Builder
    public StoredDocumentUploadWithMetaDataActionMetadata(@NonNull final String actionId,
                                                          final LocalDateTime createdAt,
                                                          final LocalDateTime lastUpdatedAt,
                                                          final String requestId,
                                                          final String documentId,
                                                          final DocumentType documentType,
                                                          final String documentLabel,
                                                          final ActionStatus status,
                                                          final byte[] metadata) {
        super(BuildUtils.primaryKey(), actionId, ActionMetadataType.DOCUMENT_UPLOAD_WITH_METADATA, createdAt, lastUpdatedAt);
        this.requestId = requestId;
        this.documentId = documentId;
        this.documentType = documentType;
        this.documentLabel = documentLabel;
        this.status = status;
        this.metadata = metadata;
    }

    @Override
    public String getActionMetadataContextKey() {
        return String.join(":", ActionMetadataType.Names.DOCUMENT_UPLOAD_WITH_METADATA, this.documentType.name(), this.documentLabel);
    }

    @Override
    public <T, J> T accept(final ActionMetadataVisitor<T, J> actionMetadataVisitor,
                           final J data) {
        return actionMetadataVisitor.visit(this, data);
    }

}
