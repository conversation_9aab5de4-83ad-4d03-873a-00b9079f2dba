--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `profile_step_audit`
    (
        `id`                      BIGINT(20)   NOT NULL,
        `REV`                     INT(11)      NOT NULL,
        `RE<PERSON>TYPE`                 TINYINT(4)  DEFAULT NULL,
        `profile_id`              VARCHAR(64)  NOT NULL,
        `profile_step_id`         VARCHAR(64)  NOT NULL,
        `profile_step_mapping_id` VARCHAR(128) NOT NULL,
        `title`                   VARCHAR(128) NOT NULL,
        `execution_rule`          TEXT         NOT NULL,
        `screen_config`           MEDIUMTEXT   NOT NULL,
        `created_at`              DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
        `last_updated_at`         DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        `last_updated_by`         VARCHAR(128) NOT NULL,
        PRIMARY KEY (`id`, `REV`),
        UNIQUE KEY `unq_idx_profile_step_id_REV` (`profile_step_id`, `REV`),
        <PERSON><PERSON><PERSON> `profile_id_idx` (`profile_id`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4;


