--liquibase formatted sql
--changeset lalith.nizampuram:alter for post_workflow_creation_action_config

CREATE TABLE `profile_audit`
(
    `id`                            bigint(20) NOT NULL,
    `REV`                           int(11) NOT NULL,
    `REVTYPE`                       tinyint(4) DEFAULT NULL,
    `profile_id`                    varchar(64)  NOT NULL,
    `organization`                  varchar(128) NOT NULL,
    `namespace`                     varchar(128) NOT NULL,
    `type`                          varchar(128) NOT NULL,
    `version`                       varchar(32)  NOT NULL,
    `workflow_tag_config`           text         DEFAULT NULL,
    `created_at`                    datetime(3) DEFAULT current_timestamp (3),
    `last_updated_at`               datetime(3) DEFAULT current_timestamp (3) ON UPDATE current_timestamp (3),
    `last_updated_by`               varchar(128) NOT NULL,
    `get_template_config`           text         DEFAULT NULL,
    `summary_view_config`           text         DEFAULT NULL,
    `profile_type`                  tinyint(3) unsigned DEFAULT 0,
    `upgrade_type`                  text         DEFAULT NULL,
    `post_completion_action_config` text         DEFAULT NULL,
    `rule`                          text         DEFAULT NULL,
    `priority`                      tinyint(3) unsigned DEFAULT NULL,
    `spec_id`                       varchar(128) DEFAULT NULL,
    `approved_by`                   varchar(32)  DEFAULT NULL,
    PRIMARY KEY (`id`, `REV`),
    UNIQUE KEY `unq_idx_profile_id_REV` (`profile_id`,`REV`),
    KEY                             `profile_key_idx` (`organization`,`namespace`,`type`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE profile_audit
    ADD COLUMN IF NOT EXISTS
    post_workflow_creation_action_config TEXT;