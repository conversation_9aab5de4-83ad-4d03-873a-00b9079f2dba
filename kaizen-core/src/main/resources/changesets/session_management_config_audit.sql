--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `session_management_config_audit`
    (
        `id`                BIGINT(20)   NOT NULL,
        `REV`               INT(11)      NOT NULL,
        `REVTYPE`           TINYINT(4)            DEFAULT NULL,
        `profile_id`        VARCHAR(64)  NOT NULL,
        `type`              VARCHAR(128) NOT NULL,
        `principal_builder` VARCHAR(512) NOT NULL,
        `disabled`          BIT(1)       NOT NULL,
        `validate_timer`    BIT(1)       NOT NULL,
        `validate_token`    BIT(1)       NOT NULL,
        `created_at`        DATETIME(3)           DEFAULT CURRENT_TIMESTAMP(3),
        `last_updated_at`   DATETIME(3)           DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        `last_updated_by`   VARCHAR(128) NOT NULL,
        `validate_data`     BIT(1)       NOT NULL DEFAULT b'0',
        `data_builder`      VARCHAR(512)          DEFAULT NULL,
        `source_type`       VARCHAR(128)          DEFAULT NULL,
        PRIMARY KEY (`id`, `REV`),
        UNIQUE KEY `profile_id_source_type_REV_idx` (`profile_id`, `source_type`, `REV`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4;