--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `template_v2`
    (
        `id`                        BIGINT(20) NOT NULL AUTO_INCREMENT,
        `workflow_type`             VARCHAR(255) DEFAULT NULL,
        `provider_id`               VARCHAR(255) DEFAULT NULL,
        `component_kit_version`     BIGINT(20)   DEFAULT NULL,
        `template_id`               VARCHAR(255) DEFAULT NULL,
        `template_version`          BIGINT(20)   DEFAULT NULL,
        `template_section_mappings` MEDIUMTEXT   DEFAULT NULL,
        `active`                    BIT(1)       DEFAULT NULL,
        `created`                   DATETIME(3)  DEFAULT CURRENT_TIMESTAMP(3),
        `updated`                   DATETIME(3)  DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        PRIMARY KEY (`id`),
        UNIQUE KEY `unq_idx_template_id` (`template_id`),
        KEY `idx_provider_id_workflow_type` (`provider_id`, `workflow_type`),
        <PERSON><PERSON>Y `get_template_idx` (`workflow_type`, `provider_id`, `component_kit_version`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4 COMMENT ='Select * from template_v2 where workflow_type=“PHONEPE_PRIVATE_LIMITED:CREDIT_CARD_DISTRIBUTION:AXIS:V1.0.0”';