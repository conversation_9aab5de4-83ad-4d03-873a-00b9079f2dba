--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `state_machine_transition_audit`
    (
        `id`              BIGINT(20)   NOT NULL,
        `REV`             INT(11)      NOT NULL,
        `<PERSON><PERSON><PERSON><PERSON>P<PERSON>`         TINYINT(4)  DEFAULT NULL,
        `action_type`     VARCHAR(128) NOT NULL,
        `version`         VARCHAR(64)  NOT NULL,
        `source`          VARCHAR(128) NOT NULL,
        `target`          VARCHAR(128) NOT NULL,
        `event`           VARCHAR(128) NOT NULL,
        `action_key`      VARCHAR(128) NOT NULL,
        `created_at`      DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
        `last_updated_at` DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        `last_updated_by` VARCHAR(128) NOT NULL,
        PRIMARY KEY (`id`, `REV`),
        KEY `action_type_version_idx` (`action_type`, `version`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4;