--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `template_v2_audit`
    (
        `id`                        BIGINT(20) NOT NULL,
        `REV`                       INT(11)    NOT NULL,
        `<PERSON><PERSON><PERSON>TYPE`                   TINYINT(4)   DEFAULT NULL,
        `workflow_type`             VARCHAR(255) DEFAULT NULL,
        `provider_id`               VARCHAR(255) DEFAULT NULL,
        `component_kit_version`     BIGINT(20)   DEFAULT NULL,
        `template_id`               VARCHAR(255) DEFAULT NULL,
        `template_version`          BIGINT(20)   DEFAULT NULL,
        `template_section_mappings` MEDIUMTEXT   DEFAULT NULL,
        `active`                    BIT(1)       DEFAULT NULL,
        `created`                   DATETIME(3)  DEFAULT CURRENT_TIMESTAMP(3),
        `updated`                   DATETIME(3)  DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        <PERSON><PERSON>ARY KEY (`id`, `REV`),
        UNIQUE KEY `unq_idx_template_id` (`template_id`, `REV`),
        KEY `idx_provider_id_workflow_type` (`provider_id`, `workflow_type`),
        KEY `get_template_idx` (`workflow_type`, `provider_id`, `component_kit_version`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4;