--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `fra_transient_context`
    (
        `id`              BIGINT(20)   NOT NULL AUTO_INCREMENT,
        `partition_id`    BIGINT(20)   NOT NULL,
        `action_id`       VARCHAR(64)  NOT NULL,
        `context`         TEXT         DEFAULT NULL,
        `document_type`   VARCHAR(128) DEFAULT NULL,
        `created_at`      DATETIME(3)  DEFAULT CURRENT_TIMESTAMP(3),
        `last_updated_at` DATETIME(3)  DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        `last_updated_by` VARCHAR(128) NOT NULL,
        PRIMARY KEY (`id`, `partition_id`),
        UNIQUE KEY `idx_unq_action_id_document_type` (`action_id`, `document_type`, `partition_id`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4
    PARTITION BY RANGE (`partition_id`)
        (PARTITION `p202210` VALUES LESS THAN (202211) ENGINE = InnoDB,
        PARTITION `p202211` VALUES LESS THAN (202212) ENGINE = InnoDB,
        PARTITION `p202212` VALUES LESS THAN (202301) ENGINE = InnoDB,
        PARTITION `p202301` VALUES LESS THAN (202302) ENGINE = InnoDB,
        PARTITION `p202302` VALUES LESS THAN (202303) ENGINE = InnoDB,
        PARTITION `p202303` VALUES LESS THAN (202304) ENGINE = InnoDB,
        PARTITION `p202304` VALUES LESS THAN (202305) ENGINE = InnoDB,
        PARTITION `p202305` VALUES LESS THAN (202306) ENGINE = InnoDB,
        PARTITION `p202306` VALUES LESS THAN (202307) ENGINE = InnoDB,
        PARTITION `p202307` VALUES LESS THAN (202308) ENGINE = InnoDB,
        PARTITION `p202308` VALUES LESS THAN (202309) ENGINE = InnoDB,
        PARTITION `p202309` VALUES LESS THAN (202310) ENGINE = InnoDB,
        PARTITION `p202310` VALUES LESS THAN (202311) ENGINE = InnoDB,
        PARTITION `p202311` VALUES LESS THAN (202312) ENGINE = InnoDB,
        PARTITION `p202312` VALUES LESS THAN (202401) ENGINE = InnoDB,
        PARTITION `p202401` VALUES LESS THAN (202402) ENGINE = InnoDB,
        PARTITION `p202402` VALUES LESS THAN (202403) ENGINE = InnoDB,
        PARTITION `p202403` VALUES LESS THAN (202404) ENGINE = InnoDB,
        PARTITION `p202404` VALUES LESS THAN (202405) ENGINE = InnoDB,
        PARTITION `p202405` VALUES LESS THAN (202406) ENGINE = InnoDB,
        PARTITION `p202406` VALUES LESS THAN (202407) ENGINE = InnoDB,
        PARTITION `p202407` VALUES LESS THAN (202408) ENGINE = InnoDB,
        PARTITION `p202408` VALUES LESS THAN (202409) ENGINE = InnoDB,
        PARTITION `p202409` VALUES LESS THAN (202410) ENGINE = InnoDB,
        PARTITION `p202410` VALUES LESS THAN (202411) ENGINE = InnoDB,
        PARTITION `p202411` VALUES LESS THAN (202412) ENGINE = InnoDB,
        PARTITION `p202412` VALUES LESS THAN (202501) ENGINE = InnoDB,
        PARTITION `p202501` VALUES LESS THAN (202502) ENGINE = InnoDB,
        PARTITION `p202502` VALUES LESS THAN (202503) ENGINE = InnoDB,
        PARTITION `p202503` VALUES LESS THAN (202504) ENGINE = InnoDB,
        PARTITION `p202504` VALUES LESS THAN (202505) ENGINE = InnoDB,
        PARTITION `p202505` VALUES LESS THAN (202506) ENGINE = InnoDB,
        PARTITION `p202506` VALUES LESS THAN (202507) ENGINE = InnoDB,
        PARTITION `p202507` VALUES LESS THAN (202508) ENGINE = InnoDB,
        PARTITION `p202508` VALUES LESS THAN (202509) ENGINE = InnoDB,
        PARTITION `p202509` VALUES LESS THAN (202510) ENGINE = InnoDB,
        PARTITION `p202510` VALUES LESS THAN (202511) ENGINE = InnoDB,
        PARTITION `p202511` VALUES LESS THAN (202512) ENGINE = InnoDB,
        PARTITION `p202512` VALUES LESS THAN (202601) ENGINE = InnoDB,
        PARTITION `p202601` VALUES LESS THAN (202602) ENGINE = InnoDB,
        PARTITION `p202602` VALUES LESS THAN (202603) ENGINE = InnoDB,
        PARTITION `p202603` VALUES LESS THAN (202604) ENGINE = InnoDB,
        PARTITION `p202604` VALUES LESS THAN (202605) ENGINE = InnoDB,
        PARTITION `p202605` VALUES LESS THAN (202606) ENGINE = InnoDB,
        PARTITION `p202606` VALUES LESS THAN (202607) ENGINE = InnoDB,
        PARTITION `p202607` VALUES LESS THAN (202608) ENGINE = InnoDB,
        PARTITION `p202608` VALUES LESS THAN (202609) ENGINE = InnoDB,
        PARTITION `p202609` VALUES LESS THAN (202610) ENGINE = InnoDB,
        PARTITION `p202610` VALUES LESS THAN (202611) ENGINE = InnoDB,
        PARTITION `p202611` VALUES LESS THAN (202612) ENGINE = InnoDB,
        PARTITION `p202612` VALUES LESS THAN (202701) ENGINE = InnoDB);