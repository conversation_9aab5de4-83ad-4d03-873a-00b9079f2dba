--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `session_management_config`
    (
        `id`                BIGINT(20)   NOT NULL AUTO_INCREMENT,
        `profile_id`        VARCHAR(64)  NOT NULL,
        `type`              VARCHAR(128) NOT NULL,
        `principal_builder` VARCHAR(512) NOT NULL,
        `data_builder`      VARCHAR(512) NOT NULL,
        `disabled`          BIT(1)       NOT NULL,
        `validate_timer`    BIT(1)       NOT NULL,
        `validate_token`    BIT(1)       NOT NULL,
        `validate_data`     BIT(1)       NOT NULL,
        `created_at`        DATETIME(3)  DEFAULT CURRENT_TIMESTAMP(3),
        `last_updated_at`   DATETIME(3)  DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        `last_updated_by`   VARCHAR(128) NOT NULL,
        `source_type`       VARCHAR(128) DEFAULT NULL,
        PRIMARY KEY (`id`),
        <PERSON>IQ<PERSON> KEY `profile_id_source_type_idx` (`profile_id`, `source_type`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4;