--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `client_callback_config`
    (
        `id`               BIGINT(20)   NOT NULL AUTO_INCREMENT,
        `profile_id`       VARCHAR(64)  NOT NULL,
        `callback_service` VARCHAR(128) NOT NULL,
        `callback_url`     VARCHAR(256) NOT NULL,
        `created_at`       DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
        `last_updated_at`  DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        `last_updated_by`  VARCHAR(128) NOT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unq_idx_profile_id` (`profile_id`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4;