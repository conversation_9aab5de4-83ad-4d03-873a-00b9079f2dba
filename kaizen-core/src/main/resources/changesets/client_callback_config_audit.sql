--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `client_callback_config_audit`
    (
        `id`               BIGINT(20)   NOT NULL,
        `REV`              INT(11)      NOT NULL,
        `REVTYPE`          TINYINT(4)  DEFAULT NULL,
        `profile_id`       VARCHAR(64)  NOT NULL,
        `callback_service` VARCHAR(128) NOT NULL,
        `callback_url`     VARCHAR(256) NOT NULL,
        `created_at`       DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
        `last_updated_at`  DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        `last_updated_by`  VARCHAR(128) NOT NULL,
        PRIMARY KEY (`id`, `REV`),
        UNIQUE KEY `unq_idx_profile_id_REV` (`profile_id`, `REV`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4;