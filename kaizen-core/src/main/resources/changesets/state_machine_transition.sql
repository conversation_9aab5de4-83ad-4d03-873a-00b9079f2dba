--liquibase formatted SQL
--changeset suhail.shabaz:"reformatting and create tables only if absent"

CREATE TABLE IF NOT EXISTS `state_machine_transition`
    (
        `id`              BIGINT(20)   NOT NULL AUTO_INCREMENT,
        `action_type`     VARCHAR(128) NOT NULL,
        `version`         VARCHAR(64)  NOT NULL,
        `source`          VARCHAR(128) NOT NULL,
        `target`          VARCHAR(128) NOT NULL,
        `event`           VARCHAR(128) NOT NULL,
        `action_key`      VARCHAR(128) NOT NULL,
        `created_at`      DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
        `last_updated_at` DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
        `last_updated_by` VARCHAR(128) NOT NULL,
        PRIMARY KEY (`id`),
        KEY `action_type_version_idx` (`action_type`, `version`)
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8mb4;