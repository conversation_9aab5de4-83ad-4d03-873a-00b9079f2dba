<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog liquibase_dbchangelog-3.8.xsd">

    <include file="changesets/action.sql" relativeToChangelogFile="true"/>
    <include file="changesets/action_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/action_detail.sql" relativeToChangelogFile="true"/>
    <include file="changesets/action_detail_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/client_callback_config.sql" relativeToChangelogFile="true"/>
    <include file="changesets/client_callback_config_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/fra_transient_context.sql" relativeToChangelogFile="true"/>
    <include file="changesets/identifier_hash.sql" relativeToChangelogFile="true"/>
    <include file="changesets/identifier_hash_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/profile.sql" relativeToChangelogFile="true"/>
    <include file="changesets/profile_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/profile_step.sql" relativeToChangelogFile="true"/>
    <include file="changesets/profile_step_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/revinfo.sql" relativeToChangelogFile="true"/>
    <include file="changesets/session_management_config.sql" relativeToChangelogFile="true"/>
    <include file="changesets/session_management_config_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/state_machine_transition.sql" relativeToChangelogFile="true"/>
    <include file="changesets/state_machine_transition_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/template_v2.sql" relativeToChangelogFile="true"/>
    <include file="changesets/template_v2_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/workflow.sql" relativeToChangelogFile="true"/>
    <include file="changesets/workflow_audit.sql" relativeToChangelogFile="true"/>
    <include file="changesets/workflow_step.sql" relativeToChangelogFile="true"/>
    <include file="changesets/workflow_step_audit.sql" relativeToChangelogFile="true"/>

</databaseChangeLog>