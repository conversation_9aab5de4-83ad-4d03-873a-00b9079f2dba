<?xml version="1.0" encoding="UTF-8"?>

<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://www.liquibase.org/xml/ns/dbchangelog"
            xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
            elementFormDefault="qualified">

    <xsd:simpleType name="propertyExpression" id="propertyExpression">
        <xsd:restriction base="xsd:string">
            <xsd:pattern value="$\{[\w\.]+\}"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="booleanExp" id="booleanExp">
        <xsd:annotation>
            <xsd:appinfo>
                <xsd:documentation>Extension to standard XSD boolean type to allow ${} parameters
                </xsd:documentation>
            </xsd:appinfo>
        </xsd:annotation>
        <xsd:union>
            <xsd:simpleType>
                <xsd:restriction base="xsd:boolean"/>
            </xsd:simpleType>
            <xsd:simpleType>
                <xsd:restriction base="propertyExpression"/>
            </xsd:simpleType>
        </xsd:union>
    </xsd:simpleType>

    <xsd:simpleType name="integerExp" id="integerExp">
        <xsd:annotation>
            <xsd:appinfo>
                <xsd:documentation>Extension to standard XSD integer type to allow ${} parameters
                </xsd:documentation>
            </xsd:appinfo>
        </xsd:annotation>
        <xsd:union>
            <xsd:simpleType>
                <xsd:restriction base="xsd:integer"/>
            </xsd:simpleType>
            <xsd:simpleType>
                <xsd:restriction base="propertyExpression"/>
            </xsd:simpleType>
        </xsd:union>
    </xsd:simpleType>

    <xsd:simpleType name="onChangeSetPreconditionErrorOrFail">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="HALT"/>
            <xsd:enumeration value="WARN"/>
            <xsd:enumeration value="CONTINUE"/>
            <xsd:enumeration value="MARK_RAN"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="onChangeLogPreconditionErrorOrFail">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="HALT"/>
            <xsd:enumeration value="WARN"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="onChangeLogPreconditionOnSqlOutput">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="TEST"/>
            <xsd:enumeration value="FAIL"/>
            <xsd:enumeration value="IGNORE"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="onChangeSetValidationFail">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="HALT"/>
            <xsd:enumeration value="MARK_RAN"/>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:simpleType name="objectQuotingStrategy">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="LEGACY"/>
            <xsd:enumeration value="QUOTE_ALL_OBJECTS"/>
            <xsd:enumeration value="QUOTE_ONLY_RESERVED_WORDS"/>
        </xsd:restriction>
    </xsd:simpleType>

    <!-- This is the main (root) part of the xsd -->
    <xsd:element name="databaseChangeLog">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="property" minOccurs="0" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:attribute name="file" type="xsd:string"/>
                        <xsd:attribute name="name" type="xsd:string"/>
                        <xsd:attribute name="value" type="xsd:string"/>
                        <xsd:attribute name="dbms" type="xsd:string"/>
                        <xsd:attribute name="context" type="xsd:string"/>
                        <xsd:attribute name="labels" type="xsd:string"/>
                        <xsd:attribute name="global" type="xsd:boolean"/>
                        <xsd:attribute name="target" type="xsd:string"/>
                        <xsd:anyAttribute namespace="##other" processContents="lax"/>
                    </xsd:complexType>
                </xsd:element>

                <xsd:element name="preConditions" minOccurs="0"
                             maxOccurs="1">
                    <xsd:complexType>
                        <xsd:choice>
                            <xsd:group ref="PreConditionChildren" maxOccurs="unbounded"/>
                        </xsd:choice>
                        <xsd:attribute name="onFailMessage" type="xsd:string"/>
                        <xsd:attribute name="onErrorMessage" type="xsd:string"/>
                        <xsd:attribute name="onFail"
                                       type="onChangeLogPreconditionErrorOrFail"/>
                        <xsd:attribute name="onError"
                                       type="onChangeLogPreconditionErrorOrFail"/>
                        <xsd:attribute name="onSqlOutput"
                                       type="onChangeLogPreconditionOnSqlOutput"/>
                    </xsd:complexType>
                </xsd:element>

                <xsd:choice minOccurs="0" maxOccurs="unbounded">
                    <xsd:element name="changeSet" minOccurs="0" maxOccurs="unbounded">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="validCheckSum" minOccurs="0"
                                             maxOccurs="unbounded">
                                    <xsd:complexType mixed="true">
                                        <xsd:sequence>
                                            <xsd:element ref="comment" minOccurs="0"
                                                         maxOccurs="1"/>
                                        </xsd:sequence>
                                    </xsd:complexType>
                                </xsd:element>
                                <xsd:element name="preConditions" minOccurs="0"
                                             maxOccurs="1">
                                    <xsd:annotation>
                                        <xsd:appinfo>
                                            <xsd:documentation>onChangeLogPreconditionOnSqlOutput
                                                determines what should
                                                happen when evaluating this precondition in
                                                updateSQL mode. TEST: Run
                                                precondition, FAIL: Fail precondition, IGNORE: Skip
                                                precondition check
                                                [DEFAULT]
                                            </xsd:documentation>
                                        </xsd:appinfo>
                                    </xsd:annotation>
                                    <xsd:complexType>
                                        <xsd:choice>
                                            <xsd:group ref="PreConditionChildren"
                                                       maxOccurs="unbounded"/>
                                        </xsd:choice>
                                        <xsd:attribute name="onFailMessage" type="xsd:string"/>
                                        <xsd:attribute name="onErrorMessage" type="xsd:string"/>
                                        <xsd:attribute name="onFail"
                                                       type="onChangeSetPreconditionErrorOrFail"/>
                                        <xsd:attribute name="onError"
                                                       type="onChangeSetPreconditionErrorOrFail"/>
                                        <xsd:attribute name="onSqlOutput"
                                                       type="onChangeLogPreconditionOnSqlOutput"/>
                                    </xsd:complexType>
                                </xsd:element>
                                <xsd:choice>
                                    <xsd:element ref="tagDatabase" maxOccurs="1"/>
                                    <xsd:group ref="changeSetChildren" minOccurs="0"
                                               maxOccurs="unbounded"/>
                                </xsd:choice>

                                <xsd:element name="modifySql" minOccurs="0"
                                             maxOccurs="unbounded">
                                    <xsd:complexType>
                                        <xsd:choice>
                                            <xsd:group ref="modifySqlChildren" minOccurs="1"
                                                       maxOccurs="unbounded"/>
                                        </xsd:choice>
                                        <xsd:attribute name="dbms" type="xsd:string"/>
                                        <xsd:attribute name="context" type="xsd:string"/>
                                        <xsd:attribute name="labels" type="xsd:string"/>
                                        <xsd:attribute name="applyToRollback" type="booleanExp"/>
                                    </xsd:complexType>
                                </xsd:element>

                            </xsd:sequence>
                            <xsd:attributeGroup ref="changeSetAttributes"/>
                            <xsd:anyAttribute namespace="##other" processContents="lax"/>
                        </xsd:complexType>
                    </xsd:element>

                    <!-- include -->
                    <xsd:element name="include" minOccurs="0" maxOccurs="unbounded">
                        <xsd:complexType>
                            <xsd:attribute name="id" type="xsd:string"/>
                            <xsd:attribute name="author" type="xsd:string"/>
                            <xsd:attribute name="file" type="xsd:string" use="required"/>
                            <xsd:attribute name="relativeToChangelogFile" type="booleanExp"/>
                            <xsd:attribute name="context" type="xsd:string"/>
                            <xsd:attribute name="labels" type="xsd:string"/>
                            <xsd:attribute name="ignore" type="xsd:string"/>
                            <xsd:attribute name="created" type="xsd:string"/>
                            <xsd:anyAttribute namespace="##other" processContents="lax"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="includeAll" minOccurs="0" maxOccurs="unbounded">
                        <xsd:complexType>
                            <xsd:attribute name="path" type="xsd:string" use="required"/>
                            <xsd:attribute name="errorIfMissingOrEmpty" type="booleanExp"
                                           default="false"/>
                            <xsd:attribute name="relativeToChangelogFile" type="booleanExp"/>
                            <xsd:attribute name="resourceComparator" type="xsd:string"/>
                            <xsd:attribute name="filter" type="xsd:string"/>
                            <xsd:attribute name="context" type="xsd:string"/>
                            <xsd:anyAttribute namespace="##other" processContents="lax"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeLogAttributes"/>
            <xsd:anyAttribute namespace="##other" processContents="lax"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:group name="modifySqlChildren">
        <xsd:choice>
            <xsd:element name="replace" minOccurs="0" maxOccurs="unbounded">
                <xsd:complexType>
                    <xsd:attribute name="replace" type="xsd:string" use="required"/>
                    <xsd:attribute name="with" type="xsd:string" use="required"/>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="regExpReplace" minOccurs="0"
                         maxOccurs="unbounded">
                <xsd:complexType>
                    <xsd:attribute name="replace" type="xsd:string" use="required"/>
                    <xsd:attribute name="with" type="xsd:string" use="required"/>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="prepend" minOccurs="0" maxOccurs="unbounded">
                <xsd:complexType>
                    <xsd:attribute name="value" type="xsd:string" use="required"/>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="append" minOccurs="0" maxOccurs="unbounded">
                <xsd:complexType>
                    <xsd:attribute name="value" type="xsd:string" use="required"/>
                </xsd:complexType>
            </xsd:element>
        </xsd:choice>
    </xsd:group>

    <!-- Attributes for DatabaseInfo -->
    <xsd:attributeGroup name="DatabaseInfoAttributes">
        <xsd:attribute name="type" type="xsd:string"/>
        <xsd:attribute name="version" type="xsd:string"/>
        <xsd:attribute name="username" type="xsd:string"/>
        <xsd:attribute name="password" type="xsd:string"/>
        <xsd:attribute name="port" type="integerExp"/>
        <xsd:attribute name="host" type="xsd:string"/>
        <xsd:attribute name="schemaName" type="xsd:string"/>
        <xsd:attribute name="driverName" type="xsd:string"/>
    </xsd:attributeGroup>

    <!-- Attributes for changeSet -->
    <xsd:attributeGroup name="changeLogAttributes">
        <xsd:attribute name="logicalFilePath" type="xsd:string"/>
        <xsd:attribute name="context" type="xsd:string"/>
        <xsd:attribute name="objectQuotingStrategy" type="objectQuotingStrategy" default="LEGACY"/>
    </xsd:attributeGroup>

    <!-- Attributes for changeSet -->
    <xsd:attributeGroup name="changeSetAttributes">
        <xsd:attribute name="id" type="xsd:string" use="required"/>
        <xsd:attribute name="author" type="xsd:string" use="required"/>
        <xsd:attribute name="context" type="xsd:string"/>
        <xsd:attribute name="labels" type="xsd:string"/>
        <xsd:attribute name="dbms" type="xsd:string"/>
        <xsd:attribute name="runOnChange" type="booleanExp"/>
        <xsd:attribute name="runAlways" type="booleanExp"/>
        <xsd:attribute name="failOnError" type="booleanExp"/>
        <xsd:attribute name="onValidationFail" type="onChangeSetValidationFail"/>
        <xsd:attribute name="runInTransaction" type="booleanExp" default="true"/>
        <xsd:attribute name="logicalFilePath" type="xsd:string"/>
        <xsd:attribute name="objectQuotingStrategy" type="objectQuotingStrategy"/>
        <xsd:attribute name="created" type="xsd:string"/>
        <xsd:attribute name="runOrder" type="xsd:string"/>
        <xsd:attribute name="ignore" type="booleanExp"/>
    </xsd:attributeGroup>

    <!-- Attributes for changes -->
    <xsd:attributeGroup name="changeAttributes">
        <xsd:anyAttribute namespace="##any" processContents="lax"/>
    </xsd:attributeGroup>

    <!-- Attributes for constraints -->
    <xsd:attributeGroup name="constraintsAttributes">
        <xsd:attribute name="nullable" type="booleanExp"/>
        <xsd:attribute name="notNullConstraintName" type="xsd:string"/>
        <xsd:attribute name="primaryKey" type="booleanExp"/>
        <xsd:attribute name="primaryKeyName" type="xsd:string"/>
        <xsd:attribute name="primaryKeyTablespace" type="xsd:string"/>
        <xsd:attribute name="unique" type="booleanExp"/>
        <xsd:attribute name="uniqueConstraintName" type="xsd:string"/>
        <xsd:attribute name="references" type="xsd:string"/>
        <xsd:attribute name="referencedTableCatalogName" type="xsd:string"/>
        <xsd:attribute name="referencedTableSchemaName" type="xsd:string"/>
        <xsd:attribute name="referencedTableName" type="xsd:string"/>
        <xsd:attribute name="referencedColumnNames" type="xsd:string"/>
        <xsd:attribute name="foreignKeyName" type="xsd:string"/>
        <xsd:attribute name="deleteCascade" type="booleanExp"/>
        <xsd:attribute name="deferrable" type="booleanExp"/>
        <xsd:attribute name="initiallyDeferred" type="booleanExp"/>
        <xsd:attribute name="checkConstraint" type="xsd:string"/>
        <xsd:attribute name="validateNullable" type="booleanExp"/>
        <xsd:attribute name="validateUnique" type="booleanExp"/>
        <xsd:attribute name="validatePrimaryKey" type="booleanExp"/>
        <xsd:attribute name="validateForeignKey" type="booleanExp"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="column">
        <xsd:attribute name="name" type="xsd:string"/>
        <xsd:attribute name="computed" type="booleanExp"/>
        <xsd:attribute name="descending" type="booleanExp"/>
        <xsd:attribute name="type" type="xsd:string"/>
        <xsd:attribute name="value" type="xsd:string"/>
        <xsd:attribute name="valueNumeric" type="xsd:string"/>
        <xsd:attribute name="valueBoolean" type="xsd:string"/>
        <xsd:attribute name="valueDate" type="xsd:string"/>
        <xsd:attribute name="valueComputed" type="xsd:string"/>
        <xsd:attribute name="valueSequenceCurrent" type="xsd:string"/>
        <xsd:attribute name="valueSequenceNext" type="xsd:string"/>
        <xsd:attribute name="valueBlobFile" type="xsd:string"/>
        <xsd:attribute name="valueClobFile" type="xsd:string"/>
        <xsd:attribute name="defaultValue" type="xsd:string"/>
        <xsd:attribute name="defaultValueNumeric" type="xsd:string"/>
        <xsd:attribute name="defaultValueDate" type="xsd:string"/>
        <xsd:attribute name="defaultValueBoolean" type="booleanExp"/>
        <xsd:attribute name="defaultValueComputed" type="xsd:string"/>
        <xsd:attribute name="defaultValueSequenceCurrent" type="xsd:string"/>
        <xsd:attribute name="defaultValueSequenceNext" type="xsd:string"/>
        <xsd:attribute name="defaultValueConstraintName" type="xsd:string"/>
        <xsd:attribute name="autoIncrement" type="booleanExp"/>
        <xsd:attribute name="startWith" type="xsd:long"/>
        <xsd:attribute name="incrementBy" type="xsd:long"/>
        <xsd:attribute name="generationType" type="xsd:string"/>
        <xsd:attribute name="defaultOnNull" type="booleanExp"/>
        <xsd:attribute name="remarks" type="xsd:string"/>
        <xsd:attribute name="encoding" type="xsd:string">
            <xsd:annotation>
                <xsd:appinfo>
                    <xsd:documentation>
                        Used with valueClobFile to specify file encoding explicitly.
                    </xsd:documentation>
                </xsd:appinfo>
            </xsd:annotation>
        </xsd:attribute>
        <xsd:anyAttribute namespace="##other" processContents="lax"/>
    </xsd:attributeGroup>

    <xsd:complexType name="columnType" mixed="true">
        <xsd:sequence>
            <xsd:element ref="constraints" minOccurs="0"/>
            <xsd:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
        <xsd:attributeGroup ref="column"/>
        <xsd:anyAttribute namespace="##other" processContents="lax"/>
    </xsd:complexType>

    <xsd:attributeGroup name="param">
        <xsd:attribute name="name" type="xsd:string"/>
        <xsd:attribute name="value" type="xsd:string"/>
        <xsd:attribute name="valueNumeric" type="xsd:string"/>
        <xsd:attribute name="valueBoolean" type="xsd:string"/>
        <xsd:attribute name="valueDate" type="xsd:string"/>
        <xsd:attribute name="valueComputed" type="xsd:string"/>
        <xsd:attribute name="valueSequenceNext" type="xsd:string"/>
        <xsd:attribute name="valueSequenceCurrent" type="xsd:string"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="tableNameAttribute">
        <xsd:attribute name="catalogName" type="xsd:string"/>
        <xsd:attribute name="schemaName" type="xsd:string"/>
        <xsd:attribute name="tableName" type="xsd:string" use="required"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="dropTableAttributes">
        <xsd:attributeGroup ref="tableNameAttribute"/>
        <xsd:attribute name="cascadeConstraints" type="booleanExp"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="renameTableAttributes">
        <xsd:attribute name="catalogName" type="xsd:string"/>
        <xsd:attribute name="schemaName" type="xsd:string"/>
        <xsd:attribute name="oldTableName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="newTableName" type="xsd:string"
                       use="required"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="renameViewAttributes">
        <xsd:attribute name="catalogName" type="xsd:string"/>
        <xsd:attribute name="schemaName" type="xsd:string"/>
        <xsd:attribute name="oldViewName" type="xsd:string" use="required"/>
        <xsd:attribute name="newViewName" type="xsd:string" use="required"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="renameColumnAttributes">
        <xsd:attributeGroup ref="tableNameAttribute"/>
        <xsd:attribute name="oldColumnName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="newColumnName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="columnDataType" type="xsd:string"/>
        <xsd:attribute name="remarks" type="xsd:string"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="dropColumnAttributes">
        <xsd:attributeGroup ref="tableNameAttribute"/>
        <xsd:attribute name="columnName" type="xsd:string"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="sequenceAttributes">
        <xsd:attribute name="catalogName" type="xsd:string"/>
        <xsd:attribute name="schemaName" type="xsd:string"/>
        <xsd:attribute name="sequenceName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="startValue" type="xsd:string"/>
        <xsd:attribute name="incrementBy" type="xsd:string"/>
        <xsd:attribute name="maxValue" type="xsd:string"/>
        <xsd:attribute name="minValue" type="xsd:string"/>
        <xsd:attribute name="ordered" type="xsd:string"/>
        <xsd:attribute name="cacheSize" type="xsd:string"/>
        <xsd:attribute name="dataType" type="xsd:string"/>
        <xsd:attribute name="cycle" type="xsd:string">
            <xsd:annotation>
                <xsd:documentation>
                    true for a cycling sequence, false for a non-cycling sequence.
                    Default is false.
                </xsd:documentation>
            </xsd:annotation>
        </xsd:attribute>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="renameSequenceAttributes">
        <xsd:attribute name="catalogName" type="xsd:string"/>
        <xsd:attribute name="schemaName" type="xsd:string"/>
        <xsd:attribute name="oldSequenceName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="newSequenceName" type="xsd:string"
                       use="required"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="indexName">
        <xsd:attribute name="indexName" type="xsd:string" use="required"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="createIndex">
        <xsd:attributeGroup ref="tableNameAttribute"/>
        <xsd:attributeGroup ref="indexName"/>
        <xsd:attribute name="associatedWith" type="xsd:string" use="optional"/>
        <xsd:attribute name="unique" type="booleanExp"/>
        <xsd:attribute name="clustered" type="booleanExp"/>
        <xsd:attribute name="tablespace" type="xsd:string"/>
        <xsd:anyAttribute namespace="##other" processContents="lax"/>
    </xsd:attributeGroup>


    <xsd:element name="addPrimaryKey">
        <xsd:complexType>
            <xsd:choice maxOccurs="unbounded">
                <xsd:any namespace="##other" processContents="lax" minOccurs="0"
                         maxOccurs="unbounded"/>
            </xsd:choice>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="columnNames" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="constraintName" type="xsd:string"/>
            <xsd:attribute name="tablespace" type="xsd:string"/>
            <xsd:attribute name="clustered" type="booleanExp"/>
            <xsd:attribute name="forIndexCatalogName" type="xsd:string"/>
            <xsd:attribute name="forIndexSchemaName" type="xsd:string"/>
            <xsd:attribute name="forIndexName" type="xsd:string"/>
            <xsd:attribute name="validate" type="booleanExp"/>
            <xsd:anyAttribute namespace="##other" processContents="lax"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropPrimaryKey">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="constraintName" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="addUniqueConstraint">
        <xsd:complexType>
            <xsd:choice maxOccurs="unbounded">
                <xsd:any namespace="##other" processContents="lax" minOccurs="0"
                         maxOccurs="unbounded"/>
            </xsd:choice>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="columnNames" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="constraintName" type="xsd:string"/>
            <xsd:attribute name="tablespace" type="xsd:string"/>
            <xsd:attribute name="disabled" type="booleanExp"/>
            <xsd:attribute name="deferrable" type="booleanExp"/>
            <xsd:attribute name="initiallyDeferred" type="booleanExp"/>
            <xsd:attribute name="forIndexCatalogName" type="xsd:string"/>
            <xsd:attribute name="forIndexSchemaName" type="xsd:string"/>
            <xsd:attribute name="forIndexName" type="xsd:string"/>
            <xsd:attribute name="clustered" type="booleanExp"/>
            <xsd:attribute name="validate" type="booleanExp"/>
            <xsd:anyAttribute namespace="##other" processContents="lax"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropUniqueConstraint">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="constraintName" type="xsd:string"/>
            <xsd:attribute name="uniqueColumns" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="modifyDataType">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="columnName" type="xsd:string" use="required"/>
            <xsd:attribute name="newDataType" type="xsd:string" use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="addLookupTable">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="existingTableCatalogName" type="xsd:string"/>
            <xsd:attribute name="existingTableSchemaName" type="xsd:string"/>
            <xsd:attribute name="existingTableName" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="existingColumnName" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="newTableCatalogName" type="xsd:string"/>
            <xsd:attribute name="newTableSchemaName" type="xsd:string"/>
            <xsd:attribute name="newTableName" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="newColumnName" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="newColumnDataType" type="xsd:string"/>
            <xsd:attribute name="constraintName" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="addAutoIncrement">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="columnName" type="xsd:string" use="required"/>
            <xsd:attribute name="columnDataType" type="xsd:string"/>
            <xsd:attribute name="startWith" type="xsd:long"/>
            <xsd:attribute name="incrementBy" type="xsd:long"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="addDefaultValue">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="columnName" type="xsd:string" use="required"/>
            <xsd:attribute name="columnDataType" type="xsd:string"/>
            <xsd:attribute name="defaultValue" type="xsd:string"/>
            <xsd:attribute name="defaultValueNumeric" type="xsd:string"/>
            <xsd:attribute name="defaultValueDate" type="xsd:string"/>
            <xsd:attribute name="defaultValueBoolean" type="xsd:string"/>
            <xsd:attribute name="defaultValueComputed" type="xsd:string"/>
            <xsd:attribute name="defaultValueSequenceNext" type="xsd:string"/>
            <xsd:attribute name="defaultValueConstraintName" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropDefaultValue">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="columnName" type="xsd:string" use="required"/>
            <xsd:attribute name="columnDataType" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="loadData">
        <xsd:complexType mixed="true">
            <xsd:sequence>
                <xsd:element name="column" minOccurs="0" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:attribute name="index" type="integerExp"/>
                        <xsd:attribute name="header" type="xsd:string"/>
                        <xsd:attribute name="name" type="xsd:string"/>
                        <xsd:attribute name="type" type="xsd:string"/>
                        <xsd:attribute name="defaultValue" type="xsd:string"/>
                        <xsd:attribute name="defaultValueNumeric" type="xsd:string"/>
                        <xsd:attribute name="defaultValueDate" type="xsd:string"/>
                        <xsd:attribute name="defaultValueBoolean" type="booleanExp"/>
                        <xsd:attribute name="defaultValueComputed" type="xsd:string"/>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="file" type="xsd:string"/>
            <xsd:attribute name="relativeToChangelogFile" type="booleanExp"/>
            <xsd:attribute name="usePreparedStatements" type="booleanExp"/>
            <xsd:attribute name="encoding" type="xsd:string" default="UTF-8"/>
            <xsd:attribute name="separator" type="xsd:string" default=","/>
            <xsd:attribute name="quotchar" type="xsd:string" default="&quot;"/>
            <xsd:attribute name="commentLineStartsWith" type="xsd:string" default="#"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="loadUpdateData">
        <xsd:complexType mixed="true">
            <xsd:sequence>
                <xsd:element name="column" minOccurs="0" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:attribute name="index" type="integerExp"/>
                        <xsd:attribute name="header" type="xsd:string"/>
                        <xsd:attribute name="name" type="xsd:string"/>
                        <xsd:attribute name="type" type="xsd:string"/>
                        <xsd:attribute name="defaultValue" type="xsd:string"/>
                        <xsd:attribute name="defaultValueNumeric" type="xsd:string"/>
                        <xsd:attribute name="defaultValueDate" type="xsd:string"/>
                        <xsd:attribute name="defaultValueBoolean" type="booleanExp"/>
                        <xsd:attribute name="defaultValueComputed" type="xsd:string"/>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="file" type="xsd:string"/>
            <xsd:attribute name="relativeToChangelogFile" type="booleanExp"/>
            <xsd:attribute name="encoding" type="xsd:string" default="UTF-8"/>
            <xsd:attribute name="primaryKey" type="xsd:string" use="required"/>
            <xsd:attribute name="onlyUpdate" type="xsd:boolean" default="false"/>
            <xsd:attribute name="separator" type="xsd:string" default=","/>
            <xsd:attribute name="quotchar" type="xsd:string" default="&quot;"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:simpleType name="fkCascadeActionOptions">
        <xsd:restriction base="xsd:token">
            <xsd:enumeration value="CASCADE"></xsd:enumeration>
            <xsd:enumeration value="SET NULL"></xsd:enumeration>
            <xsd:enumeration value="SET DEFAULT"></xsd:enumeration>
            <xsd:enumeration value="RESTRICT"></xsd:enumeration>
            <xsd:enumeration value="NO ACTION"></xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>

    <xsd:attributeGroup name="addForeignKeyConstraintAttrib">
        <xsd:attribute name="baseTableCatalogName" type="xsd:string"/>
        <xsd:attribute name="baseTableSchemaName" type="xsd:string"/>
        <xsd:attribute name="baseTableName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="baseColumnNames" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="constraintName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="referencedTableCatalogName" type="xsd:string"/>
        <xsd:attribute name="referencedTableSchemaName" type="xsd:string"/>
        <xsd:attribute name="referencedTableName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="referencedColumnNames" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="deferrable" type="booleanExp"/>
        <xsd:attribute name="initiallyDeferred" type="booleanExp"/>
        <xsd:attribute name="deleteCascade" type="booleanExp"/>
        <xsd:attribute name="onDelete" type="fkCascadeActionOptions"/>
        <xsd:attribute name="onUpdate" type="fkCascadeActionOptions"/>
        <xsd:attribute name="referencesUniqueColumn" type="booleanExp"/>
        <xsd:attribute name="validate" type="booleanExp"/>
    </xsd:attributeGroup>

    <xsd:attributeGroup name="addNotNullConstraintAttrib">
        <xsd:attributeGroup ref="tableNameAttribute"/>
        <xsd:attribute name="columnName" type="xsd:string" use="required"/>
        <xsd:attribute name="defaultNullValue" type="xsd:string"/>
        <xsd:attribute name="columnDataType" type="xsd:string"/>
        <xsd:attribute name="constraintName" type="xsd:string"/>
        <xsd:attribute name="validate" type="booleanExp"/>
    </xsd:attributeGroup>


    <xsd:element name="addNotNullConstraint">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="addNotNullConstraintAttrib"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="addForeignKeyConstraint">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="addForeignKeyConstraintAttrib"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:attributeGroup name="dbmsAttrib">
        <xsd:attribute name="type" type="xsd:string" use="required"/>
    </xsd:attributeGroup>

    <xsd:element name="dbms">
        <xsd:complexType>
            <xsd:attributeGroup ref="dbmsAttrib"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="changeSetExecuted">
        <xsd:complexType>
            <xsd:attribute name="id" type="xsd:string" use="required"/>
            <xsd:attribute name="author" type="xsd:string" use="required"/>
            <xsd:attribute name="changeLogFile" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="tableExists">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string" use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="columnExists">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string" use="required"/>
            <xsd:attribute name="columnName" type="xsd:string" use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="sequenceExists">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="sequenceName" type="xsd:string" use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="foreignKeyConstraintExists">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="foreignKeyTableName" type="xsd:string"/>
            <xsd:attribute name="foreignKeyName" type="xsd:string" use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="indexExists">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="indexName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string"/>
            <xsd:attribute name="columnNames" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="rowCount">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string"/>
            <xsd:attribute name="expectedRows" type="xsd:int"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="tableIsEmpty">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="changeLogPropertyDefined">
        <xsd:complexType>
            <xsd:attribute name="property" type="xsd:string" use="required"/>
            <xsd:attribute name="value" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="primaryKeyExists">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="primaryKeyName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="viewExists">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="viewName" type="xsd:string" use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="sqlCheck">
        <xsd:complexType mixed="true">
            <xsd:attribute name="expectedResult" type="xsd:string"
                           use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="customPrecondition">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="param" minOccurs="0" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:attribute name="name" type="xsd:string" use="required"/>
                        <xsd:attribute name="value" type="xsd:string" use="required"/>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
            <xsd:attribute name="className" type="xsd:string" use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="and">
        <xsd:complexType>
            <xsd:choice>
                <xsd:group ref="PreConditionChildren" minOccurs="1"
                           maxOccurs="unbounded"/>
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="or">
        <xsd:complexType>
            <xsd:choice>
                <xsd:group ref="PreConditionChildren" minOccurs="1"
                           maxOccurs="unbounded"/>
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="not">
        <xsd:complexType>
            <xsd:choice>
                <xsd:group ref="PreConditionChildren" minOccurs="1"
                           maxOccurs="unbounded"/>
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>

    <xsd:attributeGroup name="runningAsAttrib">
        <xsd:attribute name="username" type="xsd:string" use="required"/>
    </xsd:attributeGroup>

    <xsd:element name="runningAs">
        <xsd:complexType>
            <xsd:attributeGroup ref="runningAsAttrib"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:attributeGroup name="objectQuotingStrategyAttrib">
        <xsd:attribute name="strategy" type="xsd:string" use="required"/>
    </xsd:attributeGroup>

    <xsd:element name="expectedQuotingStrategy">
        <xsd:complexType>
            <xsd:attributeGroup ref="objectQuotingStrategyAttrib"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:attributeGroup name="dropForeignKeyConstraintAttrib">
        <xsd:attribute name="baseTableCatalogName" type="xsd:string"/>
        <xsd:attribute name="baseTableSchemaName" type="xsd:string"/>
        <xsd:attribute name="baseTableName" type="xsd:string"
                       use="required"/>
        <xsd:attribute name="constraintName" type="xsd:string"/>
    </xsd:attributeGroup>

    <xsd:element name="dropForeignKeyConstraint">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="dropForeignKeyConstraintAttrib"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:attributeGroup name="dropAllForeignKeyConstraintsAttrib">
        <xsd:attribute name="baseTableCatalogName" type="xsd:string"/>
        <xsd:attribute name="baseTableSchemaName" type="xsd:string"/>
        <xsd:attribute name="baseTableName" type="xsd:string"
                       use="required"/>
    </xsd:attributeGroup>

    <xsd:element name="dropAllForeignKeyConstraints">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="dropAllForeignKeyConstraintsAttrib"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropNotNullConstraint">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string" use="required"/>
            <xsd:attribute name="columnName" type="xsd:string" use="required"/>
            <xsd:attribute name="columnDataType" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="setTableRemarks">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string" use="required"/>
            <xsd:attribute name="remarks" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="setColumnRemarks">
        <xsd:complexType>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="tableName" type="xsd:string" use="required"/>
            <xsd:attribute name="columnName" type="xsd:string" use="required"/>
            <xsd:attribute name="remarks" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="constraints">
        <xsd:complexType>
            <xsd:attributeGroup ref="constraintsAttributes"/>
        </xsd:complexType>
    </xsd:element>


    <xsd:element name="column">
        <xsd:complexType mixed="true">
            <xsd:sequence>
                <xsd:element ref="constraints" minOccurs="0"/>
            </xsd:sequence>
            <xsd:attributeGroup ref="column"/>
            <xsd:anyAttribute namespace="##other" processContents="lax"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="param">
        <xsd:complexType>
            <xsd:attributeGroup ref="param"/>
        </xsd:complexType>
    </xsd:element>

    <!-- Children for createTable -->
    <xsd:element name="createTable">
        <xsd:complexType>
            <xsd:choice maxOccurs="unbounded">
                <xsd:element ref="column" minOccurs="1" maxOccurs="unbounded"/>
                <xsd:any namespace="##other" processContents="lax" minOccurs="0"
                         maxOccurs="unbounded"/>
            </xsd:choice>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="tablespace" type="xsd:string"/>
            <xsd:attribute name="remarks" type="xsd:string"/>
            <xsd:anyAttribute namespace="##other" processContents="lax"/>
        </xsd:complexType>
    </xsd:element>

    <!-- Children for createView -->
    <xsd:element name="createView">
        <xsd:complexType>
            <xsd:simpleContent>
                <xsd:extension base="xsd:string">
                    <xsd:attributeGroup ref="changeAttributes"/>
                    <xsd:attribute name="catalogName" type="xsd:string"/>
                    <xsd:attribute name="schemaName" type="xsd:string"/>
                    <xsd:attribute name="viewName" type="xsd:string" use="required"/>
                    <xsd:attribute name="remarks" type="xsd:string"/>
                    <xsd:attribute name="replaceIfExists" type="booleanExp"/>
                    <xsd:attribute name="fullDefinition" type="booleanExp"/>
                    <xsd:attribute name="path" type="xsd:string"/>
                    <xsd:attribute name="encoding" type="xsd:string"/>
                    <xsd:attribute name="relativeToChangelogFile" type="booleanExp"/>
                    <xsd:anyAttribute namespace="##other" processContents="lax"/>
                </xsd:extension>
            </xsd:simpleContent>
        </xsd:complexType>
    </xsd:element>

    <!-- Children for insert -->
    <xsd:element name="insert">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element ref="column" maxOccurs="unbounded"/>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="dbms" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="update">
        <xsd:complexType mixed="true">
            <xsd:sequence>
                <xsd:element ref="column" minOccurs="1" maxOccurs="unbounded"/>
                <xsd:element name="where" minOccurs="0" maxOccurs="1" type="xsd:string"/>
                <xsd:element ref="whereParams" minOccurs="0" maxOccurs="1"/>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="whereParams">
        <xsd:complexType mixed="true">
            <xsd:sequence>
                <xsd:element ref="param" minOccurs="1" maxOccurs="unbounded"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="delete">
        <xsd:complexType mixed="true">
            <xsd:sequence>
                <xsd:element name="where" minOccurs="0" maxOccurs="1"/>
                <xsd:element ref="whereParams" minOccurs="0" maxOccurs="1"/>
            </xsd:sequence>
            <xsd:attributeGroup ref="tableNameAttribute"/>
        </xsd:complexType>
    </xsd:element>

    <!-- Children for addColum -->
    <xsd:element name="addColumn">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="column" minOccurs="1" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:complexContent mixed="true">
                            <xsd:extension base="columnType">
                                <xsd:attribute name="beforeColumn" type="xsd:string"/>
                                <xsd:attribute name="afterColumn" type="xsd:string"/>
                                <xsd:attribute name="position" type="xsd:integer"/>
                            </xsd:extension>
                        </xsd:complexContent>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="rollback">
        <xsd:complexType mixed="true">
            <xsd:choice>
                <xsd:group ref="changeSetChildren" minOccurs="0"
                           maxOccurs="unbounded"/>
            </xsd:choice>
            <xsd:attribute name="changeSetPath" type="xsd:string"/>
            <xsd:attribute name="changeSetAuthor" type="xsd:string"/>
            <xsd:attribute name="changeSetId" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <!-- comment  -->
    <xsd:element name="comment" type="xsd:string"/>

    <!-- sql -->
    <xsd:element name="sql">
        <xsd:complexType mixed="true">
            <xsd:sequence>
                <xsd:element ref="comment" minOccurs="0" maxOccurs="1"/>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="stripComments" type="booleanExp"/>
            <xsd:attribute name="splitStatements" type="booleanExp"/>
            <xsd:attribute name="endDelimiter" type="xsd:string"/>
            <xsd:attribute name="dbms" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="createProcedure">
        <xsd:complexType mixed="true">
            <xsd:sequence>
                <xsd:element ref="comment" minOccurs="0" maxOccurs="1"/>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
            <xsd:attribute name="procedureName" type="xsd:string"/>
            <xsd:attribute name="path" type="xsd:string"/>
            <xsd:attribute name="encoding" type="xsd:string"/>
            <xsd:attribute name="relativeToChangelogFile" type="booleanExp"/>
            <xsd:attribute name="dbms" type="xsd:string"/>
            <xsd:attribute name="replaceIfExists" type="xsd:boolean"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="executeCommand">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="arg" minOccurs="0" maxOccurs="unbounded">
                    <xsd:complexType>
                        <xsd:attribute name="value" type="xsd:string" use="required"/>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="executable" type="xsd:string" use="required"/>
            <xsd:attribute name="os" type="xsd:string"/>
            <xsd:attribute name="timeout" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="sqlFile">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="path" type="xsd:string" use="required"/>
            <xsd:attribute name="stripComments" type="booleanExp"/>
            <xsd:attribute name="splitStatements" type="booleanExp"/>
            <xsd:attribute name="encoding" type="xsd:string" default="UTF-8"/>
            <xsd:attribute name="endDelimiter" type="xsd:string"/>
            <xsd:attribute name="relativeToChangelogFile" type="booleanExp"/>
            <xsd:attribute name="dbms" type="xsd:string"/>
            <xsd:anyAttribute namespace="##other" processContents="lax"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="tagDatabase">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="tag" type="xsd:string" use="required"/>
            <xsd:anyAttribute namespace="##other" processContents="lax"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="stop">
        <xsd:complexType mixed="true">
            <xsd:attributeGroup ref="changeAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="empty">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="output">
        <xsd:complexType mixed="true">
            <xsd:attribute name="target" type="xsd:string"/>
        </xsd:complexType>

    </xsd:element>

    <!-- renameTable -->
    <xsd:element name="renameTable">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="renameTableAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="renameView">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="renameViewAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <!-- dropTable -->
    <xsd:element name="dropTable">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="dropTableAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropView">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="viewName" type="xsd:string" use="required"/>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropProcedure">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="procedureName" type="xsd:string" use="required"/>
            <xsd:attribute name="catalogName" type="xsd:string"/>
            <xsd:attribute name="schemaName" type="xsd:string"/>
        </xsd:complexType>
    </xsd:element>

    <!-- renameColumn -->
    <xsd:element name="renameColumn">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="renameColumnAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropColumn">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="column" minOccurs="0" maxOccurs="unbounded" type="columnType"/>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="dropColumnAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="mergeColumns">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attribute name="column1Name" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="joinString" type="xsd:string" use="required"/>
            <xsd:attribute name="column2Name" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="finalColumnName" type="xsd:string"
                           use="required"/>
            <xsd:attribute name="finalColumnType" type="xsd:string"
                           use="required"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="createSequence">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="sequenceAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="alterSequence">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="sequenceAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropSequence">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="sequenceAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="renameSequence">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="renameSequenceAttributes"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="createIndex">
        <xsd:complexType>
            <xsd:choice maxOccurs="unbounded">
                <xsd:element ref="column" maxOccurs="unbounded"/>
                <xsd:any namespace="##other" processContents="lax" minOccurs="0"
                         maxOccurs="unbounded"/>
            </xsd:choice>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="createIndex"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="dropIndex">
        <xsd:complexType>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attributeGroup ref="tableNameAttribute"/>
            <xsd:attributeGroup ref="indexName"/>
            <xsd:attribute name="associatedWith" type="xsd:string" use="optional"/>
        </xsd:complexType>
    </xsd:element>

    <xsd:element name="customChange">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="param" minOccurs="0" maxOccurs="unbounded">
                    <xsd:complexType mixed="true">
                        <xsd:attribute name="name" type="xsd:string" use="required"/>
                        <xsd:attribute name="value" type="xsd:string"/>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
            <xsd:attributeGroup ref="changeAttributes"/>
            <xsd:attribute name="class" type="xsd:string" use="required"/>
            <xsd:anyAttribute processContents="lax"/>
        </xsd:complexType>
    </xsd:element>


    <!-- Children for changeSet -->
    <xsd:group name="changeSetChildren">
        <xsd:choice>
            <xsd:element ref="comment" maxOccurs="1"/>
            <xsd:element ref="createTable" maxOccurs="unbounded"/>
            <xsd:element ref="dropTable" maxOccurs="unbounded"/>
            <xsd:element ref="createView" maxOccurs="unbounded"/>
            <xsd:element ref="renameView" maxOccurs="unbounded"/>
            <xsd:element ref="dropView" maxOccurs="unbounded"/>
            <xsd:element ref="insert" maxOccurs="unbounded"/>
            <xsd:element ref="addColumn" maxOccurs="unbounded"/>
            <xsd:element ref="sql" maxOccurs="unbounded"/>
            <xsd:element ref="createProcedure" maxOccurs="unbounded"/>
            <xsd:element ref="dropProcedure" maxOccurs="unbounded"/>
            <xsd:element ref="sqlFile" maxOccurs="unbounded"/>
            <xsd:element ref="renameTable" maxOccurs="unbounded"/>
            <xsd:element ref="renameColumn" maxOccurs="unbounded"/>
            <xsd:element ref="dropColumn" maxOccurs="unbounded"/>
            <xsd:element ref="mergeColumns" maxOccurs="unbounded"/>
            <xsd:element ref="modifyDataType" maxOccurs="unbounded"/>
            <xsd:element ref="createSequence" maxOccurs="unbounded"/>
            <xsd:element ref="alterSequence" maxOccurs="unbounded"/>
            <xsd:element ref="dropSequence" maxOccurs="unbounded"/>
            <xsd:element ref="renameSequence" maxOccurs="unbounded"/>
            <xsd:element ref="createIndex" maxOccurs="unbounded"/>
            <xsd:element ref="dropIndex" maxOccurs="unbounded"/>
            <xsd:element ref="addNotNullConstraint" maxOccurs="unbounded"/>
            <xsd:element ref="dropNotNullConstraint" maxOccurs="unbounded"/>
            <xsd:element ref="addForeignKeyConstraint" maxOccurs="unbounded"/>
            <xsd:element ref="dropForeignKeyConstraint" maxOccurs="unbounded"/>
            <xsd:element ref="dropAllForeignKeyConstraints"
                         maxOccurs="unbounded"/>
            <xsd:element ref="addPrimaryKey" maxOccurs="unbounded"/>
            <xsd:element ref="dropPrimaryKey" maxOccurs="unbounded"/>
            <xsd:element ref="addLookupTable" maxOccurs="unbounded"/>
            <xsd:element ref="addAutoIncrement" maxOccurs="unbounded"/>
            <xsd:element ref="addDefaultValue" maxOccurs="unbounded"/>
            <xsd:element ref="dropDefaultValue" maxOccurs="unbounded"/>
            <xsd:element ref="addUniqueConstraint" maxOccurs="unbounded"/>
            <xsd:element ref="dropUniqueConstraint" maxOccurs="unbounded"/>
            <xsd:element ref="setTableRemarks" maxOccurs="unbounded"/>
            <xsd:element ref="setColumnRemarks" maxOccurs="unbounded"/>
            <xsd:element ref="customChange" maxOccurs="unbounded"/>
            <xsd:element ref="update" maxOccurs="unbounded"/>
            <xsd:element ref="delete" maxOccurs="unbounded"/>
            <xsd:element ref="loadData" maxOccurs="unbounded"/>
            <xsd:element ref="loadUpdateData" maxOccurs="unbounded"/>
            <xsd:element ref="executeCommand" maxOccurs="unbounded"/>
            <xsd:element ref="stop" maxOccurs="unbounded"/>
            <xsd:element ref="output" maxOccurs="unbounded"/>
            <xsd:element ref="empty" maxOccurs="unbounded"/>
            <xsd:element ref="rollback" maxOccurs="1"/>
            <xsd:any namespace="##other" processContents="lax" minOccurs="0"
                     maxOccurs="unbounded"/>
        </xsd:choice>
    </xsd:group>

    <!-- Children for Precondition -->
    <xsd:group name="PreConditionChildren">
        <xsd:sequence>
            <xsd:choice>
                <xsd:element ref="and" maxOccurs="unbounded"/>
                <xsd:element ref="or" maxOccurs="unbounded"/>
                <xsd:element ref="not" maxOccurs="unbounded"/>
                <xsd:element ref="dbms" maxOccurs="unbounded"/>
                <xsd:element ref="runningAs" maxOccurs="unbounded"/>
                <xsd:element ref="changeSetExecuted" maxOccurs="unbounded"/>
                <xsd:element ref="tableExists" maxOccurs="unbounded"/>
                <xsd:element ref="columnExists" maxOccurs="unbounded"/>
                <xsd:element ref="sequenceExists" maxOccurs="unbounded"/>
                <xsd:element ref="foreignKeyConstraintExists" maxOccurs="unbounded"/>
                <xsd:element ref="indexExists" maxOccurs="unbounded"/>
                <xsd:element ref="primaryKeyExists" maxOccurs="unbounded"/>
                <xsd:element ref="viewExists" maxOccurs="unbounded"/>
                <xsd:element ref="tableIsEmpty" maxOccurs="unbounded"/>
                <xsd:element ref="rowCount" maxOccurs="unbounded"/>
                <xsd:element ref="sqlCheck" maxOccurs="unbounded"/>
                <xsd:element ref="changeLogPropertyDefined" maxOccurs="unbounded"/>
                <xsd:element ref="expectedQuotingStrategy" maxOccurs="unbounded"/>
                <xsd:element ref="customPrecondition" maxOccurs="unbounded"/>
                <xsd:any namespace="##other" processContents="lax" minOccurs="0"
                         maxOccurs="unbounded"/>
            </xsd:choice>
        </xsd:sequence>

    </xsd:group>
</xsd:schema>
