[{"actionType": "OTP_HURDLE", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createOtpHurdleAction"}, {"source": "CREATED", "target": "OTP_HURDLE_TRIGGERED", "event": "TRIGGER_OTP_HURDLE", "actionKey": "triggerOtpHurdleAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_HURDLE_COMPLETED", "event": "OTP_HURDLE_COMPLETE", "actionKey": "successStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_GENERATION_FAILED", "event": "FAIL_OTP_GENERATION", "actionKey": "failureStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_VERIFICATION_FAILED", "event": "FAIL_OTP_VERIFICATION", "actionKey": "failureStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "OTP_HURDLE", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createOtpHurdleAction"}, {"source": "CREATED", "target": "OTP_HURDLE_TRIGGERED", "event": "TRIGGER_OTP_HURDLE", "actionKey": "triggerOtpHurdleV2Action"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_HURDLE_COMPLETED", "event": "COMPLETE_OTP_HURDLE", "actionKey": "successStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_GENERATION_FAILED", "event": "FAIL_OTP_GENERATION", "actionKey": "failureStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_VERIFICATION_FAILED", "event": "FAIL_OTP_VERIFICATION", "actionKey": "failureStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "WAIT_FOR_CONDITION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createWaitForConditionAction"}, {"source": "CREATED", "target": "EVALUATING", "event": "EVALUATING", "actionKey": "evaluatingConditionAction"}, {"source": "EVALUATING", "target": "SUCCESS", "event": "WAIT_FOR_CONDITION_SUCCESS", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "EVALUATING", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "EVALUATING", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "EVALUATING", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "CKYC_PULL", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createCkycPullAction"}, {"source": "CREATED", "target": "CKYC_PULL_TRIGGERED", "event": "TRIGGER_CKYC_PULL", "actionKey": "triggerCkycPullAction"}, {"source": "CKYC_PULL_TRIGGERED", "target": "CKYC_PULL_DATA_SUCCESS", "event": "CKYC_PULL_SUCCESS", "actionKey": "ckycPullSuccessAction"}, {"source": "CKYC_PULL_TRIGGERED", "target": "CKYC_DATA_NOT_AVAILABLE_ON_TIME", "event": "CKYC_DATA_NOT_AVAILABLE_ON_TIME", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CKYC_DATA_NOT_AVAILABLE_ON_TIME", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CKYC_PULL_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CKYC_PULL_DATA_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CKYC_DATA_NOT_AVAILABLE_ON_TIME", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CKYC_PULL_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CKYC_PULL_DATA_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CKYC_DATA_NOT_AVAILABLE_ON_TIME", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CKYC_PULL_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CKYC_PULL_DATA_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createResolveVirtualPaymentAddressAction"}, {"source": "CREATED", "target": "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCESS", "event": "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCEEDED", "actionKey": "sendToPaymentsForResolveVirtualPaymentAddressAction"}, {"source": "CREATED", "target": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILURE", "event": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILED", "actionKey": "failureStateAction"}, {"source": "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCESS", "target": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILURE", "event": "CALLBACK_NOT_RECEIVED_FROM_PAYMENTS", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCESS", "event": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCEEDED", "actionKey": "resolveVirtualPaymentAddressSuccessAction"}, {"source": "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCESS", "target": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCESS", "event": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCEEDED", "actionKey": "resolveVirtualPaymentAddressSuccessAction"}, {"source": "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCESS", "target": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILURE", "event": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILED", "actionKey": "failureStateAction"}, {"source": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SEND_TO_PAYMENTS_FOR_RESOLVE_VPA_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "RESOLVE_VIRTUAL_PAYMENT_ADDRESS_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "CKYC_PULL", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createCkycPullAction"}, {"source": "CREATED", "target": "IN_PROGRESS", "event": "TRIGGER_CKYC_PULL", "actionKey": "triggerCkycPullActionV2"}, {"source": "IN_PROGRESS", "target": "SUCCESS", "event": "CKYC_PULL_SUCCESS", "actionKey": "successStateAction"}, {"source": "IN_PROGRESS", "target": "FAILURE", "event": "CKYC_PULL_FAILURE", "actionKey": "failureStateAction"}, {"source": "IN_PROGRESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "IN_PROGRESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "IN_PROGRESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "MIGRATION_METADATA", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createEntryAction"}, {"source": "CREATED", "target": "COMPLETED", "event": "COMPLETE", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "DOCUMENT_UPLOAD", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createDocumentUploadActionV2"}, {"source": "CREATED", "target": "SUCCESS", "event": "DOCUMENT_UPLOAD_SUCCEEDED", "actionKey": "completeDocumentUploadActionV2"}, {"source": "CREATED", "target": "FAILURE", "event": "DOCUMENT_UPLOAD_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "DOCUMENT_UPLOAD", "version": "V3", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createDocumentUploadActionV3"}, {"source": "CREATED", "target": "SUCCESS", "event": "DOCUMENT_UPLOAD_SUCCEEDED", "actionKey": "completeDocumentUploadActionV3"}, {"source": "CREATED", "target": "FAILURE", "event": "DOCUMENT_UPLOAD_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "DOCUMENT_UPLOAD", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createDocumentUploadAction"}, {"source": "CREATED", "target": "DOCUMENT_DETAILS_PERSISTED", "event": "PERSIST_DOCUMENT_DETAILS", "actionKey": "completeDocumentUploadAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DOCUMENT_DETAILS_PERSISTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DOCUMENT_DETAILS_PERSISTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DOCUMENT_DETAILS_PERSISTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KRA_PAN_DATA_FETCH", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKraKycDataFetchAction"}, {"source": "CREATED", "target": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "event": "SEND_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "actionKey": "visionKraDataFetchAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "KRA_KYA_DATA_FETCH_FAILED", "event": "KRA_PAN_CHECK_FAILED", "actionKey": "kraKycDataFetchFailedAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "event": "KRA_DATA_FETCH_SUCCEEDED", "actionKey": "kraKycDataFetchSuccessfulAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KRA_PAN_DATA_FETCH", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKraKycDataFetchAction"}, {"source": "CREATED", "target": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "event": "SEND_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "actionKey": "visionKraDataFetchAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "KRA_KYA_DATA_FETCH_FAILED", "event": "KRA_PAN_CHECK_FAILED", "actionKey": "failureStateAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "event": "KRA_DATA_FETCH_SUCCEEDED", "actionKey": "successStateAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KRA_PAN_DATA_FETCH", "version": "V3", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKraKycDataFetchAction"}, {"source": "CREATED", "target": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "event": "SEND_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "actionKey": "visionKraDataFetchAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "event": "KRA_DATA_FETCH_SUCCEEDED", "actionKey": "kraKycDataFetchSuccessfulAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "KRA_KYA_DATA_FETCH_FAILED", "event": "KRA_DATA_FETCH_FAILED", "actionKey": "kraKycDataFetchFailedAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_FOR_KRA_KYC_DATA_FETCH_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KRA_KYA_DATA_FETCH_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KRA_KYA_DATA_FETCH_SUCCESSFUL", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "INSURER_KYC_SUBMISSION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createInsurerKycSubmissionAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "INSURER_KYC_SUBMITTED", "actionKey": "pseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "SUCCESS", "event": "INSURER_KYC_SUCCESS", "actionKey": "successStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "FAILURE", "event": "INSURER_KYC_FAILURE", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KRA_STATUS_CHECK", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKraStatusCheckAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "KRA_STATUS_FETCH_SCHEDULE_CLOCKWORK", "actionKey": "kycStatusPseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "kycStatusPseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "UPDATE_KYC_STATUS", "actionKey": "updateKycStatusStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "SUCCESS", "event": "COMPLETE_KYC_STATUS_CHECK", "actionKey": "kycStatusSuccessAction"}, {"source": "PSEUDO_SUCCESS", "target": "FAILURE", "event": "FAIL_KYC_STATUS_CHECK", "actionKey": "kycStatusFailureAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "CLIENT_KYC_VERIFICATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createClientKycVerificationAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "TRIGGER_CLIENT_KYC_VERIFICATION", "actionKey": "triggerClientKycVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "SUCCESS", "event": "CLIENT_KYC_VERIFICATION_SUCCESS", "actionKey": "clientKycVerificationSuccessAction"}, {"source": "PSEUDO_SUCCESS", "target": "FAILURE", "event": "CLIENT_KYC_VERIFICATION_FAILURE", "actionKey": "clientKycVerificationFailureAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "CLIENT_KYC_VERIFICATION", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createClientKycVerificationAction"}, {"source": "CREATED", "target": "IN_PROGRESS", "event": "TRIGGER_CLIENT_KYC_VERIFICATION", "actionKey": "triggerClientKycVerificationActionV2"}, {"source": "IN_PROGRESS", "target": "PENDING", "event": "CLIENT_KYC_PENDING", "actionKey": "clientKycVerificationInterimAction"}, {"source": "IN_PROGRESS", "target": "SUCCESS", "event": "CLIENT_KYC_SUCCESS", "actionKey": "successStateAction"}, {"source": "IN_PROGRESS", "target": "FAILURE", "event": "CLIENT_KYC_FAILURE", "actionKey": "failureStateAction"}, {"source": "PENDING", "target": "SUCCESS", "event": "CLIENT_KYC_SUCCESS", "actionKey": "successStateAction"}, {"source": "PENDING", "target": "FAILURE", "event": "CLIENT_KYC_FAILURE", "actionKey": "failureStateAction"}, {"source": "IN_PROGRESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENDING", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "IN_PROGRESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENDING", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "IN_PROGRESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENDING", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "ACCOUNT_MIGRATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createAccountMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_SUCCEEDED", "event": "MIGRATE_ACCOUNT", "actionKey": "completeAccountMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_FAILED", "event": "FAIL_MIGRATE_ACCOUNT", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "VIDEO_VERIFICATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createVideoVerificationAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "VIDEO_VERIFICATION_COMPLETED", "event": "COMPLETE_VIDEO_VERIFICATION", "actionKey": "completeVideoVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "VIDEO_VERIFICATION_FAILED", "event": "FAIL_VIDEO_VERIFICATION", "actionKey": "failVideoVerificationAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "VIDEO_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "VIDEO_VERIFICATION_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "VIDEO_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "VIDEO_VERIFICATION_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "VIDEO_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "VIDEO_VERIFICATION_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "VIDEO_VERIFICATION_FAILED", "event": "FAIL_VIDEO_VERIFICATION", "actionKey": "failVideoVerificationAction"}, {"source": "CREATED", "target": "VIDEO_VERIFICATION_COMPLETED", "event": "COMPLETE_VIDEO_VERIFICATION", "actionKey": "completeVideoVerificationAction"}]}, {"actionType": "PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createPersistKeyValuePairsFromApiCallAction"}, {"source": "CREATED", "target": "PERSISTED", "event": "PERSIST_METADATA", "actionKey": "completePersistKeyValuePairsFromApiCallAction"}, {"source": "CREATED", "target": "FAILURE", "event": "API_CALL_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PERSISTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PERSISTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PERSISTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KYC_DATA_SUBMISSION_WITH_SUBMIT_STATUS_CHECK", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKycDataSubmissionAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "CREATED", "target": "KYC_DATA_SENT_TO_VISION", "event": "SEND_KYC_DATA_TO_VISION", "actionKey": "submitKycDataAction"}, {"source": "PSEUDO_SUCCESS", "target": "KYC_DATA_SENT_TO_VISION", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "submitKycDataAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "KYC_DATA_SUBMITTED", "event": "KYC_DATA_SUCCESSFULLY_SUBMITTED_BY_VISION", "actionKey": "kycSubmitStatusCheckAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "KYC_DATA_SUBMISSION_FAILED", "event": "KYC_DATA_SUBMISSION_FAILED_BY_VISION", "actionKey": "failureStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "SUCCESS", "event": "KYC_SUBMIT_STATUS_SUCCESS", "actionKey": "successStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "FAILURE", "event": "KYC_SUBMIT_STATUS_FAILED", "actionKey": "failureStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KYC_DATA_SUBMISSION_WITH_SUBMIT_STATUS_CHECK", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKycDataSubmissionAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "CREATED", "target": "KYC_DATA_SENT_TO_VISION", "event": "SEND_KYC_DATA_TO_VISION", "actionKey": "submitKycDataAction"}, {"source": "PSEUDO_SUCCESS", "target": "KYC_DATA_SENT_TO_VISION", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "submitKycDataAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "KYC_DATA_SUBMITTED", "event": "KYC_DATA_SUCCESSFULLY_SUBMITTED_BY_VISION", "actionKey": "kycSubmitStatusCheckAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "KYC_DATA_SUBMISSION_FAILED", "event": "KYC_DATA_SUBMISSION_FAILED_BY_VISION", "actionKey": "failureStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "SUCCESS", "event": "KYC_SUBMIT_STATUS_SUCCESS", "actionKey": "successStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "FAILURE", "event": "KYC_SUBMIT_STATUS_FAILED", "actionKey": "kycSubmitStatusCheckFailureAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KYC_DATA_SUBMISSION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKycDataSubmissionAction"}, {"source": "CREATED", "target": "KYC_DATA_SENT_TO_VISION", "event": "SEND_KYC_DATA_TO_VISION", "actionKey": "submitKycDataAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "KYC_DATA_SUBMITTED", "event": "KYC_DATA_SUCCESSFULLY_SUBMITTED_BY_VISION", "actionKey": "successStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "KYC_DATA_SUBMISSION_FAILED", "event": "KYC_DATA_SUBMISSION_FAILED_BY_VISION", "actionKey": "failureStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "DIGILOCKER_DOCUMENT_FETCH", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createDocumentFetchV2Action"}, {"source": "CREATED", "target": "DOCUMENT_FETCH_COMPLETED", "event": "COMPLETE_DOCUMENT_FETCH", "actionKey": "completeDocumentFetchAction"}, {"source": "CREATED", "target": "DOCUMENT_FETCH_FAILED", "event": "FAIL_DOCUMENT_FETCH", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DOCUMENT_FETCH_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DOCUMENT_FETCH_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DOCUMENT_FETCH_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DOCUMENT_FETCH_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DOCUMENT_FETCH_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DOCUMENT_FETCH_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "DIGILOCKER_DOCUMENT_FETCH", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createDocumentFetchAction"}, {"source": "CREATED", "target": "DOCUMENT_FETCH_COMPLETED", "event": "COMPLETE_DOCUMENT_FETCH", "actionKey": "completeDocumentFetchAction"}, {"source": "CREATED", "target": "DOCUMENT_FETCH_FAILED", "event": "FAIL_DOCUMENT_FETCH", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DOCUMENT_FETCH_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DOCUMENT_FETCH_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DOCUMENT_FETCH_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DOCUMENT_FETCH_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DOCUMENT_FETCH_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DOCUMENT_FETCH_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "SOURCE_VERIFICATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createSourceVerificationAction"}, {"source": "CREATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "CREATED", "target": "SENT_FOR_SOURCE_VERIFICATION_TO_VISION", "event": "SEND_FOR_SOURCE_VERIFICATION_TO_VISION", "actionKey": "visionSourceVerificationAction"}, {"source": "SENT_FOR_SOURCE_VERIFICATION_TO_VISION", "target": "AUTO_APPROVED", "event": "AUTO_APPROVED_BY_VISION", "actionKey": "sourceVerificationAutoApprovedAction"}, {"source": "SENT_FOR_SOURCE_VERIFICATION_TO_VISION", "target": "AUTO_REJECTED", "event": "AUTO_REJECTED_BY_VISION", "actionKey": "sourceVerificationAutoRejectedAction"}, {"source": "CREATED", "target": "NO_DOCUMENT_AVAILABLE_ACCEPTED", "event": "ACCEPT_NO_DOCUMENT_AVAILABLE", "actionKey": "noDocumentAvailableAcceptedSourceVerificationAction"}, {"source": "CREATED", "target": "OTP_HURDLE_TRIGGERED", "event": "TRIGGER_OTP_HURDLE", "actionKey": "triggerOtpHurdleSourceVerificationAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "NO_DOCUMENT_AVAILABLE_ACCEPTED", "event": "OTP_HURDLE_COMPLETE", "actionKey": "noDocumentAvailableAcceptedSourceVerificationAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_GENERATION_FAILED", "event": "FAIL_OTP_GENERATION", "actionKey": "failureStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_VERIFICATION_FAILED", "event": "FAIL_OTP_VERIFICATION", "actionKey": "failureStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "AUTO_APPROVED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "AUTO_REJECTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "NO_DOCUMENT_AVAILABLE_ACCEPTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_FOR_SOURCE_VERIFICATION_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "AUTO_APPROVED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "AUTO_REJECTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "NO_DOCUMENT_AVAILABLE_ACCEPTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_FOR_SOURCE_VERIFICATION_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "AUTO_APPROVED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "AUTO_REJECTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "NO_DOCUMENT_AVAILABLE_ACCEPTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_FOR_SOURCE_VERIFICATION_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "FACE_MATCH", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createFaceMatchAction"}, {"source": "CREATED", "target": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "event": "SEND_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "actionKey": "updateStateAction"}, {"source": "CREATED", "target": "SENT_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "event": "SEND_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "actionKey": "failureStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "SUCCESS", "event": "FACE_MATCH_SUCCEEDED", "actionKey": "faceMatchSuccessAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "FAILURE", "event": "FACE_MATCH_FAILED", "actionKey": "faceMatchFailureAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "USER_DOCUMENT_BINDING", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createUserDocumentBindingAction"}, {"source": "CREATED", "target": "COMPLETED", "event": "USER_DOCUMENT_BINDING_SUCCESS", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "FAILED", "event": "USER_DOCUMENT_BINDING_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "MANUAL_VERIFICATION_V2", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createManualVerificationV2Action"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "SEND_FOR_MANUAL_VERIFICATION_TO_DICTAT0R", "actionKey": "dictat0rManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "TRIGGER_FALLBACK", "actionKey": "dictat0rManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "dictat0rManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "DEPENDENT_ACTION_FAILED", "event": "PARENT_PSEUDO_ACTION_FAILED", "actionKey": "failureStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "NOT_EXECUTED", "event": "EXECUTION_NOT_REQUIRED", "actionKey": "successStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "MANUAL_VERIFICATION_SUCCESS", "event": "SUCCEED_MANUAL_VERIFICATION", "actionKey": "manualVerificationV2SuccessAction"}, {"source": "PSEUDO_SUCCESS", "target": "MANUAL_VERIFICATION_FAILED", "event": "FAIL_MANUAL_VERIFICATION", "actionKey": "manualVerificationV2FailureAction"}, {"source": "PSEUDO_SUCCESS", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "manualVerificationV2FailureAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "NOT_EXECUTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "NOT_EXECUTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "NOT_EXECUTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "FACE_MATCH_V2", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createFaceMatchActionV2"}, {"source": "CREATED", "target": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "event": "SEND_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "actionKey": "updateStateAction"}, {"source": "CREATED", "target": "SENT_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "event": "SEND_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "actionKey": "failureStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "SUCCESS", "event": "FACE_MATCH_SUCCEEDED", "actionKey": "faceMatchSuccessAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "FAILURE", "event": "FACE_MATCH_FAILED", "actionKey": "faceMatchFailureAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_FACE_MATCH_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PAN_MIGRATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createPanMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_SUCCEEDED", "event": "MIGRATE_PAN", "actionKey": "completePanMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_FAILED", "event": "FAIL_MIGRATE_PAN", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "DOCUMENT_OCR", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createDocumentOCRAction"}, {"source": "CREATED", "target": "DOC_SENT_FOR_OCR_TO_VISION", "event": "SEND_DOC_FOR_OCR_TO_VISION", "actionKey": "sendDocumentForOCR"}, {"source": "DOC_SENT_FOR_OCR_TO_VISION", "target": "OCR_DATA_SAVE_COMPLETED", "event": "SAVE_VISION_OCR_DATA", "actionKey": "completeOCRDataSave"}, {"source": "DOC_SENT_FOR_OCR_TO_VISION", "target": "DOCUMENT_OCR_FAILED", "event": "FAIL_DOCUMENT_OCR", "actionKey": "failureStateAction"}, {"source": "DOCUMENT_OCR_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DOC_SENT_FOR_OCR_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OCR_DATA_SAVE_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DOCUMENT_OCR_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DOC_SENT_FOR_OCR_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OCR_DATA_SAVE_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DOCUMENT_OCR_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DOC_SENT_FOR_OCR_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OCR_DATA_SAVE_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "OCR_ACTION_V2", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createOCRAction"}, {"source": "CREATED", "target": "SENT_TO_DRISHTI_FOR_OCR_SUCCEEDED", "event": "SEND_TO_DRISHTI_FOR_OCR_SUCCEEDED", "actionKey": "updateStateAction"}, {"source": "CREATED", "target": "SENT_TO_DRISHTI_FOR_OCR_FAILED", "event": "SEND_TO_DRISHTI_FOR_OCR_FAILED", "actionKey": "failureStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_OCR_SUCCEEDED", "target": "SUCCESS", "event": "OCR_SUCCEEDED", "actionKey": "ocrSuccessAction"}, {"source": "SENT_TO_DRISHTI_FOR_OCR_SUCCEEDED", "target": "FAILURE", "event": "OCR_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_OCR_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_OCR_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_OCR_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_OCR_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_OCR_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_OCR_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "AOF_GENERATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createAofGenerationAction"}, {"source": "CREATED", "target": "SENT_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED", "event": "SEND_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED", "actionKey": "updateStateAction"}, {"source": "CREATED", "target": "SENT_TO_VISION_FOR_AOF_GENERATION_FAILED", "event": "SEND_TO_VISION_FOR_AOF_GENERATION_FAILED", "actionKey": "failureStateAction"}, {"source": "SENT_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED", "target": "SUCCESS", "event": "AOF_GENERATION_SUCCESS", "actionKey": "successStateAction"}, {"source": "SENT_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED", "target": "FAILURE", "event": "AOF_GENERATION_FAILURE", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_VISION_FOR_AOF_GENERATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_VISION_FOR_AOF_GENERATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_VISION_FOR_AOF_GENERATION_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_VISION_FOR_AOF_GENERATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "AADHAAR_VERIFICATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createAadhaarVerificationAction"}, {"source": "CREATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "CREATED", "target": "OTP_HURDLE_TRIGGERED", "event": "TRIGGER_OTP_HURDLE", "actionKey": "triggerOtpHurdleAadhaarVerificationAction"}, {"source": "CREATED", "target": "OTP_GENERATION_FAILED", "event": "FAIL_OTP_GENERATION", "actionKey": "failureStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "AADHAAR_VERIFICATION_SUCCEED", "event": "AADHAAR_VERIFICATION_SUCCEEDED", "actionKey": "aadhaarVerificationSuccessAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "AADHAAR_VERIFICATION_FAILURE", "event": "AADHAAR_VERIFICATION_FAILED", "actionKey": "failureStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_GENERATION_FAILED", "event": "RESEND_OTP_GENERATION_FAILED", "actionKey": "failureStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "AADHAAR_VERIFICATION_SUCCEED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "AADHAAR_VERIFICATION_FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "AADHAAR_VERIFICATION_SUCCEED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "AADHAAR_VERIFICATION_FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "AADHAAR_VERIFICATION_SUCCEED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "AADHAAR_VERIFICATION_FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "MO_PAN_MIGRATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createMoPanMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_SUCCEEDED", "event": "MIGRATE_PAN", "actionKey": "completePanMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_FAILED", "event": "FAIL_MIGRATE_PAN", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KYC_ESIGN_DOCUMENT_FETCH", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKycEsignDocumentFetchAction"}, {"source": "CREATED", "target": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "event": "SEND_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "actionKey": "kycEsignDocumentFetchAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "SUCCESS", "event": "KYC_ESIGN_DOCUMENT_SUCCESSFULLY_FETCHED", "actionKey": "successStateAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "FAILED", "event": "KYC_ESIGN_DOCUMENT_FETCH_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KYC_ESIGN_DOCUMENT_FETCH", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKycEsignDocumentFetchAction"}, {"source": "CREATED", "target": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "event": "SEND_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "actionKey": "kycEsignDocumentFetchAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "ESIGNED_DOCUMENT_FETCH_SUCCESS", "event": "KYC_ESIGN_DOCUMENT_SUCCESSFULLY_FETCHED", "actionKey": "eSignedDocumentPersistSuccessAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "FAILED", "event": "KYC_ESIGN_DOCUMENT_FETCH_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "ESIGNED_DOCUMENT_FETCH_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "ESIGNED_DOCUMENT_FETCH_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "ESIGNED_DOCUMENT_FETCH_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_VISION_FOR_ESIGN_DOCUMENT_FETCH", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "MANUAL_VERIFICATION", "version": "V2_TRIGGER_ON_DEPENDENT_FAILURE", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createManualVerificationAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "SEND_FOR_MANUAL_VERIFICATION_TO_SALESFORCE", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "TRIGGER_FALLBACK", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "NOT_EXECUTED", "event": "EXECUTION_NOT_REQUIRED", "actionKey": "successStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "MANUAL_VERIFICATION_SUCCESS", "event": "SUCCEED_MANUAL_VERIFICATION", "actionKey": "manualVerificationSuccessAction"}, {"source": "PSEUDO_SUCCESS", "target": "MANUAL_VERIFICATION_FAILED", "event": "FAIL_MANUAL_VERIFICATION", "actionKey": "manualVerificationFailureAction"}, {"source": "PSEUDO_SUCCESS", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "manualVerificationFailureAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "PARENT_PSEUDO_ACTION_FAILED", "actionKey": "salesforceManualVerificationAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "NOT_EXECUTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "NOT_EXECUTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "NOT_EXECUTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "HOPE_RULE_VALIDATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createHopeRuleValidationAction"}, {"source": "CREATED", "target": "HOPE_RULE_VALIDATION_SUCCEEDED", "event": "SUCCEED_HOPE_RULE_VALIDATION", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "HOPE_RULE_VALIDATION_FAILED", "event": "FAIL_HOPE_RULE_VALIDATION", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "HOPE_RULE_VALIDATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "HOPE_RULE_VALIDATION_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "HOPE_RULE_VALIDATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "HOPE_RULE_VALIDATION_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "HOPE_RULE_VALIDATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "HOPE_RULE_VALIDATION_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "ACCOUNT_VERIFICATION", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createAccountVerificationAction"}, {"source": "CREATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "CREATED", "target": "INPUT_ACCOUNT_VALIDATED", "event": "INPUT_ACCOUNT_VALIDATION_COMPLETED", "actionKey": "inputAccountValidationAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "PENNY_DROP_INITIATED", "event": "INITIATE_PENNY_DROP", "actionKey": "initiatePennyDropAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "CREATED", "target": "PENNY_DROP_INITIATED", "event": "INITIATE_PENNY_DROP", "actionKey": "initiatePennyDropAction"}, {"source": "PENNY_DROP_INITIATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PENNY_DROP_FAILED", "event": "PENNY_DROP_FAILED", "actionKey": "failureStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PENNY_DROP_COMPLETED", "event": "PENNY_DROP_COMPLETED", "actionKey": "accountVerificationCompletedAction"}, {"source": "CREATED", "target": "INPUT_ACCOUNT_VALIDATION_FAILED", "event": "INPUT_ACCOUNT_VALIDATION_FAILED", "actionKey": "failureStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "COMPLETED", "event": "ACCOUNT_VALIDATED", "actionKey": "successStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PENNY_DROP_FAILED", "event": "PENNY_DROP_FAILED", "actionKey": "failureStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PENNY_DROP_COMPLETED", "event": "PENNY_DROP_COMPLETED", "actionKey": "accountVerificationCompletedAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "ACCOUNT_VERIFICATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createAccountVerificationAction"}, {"source": "CREATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "CREATED", "target": "INPUT_ACCOUNT_VALIDATED", "event": "INPUT_ACCOUNT_VALIDATION_COMPLETED", "actionKey": "inputAccountValidationAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "PENNY_DROP_INITIATED", "event": "INITIATE_PENNY_DROP", "actionKey": "initiatePennyDropAction"}, {"source": "CREATED", "target": "PENNY_DROP_INITIATED", "event": "INITIATE_PENNY_DROP", "actionKey": "initiatePennyDropAction"}, {"source": "PENNY_DROP_INITIATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PENNY_DROP_FAILED", "event": "PENNY_DROP_FAILED", "actionKey": "failureStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PENNY_DROP_COMPLETED", "event": "PENNY_DROP_COMPLETED", "actionKey": "accountVerificationCompletedAction"}, {"source": "CREATED", "target": "INPUT_ACCOUNT_VALIDATION_FAILED", "event": "INPUT_ACCOUNT_VALIDATION_FAILED", "actionKey": "failureStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "COMPLETED", "event": "ACCOUNT_VALIDATED", "actionKey": "successStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PENNY_DROP_FAILED", "event": "PENNY_DROP_FAILED", "actionKey": "failureStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PENNY_DROP_COMPLETED", "event": "PENNY_DROP_COMPLETED", "actionKey": "accountVerificationCompletedAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "INPUT_ACCOUNT_VALIDATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "CONFIRMATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createConfirmationAction"}, {"source": "CREATED", "target": "CONFIRMATION_SENT", "event": "SEND_FOR_CONFIRMATION", "actionKey": "sendForConfirmationAction"}, {"source": "CONFIRMATION_SENT", "target": "SUCCESS", "event": "CONFIRMED", "actionKey": "successStateAction"}, {"source": "CONFIRMATION_SENT", "target": "FAILURE", "event": "FAILURE", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CONFIRMATION_SENT", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CONFIRMATION_SENT", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CONFIRMATION_SENT", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KRA_PAN_STATUS_FETCH", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKraStatusFetchAction"}, {"source": "CREATED", "target": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "event": "SEND_FOR_KRA_STATUS_FETCH_TO_VISION", "actionKey": "visionKraStatusFetchAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "COMPLETED", "event": "KRA_FETCH_PAN_STATUS_SUCCEEDED", "actionKey": "completeKraStatusFetchAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PAN_MIGRATION_V2", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createPanMigrationV2Action"}, {"source": "CREATED", "target": "MIGRATION_FAILED", "event": "FAIL_MIGRATION", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "MIGRATION_SUCCEEDED", "event": "MIGRATION_SUCCESS", "actionKey": "completePanMigrationV2Action"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PERSIST_KEY_VALUE_PAIRS", "version": "V_INITIAL_ACTION_FAILURE", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createFailureDummyInitialAction"}, {"source": "CREATED", "target": "COMPLETED", "event": "FAIL_DUMMY_INITIAL_ACTION", "actionKey": "failureDummyInitialAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KYC_STATUS_CHECK", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKycStatusCheckAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "kycStatusPseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "UPDATE_KYC_STATUS", "actionKey": "updateKycStatusStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "SUCCESS", "event": "COMPLETE_KYC_STATUS_CHECK", "actionKey": "kycStatusSuccessAction"}, {"source": "PSEUDO_SUCCESS", "target": "FAILURE", "event": "FAIL_KYC_STATUS_CHECK", "actionKey": "kycStatusFailureAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "OTP_HURDLE_V2", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createOtpHurdleAction"}, {"source": "CREATED", "target": "OTP_HURDLE_TRIGGERED", "event": "TRIGGER_OTP_HURDLE", "actionKey": "triggerOtpHurdleV2Action"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_HURDLE_COMPLETED", "event": "COMPLETE_OTP_HURDLE", "actionKey": "successStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_GENERATION_FAILED", "event": "FAIL_OTP_GENERATION", "actionKey": "failureStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "OTP_VERIFICATION_FAILED", "event": "FAIL_OTP_VERIFICATION", "actionKey": "failureStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "OTP_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_GENERATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_HURDLE_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "OTP_HURDLE_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KRA_PAN_STATUS_FETCH", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKraStatusFetchAction"}, {"source": "CREATED", "target": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "event": "SEND_FOR_KRA_STATUS_FETCH_TO_VISION", "actionKey": "visionKraStatusFetchAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "COMPLETED", "event": "KRA_FETCH_PAN_STATUS_SUCCEEDED", "actionKey": "completeKraStatusFetchAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "FAILED", "event": "KRA_PAN_CHECK_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_FOR_KRA_STATUS_FETCH_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "LIVENESS_CHECK", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createLivenessCheckAction"}, {"source": "CREATED", "target": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED", "event": "SEND_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED", "actionKey": "updateStateAction"}, {"source": "CREATED", "target": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_FAILED", "event": "SEND_TO_DRISHTI_FOR_LIVENESS_CHECK_FAILED", "actionKey": "failureStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED", "target": "SUCCESS", "event": "LIVENESS_CHECK_SUCCEEDED", "actionKey": "livenessCheckSuccessAction"}, {"source": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED", "target": "FAILURE", "event": "LIVENESS_CHECK_FAILED", "actionKey": "livenessCheckFailureAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SENT_TO_DRISHTI_FOR_LIVENESS_CHECK_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "FETCH_RENT_PAY_RECEIVER_DETAILS_FROM_NEXUS", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createFetchRentPayReceiverDetailsFromNexus"}, {"source": "CREATED", "target": "SUCCESS", "event": "RENT_PAY_RECEIVER_DETAILS_FETCH_SUCCEED", "actionKey": "fetchRentPayReceiverDetailsFromNexusSuccessAction"}, {"source": "CREATED", "target": "FAILURE", "event": "RENT_PAY_RECEIVER_DETAILS_FETCH_FAILED", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PERSIST_KEY_VALUE_PAIRS", "version": "V_INITIAL_ACTION_SUCCESS", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createSuccessDummyInitialAction"}, {"source": "CREATED", "target": "COMPLETED", "event": "SUCCEED_DUMMY_INITIAL_ACTION", "actionKey": "successDummyInitialAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "SELFIE_HURDLE", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createSelfieHurdleAction"}, {"source": "CREATED", "target": "SELFIE_HURDLE_TRIGGERED", "event": "TRIGGER_SELFIE_HURDLE", "actionKey": "triggerSelfieHurdleAction"}, {"source": "SELFIE_HURDLE_TRIGGERED", "target": "SELFIE_HURDLE_COMPLETED", "event": "SELFIE_HURDLE_COMPLETE", "actionKey": "selfieHurdleSuccessAction"}, {"source": "SELFIE_HURDLE_TRIGGERED", "target": "SELFIE_HURDLE_FAILED", "event": "SELFIE_HURDLE_FAIL", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SELFIE_HURDLE_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SELFIE_HURDLE_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SELFIE_HURDLE_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SELFIE_HURDLE_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SELFIE_HURDLE_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SELFIE_HURDLE_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SELFIE_HURDLE_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SELFIE_HURDLE_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SELFIE_HURDLE_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "SCHEDULE_WORKFLOW_ABORT", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createScheduleWorkflowAbortAction"}, {"source": "CREATED", "target": "SCHEDULE_CREATION_SUCCESSFUL", "event": "SCHEDULE_WORKFLOW_ABORT_SUCCESS", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "SCHEDULE_CREATION_FAILED", "event": "SCHEDULE_WORKFLOW_ABORT_FAILURE", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SCHEDULE_CREATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SCHEDULE_CREATION_SUCCESSFUL", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SCHEDULE_CREATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SCHEDULE_CREATION_SUCCESSFUL", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SCHEDULE_CREATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SCHEDULE_CREATION_SUCCESSFUL", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "COMPARATOR", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createComparatorAction"}, {"source": "CREATED", "target": "IN_PROGRESS", "event": "PROCESS_COMPARATOR", "actionKey": "processComparatorAction"}, {"source": "IN_PROGRESS", "target": "SUCCESS", "event": "TEXT_COMPARISON_SUCCEEDED", "actionKey": "textComparatorSuccessAction"}, {"source": "IN_PROGRESS", "target": "FAILURE", "event": "TEXT_COMPARISON_FAILED", "actionKey": "textComparatorFailureAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "IN_PROGRESS", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "processComparatorAction"}, {"source": "PSEUDO_SUCCESS", "target": "DEPENDENT_ACTION_FAILED", "event": "PARENT_PSEUDO_ACTION_FAILED", "actionKey": "failureStateAction"}, {"source": "IN_PROGRESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "IN_PROGRESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "IN_PROGRESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "COMPARATOR", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createComparatorAction"}, {"source": "CREATED", "target": "IN_PROGRESS", "event": "PROCESS_COMPARATOR", "actionKey": "processComparatorAction"}, {"source": "IN_PROGRESS", "target": "SUCCESS", "event": "TEXT_COMPARISON_SUCCEEDED", "actionKey": "textComparatorSuccessAction"}, {"source": "IN_PROGRESS", "target": "FAILURE", "event": "TEXT_COMPARISON_FAILED", "actionKey": "textComparatorFailureAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "IN_PROGRESS", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "processComparatorAction"}, {"source": "PSEUDO_SUCCESS", "target": "DEPENDENT_ACTION_FAILED", "event": "PARENT_PSEUDO_ACTION_FAILED", "actionKey": "failureStateAction"}, {"source": "IN_PROGRESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "IN_PROGRESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "IN_PROGRESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PENNY_DROP", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createPennyDropAction"}, {"source": "CREATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "CREATED", "target": "PENNY_DROP_INITIATED", "event": "INITIATE_PENNY_DROP", "actionKey": "initiatePennyDropAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PENNY_DROP_FAILED", "event": "PENNY_DROP_FAILED", "actionKey": "failureStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PENNY_DROP_COMPLETED", "event": "PENNY_DROP_COMPLETED", "actionKey": "accountVerificationCompletedAction"}, {"source": "PENNY_DROP_INITIATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PENNY_DROP_FAILED", "event": "PENNY_DROP_FAILED", "actionKey": "failureStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "PENNY_DROP_COMPLETED", "event": "PENNY_DROP_COMPLETED", "actionKey": "accountVerificationCompletedAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_INITIATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PENNY_DROP_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "REDIRECTION", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createRedirectionAction"}, {"source": "CREATED", "target": "REDIRECTION_URL_GENERATION_COMPLETED", "event": "COMPLETE_REDIRECTION_URL_GENERATION", "actionKey": "completeRedirectionUrlGenerationAction"}, {"source": "CREATED", "target": "REDIRECTION_URL_GENERATION_FAILED", "event": "FAIL_REDIRECTION_URL_GENERATION", "actionKey": "failureStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "REDIRECTION_HURDLE_TRIGGERED", "event": "COMPLETE_TRIGGER_REDIRECTION_HURDLE", "actionKey": "updateStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "REDIRECTION_HURDLE_TRIGGER_FAILED", "event": "FAIL_TRIGGER_REDIRECTION_HURDLE", "actionKey": "failureStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "REDIRECTION_HURDLE_COMPLETED", "event": "COMPLETE_REDIRECTION_HURDLE", "actionKey": "completeRedirectionAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "REDIRECTION_HURDLE_FAILED", "event": "FAIL_REDIRECTION_HURDLE", "actionKey": "failureStateAction"}, {"source": "REDIRECTION_HURDLE_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGER_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_HURDLE_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_URL_GENERATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_HURDLE_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGER_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_HURDLE_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_URL_GENERATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_HURDLE_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGER_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_HURDLE_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_URL_GENERATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "REDIRECTION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createRedirectionAction"}, {"source": "CREATED", "target": "REDIRECTION_URL_GENERATION_COMPLETED", "event": "COMPLETE_REDIRECTION_URL_GENERATION", "actionKey": "completeRedirectionUrlGenerationAction"}, {"source": "CREATED", "target": "REDIRECTION_URL_GENERATION_FAILED", "event": "FAIL_REDIRECTION_URL_GENERATION", "actionKey": "failureStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "REDIRECTION_HURDLE_TRIGGERED", "event": "COMPLETE_TRIGGER_REDIRECTION_HURDLE", "actionKey": "updateStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "REDIRECTION_HURDLE_TRIGGER_FAILED", "event": "FAIL_TRIGGER_REDIRECTION_HURDLE", "actionKey": "failureStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "REDIRECTION_HURDLE_COMPLETED", "event": "COMPLETE_REDIRECTION_HURDLE", "actionKey": "successStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "REDIRECTION_HURDLE_FAILED", "event": "FAIL_REDIRECTION_HURDLE", "actionKey": "failureStateAction"}, {"source": "REDIRECTION_HURDLE_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGER_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_HURDLE_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_URL_GENERATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "REDIRECTION_HURDLE_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGER_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_HURDLE_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_URL_GENERATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "REDIRECTION_HURDLE_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_URL_GENERATION_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGER_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_HURDLE_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_HURDLE_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "REDIRECTION_URL_GENERATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "CONSENT", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createConsentAction"}, {"source": "CREATED", "target": "COMPLETED", "event": "COMPLETE_CONSENT", "actionKey": "completeConsentAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PERSIST_KEY_VALUE_PAIRS_V2", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createPersistKeyValuePairsV2Action"}, {"source": "CREATED", "target": "PERSISTED", "event": "PERSIST_METADATA", "actionKey": "completePersistKeyValuePairsV2Action"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PERSISTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PERSISTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PERSISTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "GST_MIGRATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createGstMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_FAILED", "event": "FAIL_MIGRATION", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "MIGRATION_SUCCEEDED", "event": "MIGRATION_SUCCESS", "actionKey": "completeGstMigrationAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PERSIST_FORENSICS", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createPersistForensicsAction"}, {"source": "CREATED", "target": "FAILED", "event": "FAIL_FORENSICS", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "PERSISTED", "event": "PERSIST_FORENSICS_DATA", "actionKey": "completePersistForensicsAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PERSISTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PERSISTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PERSISTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PERSIST_FORENSICS", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createPersistForensicsActionV2"}, {"source": "CREATED", "target": "FAILED", "event": "FAIL_FORENSICS", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "PERSISTED", "event": "PERSIST_FORENSICS_DATA", "actionKey": "completePersistForensicsAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PERSISTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PERSISTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PERSISTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "KRA_DATA_SUBMISSION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createKycDataSubmissionAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "CREATED", "target": "KYC_DATA_SENT_TO_VISION", "event": "SEND_KYC_DATA_TO_VISION", "actionKey": "submitKycDataAction"}, {"source": "PSEUDO_SUCCESS", "target": "KYC_DATA_SENT_TO_VISION", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "submitKycDataAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "KYC_DATA_SUBMITTED", "event": "KYC_DATA_SUCCESSFULLY_SUBMITTED_BY_VISION", "actionKey": "successStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "KYC_DATA_SUBMISSION_FAILED", "event": "KYC_DATA_SUBMISSION_FAILED_BY_VISION", "actionKey": "failureStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "KYC_DATA_SUBMITTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KYC_DATA_SENT_TO_VISION", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "KYC_DATA_SUBMISSION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "UPI_ACCOUNT_FETCH", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createUpiAccountFetchAction"}, {"source": "CREATED", "target": "CREATED", "event": "FRA_ACCOUNT_CHECK", "actionKey": "fraAccountCheckAction"}, {"source": "CREATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "CREATED", "target": "COMPLETED", "event": "FETCH_UPI_ACCOUNT", "actionKey": "completeUpiAccountFetchAction"}, {"source": "CREATED", "target": "COMPLETED", "event": "FETCH_UPI_ACCOUNT", "actionKey": "completeUpiAccountFetchAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "UPI_ACCOUNT_FETCH", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createUpiAccountFetchAction"}, {"source": "CREATED", "target": "CREATED", "event": "FRA_ACCOUNT_CHECK", "actionKey": "fraAccountCheckAction"}, {"source": "CREATED", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "fraudBlockedAction"}, {"source": "CREATED", "target": "COMPLETED", "event": "FETCH_UPI_ACCOUNT", "actionKey": "completeUpiAccountFetchV2Action"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "API_CALL_ACTION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createEntryAction"}, {"source": "CREATED", "target": "SUCCESS", "event": "COMPLETE", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "ONE_TO_MANY_COMPARATOR", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createComparatorAction"}, {"source": "CREATED", "target": "IN_PROGRESS", "event": "PROCESS_COMPARATOR", "actionKey": "processOneToManyComparatorAction"}, {"source": "IN_PROGRESS", "target": "SUCCESS", "event": "TEXT_COMPARISON_SUCCEEDED", "actionKey": "oneToManyTextComparatorSuccessAction"}, {"source": "IN_PROGRESS", "target": "FAILURE", "event": "TEXT_COMPARISON_FAILED", "actionKey": "oneToManyTextComparatorFailureAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "IN_PROGRESS", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "processOneToManyComparatorAction"}, {"source": "PSEUDO_SUCCESS", "target": "DEPENDENT_ACTION_FAILED", "event": "PARENT_PSEUDO_ACTION_FAILED", "actionKey": "failureStateAction"}, {"source": "IN_PROGRESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FAILURE", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "IN_PROGRESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FAILURE", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "IN_PROGRESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FAILURE", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "SEND_TERMINAL_ACTION_TO_SDK", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createSendTerminalActionToSdkAction"}, {"source": "CREATED", "target": "SUCCESS", "event": "SEND_TERMINAL_ACTION_TO_SDK", "actionKey": "completeSendTerminalActionToSdkAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "SMS_CONSENT", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createSmsConsentAction"}, {"source": "CREATED", "target": "SMS_CONSENT_TRIGGERED", "event": "TRIGGER_SMS_CONSENT", "actionKey": "triggerSmsConsentAction"}, {"source": "SMS_CONSENT_TRIGGERED", "target": "SMS_CONSENT_COMPLETED", "event": "COMPLETE_SMS_CONSENT", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SMS_CONSENT_TRIGGERED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SMS_CONSENT_COMPLETED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SMS_CONSENT_TRIGGERED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SMS_CONSENT_COMPLETED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SMS_CONSENT_TRIGGERED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SMS_CONSENT_COMPLETED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "CKYC", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createCkycAction"}, {"source": "CREATED", "target": "SUCCESS", "event": "CKYC_SUCCESS", "actionKey": "ckycSuccessAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "TRIGGER_EVENT_ACTION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createTriggerEventAction"}, {"source": "CREATED", "target": "SUCCESS", "event": "SUCCESS", "actionKey": "successStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "MANUAL_VERIFICATION", "version": "V2", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createManualVerificationAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "SEND_FOR_MANUAL_VERIFICATION_TO_SALESFORCE", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "TRIGGER_FALLBACK", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "NOT_EXECUTED", "event": "EXECUTION_NOT_REQUIRED", "actionKey": "successStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "MANUAL_VERIFICATION_SUCCESS", "event": "SUCCEED_MANUAL_VERIFICATION", "actionKey": "manualVerificationSuccessAction"}, {"source": "PSEUDO_SUCCESS", "target": "MANUAL_VERIFICATION_FAILED", "event": "FAIL_MANUAL_VERIFICATION", "actionKey": "manualVerificationFailureAction"}, {"source": "PSEUDO_SUCCESS", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "manualVerificationFailureAction"}, {"source": "PSEUDO_SUCCESS", "target": "DEPENDENT_ACTION_FAILED", "event": "PARENT_PSEUDO_ACTION_FAILED", "actionKey": "failureStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "NOT_EXECUTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "NOT_EXECUTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "DEPENDENT_ACTION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "NOT_EXECUTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "VALHALLA_ACCOUNT_MIGRATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createValhallaAccountMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_SUCCEEDED", "event": "MIGRATE_ACCOUNT", "actionKey": "completeAccountMigrationAction"}, {"source": "CREATED", "target": "MIGRATION_FAILED", "event": "FAIL_MIGRATE_ACCOUNT", "actionKey": "failureStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MIGRATION_SUCCEEDED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "MANUAL_VERIFICATION", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createManualVerificationAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "MOVE_TO_PSEUDO_SUCCESS", "actionKey": "pseudoSuccessStateAction"}, {"source": "CREATED", "target": "PSEUDO_SUCCESS", "event": "SEND_FOR_MANUAL_VERIFICATION_TO_SALESFORCE", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "TRIGGER_FALLBACK", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "PSEUDO_SUCCESS", "event": "PARENT_PSEUDO_ACTION_SUCCEED", "actionKey": "salesforceManualVerificationAction"}, {"source": "PSEUDO_SUCCESS", "target": "NOT_EXECUTED", "event": "EXECUTION_NOT_REQUIRED", "actionKey": "successStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "MANUAL_VERIFICATION_SUCCESS", "event": "SUCCEED_MANUAL_VERIFICATION", "actionKey": "manualVerificationSuccessAction"}, {"source": "PSEUDO_SUCCESS", "target": "MANUAL_VERIFICATION_FAILED", "event": "FAIL_MANUAL_VERIFICATION", "actionKey": "manualVerificationFailureAction"}, {"source": "PSEUDO_SUCCESS", "target": "FRAUD_BLOCKED", "event": "FRAUD_BLOCKED", "actionKey": "manualVerificationFailureAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "FRAUD_BLOCKED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "NOT_EXECUTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "FRAUD_BLOCKED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "NOT_EXECUTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "MANUAL_VERIFICATION_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "FRAUD_BLOCKED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "NOT_EXECUTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PSEUDO_SUCCESS", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "MANUAL_VERIFICATION_FAILED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "PERSIST_KEY_VALUE_PAIRS", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createPersistKeyValuePairsAction"}, {"source": "CREATED", "target": "PERSISTED", "event": "PERSIST_METADATA", "actionKey": "completePersistKeyValuePairsAction"}, {"source": "CREATED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "PERSISTED", "target": "INVALIDATED", "event": "INVALIDATE_ACTION", "actionKey": "invalidateStateAction"}, {"source": "CREATED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "PERSISTED", "target": "ABORTED", "event": "ABORT_ACTION", "actionKey": "abortStateAction"}, {"source": "CREATED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}, {"source": "PERSISTED", "target": "DISCARDED", "event": "DISCARD_ACTION", "actionKey": "discardStateAction"}]}, {"actionType": "REVERSE_PENNY_DROP", "version": "V1", "stateMachineTransitions": [{"source": "CREATED", "target": "CREATED", "event": "CREATE_ENTRY", "actionKey": "createReversePennyDropAction"}, {"source": "CREATED", "target": "WAITING_FOR_INTENT_RESPONSE_FROM_GULLAK", "event": "INTENT_GENERATION_CALL_TO_GULLAK_SUCCEEDED", "actionKey": "intentGenerationSuccessReversePennyDropAction"}, {"source": "CREATED", "target": "FAILED", "event": "INTENT_GENERATION_CALL_FAILED", "actionKey": "failureStateAction"}, {"source": "WAITING_FOR_INTENT_RESPONSE_FROM_GULLAK", "target": "WAITING_FOR_PAYMENT_COMPLETION", "event": "TRIGGER_UI_RESPONSE", "actionKey": "triggerUiResponseReversePennyDropAction"}, {"source": "WAITING_FOR_INTENT_RESPONSE_FROM_GULLAK", "target": "FAILED", "event": "INTENT_GENERATION_FAILED", "actionKey": "failureStateAction"}, {"source": "WAITING_FOR_PAYMENT_COMPLETION", "target": "SUCCESS", "event": "PAYMENT_SUCCESS", "actionKey": "successStateAction"}, {"source": "WAITING_FOR_PAYMENT_COMPLETION", "target": "FAILED", "event": "PAYMENT_FAILURE", "actionKey": "failureStateAction"}]}]