<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.phonepe.verified</groupId>
        <artifactId>kaizen</artifactId>
        <version>0.0.6-stratos-SNAPSHOT</version>
    </parent>

    <artifactId>kaizen-models</artifactId>

    <properties>
        <sonar.coverage.exclusions>
            **/*
        </sonar.coverage.exclusions>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-util</artifactId>
            <version>${dropwizard.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>${validation.api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate.validator.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.shadow</groupId>
            <artifactId>shadow-v2-models</artifactId>
            <version>${shadow.v2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>zeus-models</artifactId>
            <version>${zeus.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <release>${maven.compiler.models.release}</release>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                    <generatedSourcesDirectory>${project.build.directory}/generated-sources/
                    </generatedSourcesDirectory>
                </configuration>
                <dependencies>
                    <dependency>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                        <version>${asm.version}</version>
                    </dependency>
                </dependencies>
                <groupId>org.apache.maven.plugins</groupId>
                <version>${maven.compiler.version}</version>
            </plugin>
        </plugins>
    </build>
</project>