package com.phonepe.verified.kaizen.models.responses;

public enum State {

    NOT_STARTED {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitNotStarted(data);
        }
    },
    CREATED {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.vistCreated(data);
        }
    },
    INITIAL_ACTION_IN_PROGRESS {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitInitialActionInProgress(data);
        }
    },
    READY {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitReady(data);
        }
    },
    IN_PROGRESS {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitInProgress(data);
        }
    },
    SUCCESS {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitSuccess(data);
        }
    },
    FAILURE {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitFailure(data);
        }
    },
    INVALIDATED {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitInvalidated(data);
        }
    },
    ABORTED {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitAborted(data);
        }
    },
    DISCARDED {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitDiscarded(data);
        }
    },
    SKIPPED {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitSkipped(data);
        }
    },
    AUTO_SKIPPED {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitAutoSkipped(data);
        }
    },
    PSEUDO_SUCCESS {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitPseudoSuccess(data);
        }
    },
    PURGED {
        @Override
        public <T, U> T accept(final StateVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitPurged(data);
        }
    };

    public abstract <T, U> T accept(final StateVisitor<T, U> visitor,
                                    U data);

    public interface StateVisitor<T, U> {

        T visitNotStarted(U data);

        T vistCreated(U data);

        T visitInitialActionInProgress(U data);

        T visitReady(U data);

        T visitInProgress(U data);

        T visitSuccess(U data);

        T visitFailure(U data);

        T visitInvalidated(U data);

        T visitAborted(U data);

        T visitSkipped(U data);

        T visitAutoSkipped(U data);

        T visitPseudoSuccess(U data);

        T visitDiscarded(U data);

        T visitPurged(U data);
    }
}
