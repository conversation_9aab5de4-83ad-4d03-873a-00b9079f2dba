package com.phonepe.verified.kaizen.models.requests.details;

public enum FetchDetailsFromSecondarySources {

    NEVER {
        @Override
        public <T, U> T accept(final FetchDetailsFromSecondarySourcesVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitNever(data);
        }
    },
    WHEN_PV_DETAILS_ABSENT {
        @Override
        public <T, U> T accept(final FetchDetailsFromSecondarySourcesVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitWhenPvDetailsAbsent(data);
        }
    },
    ALWAYS {
        @Override
        public <T, U> T accept(final FetchDetailsFromSecondarySourcesVisitor<T, U> visitor,
                               final U data) {
            return visitor.visitAlways(data);
        }
    };

    public abstract <T, U> T accept(final FetchDetailsFromSecondarySourcesVisitor<T, U> visitor,
                                    U data);

    public interface FetchDetailsFromSecondarySourcesVisitor<T, U> {

        T visitNever(U data);

        T visitWhenPvDetailsAbsent(U data);

        T visitAlways(U data);
    }
}
