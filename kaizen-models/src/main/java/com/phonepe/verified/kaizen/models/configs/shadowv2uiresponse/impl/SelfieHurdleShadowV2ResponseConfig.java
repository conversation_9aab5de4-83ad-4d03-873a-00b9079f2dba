package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.data.fields.FormattedLabel;
import com.phonepe.shadow.page.button.ButtonDetail;
import com.phonepe.shadow.template.mapping.loader.SubmitLoader;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SelfieHurdleShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String uploadUrl;

    private String submitUrl;

    private String deleteUrl;

    private ButtonDetail captureButton;

    private ButtonDetail reCaptureButton;

    private ButtonDetail submitButton;

    private String uploadFailureText;

    private SubmitLoader submitLoader;

    private FormattedLabel note;

    public SelfieHurdleShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.SELFIE_HURDLE);
    }

    @Builder
    public SelfieHurdleShadowV2ResponseConfig(final String uploadUrl,
                                              final String submitUrl,
                                              final String deleteUrl,
                                              final ButtonDetail captureButton,
                                              final ButtonDetail reCaptureButton,
                                              final ButtonDetail submitButton,
                                              final String uploadFailureText,
                                              final SubmitLoader submitLoader,
                                              final FormattedLabel note) {

        super(ShadowV2ResponseType.SELFIE_HURDLE);
        this.uploadUrl = uploadUrl;
        this.submitUrl = submitUrl;
        this.deleteUrl = deleteUrl;
        this.captureButton = captureButton;
        this.reCaptureButton = reCaptureButton;
        this.submitButton = submitButton;
        this.uploadFailureText = uploadFailureText;
        this.submitLoader = submitLoader;
        this.note = note;
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
