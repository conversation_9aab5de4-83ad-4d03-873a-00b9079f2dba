package com.phonepe.verified.kaizen.models.configs.summary;

import lombok.experimental.UtilityClass;

public enum SummaryViewVersion {
    V1 {
        @Override
        public <T> T accept(final SummaryViewVersionVisitor<T> visitor) {
            return visitor.visitV1();
        }
    };

    public abstract <T> T accept(SummaryViewVersionVisitor<T> visitor);

    public interface SummaryViewVersionVisitor<T> {

        T visitV1();
    }

    @UtilityClass
    public static final class Names {

        public static final String V1 = "V1";
    }

}
