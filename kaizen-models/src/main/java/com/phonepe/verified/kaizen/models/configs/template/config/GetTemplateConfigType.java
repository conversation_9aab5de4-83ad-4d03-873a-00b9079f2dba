package com.phonepe.verified.kaizen.models.configs.template.config;

import lombok.experimental.UtilityClass;

public enum GetTemplateConfigType {

    ALWAYS_RESUME {
        @Override
        public <T, J> T accept(final GetTemplateConfigVersionVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAlwaysResume(data);
        }
    },
    ALWAYS_SUMMARY_VIEW {
        @Override
        public <T, J> T accept(final GetTemplateConfigVersionVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAlwaysSummaryView(data);
        }
    },
    SUMMARY_VIEW_BASED_ON_FLAG {
        @Override
        public <T, J> T accept(final GetTemplateConfigVersionVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSummaryViewBasedOnFlag(data);
        }
    },
    HOPE_RULE_BASED_SUMMARY_VIEW {
        @Override
        public <T, J> T accept(final GetTemplateConfigVersionVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitHopeRuleBasedSummaryView(data);
        }
    };

    public abstract <T, J> T accept(GetTemplateConfigVersionVisitor<T, J> visitor,
                                    J data);


    public interface GetTemplateConfigVersionVisitor<T, J> {

        T visitAlwaysResume(J data);

        T visitAlwaysSummaryView(J data);

        T visitSummaryViewBasedOnFlag(J data);

        T visitHopeRuleBasedSummaryView(J data);
    }

    @UtilityClass
    public static final class Names {

        public static final String ALWAYS_RESUME = "ALWAYS_RESUME";
        public static final String ALWAYS_SUMMARY_VIEW = "ALWAYS_SUMMARY_VIEW";
        public static final String SUMMARY_VIEW_BASED_ON_FLAG = "SUMMARY_VIEW_BASED_ON_FLAG";
        public static final String HOPE_RULE_BASED_SUMMARY_VIEW = "HOPE_RULE_BASED_SUMMARY_VIEW";
    }
}
