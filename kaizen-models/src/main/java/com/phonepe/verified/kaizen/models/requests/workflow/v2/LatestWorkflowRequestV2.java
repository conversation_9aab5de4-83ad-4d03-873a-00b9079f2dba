package com.phonepe.verified.kaizen.models.requests.workflow.v2;

import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileCriteria;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class LatestWorkflowRequestV2 {

    @NotEmpty
    private final String entityId;

    @NotNull
    private final EntityType entityType;

    @NotNull
    private final List<ProfileCriteria> profileCriteria;
}
