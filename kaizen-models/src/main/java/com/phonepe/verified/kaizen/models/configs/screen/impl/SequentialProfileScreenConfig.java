package com.phonepe.verified.kaizen.models.configs.screen.impl;

import com.phonepe.verified.kaizen.models.configs.screen.OrderedProfileScreen;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfigType;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SequentialProfileScreenConfig extends ProfileScreenConfig {

    @Valid
    @NotEmpty
    private List<OrderedProfileScreen> orderedProfileScreenList;

    public SequentialProfileScreenConfig() {
        super(ProfileScreenConfigType.SEQUENTIAL);
    }

    @Builder
    public SequentialProfileScreenConfig(final List<OrderedProfileScreen> orderedProfileScreenList) {
        this();
        this.orderedProfileScreenList = orderedProfileScreenList;
    }

    @AssertTrue(message = "Orders should be unique")
    private boolean isValid() {

        final Set<Integer> orderSet = orderedProfileScreenList.stream()
                .map(OrderedProfileScreen::getOrder)
                .collect(Collectors.toSet());

        return orderSet.size() == orderedProfileScreenList.size();
    }

    @Override
    public <T> T accept(final ProfileScreenVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
