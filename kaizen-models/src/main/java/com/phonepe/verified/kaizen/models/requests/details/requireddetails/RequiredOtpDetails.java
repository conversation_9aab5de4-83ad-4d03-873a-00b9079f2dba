package com.phonepe.verified.kaizen.models.requests.details.requireddetails;

import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetail;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetailVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RequiredOtpDetails extends RequiredDetail {

    @Builder
    public RequiredOtpDetails() {
        super(DetailType.OTP_DETAILS);
    }

    @Override
    public <T, V> T accept(final RequiredDetailVisitor<T, V> visitor,
                           final V data) {
        return visitor.visit(this, data);
    }
}
