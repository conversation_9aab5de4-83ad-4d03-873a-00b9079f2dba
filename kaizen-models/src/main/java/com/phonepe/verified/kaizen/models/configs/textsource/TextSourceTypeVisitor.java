package com.phonepe.verified.kaizen.models.configs.textsource;

import com.phonepe.verified.kaizen.models.configs.textsource.impl.ActionHandleBarsTextSourceConfig;
import com.phonepe.verified.kaizen.models.configs.textsource.impl.ContextHandleBarsTextSourceConfig;

public interface TextSourceTypeVisitor<T, U> {

    T visit(ActionHandleBarsTextSourceConfig actionHandleBarsTextSourceConfig,
            U data);

    T visit(ContextHandleBarsTextSourceConfig contextHandleBarsTextSourceConfig,
            U data);

}
