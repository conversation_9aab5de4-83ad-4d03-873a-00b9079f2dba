package com.phonepe.verified.kaizen.models.responses.details;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.data.details.DetailType.Names;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.details.details.ConsentDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.DocumentUploadDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.KeyValueDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.OtpDetails;
import java.time.LocalDateTime;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@Type(value = DocumentUploadDetail.class, name = Names.DOCUMENT_UPLOAD),
        @Type(value = KeyValueDetail.class, name = Names.KEY_VALUE),
        @Type(value = ConsentDetail.class, name = Names.CONSENT),
        @Type(value = OtpDetails.class, name = Names.OTP_DETAILS)})
public abstract class Detail {

    private DetailType type;

    @Nullable
    private String actionId;

    private State actionCompletionState;

    @Nullable
    private String actionMappingId;

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;

    // TODO: Remove the Nullable annotations after fallback-detail fetch code is removed, post MO migration
    @Nullable
    private String workflowId;

    @Nullable
    private String workflowStepId;

    protected Detail(final DetailType type) {
        this.type = type;
    }

    public abstract <T> T accept(DetailVisitor<T> visitor);
}
