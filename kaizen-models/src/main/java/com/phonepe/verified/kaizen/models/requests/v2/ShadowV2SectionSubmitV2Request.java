package com.phonepe.verified.kaizen.models.requests.v2;

import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.shadow.models.request.BaseShadowUserRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShadowV2SectionSubmitV2Request implements BaseShadowUserRequest {

    private String intent;

    private long componentKitVersion;

    @Valid
    @NotNull
    private SectionInputData sectionInputData;

    @Builder(builderClassName = "ShadowV2SectionSubmitV2RequestBuilder", builderMethodName = "buildShadowV2SectionSubmitV2Request")
    public ShadowV2SectionSubmitV2Request(final SectionInputData sectionInputData,
                                          final long componentKitVersion,
                                          final String intent) {
        this.sectionInputData = sectionInputData;
        this.componentKitVersion = componentKitVersion;
        this.intent = intent;
    }
}
