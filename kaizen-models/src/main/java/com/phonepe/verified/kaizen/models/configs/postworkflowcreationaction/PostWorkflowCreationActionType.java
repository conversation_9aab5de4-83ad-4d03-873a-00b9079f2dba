package com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction;

import lombok.experimental.UtilityClass;

public enum PostWorkflowCreationActionType {

    SCHEDULE_WORKFLOW_AUTO_ABORT {
        @Override
        public <T, J> T accept(final PostWorkflowCreationActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitScheduleWorkflowAutoAbort(data);
        }
    },
    SCHEDULE_WORKFLOW_AUTO_SKIP {
        @Override
        public <T, J> T accept(final PostWorkflowCreationActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitScheduleWorkflowAutoSkip(data);
        }
    };

    public abstract <T, J> T accept(PostWorkflowCreationActionTypeVisitor<T, J> visitor,
                                    J data);

    public interface PostWorkflowCreationActionTypeVisitor<T, J> {

        T visitScheduleWorkflowAutoAbort(J data);

        T visitScheduleWorkflowAutoSkip(J data);
    }


    @UtilityClass
    public final class Names {

        public final String SCHEDULE_WORKFLOW_AUTO_ABORT = "SCHEDULE_WORKFLOW_AUTO_ABORT";
        public final String SCHEDULE_WORKFLOW_AUTO_SKIP = "SCHEDULE_WORKFLOW_AUTO_SKIP";
    }
}
