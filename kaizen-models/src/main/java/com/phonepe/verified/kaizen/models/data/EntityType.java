package com.phonepe.verified.kaizen.models.data;

import lombok.experimental.UtilityClass;

public enum EntityType {
    MERCHANT {
        @Override
        public <T> T accept(final EntityTypeVisitor<T> visitor) {
            return visitor.visitMerchant();
        }
    },
    CUSTOMER {
        @Override
        public <T> T accept(final EntityTypeVisitor<T> visitor) {
            return visitor.visitCustomer();
        }
    },

    AGENT {
        @Override
        public <T> T accept(final EntityTypeVisitor<T> visitor) {
            return visitor.visitAgent();
        }
    };

    public abstract <T> T accept(EntityType.EntityTypeVisitor<T> visitor);

    public interface EntityTypeVisitor<T> {

        T visitMerchant();

        T visitCustomer();

        T visitAgent();
    }

    @UtilityClass
    public static class Names {

        public static final String MERCHANT = "MERCHANT";
        public static final String CUSTOMER = "CUSTOMER";
        public static final String AGENT = "AGENT";
    }
}
