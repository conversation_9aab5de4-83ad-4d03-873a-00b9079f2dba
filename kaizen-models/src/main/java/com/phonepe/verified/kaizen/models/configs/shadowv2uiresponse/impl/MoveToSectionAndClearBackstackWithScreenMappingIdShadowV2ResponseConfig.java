package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class MoveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    @NotNull
    private String screenMappingId;

    @Valid
    @Nullable
    private ShadowV2ResponseConfig backButtonAction;

    private boolean checkExistingStack;

    private String clearStackUpToMappingId;

    private Map<String, Object> analyticsMetadata;

    @Builder
    public MoveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig(final String screenMappingId,
                                                                                   @Nullable final ShadowV2ResponseConfig backButtonAction,
                                                                                   final boolean checkExistingStack,
                                                                                   final String clearStackUpToMappingId,
                                                                                   final Map<String, Object> analyticsMetadata) {
        this();
        this.screenMappingId = screenMappingId;
        this.backButtonAction = backButtonAction;
        this.checkExistingStack = checkExistingStack;
        this.clearStackUpToMappingId = clearStackUpToMappingId;
        this.analyticsMetadata = analyticsMetadata;
    }

    public MoveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.MOVE_TO_SECTION_AND_CLEAR_BACK_STACK_WITH_SCREEN_MAPPING_ID);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
