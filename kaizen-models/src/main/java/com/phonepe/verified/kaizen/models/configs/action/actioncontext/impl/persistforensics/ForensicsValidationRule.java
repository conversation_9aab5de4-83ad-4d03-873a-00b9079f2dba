package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.persistforensics;

import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ForensicsValidationRule {

    @NotEmpty
    private String evaluationRule;

    @NotNull
    private ActionFailureErrorCode errorCode;

}
