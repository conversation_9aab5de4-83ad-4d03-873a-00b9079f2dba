package com.phonepe.verified.kaizen.models.data;

import lombok.experimental.UtilityClass;

public enum SessionType {
    CLIENT_MANAGED {
        @Override
        public <T> T accept(final SessionTypeVisitor<T> visitor) {
            return visitor.visitClientManaged();
        }
    };

    public abstract <T> T accept(SessionTypeVisitor<T> visitor);

    public interface SessionTypeVisitor<T> {

        T visitClientManaged();

    }

    @UtilityClass
    public static class Names {

        public static final String CLIENT_MANAGED = "CLIENT_MANAGED";
    }
}
