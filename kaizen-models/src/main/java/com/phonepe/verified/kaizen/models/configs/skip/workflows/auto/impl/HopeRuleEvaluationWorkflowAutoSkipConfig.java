package com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl;

import com.google.common.collect.ImmutableSet;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfigType;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfigVisitor;
import com.phonepe.verified.kaizen.models.responses.State;
import io.dropwizard.util.Duration;
import java.util.Optional;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Getter
@EqualsAndHashCode(callSuper = true)
public class HopeRuleEvaluationWorkflowAutoSkipConfig extends WorkflowAutoSkipConfig {

    @NotEmpty
    private final Set<State> workflowStepStateBlacklist;

    private final boolean rescheduleIfEvaluationFails;

    @NotEmpty
    private final String hopeRule;

    @Builder
    @Jacksonized
    public HopeRuleEvaluationWorkflowAutoSkipConfig(final Duration skipAfter,
                                                    final String hopeRule,
                                                    final Set<State> workflowStepStateBlacklist,
                                                    final boolean rescheduleIfEvaluationFails) {

        super(WorkflowAutoSkipConfigType.HOPE_RULE_EVALUATION, skipAfter);

        this.hopeRule = hopeRule;
        this.workflowStepStateBlacklist = Optional.ofNullable(workflowStepStateBlacklist)
                .orElse(ImmutableSet.of(State.PSEUDO_SUCCESS));
        this.rescheduleIfEvaluationFails = rescheduleIfEvaluationFails;
    }

    @Override
    public <T, J> T accept(final WorkflowAutoSkipConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
