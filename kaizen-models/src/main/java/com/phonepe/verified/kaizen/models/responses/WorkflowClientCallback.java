package com.phonepe.verified.kaizen.models.responses;

import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.EntityType;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowClientCallback {

    private String entityId;

    private EntityType entityType;

    private String workflowId;

    private State workflowState;

    /*
        Only present when workflowState is FAILURE
     */
    @Nullable
    private ActionFailureErrorCode failureErrorCode;

    @Nullable
    private String failureReason;

    private String tag;

    private String workflowType;

    private String version;

    private ProfileType profileType;

    private String addOnType;

    @Nullable
    private String requestId;

    @Nullable
    private String accessToken;
}
