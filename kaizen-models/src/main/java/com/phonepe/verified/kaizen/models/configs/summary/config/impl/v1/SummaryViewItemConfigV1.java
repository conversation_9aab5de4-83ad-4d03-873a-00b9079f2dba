package com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1;

import com.phonepe.shadow.page.field.impl.FailureDetail;
import com.phonepe.shadow.page.field.impl.summary.SummaryViewStatus;
import com.phonepe.verified.kaizen.models.configs.summary.SummaryViewButton;
import com.phonepe.verified.kaizen.models.configs.summary.config.EditableConfig;
import java.io.Serializable;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("java:S1948")
public class SummaryViewItemConfigV1 implements Serializable {

    private static final long serialVersionUID = 8150038729400495615L;

    private String title;

    private String subtitle;

    private String identifierNumberTemplate;

    private String profileStepMappingId;

    private FailureDetail failureDetail;

    private Map<SummaryViewStatus, SummaryViewButton> summaryViewStatusButtonMap;

    private Map<SummaryViewStatus, String> summaryViewStatusImageMap;

    private Map<SummaryViewStatus, String> summaryViewStatusIconMap;

    private boolean editable;

    @Default
    private EditableConfig editableConfig = new DefaultEditableConfig();
}
