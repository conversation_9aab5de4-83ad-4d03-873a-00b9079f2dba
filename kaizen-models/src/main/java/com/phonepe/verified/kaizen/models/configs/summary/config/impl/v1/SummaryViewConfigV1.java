package com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1;

import com.phonepe.shadow.page.field.ProgressDetail;
import com.phonepe.verified.kaizen.models.configs.summary.SummaryViewVersion;
import com.phonepe.verified.kaizen.models.configs.summary.config.SummaryViewConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.visitor.SummaryViewConfigVisitor;
import java.util.List;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("java:S1948")
public class SummaryViewConfigV1 extends SummaryViewConfig {

    private static final long serialVersionUID = 2388662656387322250L;

    private String title;

    private String subtitle;

    private ProgressDetail progressDetail;

    private List<SummaryViewItemConfigV1> summaryViewItemConfigList;

    @Builder
    public SummaryViewConfigV1(final String title,
                               final String subtitle,
                               final ProgressDetail progressDetail,
                               final List<SummaryViewItemConfigV1> summaryViewItemConfigList) {
        this();
        this.title = title;
        this.subtitle = subtitle;
        this.progressDetail = progressDetail;
        this.summaryViewItemConfigList = summaryViewItemConfigList;
    }

    protected SummaryViewConfigV1() {
        super(SummaryViewVersion.V1);
    }

    @Override
    public <T, J> T accept(final SummaryViewConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
