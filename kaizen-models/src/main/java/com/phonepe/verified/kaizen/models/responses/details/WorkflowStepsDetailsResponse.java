package com.phonepe.verified.kaizen.models.responses.details;

import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.responses.State;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowStepsDetailsResponse {

    private String title;

    private String profileStepMappingId;

    private State workflowStepState;

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;

    private ActionFailureErrorCode failureErrorCode;

    private String failureReason;

    private List<ActionLevelDetails> actionLevelDetailsList;
}
