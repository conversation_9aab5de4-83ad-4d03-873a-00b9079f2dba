package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components;

import com.phonepe.shadow.models.response.actions.ButtonType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ButtonActionConfig {

    private String buttonText;

    @Valid
    private ShadowV2ResponseConfig action;

    private ButtonType buttonType;
}
