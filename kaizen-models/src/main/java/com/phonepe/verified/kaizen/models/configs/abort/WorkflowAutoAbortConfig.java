package com.phonepe.verified.kaizen.models.configs.abort;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfigType.Names;
import com.phonepe.verified.kaizen.models.configs.abort.impl.NoValidStepSubmissionPresentWorkflowAutoAbortConfig;
import io.dropwizard.util.Duration;
import java.io.Serializable;
import lombok.Getter;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(name = Names.NO_VALID_STEP_SUBMISSION_PRESENT, value = NoValidStepSubmissionPresentWorkflowAutoAbortConfig.class)})
public abstract class WorkflowAutoAbortConfig implements Serializable {

    private static final long serialVersionUID = 8619478744765950391L;

    private final WorkflowAutoAbortConfigType type;

    private final String abortReason;

    private final Duration abortAfter;

    protected WorkflowAutoAbortConfig(final WorkflowAutoAbortConfigType type,
                                      final String abortReason,
                                      final Duration abortAfter) {
        this.type = type;
        this.abortReason = abortReason;
        this.abortAfter = abortAfter;
    }

    public abstract <T, J> T accept(WorkflowAutoAbortConfigVisitor<T, J> visitor,
                                    J data);
}
