package com.phonepe.verified.kaizen.models.requests.profiles;

import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class ProfileIdentifier {

    @NotEmpty
    private final String organization;

    @NotEmpty
    private final String namespace;

    @NotEmpty
    private final String type;

    @NotEmpty
    private final String version;

    @NotNull
    private final ProfileType profileType;

    @Nullable
    private String addOnType;
}
