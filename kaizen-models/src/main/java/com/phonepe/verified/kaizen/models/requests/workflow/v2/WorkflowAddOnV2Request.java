package com.phonepe.verified.kaizen.models.requests.workflow.v2;

import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.data.keys.ProfileKey;
import com.phonepe.zeus.models.Farm;
import io.dropwizard.util.Strings;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.AssertFalse;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowAddOnV2Request {

    @NotEmpty
    private String entityId;

    @NotNull
    private EntityType entityType;

    @NotEmpty
    private String userReferenceId;

    @Pattern(regexp = "^\\d{10}$")
    private String phoneNumber;

    @Email
    private String emailId;

    @Valid
    @NotNull
    private ProfileKey profileKey;

    @Nullable
    private Farm callerFarmId;

    @AssertFalse(message = "phoneNumber and emailId both can't be empty. One of them should be provided.")
    private boolean isValidPhoneNumberEmailId() {
        return Strings.isNullOrEmpty(phoneNumber) && Strings.isNullOrEmpty(emailId);
    }
}
