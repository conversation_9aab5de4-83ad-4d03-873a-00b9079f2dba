package com.phonepe.verified.kaizen.models.data.keys;

import com.phonepe.verified.kaizen.models.data.ActionType;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(staticName = "newInstance")
public class StateMachineRegistryKey {

    @NotNull
    private ActionType actionType;

    @NotEmpty
    private String stateMachineVersion;

}
