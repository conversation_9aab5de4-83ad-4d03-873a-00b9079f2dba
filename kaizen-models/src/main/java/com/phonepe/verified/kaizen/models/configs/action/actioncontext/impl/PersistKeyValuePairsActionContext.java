package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PersistKeyValuePairsActionContext extends StepActionContext {

    private Map<String, String> keyValuePairs;

    public PersistKeyValuePairsActionContext() {
        super(ActionType.PERSIST_KEY_VALUE_PAIRS);
    }

    @Builder
    public PersistKeyValuePairsActionContext(final Map<String, String> keyValuePairs) {
        this();
        this.keyValuePairs = keyValuePairs;
    }
}
