package com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig;

import lombok.experimental.UtilityClass;

public enum PseudoSuccessActionFailureWorkflowBehaviourConfigType {
    ALWAYS_FAIL_WORKFLOW {
        @Override
        public <T> T accept(final PseudoSuccessActionFailureWorkflowBehaviourConfigTypeVisitor<T> visitor) {
            return visitor.visitAlwaysFailWorkflow();
        }
    },
    DO_NOT_FAIL_WORKFLOW_BASED_ON_ACTION_FAILURE_ERROR_CODE {
        @Override
        public <T> T accept(final PseudoSuccessActionFailureWorkflowBehaviourConfigTypeVisitor<T> visitor) {
            return visitor.visitDoNotFailWorkflowBasedOnActionFailureErrorCode();
        }
    },
    DO_NOT_FAIL_WORKFLOW {
        @Override
        public <T> T accept(final PseudoSuccessActionFailureWorkflowBehaviourConfigTypeVisitor<T> visitor) {
            return visitor.visitDoNotFailWorkflow();
        }
    };

    public abstract <T> T accept(PseudoSuccessActionFailureWorkflowBehaviourConfigTypeVisitor<T> visitor);

    public interface PseudoSuccessActionFailureWorkflowBehaviourConfigTypeVisitor<T> {

        T visitAlwaysFailWorkflow();

        T visitDoNotFailWorkflowBasedOnActionFailureErrorCode();

        T visitDoNotFailWorkflow();
    }

    @UtilityClass
    public class Names {

        public static final String ALWAYS_FAIL_WORKFLOW = "ALWAYS_FAIL_WORKFLOW";
        public static final String DO_NOT_FAIL_WORKFLOW_BASED_ON_ACTION_FAILURE_ERROR_CODE = "DO_NOT_FAIL_WORKFLOW_BASED_ON_ACTION_FAILURE_ERROR_CODE";
        public static final String DO_NOT_FAIL_WORKFLOW = "DO_NOT_FAIL_WORKFLOW";
    }
}
