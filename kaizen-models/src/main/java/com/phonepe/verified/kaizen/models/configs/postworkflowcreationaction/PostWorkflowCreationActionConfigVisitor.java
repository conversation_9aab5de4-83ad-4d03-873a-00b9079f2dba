package com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction;

import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.impl.ScheduleWorkflowAutoAbortActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.impl.ScheduleWorkflowAutoSkipActionConfig;

public interface PostWorkflowCreationActionConfigVisitor<T, J> {

    T visit(ScheduleWorkflowAutoAbortActionConfig config,
            J data);

    T visit(ScheduleWorkflowAutoSkipActionConfig config,
            J data);
}