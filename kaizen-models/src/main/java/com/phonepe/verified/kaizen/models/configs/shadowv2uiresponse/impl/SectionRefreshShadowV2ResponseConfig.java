package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import java.util.Set;
import javax.annotation.Nullable;
import javax.validation.Valid;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SectionRefreshShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    @Valid
    @Nullable
    private ShadowV2ResponseConfig backButtonAction;

    private Set<String> setDefaultValueToNullForFieldIds;

    @Builder
    public SectionRefreshShadowV2ResponseConfig(@Nullable final ShadowV2ResponseConfig backButtonAction,
                                                final Set<String> setDefaultValueToNullForFieldIds) {
        this();
        this.backButtonAction = backButtonAction;
        this.setDefaultValueToNullForFieldIds = setDefaultValueToNullForFieldIds;
    }

    public SectionRefreshShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.SECTION_REFRESH);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
