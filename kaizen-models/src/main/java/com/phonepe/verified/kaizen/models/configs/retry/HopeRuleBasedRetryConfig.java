package com.phonepe.verified.kaizen.models.configs.retry;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * When evaluation rule evaluates to false then disable retries.
 * When evaluation rule evaluates to true then allow retries up to retryCount.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class HopeRuleBasedRetryConfig extends RetryConfig {

    @Min(1)
    private int retryCount;

    @NotEmpty
    private String evaluationRule;

    public HopeRuleBasedRetryConfig() {
        super(RetryConfigType.HOPE_RULE_BASED);
    }

    @Override
    public <T, J> T accept(final RetryConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
