package com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionType;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode(callSuper = true)
public class TagCalculationOnWorkflowContextPostCompletionActionConfig extends PostCompletionActionConfig {

    private static final long serialVersionUID = 8397830502806821143L;

    protected TagCalculationOnWorkflowContextPostCompletionActionConfig() {
        super(PostCompletionActionType.TAG_CALCULATION_ON_WORKFLOW_CONTEXT);
    }

    @Override
    public <T, J> T accept(final PostCompletionActionConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
