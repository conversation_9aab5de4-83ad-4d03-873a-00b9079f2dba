package com.phonepe.verified.kaizen.models.configs.textsource.impl;

import com.phonepe.verified.kaizen.models.configs.textsource.TextSourceConfig;
import com.phonepe.verified.kaizen.models.configs.textsource.TextSourceType;
import com.phonepe.verified.kaizen.models.configs.textsource.TextSourceTypeVisitor;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ContextHandleBarsTextSourceConfig extends TextSourceConfig {

    private static final long serialVersionUID = -8654733166163137022L;

    @NotEmpty
    private String handlebarsTranslator;

    private boolean returnNullWhenActionNotFound;

    protected ContextHandleBarsTextSourceConfig() {
        super(TextSourceType.CONTEXT_HANDLEBARS_TEXT_SOURCE);
    }

    @Builder
    public ContextHandleBarsTextSourceConfig(final String handlebarsTranslator,
                                             final boolean returnNullWhenActionNotFound) {
        this();
        this.handlebarsTranslator = handlebarsTranslator;
        this.returnNullWhenActionNotFound = returnNullWhenActionNotFound;
    }

    @Override
    public <T, U> T accept(final TextSourceTypeVisitor<T, U> visitor,
                           final U data) {
        return visitor.visit(this, data);
    }
}
