package com.phonepe.verified.kaizen.models.configs.summary.config;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.DefaultEditableConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.HopeRuleEditableConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.visitor.SummaryViewEditableConfigVisitor;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(name = EditableConfigType.HOPE_RULE_TEXT, value = HopeRuleEditableConfig.class),
        @JsonSubTypes.Type(name = EditableConfigType.DEFAULT_TEXT, value = DefaultEditableConfig.class)})
public abstract class EditableConfig implements Serializable {

    private static final long serialVersionUID = 8972458920278963116L;

    protected EditableConfigType type;

    public abstract <T, J> T evaluate(SummaryViewEditableConfigVisitor<T, J> visitor,
                                      J data);
}
