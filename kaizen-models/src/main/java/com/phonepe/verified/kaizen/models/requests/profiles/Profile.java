package com.phonepe.verified.kaizen.models.requests.profiles;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.google.common.collect.ImmutableList;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType.Names;
import com.phonepe.verified.kaizen.models.configs.summary.config.SummaryViewConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "profileType")
@JsonSubTypes({@JsonSubTypes.Type(value = PrimaryProfile.class, name = Names.PRIMARY),
        @JsonSubTypes.Type(value = AddOnProfile.class, name = Names.ADD_ON)})
public abstract class Profile {

    private String profileId;

    @NotEmpty
    private String organization;

    @NotEmpty
    private String namespace;

    @NotEmpty
    private String type;

    @NotEmpty
    private String version;

    private SummaryViewConfig summaryViewConfig;

    private GetTemplateConfig getTemplateConfig;

    @Nullable
    private PostCompletionActionConfig postCompletionActionConfig;

    private List<PostWorkflowCreationActionConfig> postWorkflowCreationActionConfigs = ImmutableList.of(); // Note: No guarantees on encounter order

    @NotNull
    private ProfileType profileType;

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;

    @Valid
    @NotEmpty
    private List<ProfileStep> profileSteps;

    protected Profile(final ProfileType profileType) {
        this.profileType = profileType;
    }

    public abstract <T, J> T accept(ProfileVisitor<T, J> visitor,
                                    J data);

    public abstract Profile withProfileSteps(@Valid @NotEmpty List<ProfileStep> profileSteps);

    public String getAddOnType() {
        return null;
    }
}
