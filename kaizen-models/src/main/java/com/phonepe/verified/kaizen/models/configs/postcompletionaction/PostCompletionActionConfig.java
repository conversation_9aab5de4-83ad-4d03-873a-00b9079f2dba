package com.phonepe.verified.kaizen.models.configs.postcompletionaction;


import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionType.Names;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.AndPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.ClientCallbackPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.OrPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.TagCalculationOnEntityDetailsPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.TagCalculationOnWorkflowContextPostCompletionActionConfig;
import java.io.Serializable;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.AND, value = AndPostCompletionActionConfig.class),
        @JsonSubTypes.Type(name = Names.OR, value = OrPostCompletionActionConfig.class),
        @JsonSubTypes.Type(name = Names.CLIENT_CALLBACK, value = ClientCallbackPostCompletionActionConfig.class),
        @JsonSubTypes.Type(name = Names.TAG_CALCULATION_ON_WORKFLOW_CONTEXT, value = TagCalculationOnWorkflowContextPostCompletionActionConfig.class),
        @JsonSubTypes.Type(name = Names.TAG_CALCULATION_ON_ENTITY_DETAILS, value = TagCalculationOnEntityDetailsPostCompletionActionConfig.class)})
public abstract class PostCompletionActionConfig implements Serializable {

    private static final long serialVersionUID = -3373802979869851407L;

    private PostCompletionActionType type;

    public abstract <T, J> T accept(PostCompletionActionConfigVisitor<T, J> visitor,
                                    J data);
}
