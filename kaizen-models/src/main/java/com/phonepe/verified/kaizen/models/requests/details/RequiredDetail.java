package com.phonepe.verified.kaizen.models.requests.details;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.data.details.DetailType.Names;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredConsentDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredDocumentUploadDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredKeyValueDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredOtpDetails;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@Type(value = RequiredDocumentUploadDetail.class, name = Names.DOCUMENT_UPLOAD),
        @Type(value = RequiredKeyValueDetail.class, name = Names.KEY_VALUE),
        @Type(value = RequiredConsentDetail.class, name = Names.CONSENT),
        @Type(value = RequiredOtpDetails.class, name = Names.OTP_DETAILS)})
public abstract class RequiredDetail {

    @NotNull
    private DetailType type;

    public abstract <T, V> T accept(final RequiredDetailVisitor<T, V> visitor,
                                    V data);
}