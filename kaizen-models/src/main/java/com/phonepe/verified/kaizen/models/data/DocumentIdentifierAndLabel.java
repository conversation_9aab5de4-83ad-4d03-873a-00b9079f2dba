package com.phonepe.verified.kaizen.models.data;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(staticName = "newInstance")
public class DocumentIdentifierAndLabel {

    @NotEmpty
    private String documentId;

    @NotEmpty
    private String documentLabel;
}
