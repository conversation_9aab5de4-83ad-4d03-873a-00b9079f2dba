package com.phonepe.verified.kaizen.models.responses;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KaizenResponseCode {

    SUCCESS(200, "Success"),

    /* 5xx for internal errors */
    INTERNAL_SERVER_ERROR(500, "Internal Server Error"),
    SERIALIZATION_ERROR(500, "Internal Serialization Error"),
    DESERIALIZATION_ERROR(500, "Internal Deserialization Error"),
    DB_ERROR(500, "Internal Database Error"),
    COMMUNICATION_ERROR(500, "Internal Communication Error"),
    AEROSPIKE_WRITE_ERROR(500, "Internal Aerospike Write Error"),
    AEROSPIKE_READ_ERROR(500, "Internal Aerospike Read Error"),
    AEROSPIKE_SCAN_ERROR(500, "Internal Aerospike Scan Error"),
    AEROSPIKE_DELETE_ERROR(500, "Internal Aerospike Delete Error"),
    REQUEST_NOT_AUTHORIZED(500, "Request is not authorized"),
    FILE_TOO_LARGE(500, "File is too large"),
    CORRUPT_FILE(500, "File is corrupted"),
    FILE_NOT_SUPPORTED(500, "Unsupported file type"),
    DOCSTORE_FILE_UPLOAD_ERROR(500, "Error while uploading file to DocStore"),
    DOCSTORE_FILE_DOWNLOAD_ERROR(500, "Error while downloading file from DocStore"),
    CLOCKWORK_SCHEDULING_ERROR(500, "Error while scheduling callback on Clockwork"),
    DOCSTORE_FILE_DELETE_ERROR(500, "Error while deleting file from DocStore"),
    DUPLICATE_ACTION_KEY_ERROR(500, "Same action key configured for two different actions"),
    QUEUE_EXCEPTION(500, "Exception while queuing the message"),
    CACHE_ERROR(500, "Error while getting data from cache"),
    TRANSFORMATION_ERROR(500, "Error in transformation"),
    PROFILE_SCREEN_ACTION_NOT_COMPLETED(500, "Profile Screen action not completed"),
    RETRY_EXCEPTION(500, "Exception while retrying"),
    EXECUTION_EXCEPTION(500, "Execution exception while retrying"),
    SALESFORCE_RETRY_EXHAUSTED(503, "Invalid document upload transition context"),
    INVALID_ACCOUNT_VERIFICATION_METADATA(500, "Invalid account verification metadata"),
    INVALID_SESSION_CONFIG(500, "Invalid session config"),

    /* 4xx for auth , permissions, validation */
    AUTH_ERROR(401, "Authorization Error"),
    OPERATION_NOT_ALLOWED(405, "Operation is not allowed"),
    WRONG_INPUT_ERROR(400, "Invalid Input"),
    INVALID_FILE(400, "Invalid file"),
    INVALID_ACTION_COMPLETION_STATE(400, "Invalid action completion state"),
    INVALID_REQUEST(400, "Invalid request"),
    INVALID_STATE(400, "Invalid state"),
    NO_AUDIT_REPORT_REQUEST(400, "No Audit Report Request received from this UserID. Please make a request!"),
    AUDIT_REPORT_GENERATION_IN_PROGRESS(400, "Audit report generation still in progress. Please try later"),
    AUDIT_REPORT_GENERATION_NOT_ALLOWED(400, "You have generated a report recently. Please try again after some time."),
    WORKFLOW_NOT_FOUND(404, "Workflow Not Found"),
    WORKFLOW_NOT_COMPLETED(400, "Workflow is not in completed state"),
    WORKFLOW_ALREADY_COMPLETED(409, "Workflow has already been completed. No further processing can be done"),
    WORKFLOW_STEP_ALREADY_COMPLETED(409, "Workflow Step has already been completed. No further processing can be done"),
    WORKFLOW_STEP_NOT_FOUND(404, "Workflow Step Not Found"),
    WORKFLOW_STEP_NOT_FOUND_IN_PENDING(404, "Workflow Step Not Found In Pending state"),
    WORKFLOW_STEP_IN_PENDING(404, "Workflow Step In Pending state"),
    WORKFLOW_STEP_NOT_FOUND_IN_SKIPPED(404, "Workflow Step Not Found In Skipped state"),
    TRANSITION_NOT_ALLOWED(409, "Transition not allowed"),
    TRANSITION_LOCK_KEY_NOT_FOUND(404, "Transition Lock Key Not Found"),
    UNABLE_TO_ACQUIRE_TRANSITION_LOCK(409, "Unable to acquire transition lock"),
    UNABLE_TO_ACQUIRE_WORKFLOW_CONTEXT_LOCK(409, "Unable to acquire workflow context lock"),
    UNABLE_TO_ACQUIRE_WALLET_MIN_KYC_MIGRATION_LOCK(409, "Unable to acquire wallet min kyc migration lock"),
    UNABLE_TO_ACQUIRE_RESPONDED_TO_REVOLVER_AGAINST_REQUEST_ID_LOCK(409,
            "Unable to acquire responded to revolver against requestId lock"),
    WALLET_MIN_KYC_MIGRATION_FAILED(500, "Wallet min kyc migration failed"),
    WALLET_MIN_KYC_CREATION_FAILED(500, "Wallet min kyc creation failed"),
    FRAUD_BLOCKED(400, "Blocked due to suspected fraud"),
    SB_KYC_MIGRATION_FAILED(500, "Stockbroking KYC migration failed"),
    STATE_MACHINE_NOT_FOUND(404, "State machine not found"),
    DOCUMENT_NOT_FOUND(404, "Document not found"),
    DOCUMENT_TYPE_CONFIG_NOT_FOUND(404, "Document type config not found on the template"),
    DOCUMENT_TYPE_SIZE_LIMIT_CONFIG_NOT_FOUND(404, "Document type file size limit config not found on the template"),
    ACTION_NOT_FOUND(404, "Action not found"),
    ACTION_METADATA_NOT_FOUND(404, "Action metadata not found"),
    INVALID_STATE_MACHINE(400, "Invalid State Machine"),
    INVALID_STATE_MACHINE_INITIAL_STATE(400, "Invalid State Machine Initial State"),
    INVALID_STATE_MACHINE_TERMINAL_STATES(400, "Invalid State Machine Terminal States"),
    STATE_MACHINE_INVALID_PSEUDO_SUCCESS_STATE(400, "State Machine should have zero or one pseudo success transition"),
    INVALID_STATE_MACHINE_ACTIONS(400, "Invalid State Machine Actions"),
    STATE_MACHINE_ACTION_NOT_FOUND(404, "State Machine Actions not found"),
    STATE_MACHINE_ALREADY_EXISTS(400, "State Machine with the same Id exists"),
    VERIFICATION_PROFILE_ALREADY_EXIST(409, "Verification Profile Already Exist"),
    VERIFICATION_PROFILE_NOT_FOUND(404, "Verification Profile Not Found"),
    ADDON_PROFILE_NOT_SUPPORTED(404, "Add On Not Supported"),
    VERIFICATION_PROFILE_STEPS_NOT_FOUND(404, "Verification Profile Steps Not Found"),
    VERIFICATION_PROFILE_STEP_NOT_FOUND(404, "Verification Profile Step Not Found"),
    INITIAL_ACTION_PROFILE_STEP_NOT_FOUND(404, "Initial Action Profile Step Not Found"),
    INITIAL_ACTION_PROFILE_STEP_NOT_SUPPORTED_IN_SYNC(404,
            "Initial Action Profile Step can't be executed in sync workflow init"),
    INVALID_COMPARATOR_METADATA(400, "Invalid comparator metadata"),
    UNSUPPORTED_OPERATION(400, "Unsupported Operation"),
    TEMPLATE_NOT_FOUND(404, "Template Not Found"),
    SECTION_MAPPING_ID_NOT_FOUND_IN_TEMPLATE(404, "Section Mapping Id not found in Template"),
    WORKFLOW_STEP_ALREADY_EXISTS(409, "Workflowstep already exists"),
    ACCOUNT_NOT_FOUND(404, "Account Not Found"),
    INVALID_IFSC(404, "Invalid IFSC code"),
    INVALID_NEFT_IFSC(404, "Invalid NEFT IFSC code"),
    HTTP_EXECUTOR_NOT_FOUND(404, "Http Executor Not Found"),
    UNEXPECTED_WORKFLOW_STEP_STATE(400, "Completed workflow step has unsupported status"),
    FIELD_TYPE_NOT_FOUND(404, "Field data type not found"),
    PROFILE_SCREEN_NOT_FOUND(404, "Profile Screen not found"),
    INVALID_BOTTOM_SHEET_UI_RESPONSE_CONFIG(400, "Left and right button both cannot be null at the same instance"),
    REQUIRED_DETAILS_NOT_FOUND_FOR_WORKFLOW(404, "Required details not found for given workflow"),
    CANNOT_PROCEED_WITHOUT_DOCUMENT(400, "Document should be present"),
    CANNOT_PROCEED_WITHOUT_DOB(400, "Date of birth missing"),
    STEP_ACTION_CONTEXT_NOT_FOUND(404, "Step Action Context not found"),
    DUPLICATE_PROFILE_STEP_MAPPING_ID_FOUND(400, "Profile step mapping id should be unique across profile"),
    DUPLICATE_SCREEN_MAPPING_ID_FOUND(400, "Screen mapping id should be unique across profile"),
    DUPLICATE_ACTION_MAPPING_ID_FOUND(400, "Action mapping id should be unique across profile"),
    CACHE_NOT_FOUND(404, "Cache with given cache name not found"),
    INVALID_DOCUMENT_UPLOAD_REQUEST(400, "Document upload request is invalid"),
    INVALID_DOCUMENT_DOWNLOAD_REQUEST(400, "Document download request is invalid"),
    ACTION_MAPPING_ID_NOT_FOUND(404, "Action Mapping Id Not Found"),
    INVALID_ACTION_MAPPING_ID(400, "Invalid Action Mapping Id Input"),
    INVALID_DOCUMENT_UPLOAD_TRANSITION_CONTEXT(400, "Invalid document upload transition context"),
    SMS_VERIFICATION_EXPIRED_PVSDK(400, "Session expired"),
    SESSION_CONFIG_NOT_FOUND(404, "Session config not found"),
    CALLBACK_CLIENT_CONFIG_NOT_FOUND(404, "Callback Client config not found"),
    FILE_SIZE_LIMIT_EXCEEDED(413, "File size is too large"),
    UNSUPPORTED_FILE_TYPE(415, "Unsupported file type"),
    UNSUPPORTED_PASSWORD_PROTECTED_FILE_TYPE(415, "Unsupported password protected file type"),
    KILLSWITCH_ENGAGED(400, "Current operation is under KILL SWITCH"),
    UNSUPPORTED_DOCUMENT_TYPE(415, "Unsupported document type for Idsafe encryption"),
    PAN_DETAILS_NOT_FOUND(404, "PAN Details not found"),
    PREVIOUSLY_PERSISTED_CKYC_NUMBER_DIFFERENT(409, "Previously persisted cKYC number is different"),
    ACCOUNT_DETAILS_NOT_FOUND(404, "Account Details not found"),
    ACCESS_TOKEN_DATA_NOT_AVAILABLE_OR_EXPIRED(401, "Access Token data is either not available or expired"),
    ACCESS_TOKEN_NOT_AVAILABLE(401, "Access Token is either not available or empty"),

    AUTH_TOKEN_NOT_AVAILABLE(401, "Auth token is either not available or empty"),
    REFRESH_TOKEN_NOT_AVAILABLE(401, "Refresh token is either not available or empty"),

    AUTH_Z_TOKEN_NOT_AVAILABLE(401, "Token data is either not available or empty"),
    AUTH_TOKEN_EXPIRED(412, "Auth token expired"),
    REFRESH_TOKEN_EXPIRED(412, "Refresh token expired"),
    REFRESH_TOKEN_EXPIRED_FOR_WEB(401, "Refresh token expired"),
    AUTH_Z_FORBIDDEN(403, "Access Forbidden"),
    INVALID_VERIFICATION_INIT_REQUEST(400,
            "Either CallbackServiceName or CallbackUrl is null in VerificationInitRequest"),
    REQUEST_ID_EXPIRED(400, "Request id has been expired or is invalid"),
    WORKFLOW_NOT_READY(400, "Workflow is not yet ready, please try after some time"),
    HOPE_RULE_VALIDATION_FAILED(422, "Hope rule validation failed"),

    UNAUTHORIZED_OPERATION(403, AUTH_Z_FORBIDDEN.message),
    USER_NOT_AUTHORIZED_TO_PERFORM_THIS_ACTION(403, AUTH_Z_FORBIDDEN.message),
    FRA_RULE_REFERENCE_ID_IS_NOT_CONFIGURED(500, "Fra rule referenceId is not configured"),
    INVALIDATION_OF_WORKFLOW_STEP_NOT_ALLOWED(400, "Invalidation of Workflow step not allowed"),

    PROFILE_NOT_SUPPORTED_FOR_CKYC_DETAILS(400, "Profile not supported for cKYC details"),
    CKYC_MANDATORY_DATA_UNAVAILABLE(400, "cKYC mandatory data not available"),
    PYXIS_WORKFLOW_ID_NOT_AVAILABLE(400, "Pyxis workflow id not available"),
    HAWKEYE_NOTIFICATION_FAILED(500, "Hawkeye notification failed"),
    SUMMARY_CONFIG_NOT_AVAILABLE(400, "Use case does not have summary config"),
    WORKFLOW_NOT_IN_RECONCILIABLE_STATE(400, "Workflow not in reconcilable state for KYC status"),
    INCOMPLETE_REDIRECTION_HURDLE_ACTION(500, "Incomplete redirection hurdle action"),
    PINCODE_DETAILS_NOT_FOUND(404, "Pincode Details not found!"),
    UNSUPPORTED_MASKING_OPERATION(400, "Masking can only be performed for either front image or back image or both"),
    OCR_DOC_NOT_FOUND(400, "Unable to get documents for OCR"),
    EXTRACTION_DETAILS_NOT_FOUND(500, "Unable to get extraction details"),
    REQUIRED_DETAILS_NOT_FOUND(500, "Unable to get required details"),
    DRISHTI_EXTRACTION_DETAILS_NOT_FOUND(500, "Unable to get drishti extraction details"),
    FIELD_ID_NOT_FOUND_IN_TEMPLATE(500, "Given fieldId not found in template"),
    INVALID_QUALITY_FOR_COMPRESSION(500,
            "Please enter a valid range for image quality - please make it between 0 to 0.5"),
    MANUAL_RETRY_OF_ACTION_NOT_ALLOWED(412, "Manual Retry of Action not allowed."),
    REVOLVER_CALLBACK_FAILED(500, "Failure in sending callback to revolver"),
    ENTITY_DETAILS_PROFILE_CRITERIA_TYPE_NOT_CONFIGURED(404, "Entity Details profile criteria type not configured"),
    UNABLE_TO_ACQUIRE_PROFILE_LOCK(423, "Unable to acquire profile lock"),
    COMPARATOR_RIGHT_TEXT_SOURCE_NOT_CONFIGURED(404, "Comparator right text source not configured in profile"),
    UI_REQUEST_CONTEXT_NOT_FOUND(500, "Ui Request Context Not Found");
    private final int statusCode;

    private final String message;
}
