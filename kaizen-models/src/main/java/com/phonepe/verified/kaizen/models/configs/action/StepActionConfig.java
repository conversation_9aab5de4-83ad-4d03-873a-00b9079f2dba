package com.phonepe.verified.kaizen.models.configs.action;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.action.StepActionConfigType.Names;
import com.phonepe.verified.kaizen.models.configs.action.impl.AndStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.EvaluatedStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.OrStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.STANDARD, value = StandardStepActionConfig.class),
        @JsonSubTypes.Type(name = Names.AND, value = AndStepActionConfig.class),
        @JsonSubTypes.Type(name = Names.OR, value = OrStepActionConfig.class),
        @JsonSubTypes.Type(name = Names.EVALUATED, value = EvaluatedStepActionConfig.class)})
public abstract class StepActionConfig implements Serializable {

    private static final long serialVersionUID = 3355902212035230320L;

    private StepActionConfigType type;

    public abstract <T, J> T accept(StepActionVisitor<T, J> visitor,
                                    J data);
}
