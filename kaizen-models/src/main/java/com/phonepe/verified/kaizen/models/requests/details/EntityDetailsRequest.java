package com.phonepe.verified.kaizen.models.requests.details;

import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileCriteria;
import com.phonepe.verified.kaizen.models.responses.State;
import java.util.Collections;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class EntityDetailsRequest {

    @NotEmpty
    private final String entityId;

    @NotNull
    private final EntityType entityType;

    @Valid
    @NotNull
    private final List<@NotNull ProfileCriteria> profileCriteria;

    @Default
    private final FetchDetailsFromSecondarySources fetchDetailsFromSecondarySources = FetchDetailsFromSecondarySources.WHEN_PV_DETAILS_ABSENT;

    @Valid
    @NotEmpty
    private final List<RequiredDetail> requiredDetails;

    @Default
    private final List<State> validKycDetailActionStates = Collections.singletonList(State.SUCCESS);

    @Default
    private final List<State> validWorkflowStates = Collections.singletonList(State.SUCCESS);
}
