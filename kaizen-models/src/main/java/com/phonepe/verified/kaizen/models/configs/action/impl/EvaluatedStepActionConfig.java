package com.phonepe.verified.kaizen.models.configs.action.impl;

import com.phonepe.verified.kaizen.models.configs.action.StepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.StepActionConfigType;
import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvaluatedStepActionConfig extends StepActionConfig {

    private static final long serialVersionUID = 3490259725197808111L;

    @NotEmpty
    private String evaluationRule;

    @Valid
    @NotNull
    private StepActionConfig config;

    public EvaluatedStepActionConfig() {
        super(StepActionConfigType.EVALUATED);
    }

    @Builder
    public EvaluatedStepActionConfig(final String evaluationRule,
                                     final StepActionConfig config) {
        this();
        this.evaluationRule = evaluationRule;
        this.config = config;
    }

    @Override
    public <T, J> T accept(final StepActionVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
