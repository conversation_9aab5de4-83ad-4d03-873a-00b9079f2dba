package com.phonepe.verified.kaizen.models.configs.skip.workflows.auto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfigType.Names;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl.AnyStepInSuccessWorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl.HopeRuleEvaluationWorkflowAutoSkipConfig;
import io.dropwizard.util.Duration;
import java.io.Serializable;
import lombok.Getter;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(name = Names.ANY_STEP_IN_SUCCESS, value = AnyStepInSuccessWorkflowAutoSkipConfig.class),
        @JsonSubTypes.Type(name = Names.HOPE_RULE_EVALUATION, value = HopeRuleEvaluationWorkflowAutoSkipConfig.class)})
public abstract class WorkflowAutoSkipConfig implements Serializable {

    private static final long serialVersionUID = -8829233499795013840L;

    private final WorkflowAutoSkipConfigType type;

    private final Duration skipAfter;

    protected WorkflowAutoSkipConfig(final WorkflowAutoSkipConfigType type,
                                     final Duration skipAfter) {
        this.type = type;
        this.skipAfter = skipAfter;
    }

    public abstract <T, J> T accept(WorkflowAutoSkipConfigVisitor<T, J> visitor,
                                    J data);
}
