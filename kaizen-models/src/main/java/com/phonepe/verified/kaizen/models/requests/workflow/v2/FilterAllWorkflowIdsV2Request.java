package com.phonepe.verified.kaizen.models.requests.workflow.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.phonepe.verified.kaizen.models.responses.State;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class FilterAllWorkflowIdsV2Request {

    @NotEmpty
    private final String organization;

    @NotEmpty
    private final String namespace;

    @NotEmpty
    private final String type;

    @NotEmpty
    private final List<@NotNull State> workflowStates;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private final LocalDateTime fromTime;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private final LocalDateTime toTime;
}
