package com.phonepe.verified.kaizen.models.responses.profiles;

import com.phonepe.verified.kaizen.models.requests.profiles.ProfileChangeStatus;
import com.phonepe.verified.kaizen.models.requests.profiles.ProfileIdentifier;
import java.time.LocalDateTime;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class ProfileChangeMetadata {

    @NotEmpty
    private final String requestId;

    @NotEmpty
    private final String requestedBy;

    @Nullable
    private final String reviewedBy;

    @NotNull
    private final LocalDateTime requestCreatedAt;

    @NotNull
    private final ProfileChangeStatus status;

    @Valid
    @NotNull
    private final ProfileIdentifier profileIdentifier;
}
