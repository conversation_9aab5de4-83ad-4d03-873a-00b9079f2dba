package com.phonepe.verified.kaizen.models.requests.document.prefill;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentSource {

    @NotEmpty
    private String organization;

    @NotEmpty
    private String namespace;

    @NotEmpty
    private String type;

    @Min(1)
    private int order;

    public String getKey() {
        return this.organization + ":" + this.namespace + ":" + this.type;
    }

}
