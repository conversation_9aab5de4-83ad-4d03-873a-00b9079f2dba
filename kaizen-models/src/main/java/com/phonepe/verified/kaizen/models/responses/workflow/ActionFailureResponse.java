package com.phonepe.verified.kaizen.models.responses.workflow;

import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionFailureResponse {

    @NotNull
    private ActionFailureErrorCode actionFailureErrorCode;

    @NotEmpty
    private String reason;

    @NotEmpty
    private String actionFailureMappingId;
}
