package com.phonepe.verified.kaizen.models.requests.profiles;

import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class ProfileCriteria implements Serializable {

    private static final long serialVersionUID = -4375834539203806963L;

    @NotEmpty
    private final String organization;

    @NotEmpty
    private final String namespace;

    @NotEmpty
    private final String type;
}
