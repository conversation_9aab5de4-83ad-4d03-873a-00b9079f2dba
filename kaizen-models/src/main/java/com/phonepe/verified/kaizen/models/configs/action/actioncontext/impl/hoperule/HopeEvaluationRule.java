package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.hoperule;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class HopeEvaluationRule {

    @NotEmpty
    private String evaluationRule;

    @NotNull
    private String errorCode;
}
