package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.OtpActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import io.dropwizard.util.Strings;
import java.util.Map;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class OtpActionContextImpl extends StepActionContext implements OtpActionContext {

    private String otpProfile;

    @Valid
    private Map<TemplateType, UiResponseConfig> otpResponseMap;

    public OtpActionContextImpl() {
        super(ActionType.OTP_HURDLE);
    }

    @Builder
    public OtpActionContextImpl(final String otpProfile,
                                final Map<TemplateType, UiResponseConfig> otpResponseMap) {
        this();
        this.otpProfile = otpProfile;
        this.otpResponseMap = otpResponseMap;
    }

    @AssertTrue(message = "otpProfile and otpResponseMap cannot be empty")
    private boolean isValid() {
        return !Strings.isNullOrEmpty(otpProfile) && Objects.nonNull(otpResponseMap) && !otpResponseMap.isEmpty();
    }
}
