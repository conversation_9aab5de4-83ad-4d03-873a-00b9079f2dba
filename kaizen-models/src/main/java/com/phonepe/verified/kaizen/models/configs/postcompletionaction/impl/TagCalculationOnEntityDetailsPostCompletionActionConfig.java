package com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionType;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TagCalculationOnEntityDetailsPostCompletionActionConfig extends PostCompletionActionConfig {

    private static final long serialVersionUID = -6725628947081370479L;

    protected TagCalculationOnEntityDetailsPostCompletionActionConfig() {
        super(PostCompletionActionType.TAG_CALCULATION_ON_ENTITY_DETAILS);
    }

    @Override
    public <T, J> T accept(final PostCompletionActionConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }

}
