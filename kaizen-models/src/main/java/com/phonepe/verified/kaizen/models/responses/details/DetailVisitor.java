package com.phonepe.verified.kaizen.models.responses.details;

import com.phonepe.verified.kaizen.models.responses.details.details.ConsentDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.DocumentUploadDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.KeyValueDetail;
import com.phonepe.verified.kaizen.models.responses.details.details.OtpDetails;

public interface DetailVisitor<T> {

    T visit(KeyValueDetail keyValueDetail);

    T visit(DocumentUploadDetail documentUploadDetail);

    T visit(ConsentDetail consentDetail);

    T visit(OtpDetails otpDetails);
}
