package com.phonepe.verified.kaizen.models.requests.profiles;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.summary.config.SummaryViewConfig;
import com.phonepe.verified.kaizen.models.configs.tag.WorkflowTagConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PrimaryProfile extends Profile {

    @Valid
    private WorkflowTagConfig workflowTagConfig;

    protected PrimaryProfile() {
        super(ProfileType.PRIMARY);
    }

    public PrimaryProfile(final String profileId,
                          final String organization,
                          final String namespace,
                          final String type,
                          final String version,
                          final SummaryViewConfig summaryViewConfig,
                          final GetTemplateConfig getTemplateConfig,
                          final PostCompletionActionConfig postCompletionActionConfig,
                          @Nullable final List<PostWorkflowCreationActionConfig> postWorkflowCreationActionConfigs,
                          final WorkflowTagConfig workflowTagConfig,
                          final LocalDateTime createdAt,
                          final LocalDateTime lastUpdatedAt,
                          final List<ProfileStep> profileSteps) {
        super(profileId, organization, namespace, type, version, summaryViewConfig, getTemplateConfig,
                postCompletionActionConfig, postWorkflowCreationActionConfigs, ProfileType.PRIMARY, createdAt,
                lastUpdatedAt, profileSteps);

        this.workflowTagConfig = workflowTagConfig;
    }

    public static PrimaryProfileBuilder builder() {
        return new PrimaryProfileBuilder();
    }

    @Override
    public <T, J> T accept(final ProfileVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }

    @Override
    public PrimaryProfile withProfileSteps(@Valid @NotEmpty final List<ProfileStep> profileSteps) {
        return getProfileSteps() == profileSteps
               ? this
               : new PrimaryProfile(getProfileId(), getOrganization(), getNamespace(), getType(), getVersion(),
                       getSummaryViewConfig(), getGetTemplateConfig(), getPostCompletionActionConfig(),
                       getPostWorkflowCreationActionConfigs(), getWorkflowTagConfig(), getCreatedAt(),
                       getLastUpdatedAt(), profileSteps);
    }

    public static class PrimaryProfileBuilder {

        private String profileId;
        private String organization;
        private String namespace;
        private String type;
        private String version;
        private SummaryViewConfig summaryViewConfig;
        private GetTemplateConfig getTemplateConfig;
        private PostCompletionActionConfig postCompletionActionConfig;
        private List<PostWorkflowCreationActionConfig> postWorkflowCreationActionConfigs;
        private WorkflowTagConfig workflowTagConfig;
        private LocalDateTime createdAt;
        private LocalDateTime lastUpdatedAt;
        private List<ProfileStep> profileSteps;

        PrimaryProfileBuilder() {
        }

        public PrimaryProfileBuilder profileId(String profileId) {
            this.profileId = profileId;
            return this;
        }

        public PrimaryProfileBuilder organization(String organization) {
            this.organization = organization;
            return this;
        }

        public PrimaryProfileBuilder namespace(String namespace) {
            this.namespace = namespace;
            return this;
        }

        public PrimaryProfileBuilder type(String type) {
            this.type = type;
            return this;
        }

        public PrimaryProfileBuilder version(String version) {
            this.version = version;
            return this;
        }

        public PrimaryProfileBuilder summaryViewConfig(SummaryViewConfig summaryViewConfig) {
            this.summaryViewConfig = summaryViewConfig;
            return this;
        }

        public PrimaryProfileBuilder getTemplateConfig(GetTemplateConfig getTemplateConfig) {
            this.getTemplateConfig = getTemplateConfig;
            return this;
        }

        public PrimaryProfileBuilder postCompletionActionConfig(PostCompletionActionConfig postCompletionActionConfig) {
            this.postCompletionActionConfig = postCompletionActionConfig;
            return this;
        }

        public PrimaryProfileBuilder postWorkflowCreationActionConfigs(List<PostWorkflowCreationActionConfig> postWorkflowCreationActionConfigs) {
            this.postWorkflowCreationActionConfigs = postWorkflowCreationActionConfigs;
            return this;
        }

        public PrimaryProfileBuilder workflowTagConfig(WorkflowTagConfig workflowTagConfig) {
            this.workflowTagConfig = workflowTagConfig;
            return this;
        }

        public PrimaryProfileBuilder createdAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public PrimaryProfileBuilder lastUpdatedAt(LocalDateTime lastUpdatedAt) {
            this.lastUpdatedAt = lastUpdatedAt;
            return this;
        }

        public PrimaryProfileBuilder profileSteps(List<ProfileStep> profileSteps) {
            this.profileSteps = profileSteps;
            return this;
        }

        public PrimaryProfile build() {
            return new PrimaryProfile(this.profileId, this.organization, this.namespace, this.type, this.version,
                    this.summaryViewConfig, this.getTemplateConfig, this.postCompletionActionConfig,
                    this.postWorkflowCreationActionConfigs, this.workflowTagConfig, this.createdAt, this.lastUpdatedAt,
                    this.profileSteps);
        }

        public String toString() {
            return "PrimaryProfile.PrimaryProfileBuilder(profileId=" + this.profileId + ", organization="
                    + this.organization + ", namespace=" + this.namespace + ", type=" + this.type + ", version="
                    + this.version + ", summaryViewConfig=" + this.summaryViewConfig + ", getTemplateConfig="
                    + this.getTemplateConfig + ", postCompletionActionConfig=" + this.postCompletionActionConfig
                    + ", postWorkflowCreationActionConfigs=" + this.postWorkflowCreationActionConfigs
                    + ", workflowTagConfig=" + this.workflowTagConfig + ", createdAt=" + this.createdAt
                    + ", lastUpdatedAt=" + this.lastUpdatedAt + ", profileSteps=" + this.profileSteps + ")";
        }
    }
}
