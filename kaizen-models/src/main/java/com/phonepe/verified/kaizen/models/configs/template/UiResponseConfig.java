package com.phonepe.verified.kaizen.models.configs.template;


import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType.Names;
import com.phonepe.verified.kaizen.models.configs.template.impl.ShadowV2UiResponseConfig;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.SHADOW_V2, value = ShadowV2UiResponseConfig.class),})
public abstract class UiResponseConfig implements Serializable {

    @NotNull
    private TemplateType type;

    public abstract <T> T accept(UiResponseConfigVisitor<T> visitor);

}
