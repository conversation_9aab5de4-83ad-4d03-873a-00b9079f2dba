package com.phonepe.verified.kaizen.models.requests.details;

import com.phonepe.verified.kaizen.models.data.EntityType;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowStepsDetailsRequest {

    @NotEmpty
    private String entityId;

    @NotNull
    private EntityType entityType;

    @NotEmpty
    private String workflowId;
}
