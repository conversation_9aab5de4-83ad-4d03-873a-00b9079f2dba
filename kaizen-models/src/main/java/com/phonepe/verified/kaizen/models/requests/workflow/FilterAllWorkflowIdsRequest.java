package com.phonepe.verified.kaizen.models.requests.workflow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.phonepe.verified.kaizen.models.responses.State;
import java.time.LocalDateTime;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterAllWorkflowIdsRequest {

    @NotEmpty
    private String organization;

    @NotEmpty
    private String namespace;

    @NotEmpty
    private String type;

    @NotNull
    private State state;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fromTime;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime toTime;
}
