package com.phonepe.verified.kaizen.models.configs.template.config.impl;

import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfigType;
import com.phonepe.verified.kaizen.models.configs.template.config.visitor.GetTemplateConfigVisitor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
public class AlwaysSummaryViewGetTemplateConfig extends GetTemplateConfig {

    private static final long serialVersionUID = -3007936312707949949L;

    private String summaryViewScreenMappingId;

    @Builder
    public AlwaysSummaryViewGetTemplateConfig(final String summaryViewScreenMappingId) {
        super(GetTemplateConfigType.ALWAYS_SUMMARY_VIEW);
        this.summaryViewScreenMappingId = summaryViewScreenMappingId;
    }

    protected AlwaysSummaryViewGetTemplateConfig() {
        super(GetTemplateConfigType.ALWAYS_SUMMARY_VIEW);
    }

    @Override
    public <T, J> T accept(final GetTemplateConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
