package com.phonepe.verified.kaizen.models.configs.postcompletionaction;

import lombok.experimental.UtilityClass;

public enum PostCompletionActionType {

    AND {
        @Override
        public <T, J> T accept(final PostCompletionActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAnd(data);
        }
    },
    OR {
        @Override
        public <T, J> T accept(final PostCompletionActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitOr(data);
        }
    },
    CLIENT_CALLBACK {
        @Override
        public <T, J> T accept(final PostCompletionActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitClientCallback(data);
        }
    },
    TAG_CALCULATION_ON_WORKFLOW_CONTEXT {
        @Override
        public <T, J> T accept(final PostCompletionActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitTagCalculationOnWorkflowContext(data);
        }
    },
    TAG_CALCULATION_ON_ENTITY_DETAILS {
        @Override
        public <T, J> T accept(final PostCompletionActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitTagCalculationOnEntityDetails(data);
        }
    };

    public abstract <T, J> T accept(PostCompletionActionTypeVisitor<T, J> visitor,
                                    J data);

    public interface PostCompletionActionTypeVisitor<T, J> {

        T visitAnd(J data);

        T visitOr(J data);

        T visitClientCallback(J data);

        T visitTagCalculationOnWorkflowContext(J data);

        T visitTagCalculationOnEntityDetails(J data);
    }

    @UtilityClass
    public final class Names {

        public final String AND = "AND";
        public final String OR = "OR";
        public final String CLIENT_CALLBACK = "CLIENT_CALLBACK";
        public final String TAG_CALCULATION_ON_WORKFLOW_CONTEXT = "TAG_CALCULATION_ON_WORKFLOW_CONTEXT";
        public final String TAG_CALCULATION_ON_ENTITY_DETAILS = "TAG_CALCULATION_ON_ENTITY_DETAILS";
    }
}