package com.phonepe.verified.kaizen.models.configs.template.config.impl;

import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfigType;
import com.phonepe.verified.kaizen.models.configs.template.config.visitor.GetTemplateConfigVisitor;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
public class AlwaysResumeGetTemplateConfig extends GetTemplateConfig {

    public static final AlwaysResumeGetTemplateConfig INSTANCE = new AlwaysResumeGetTemplateConfig();

    private static final long serialVersionUID = 7809059867008769735L;

    protected AlwaysResumeGetTemplateConfig() {
        super(GetTemplateConfigType.ALWAYS_RESUME);
    }

    @Override
    public <T, J> T accept(final GetTemplateConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
