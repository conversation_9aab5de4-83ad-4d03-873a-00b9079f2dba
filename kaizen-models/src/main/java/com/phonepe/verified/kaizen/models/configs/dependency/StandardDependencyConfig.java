package com.phonepe.verified.kaizen.models.configs.dependency;

import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class StandardDependencyConfig extends DependencyConfig {

    @NotEmpty
    private String actionMappingId;

    public StandardDependencyConfig() {

        super(DependencyConfigType.STANDARD_CONFIG);
    }

    @Override
    public <T, J> T accept(final DependencyConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
