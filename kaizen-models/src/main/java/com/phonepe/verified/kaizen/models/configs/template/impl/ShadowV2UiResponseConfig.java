package com.phonepe.verified.kaizen.models.configs.template.impl;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfigVisitor;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ShadowV2UiResponseConfig extends UiResponseConfig {

    private static final long serialVersionUID = 3081533698199328147L;

    @Valid
    @NotNull
    private ShadowV2ResponseConfig shadowV2ResponseConfig;

    @Builder
    public ShadowV2UiResponseConfig(final ShadowV2ResponseConfig shadowV2ResponseConfig) {
        this();
        this.shadowV2ResponseConfig = shadowV2ResponseConfig;
    }

    public ShadowV2UiResponseConfig() {
        super(TemplateType.SHADOW_V2);
    }

    @Override
    public <T> T accept(final UiResponseConfigVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
