package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType.Names;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl.*;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.MOVE_TO_SECTION, value = MoveToSectionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.MOVE_TO_SECTION_AND_CLEAR_BACK_STACK, value = MoveToSectionAndClearBackstackShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.MOVE_TO_PRE_SDK_SCREEN_ACTION, value = MoveToPreSdkScreenActionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.SHADOW_V2_ACTION, value = ShadowV2ActionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.MOVE_BACK_ACTION, value = MoveBackActionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.OPEN_BOTTOM_SHEET, value = OpenBottomSheetShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.OPEN_BOTTOM_SHEET_V2, value = OpenBottomSheetV2ShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.OPEN_GENERIC_DIALOG, value = OpenGenericDialogActionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.OPEN_STATUS_PAGE, value = OpenStatusPageShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.OTP_HURDLE, value = OtpHurdleShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.VALIDATION_ACTION, value = ValidationActionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.TERMINAL_ACTION, value = TerminalActionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.EVALUATED, value = EvaluatedShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.SECTION_REFRESH, value = SectionRefreshShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.RICH_TEXT_BOTTOM_SHEET, value = RichTextBottomSheetShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.UPDATE_FIELDS_ACTION, value = UpdateFieldsActionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.API_CALL, value = ApiCallShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.OPEN_WEB_VIEW_ACTION, value = OpenWebViewActionShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.SEND_SMS_HURDLE, value = SendSmsHurdleShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.SELFIE_HURDLE, value = SelfieHurdleShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.OPEN_POPUP_WITH_TIMER, value = OpenPopupWithTimerShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.OPEN_POPUP_WITH_TIMER, value = MoveToSectionWithScreenMappingIdShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.MOVE_TO_SECTION_WITH_SCREEN_MAPPING_ID, value = MoveToSectionWithScreenMappingIdShadowV2ResponseConfig.class),
        @JsonSubTypes.Type(name = Names.MOVE_TO_SECTION_AND_CLEAR_BACK_STACK_WITH_SCREEN_MAPPING_ID, value = MoveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig.class),})
public abstract class ShadowV2ResponseConfig {

    @NotNull
    private ShadowV2ResponseType type;

    public abstract <T, U> T accept(ShadowV2ResponseVisitor<T, U> visitor,
                                    U input);
}
