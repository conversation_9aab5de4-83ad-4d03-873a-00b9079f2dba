package com.phonepe.verified.kaizen.models.configs.template;

import lombok.experimental.UtilityClass;

public enum TemplateType {

    SHADOW_V2 {
        @Override
        public <T> T accept(final TemplateTypeVisitor<T> visitor) {
            return visitor.visitShadowV2();
        }
    };

    public abstract <T> T accept(TemplateType.TemplateTypeVisitor<T> visitor);

    public interface TemplateTypeVisitor<T> {

        T visitShadowV2();

    }

    @UtilityClass
    public static final class Names {

        public static final String SHADOW_V2 = "SHADOW_V2";
    }

}
