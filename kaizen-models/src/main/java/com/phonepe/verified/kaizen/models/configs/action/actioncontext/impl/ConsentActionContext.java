package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.ConsentActionData;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.List;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class ConsentActionContext extends StepActionContext {

    @NotEmpty
    private List<@NotNull ConsentActionData> consentActionDataList;

    @Builder
    public ConsentActionContext(final List<ConsentActionData> consentActionDataList) {
        this();
        this.consentActionDataList = consentActionDataList;
    }

    public ConsentActionContext() {
        super(ActionType.CONSENT);
    }

    @AssertTrue(message = "ConsentType should be unique")
    private boolean isValid() {

        return !consentActionDataList.isEmpty()
                && this.consentActionDataList.size() == this.consentActionDataList.stream()
                .map(ConsentActionData::getConsentType)
                .distinct()
                .count();
    }
}
