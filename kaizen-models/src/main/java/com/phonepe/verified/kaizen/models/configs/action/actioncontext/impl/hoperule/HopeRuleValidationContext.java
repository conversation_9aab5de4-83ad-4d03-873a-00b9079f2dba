package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.hoperule;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class HopeRuleValidationContext extends StepActionContext {

    private List<HopeEvaluationRule> hopeEvaluationRules;

    public HopeRuleValidationContext() {
        super(ActionType.HOPE_RULE_VALIDATION);
    }

    @Builder
    public HopeRuleValidationContext(final List<HopeEvaluationRule> hopeEvaluationRules) {
        this();
        this.hopeEvaluationRules = hopeEvaluationRules;
    }
}
