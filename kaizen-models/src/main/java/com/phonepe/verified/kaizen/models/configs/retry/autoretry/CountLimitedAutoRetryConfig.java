package com.phonepe.verified.kaizen.models.configs.retry.autoretry;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CountLimitedAutoRetryConfig extends AutoRetryConfig {

    private int retryCount;

    public CountLimitedAutoRetryConfig() {
        super(AutoRetryConfigType.COUNT_LIMITED);
    }

    @Override
    public <T, J> T accept(final AutoRetryConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
