package com.phonepe.verified.kaizen.models.requests.statemachines;

import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddStateMachineRegistryRequest {

    @NotNull
    private ActionType actionType;

    @NotEmpty
    private String version;

    @NotNull
    @Size(min = 1)
    private List<StateMachineTransition> stateMachineTransitions;

}
