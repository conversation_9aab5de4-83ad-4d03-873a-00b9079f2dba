package com.phonepe.verified.kaizen.models.configs.tag;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowTagConfig implements Serializable {

    private static final long serialVersionUID = -6997478178222297838L;

    @Valid
    @NotEmpty
    private List<OrderedWorkflowTagRule> orderedWorkflowTagRules;

    private String defaultTag;

    @AssertTrue(message = "Orders should be unique")
    private boolean isValid() {

        final Set<Integer> orderSet = orderedWorkflowTagRules.stream()
                .map(OrderedWorkflowTagRule::getOrder)
                .collect(Collectors.toSet());

        return orderSet.size() == orderedWorkflowTagRules.size();
    }
}
