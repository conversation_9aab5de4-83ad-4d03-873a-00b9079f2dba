package com.phonepe.verified.kaizen.models.configs.retry;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.retry.RetryConfigType.Names;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true, defaultImpl = CountBasedRetryConfig.class)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.COUNT_BASED, value = CountBasedRetryConfig.class),
        @JsonSubTypes.Type(name = Names.HOPE_RULE_BASED, value = HopeRuleBasedRetryConfig.class),
        @JsonSubTypes.Type(name = Names.MANUAL_OPS_BASED, value = ManualOpsBasedRetryConfig.class),
        @JsonSubTypes.Type(name = Names.COMPOSITE, value = CompositeRetryConfig.class)})
public abstract class RetryConfig {

    private RetryConfigType type;

    public abstract <T, J> T accept(final RetryConfigVisitor<T, J> visitor,
                                    final J data);

}
