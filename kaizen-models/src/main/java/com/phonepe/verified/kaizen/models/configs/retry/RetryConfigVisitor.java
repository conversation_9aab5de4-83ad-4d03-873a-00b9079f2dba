package com.phonepe.verified.kaizen.models.configs.retry;

public interface RetryConfigVisitor<T, J> {

    T visit(CountBasedRetryConfig countBasedRetryConfig,
            J data);

    T visit(HopeRuleBasedRetryConfig hopeRuleBasedRetryConfig,
            J data);

    T visit(ManualOpsBasedRetryConfig manualOpsBasedRetryConfig,
            J data);

    T visit(CompositeRetryConfig compositeRetryConfig,
            J data);
}
