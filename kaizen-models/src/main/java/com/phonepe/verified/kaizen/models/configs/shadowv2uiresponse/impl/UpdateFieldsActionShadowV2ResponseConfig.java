package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.page.field.Field;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateFieldsActionShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private List<Field> fields;

    public UpdateFieldsActionShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.UPDATE_FIELDS_ACTION);
    }

    @Builder
    public UpdateFieldsActionShadowV2ResponseConfig(final List<Field> fields) {
        this();
        this.fields = fields;
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
