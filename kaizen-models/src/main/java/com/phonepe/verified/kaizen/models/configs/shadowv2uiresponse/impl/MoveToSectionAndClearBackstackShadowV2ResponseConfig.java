package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.MoveToSectionScreen;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MoveToSectionAndClearBackstackShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    @NotNull
    private MoveToSectionScreen screen;

    @Valid
    @Nullable
    private ShadowV2ResponseConfig backButtonAction;

    private boolean checkExistingStack;

    private String clearStackUpToMappingId;

    @Builder
    public MoveToSectionAndClearBackstackShadowV2ResponseConfig(final MoveToSectionScreen screen,
                                                                @Nullable final ShadowV2ResponseConfig backButtonAction,
                                                                final boolean checkExistingStack,
                                                                final String clearStackUpToMappingId) {
        this();
        this.screen = screen;
        this.backButtonAction = backButtonAction;
        this.checkExistingStack = checkExistingStack;
        this.clearStackUpToMappingId = clearStackUpToMappingId;
    }

    public MoveToSectionAndClearBackstackShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.MOVE_TO_SECTION_AND_CLEAR_BACK_STACK);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
