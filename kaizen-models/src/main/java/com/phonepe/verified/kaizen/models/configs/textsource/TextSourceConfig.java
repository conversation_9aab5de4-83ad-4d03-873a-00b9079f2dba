package com.phonepe.verified.kaizen.models.configs.textsource;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.textsource.TextSourceType.Names;
import com.phonepe.verified.kaizen.models.configs.textsource.impl.ActionHandleBarsTextSourceConfig;
import com.phonepe.verified.kaizen.models.configs.textsource.impl.ContextHandleBarsTextSourceConfig;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(name = Names.ACTION_HANDLEBARS_TEXT_SOURCE, value = ActionHandleBarsTextSourceConfig.class),
        @JsonSubTypes.Type(name = Names.CONTEXT_HANDLEBARS_TEXT_SOURCE, value = ContextHandleBarsTextSourceConfig.class)})
public abstract class TextSourceConfig implements Serializable {

    @NotNull
    private TextSourceType type;

    public abstract <T, U> T accept(TextSourceTypeVisitor<T, U> visitor,
                                    U data);

}
