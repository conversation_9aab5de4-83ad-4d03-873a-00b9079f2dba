package com.phonepe.verified.kaizen.models.requests.workflow;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowStatusRequest {

    @NotEmpty
    private String organization;

    @NotEmpty
    private String namespace;

    @NotEmpty
    private String requestId;
}
