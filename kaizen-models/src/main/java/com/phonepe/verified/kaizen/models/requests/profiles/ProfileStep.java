package com.phonepe.verified.kaizen.models.requests.profiles;

import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import java.time.LocalDateTime;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProfileStep {

    private String profileId;

    private String profileStepId;

    @NotEmpty
    private String profileStepMappingId;

    @NotEmpty
    private String title;

    @NotEmpty
    private String executionRule;

    @Valid
    @NotNull
    private ProfileScreenConfig profileScreenConfig;

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;
}
