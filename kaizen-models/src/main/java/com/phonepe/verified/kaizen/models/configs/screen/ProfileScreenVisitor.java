package com.phonepe.verified.kaizen.models.configs.screen;

import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;

public interface ProfileScreenVisitor<T> {

    T visit(StandardProfileScreenConfig standardProfileScreenConfig);

    T visit(SequentialProfileScreenConfig sequentialProfileScreenConfig);

}
