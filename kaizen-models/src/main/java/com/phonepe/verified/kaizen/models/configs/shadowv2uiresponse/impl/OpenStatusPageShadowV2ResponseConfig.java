package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.page.field.AssetDetail;
import com.phonepe.shadow.page.field.Effect;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.AutoActionConfig;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpenStatusPageShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String imageUrl;

    private List<Effect> effects;

    private String subtitle;

    private String footer;

    @Valid
    @NotNull
    private ShadowV2ResponseConfig action;

    @Valid
    private AutoActionConfig autoActionConfig;

    private String bottomButtonText;

    private String description;

    private String note;

    private AssetDetail assetDetail;


    @Builder
    public OpenStatusPageShadowV2ResponseConfig(final String imageUrl,
                                                final List<Effect> effects,
                                                final String subtitle,
                                                final String footer,
                                                @NonNull final ShadowV2ResponseConfig action,
                                                final AutoActionConfig autoActionConfig,
                                                final String bottomButtonText,
                                                final String description,
                                                final String note,
                                                final AssetDetail assetDetail) {
        this();
        this.imageUrl = imageUrl;
        this.effects = effects;
        this.subtitle = subtitle;
        this.footer = footer;
        this.action = action;
        this.autoActionConfig = autoActionConfig;
        this.bottomButtonText = bottomButtonText;
        this.description = description;
        this.note = note;
        this.assetDetail = assetDetail;
    }

    public OpenStatusPageShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.OPEN_STATUS_PAGE);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
