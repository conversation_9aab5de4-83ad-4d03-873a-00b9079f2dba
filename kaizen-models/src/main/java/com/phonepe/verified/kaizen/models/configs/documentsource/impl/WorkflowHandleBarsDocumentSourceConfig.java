package com.phonepe.verified.kaizen.models.configs.documentsource.impl;

import com.phonepe.verified.kaizen.models.configs.documentsource.DocumentSourceConfig;
import com.phonepe.verified.kaizen.models.configs.documentsource.DocumentSourceType;
import com.phonepe.verified.kaizen.models.configs.documentsource.DocumentSourceTypeVisitor;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowHandleBarsDocumentSourceConfig extends DocumentSourceConfig {

    private List<HandleBarsDocumentSourceConfig> documentActionConfigs;

    public WorkflowHandleBarsDocumentSourceConfig() {
        super(DocumentSourceType.WORKFLOW_HANDLEBARS_DOCUMENT_SOURCE);
    }

    @Builder
    public WorkflowHandleBarsDocumentSourceConfig(final List<HandleBarsDocumentSourceConfig> handleBarsDocumentSourceConfigList) {
        this();
        this.documentActionConfigs = handleBarsDocumentSourceConfigList;
    }

    @Override
    public <T, U> T accept(final DocumentSourceTypeVisitor<T, U> visitor,
                           final U data) {
        return visitor.visit(this, data);
    }
}
