package com.phonepe.verified.kaizen.models.configs.summary.config.visitor;

import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.DefaultEditableConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.HopeRuleEditableConfig;

public interface SummaryViewEditableConfigVisitor<T, J> {

    T visitDefault(DefaultEditableConfig defaultEditableConfig,
                   J data);

    T visitHopeRule(HopeRuleEditableConfig hopeRuleEditableConfig,
                    J data);
}
