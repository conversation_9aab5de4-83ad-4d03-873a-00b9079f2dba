package com.phonepe.verified.kaizen.models.data;

import com.google.common.collect.ImmutableSet;
import java.util.Set;
import lombok.experimental.UtilityClass;

public enum ActionType {

    DOCUMENT_UPLOAD {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitDocumentUpload(data);
        }
    },
    OTP_HURDLE {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitOtpHurdle(data);
        }
    },
    SELFIE_HURDLE {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSelfieHurdle(data);
        }
    },
    PERSIST_KEY_VALUE_PAIRS {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPersistKeyValuePairs(data);
        }
    },
    PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPersistKeyValuePairsFromApiCall(data);
        }
    },
    PERSIST_FORENSICS {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPersistForensics(data);
        }
    },
    HOPE_RULE_VALIDATION {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitHopeRuleValidation(data);
        }
    },
    SKIP_WORKFLOW {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSkipWorkflow(data);
        }
    },
    CONSENT {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitConsent(data);
        }
    },
    SKIP_WORKFLOW_STEP {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSkipWorkflowStep(data);
        }
    },
    CONFIRMATION {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitConfirmation(data);
        }
    },
    SCHEDULE_WORKFLOW_ABORT {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitScheduleWorkflowAbort(data);
        }
    },
    TRIGGER_EVENT_ACTION {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitTriggerEventAction(data);
        }
    },
    WAIT_FOR_CONDITION {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitWaitForCondition(data);
        }
    },
    OTP_HURDLE_V2 {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitOtpHurdleV2(data);
        }
    },
    API_CALL_ACTION {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitApiCallAction(data);
        }
    },
    SEND_TERMINAL_ACTION_TO_SDK {
        @Override
        public <T, J> T accept(final ActionTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSendTerminalActionToSdk(data);
        }
    };

    public static final Set<ActionType> CONTEXT_REQUIRED_ACTION_TYPES = ImmutableSet.of(CONSENT,
            SCHEDULE_WORKFLOW_ABORT, TRIGGER_EVENT_ACTION, OTP_HURDLE_V2, API_CALL_ACTION);

    public static final Set<ActionType> DOCUMENT_UPLOAD_ACTION_TYPES = ImmutableSet.of(ActionType.DOCUMENT_UPLOAD,
            ActionType.SELFIE_HURDLE);

    public abstract <T, J> T accept(ActionType.ActionTypeVisitor<T, J> visitor,
                                    J data);

    public interface ActionTypeVisitor<T, J> {

        T visitDocumentUpload(J data);

        T visitPersistKeyValuePairs(J data);

        T visitPersistForensics(J data);

        T visitHopeRuleValidation(J data);

        T visitSkipWorkflow(J data);

        T visitSkipWorkflowStep(J data);

        T visitConsent(J data);

        T visitOtpHurdle(J data);

        T visitConfirmation(J data);

        T visitScheduleWorkflowAbort(J data);

        T visitSelfieHurdle(J data);

        T visitTriggerEventAction(J data);

        T visitWaitForCondition(J data);

        T visitOtpHurdleV2(J data);

        T visitApiCallAction(J data);

        T visitSendTerminalActionToSdk(J data);

        T visitPersistKeyValuePairsFromApiCall(J data);
    }

    @UtilityClass
    public static class Names {

        public final String DOCUMENT_UPLOAD = "DOCUMENT_UPLOAD";
        public final String OTP_HURDLE = "OTP_HURDLE";
        public final String SELFIE_HURDLE = "SELFIE_HURDLE";
        public final String PERSIST_KEY_VALUE_PAIRS = "PERSIST_KEY_VALUE_PAIRS";
        public final String PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL = "PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL";
        public final String PERSIST_FORENSICS = "PERSIST_FORENSICS";
        public final String HOPE_RULE_VALIDATION = "HOPE_RULE_VALIDATION";
        public final String SKIP_WORKFLOW = "SKIP_WORKFLOW";
        public final String CONSENT = "CONSENT";
        public final String SKIP_WORKFLOW_STEP = "SKIP_WORKFLOW_STEP";
        public final String CONFIRMATION = "CONFIRMATION";
        public final String SCHEDULE_WORKFLOW_ABORT = "SCHEDULE_WORKFLOW_ABORT";
        public final String TRIGGER_EVENT_ACTION = "TRIGGER_EVENT_ACTION";
        public final String WAIT_FOR_CONDITION = "WAIT_FOR_CONDITION";
        public final String OTP_HURDLE_V2 = "OTP_HURDLE_V2";
        public final String API_CALL_ACTION = "API_CALL_ACTION";
        public final String SEND_TERMINAL_ACTION_TO_SDK = "SEND_TERMINAL_ACTION_TO_SDK";
    }
}
