package com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl;

import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfigType;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfigVisitor;
import io.dropwizard.util.Duration;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Getter
@EqualsAndHashCode(callSuper = true)
public class AnyStepInSuccessWorkflowAutoSkipConfig extends WorkflowAutoSkipConfig {

    private final boolean rescheduleForStepInPseudoSuccess;

    @Builder
    @Jacksonized
    public AnyStepInSuccessWorkflowAutoSkipConfig(final Duration skipAfter,
                                                  final boolean rescheduleForStepInPseudoSuccess) {

        super(WorkflowAutoSkipConfigType.ANY_STEP_IN_SUCCESS, skipAfter);
        this.rescheduleForStepInPseudoSuccess = rescheduleForStepInPseudoSuccess;
    }

    @Override
    public <T, J> T accept(final WorkflowAutoSkipConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
