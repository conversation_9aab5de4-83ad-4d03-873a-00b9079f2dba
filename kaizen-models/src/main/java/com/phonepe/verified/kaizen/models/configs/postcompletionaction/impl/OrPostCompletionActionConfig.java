package com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionType;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrPostCompletionActionConfig extends PostCompletionActionConfig {


    private static final long serialVersionUID = -2477605760732160564L;

    @Valid
    @NotNull
    private PostCompletionActionConfig left;

    @Valid
    @NotNull
    private PostCompletionActionConfig right;

    protected OrPostCompletionActionConfig() {
        super(PostCompletionActionType.OR);
    }

    @Builder
    public OrPostCompletionActionConfig(final PostCompletionActionConfig left,
                                        final PostCompletionActionConfig right) {
        this();
        this.left = left;
        this.right = right;
    }

    @Override
    public <T, J> T accept(final PostCompletionActionConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }

}
