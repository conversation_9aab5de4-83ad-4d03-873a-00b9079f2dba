package com.phonepe.verified.kaizen.models.configs.template.config.impl;

import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfigType;
import com.phonepe.verified.kaizen.models.configs.template.config.visitor.GetTemplateConfigVisitor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
public class SummaryViewBasedOnFlagGetTemplateConfig extends GetTemplateConfig {

    private static final long serialVersionUID = 4720003592968816429L;

    private String summaryViewScreenMappingId;

    @Builder
    public SummaryViewBasedOnFlagGetTemplateConfig(final String summaryViewScreenMappingId) {
        super(GetTemplateConfigType.SUMMARY_VIEW_BASED_ON_FLAG);
        this.summaryViewScreenMappingId = summaryViewScreenMappingId;
    }

    protected SummaryViewBasedOnFlagGetTemplateConfig() {
        super(GetTemplateConfigType.SUMMARY_VIEW_BASED_ON_FLAG);
    }

    @Override
    public <T, J> T accept(final GetTemplateConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
