package com.phonepe.verified.kaizen.models.configs.action;

import com.phonepe.verified.kaizen.models.configs.action.impl.AndStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.EvaluatedStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.OrStepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.impl.StandardStepActionConfig;

public interface StepActionVisitor<T, J> {

    T visit(StandardStepActionConfig standardStepActionConfig,
            J data);

    T visit(AndStepActionConfig andStepActionConfig,
            J data);

    T visit(OrStepActionConfig orStepActionConfig,
            J data);

    T visit(EvaluatedStepActionConfig evaluatedStepActionConfig,
            J data);

}
