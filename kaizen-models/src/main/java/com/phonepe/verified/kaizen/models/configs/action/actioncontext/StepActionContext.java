package com.phonepe.verified.kaizen.models.configs.action.actioncontext;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.ApiCallActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.ConfirmationActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.ConsentActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.OtpActionContextImpl;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.OtpHurdleV2ActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.PersistKeyValuePairsActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.PersistKeyValuePairsFromApiCallActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.ScheduleWorkflowAbortActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.SelfieHurdleActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.TriggerEventActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.WaitForConditionActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.hoperule.HopeRuleValidationContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.persistforensics.PersistForensicsActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.data.ActionType.Names;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(name = Names.PERSIST_KEY_VALUE_PAIRS, value = PersistKeyValuePairsActionContext.class),
        @JsonSubTypes.Type(name = Names.PERSIST_FORENSICS, value = PersistForensicsActionContext.class),
        @JsonSubTypes.Type(name = Names.OTP_HURDLE, value = OtpActionContextImpl.class),
        @JsonSubTypes.Type(name = Names.CONSENT, value = ConsentActionContext.class),
        @JsonSubTypes.Type(name = Names.HOPE_RULE_VALIDATION, value = HopeRuleValidationContext.class),
        @JsonSubTypes.Type(name = Names.CONFIRMATION, value = ConfirmationActionContext.class),
        @JsonSubTypes.Type(name = Names.SCHEDULE_WORKFLOW_ABORT, value = ScheduleWorkflowAbortActionContext.class),
        @JsonSubTypes.Type(name = Names.SELFIE_HURDLE, value = SelfieHurdleActionContext.class),
        @JsonSubTypes.Type(name = Names.TRIGGER_EVENT_ACTION, value = TriggerEventActionContext.class),
        @JsonSubTypes.Type(name = Names.WAIT_FOR_CONDITION, value = WaitForConditionActionContext.class),
        @JsonSubTypes.Type(name = Names.OTP_HURDLE_V2, value = OtpHurdleV2ActionContext.class),
        @JsonSubTypes.Type(name = Names.API_CALL_ACTION, value = ApiCallActionContext.class),
        @JsonSubTypes.Type(name = Names.PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL, value = PersistKeyValuePairsFromApiCallActionContext.class)})
public abstract class StepActionContext implements Serializable {

    private static final long serialVersionUID = 2406379319997322662L;

    @NotNull
    private ActionType type;
}