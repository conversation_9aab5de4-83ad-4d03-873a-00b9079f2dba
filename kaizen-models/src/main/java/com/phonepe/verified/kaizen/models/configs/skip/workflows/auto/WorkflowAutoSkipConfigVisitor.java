package com.phonepe.verified.kaizen.models.configs.skip.workflows.auto;

import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl.AnyStepInSuccessWorkflowAutoSkipConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.impl.HopeRuleEvaluationWorkflowAutoSkipConfig;

public interface WorkflowAutoSkipConfigVisitor<T, J> {

    T visit(AnyStepInSuccessWorkflowAutoSkipConfig config,
            J data);

    T visit(HopeRuleEvaluationWorkflowAutoSkipConfig config,
            J data);
}