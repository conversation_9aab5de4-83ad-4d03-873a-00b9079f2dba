package com.phonepe.verified.kaizen.models.data;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MimeType {

    PDF("application/pdf") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitPdf(data);
        }
    },
    PNG("image/png") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitPng(data);
        }
    },
    JPEG("image/jpeg") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitJpeg(data);
        }
    },
    MP4("video/mp4") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitMp4(data);
        }
    },
    OCTET_STREAM("application/octet-stream") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitOctetStream(data);
        }
    },
    PLAIN_TEXT("text/plain") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitPlainText(data);
        }
    },
    XML("application/xml") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitXml(data);
        }
    },
    JSON("application/json") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitJson(data);
        }
    },
    DOC("application/msword") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitDoc(data);
        }
    },
    DOCX("application/vnd.openxmlformats-officedocument.wordprocessingml.document") {
        @Override
        public <T, MimeTypeVisitorData> T accept(final MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                 final MimeTypeVisitorData data) {
            return visitor.visitDocx(data);
        }
    };

    private static final Map<String, MimeType> TYPE_MAPPING = Arrays.stream(MimeType.values())
            .collect(Collectors.toMap(MimeType::getValue, Function.identity()));

    private final String value;

    public static MimeType fromString(final String mimeTypeStr) {

        return TYPE_MAPPING.getOrDefault(mimeTypeStr, null);
    }

    public abstract <T, MimeTypeVisitorData> T accept(MimeType.MimeTypeVisitor<T, MimeTypeVisitorData> visitor,
                                                      MimeTypeVisitorData data);

    public interface MimeTypeVisitor<T, MimeTypeVisitorData> {

        T visitPdf(MimeTypeVisitorData data);

        T visitPng(MimeTypeVisitorData data);

        T visitJpeg(MimeTypeVisitorData data);

        T visitMp4(MimeTypeVisitorData data);

        T visitOctetStream(MimeTypeVisitorData data);

        T visitPlainText(MimeTypeVisitorData data);

        T visitXml(MimeTypeVisitorData data);

        T visitJson(MimeTypeVisitorData data);

        T visitDoc(MimeTypeVisitorData data);

        T visitDocx(MimeTypeVisitorData data);
    }
}
