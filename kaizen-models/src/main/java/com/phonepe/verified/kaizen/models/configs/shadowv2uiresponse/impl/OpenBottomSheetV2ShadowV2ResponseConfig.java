package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.data.action.popup.ImageDetail;
import com.phonepe.shadow.data.fields.GenericLabelStyle;
import com.phonepe.shadow.models.Info;
import com.phonepe.shadow.models.Orientation;
import com.phonepe.shadow.models.response.actions.ButtonStyleDetail;
import com.phonepe.shadow.page.field.AssetDetail;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import java.util.List;
import javax.validation.Valid;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpenBottomSheetV2ShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String title;

    private GenericLabelStyle titleStyle;

    private String subTitle;

    private GenericLabelStyle subTitleStyle;

    private Orientation buttonsAxis;

    private ImageDetail imageDetail;

    private String leftButtonText;

    private ButtonStyleDetail leftButtonStyle;

    @Valid
    private ShadowV2ResponseConfig leftAction;

    private String rightButtonText;

    private ButtonStyleDetail rightButtonStyle;
    @Valid
    private ShadowV2ResponseConfig rightAction;

    private boolean cancelable;

    private List<Info> infoList;

    private String footer;

    private AssetDetail assetDetail;

    @Builder
    public OpenBottomSheetV2ShadowV2ResponseConfig(final String title,
                                                   final GenericLabelStyle titleStyle,
                                                   final String subTitle,
                                                   final GenericLabelStyle subTitleStyle,
                                                   final String leftButtonText,
                                                   final ButtonStyleDetail leftButtonStyle,
                                                   final ShadowV2ResponseConfig leftAction,
                                                   final String rightButtonText,
                                                   final ButtonStyleDetail rightButtonStyle,
                                                   final ShadowV2ResponseConfig rightAction,
                                                   final Orientation buttonsAxis,
                                                   final ImageDetail imageDetail,
                                                   final List<Info> infoList,
                                                   final String footer,
                                                   final boolean cancelable,
                                                   final AssetDetail assetDetail) {
        this();
        this.title = title;
        this.titleStyle = titleStyle;
        this.subTitle = subTitle;
        this.subTitleStyle = subTitleStyle;
        this.leftButtonText = leftButtonText;
        this.leftButtonStyle = leftButtonStyle;
        this.leftAction = leftAction;
        this.rightButtonText = rightButtonText;
        this.rightButtonStyle = rightButtonStyle;
        this.rightAction = rightAction;
        this.buttonsAxis = buttonsAxis;
        this.imageDetail = imageDetail;
        this.infoList = infoList;
        this.footer = footer;
        this.cancelable = cancelable;
        this.assetDetail = assetDetail;
    }

    public OpenBottomSheetV2ShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.OPEN_BOTTOM_SHEET_V2);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
