package com.phonepe.verified.kaizen.models.responses.details;

import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.ActionType;
import com.phonepe.verified.kaizen.models.responses.State;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionLevelDetails {

    private String actionId;

    private ActionType actionType;

    private String actionMappingId;

    private String screenMappingId;

    private ActionFailureErrorCode failureErrorCode;

    private String failureReason;

    private State completionState;

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;

    private String lastUpdatedBy;

    private String lastUpdaterType;

    private boolean manualRetryEnabled;

    private String workflowStepId;

}
