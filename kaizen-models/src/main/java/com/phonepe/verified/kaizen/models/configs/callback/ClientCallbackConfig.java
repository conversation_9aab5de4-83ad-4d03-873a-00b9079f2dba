package com.phonepe.verified.kaizen.models.configs.callback;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientCallbackConfig {

    @NotEmpty
    private String profileId;

    @NotEmpty
    private String callbackService;

    @NotEmpty
    private String callbackUrl;

    private String lastUpdatedBy;
}
