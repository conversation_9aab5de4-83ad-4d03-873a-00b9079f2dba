package com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionType.Names;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.impl.ScheduleWorkflowAutoAbortActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.impl.ScheduleWorkflowAutoSkipActionConfig;
import java.io.Serializable;
import lombok.Getter;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(name = Names.SCHEDULE_WORKFLOW_AUTO_ABORT, value = ScheduleWorkflowAutoAbortActionConfig.class),
        @JsonSubTypes.Type(name = Names.SCHEDULE_WORKFLOW_AUTO_SKIP, value = ScheduleWorkflowAutoSkipActionConfig.class)})
public abstract class PostWorkflowCreationActionConfig implements Serializable {

    private static final long serialVersionUID = 3431477245316220902L;

    private final PostWorkflowCreationActionType type;

    protected PostWorkflowCreationActionConfig(final PostWorkflowCreationActionType type) {
        this.type = type;
    }

    public abstract <T, J> T accept(PostWorkflowCreationActionConfigVisitor<T, J> visitor,
                                    J data);
}
