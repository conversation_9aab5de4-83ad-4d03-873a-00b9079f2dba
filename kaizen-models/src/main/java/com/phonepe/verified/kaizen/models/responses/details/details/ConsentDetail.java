package com.phonepe.verified.kaizen.models.responses.details.details;

import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.details.Detail;
import com.phonepe.verified.kaizen.models.responses.details.DetailVisitor;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ConsentDetail extends Detail {

    private boolean consentGranted;

    private String consentGrantedBy;

    private String consentPrivacyPolicyUrl;

    private String consentType;

    private String sourceType;

    private String sourceVersion;

    private String sourcePlatform;

    public ConsentDetail() {
        super(DetailType.CONSENT);
    }

    @Builder
    public ConsentDetail(final String actionId,
                         final State actionCompletionState,
                         final String actionMappingId,
                         final LocalDateTime createdAt,
                         final LocalDateTime lastUpdatedAt,
                         final boolean consentGranted,
                         final String consentGrantedBy,
                         final String consentPrivacyPolicyUrl,
                         final String consentType,
                         final String sourceType,
                         final String sourceVersion,
                         final String sourcePlatform,
                         final String workflowId,
                         final String workflowStepId) {
        super(DetailType.CONSENT, actionId, actionCompletionState, actionMappingId, createdAt, lastUpdatedAt,
                workflowId, workflowStepId);
        this.consentGranted = consentGranted;
        this.consentGrantedBy = consentGrantedBy;
        this.consentPrivacyPolicyUrl = consentPrivacyPolicyUrl;
        this.consentType = consentType;
        this.sourceType = sourceType;
        this.sourceVersion = sourceVersion;
        this.sourcePlatform = sourcePlatform;
    }

    @Override
    public <T> T accept(final DetailVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
