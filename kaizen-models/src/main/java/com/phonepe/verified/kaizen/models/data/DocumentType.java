package com.phonepe.verified.kaizen.models.data;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DocumentType {

    PAN(true) {
        @Override
        public <T, J> T accept(final DocumentType.DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPan(data);
        }
    },
    AADHAAR(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAadhaar(data);
        }
    },
    AADHAAR_FACE_IMAGE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAadhaarFaceImage(data);
        }
    },
    AADHAAR_VID(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAadhaarVid(data);
        }
    },
    AADHAAR_UID_TOKEN(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAadhaarUidToken(data);
        }
    },
    GST(true) {
        @Override
        public <T, J> T accept(final DocumentType.DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitGst(data);
        }
    },
    VOTER(true) {
        @Override
        public <T, J> T accept(final DocumentType.DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitVoter(data);
        }
    },
    DRIVING_LICENSE(true) {
        @Override
        public <T, J> T accept(final DocumentType.DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitDrivingLicense(data);
        }
    },
    PASSPORT(true) {
        @Override
        public <T, J> T accept(final DocumentType.DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPassport(data);
        }
    },
    ELECTRICITY_BILL(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitElectricityBill(data);
        }
    },
    AUTHORIZED_SIGNATORY_AADHAAR(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryAadhaar(data);
        }
    },
    AUTHORIZED_SIGNATORY_PASSPORT(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryPassport(data);
        }
    },
    AUTHORIZED_SIGNATORY_PAN(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryPan(data);
        }
    },
    AUTHORIZED_SIGNATORY_DRIVING_LICENSE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryDrivingLicense(data);
        }
    },
    CERTIFICATE_OF_INCORPORATION(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitCertificateOfIncorporation(data);
        }
    },
    PARTNERSHIP_DEED(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPartnershipDeed(data);
        }
    },
    SEA_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSeaCertificate(data);
        }
    },
    UA_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitUaCertificate(data);
        }
    },
    VAT_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitVatCertificate(data);
        }
    },
    TRADE_LICENSE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitTradeLicense(data);
        }
    },
    MCD_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitMcdCertificate(data);
        }
    },
    FOOD_LICENSE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitFoodLicense(data);
        }
    },
    CST_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitCstCertificate(data);
        }
    },
    PROPERTY_TAX_RECEIPT(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitPropertyTaxReceipt(data);
        }
    },
    MUNICIPAL_KHATA_COPY(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitMunicipalKhataReceipt(data);
        }
    },
    BANK_PROOF(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitBankProof(data);
        }
    },
    IE_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitIeCertificate(data);
        }
    },
    LLP_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitLlpCertificate(data);
        }
    },
    MUNICIPAL_CORPORATION_TAX_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitMunicipalCorporationTaxCertificate(data);
        }
    },
    TAN_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitTanCertificate(data);
        }
    },
    AUTHORIZED_SIGNATORY_DECLARATION(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryDeclaration(data);
        }
    },
    AUTHORIZED_SIGNATORY_APPOINTMENT_LETTER(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryAppointmentLetter(data);
        }
    },
    AUTHORIZED_SIGNATORY_KARTA_DECLARATION(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryKartaDeclaration(data);
        }
    },
    AUTHORIZED_SIGNATORY_PARTNER_RESOLUTION(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryPartnerResolution(data);
        }
    },
    AUTHORIZED_SIGNATORY_BOARD_RESOLUTION(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryBoardResolution(data);
        }
    },
    AUTHORIZED_SIGNATORY_MANAGING_BODY_RESOLUTION(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryManagingBodyResolution(data);
        }
    },
    AUTHORIZED_SIGNATORY_MANDATE_LETTER_RESOLUTION(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAuthorizedSignatoryMandateLetterResolution(data);
        }
    },
    SELFIE(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSelfie(data);
        }
    },
    DIGITAL_SIGNATURE(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitDigitalSignature(data);
        }
    },
    BRN_CERTIFICATE_OF_THE_STATE_OF_RAJASTHAN(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitBrnCertificateOfTheStateOfRajasthan(data);
        }
    },
    DRUG_LICENSE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitDrugLicense(data);
        }
    },
    CENTRAL_SALES_TAX(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitCentralSalesTax(data);
        }
    },
    MUNICIPAL_CORPORATION_DEPARTMENT_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitMunicipalCorporationDepartmentCertificate(data);
        }
    },
    TRUST_DEED(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitTrustDeed(data);
        }
    },
    OTP_BASED_VIDEO_KYC(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitOtpBasedVideoKyc(data);
        }
    },

    ESIGNED_ACCOUNT_OPENING_FORM(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitEsignedAccountOpeningForm(data);
        }
    },
    DIGIO_KYC_WORKFLOW_DETAILS(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitDigioKycWorkflowDetails(data);
        }
    },
    TIN_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitTinCertificate(data);
        }
    },
    LABOUR_CERTIFICATE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitLabourCertificate(data);
        }
    },
    LIQUOR_LICENSE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitLiquorLicense(data);
        }
    },
    DIAGNOSTIC_LICENSE(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitDiagnosticLicense(data);
        }
    },
    NREGA_JOB_CARD(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitNregaJobCard(data);
        }
    },
    LETTER_FROM_NATIONAL_POPULATION_REGISTER(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitLetterFromNationalPopulationRegister(data);
        }
    },
    CENTRAL_GOVT_ID(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitCentralGovtId(data);
        }
    },
    STATE_GOVT_ID(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitStateGovtId(data);
        }
    },
    CKYC_JSON_BLOB(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitCkycJsonBlob(data);
        }
    },
    GRAM_PANCHAYAT_DOCUMENTS(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitGramPanchayatDocuments(data);
        }
    },
    INCOME_PROOF(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitIncomeProof(data);
        }
    },
    CANCELLED_CHECK(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitCancelledCheck(data);
        }
    },
    BANK_STATEMENT(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitBankStatement(data);
        }
    },
    DUNS_NUMBER(true) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitDunsNumber(data);
        }
    },
    NATIONAL_ID_CARD(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitNationalIdCard(data);
        }
    },
    BANK_ACCOUNT(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitBankAccount(data);
        }
    },
    FORM_16(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitForm16(data);
        }
    },
    SALARY_SLIP(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSalarySlip(data);
        }
    },
    INVOICE_COPY(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitInvoiceCopy(data);
        }
    },
    PROOF_OF_DELIVERY(false) {
        @Override
        public <T, J> T accept(final DocumentTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitProofOfDelivery(data);
        }
    };

    private final boolean fraudCheckRequired;

    public abstract <T, J> T accept(DocumentTypeVisitor<T, J> visitor,
                                    J data);

    public interface DocumentTypeVisitor<T, J> {

        T visitPan(J data);

        T visitAadhaar(J data);

        T visitAadhaarVid(J data);

        T visitAadhaarUidToken(J data);

        T visitGst(J data);

        T visitVoter(J data);

        T visitDrivingLicense(J data);

        T visitPassport(J data);

        T visitElectricityBill(J data);

        T visitAuthorizedSignatoryAadhaar(J data);

        T visitAuthorizedSignatoryPassport(J data);

        T visitAuthorizedSignatoryPan(J data);

        T visitAuthorizedSignatoryDrivingLicense(J data);

        T visitCertificateOfIncorporation(J data);

        T visitPartnershipDeed(J data);

        T visitSeaCertificate(J data);

        T visitUaCertificate(J data);

        T visitVatCertificate(J data);

        T visitTradeLicense(J data);

        T visitMcdCertificate(J data);

        T visitFoodLicense(J data);

        T visitCstCertificate(J data);

        T visitPropertyTaxReceipt(J data);

        T visitMunicipalKhataReceipt(J data);

        T visitBankProof(J data);

        T visitIeCertificate(J data);

        T visitLlpCertificate(J data);

        T visitMunicipalCorporationTaxCertificate(J data);

        T visitTanCertificate(J data);

        T visitAuthorizedSignatoryDeclaration(J data);

        T visitAuthorizedSignatoryAppointmentLetter(J data);

        T visitAuthorizedSignatoryMandateLetterResolution(J data);

        T visitAuthorizedSignatoryManagingBodyResolution(J data);

        T visitAuthorizedSignatoryBoardResolution(J data);

        T visitAuthorizedSignatoryPartnerResolution(J data);

        T visitAuthorizedSignatoryKartaDeclaration(J data);

        T visitBrnCertificateOfTheStateOfRajasthan(J data);

        T visitSelfie(J data);

        T visitDigitalSignature(J data);

        T visitDrugLicense(J data);

        T visitCentralSalesTax(J data);

        T visitMunicipalCorporationDepartmentCertificate(J data);

        T visitTrustDeed(J data);

        T visitOtpBasedVideoKyc(J data);

        T visitEsignedAccountOpeningForm(J data);

        T visitDigioKycWorkflowDetails(J data);

        T visitTinCertificate(J data);

        T visitLabourCertificate(J data);

        T visitLiquorLicense(J data);

        T visitDiagnosticLicense(J data);

        T visitNregaJobCard(J data);

        T visitLetterFromNationalPopulationRegister(J data);

        T visitAadhaarFaceImage(J data);

        T visitCentralGovtId(J data);

        T visitStateGovtId(J data);

        T visitCkycJsonBlob(J data);

        T visitGramPanchayatDocuments(J data);

        T visitIncomeProof(J data);

        T visitCancelledCheck(J data);

        T visitBankStatement(J data);

        T visitDunsNumber(J data);

        T visitNationalIdCard(J data);

        T visitBankAccount(J data);

        T visitForm16(J data);

        T visitSalarySlip(J data);

        T visitInvoiceCopy(J data);

        T visitProofOfDelivery(J data);

    }
}
