package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.shadow.common.HttpMethod;
import com.phonepe.shadow.template.mapping.loader.SubmitLoader;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ApiCallShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String url;

    private HttpMethod method;

    private JsonNode payload;

    private Map<String, String> headers;

    private SubmitLoader submitLoader;

    @Builder
    public ApiCallShadowV2ResponseConfig(final String url,
                                         final HttpMethod method,
                                         final JsonNode payload,
                                         final Map<String, String> headers,
                                         final SubmitLoader submitLoader) {
        this();
        this.url = url;
        this.method = method;
        this.payload = payload;
        this.headers = headers;
        this.submitLoader = submitLoader;
    }

    public ApiCallShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.API_CALL);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
