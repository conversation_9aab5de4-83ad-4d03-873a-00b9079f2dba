package com.phonepe.verified.kaizen.models.configs.template.config.visitor;

import com.phonepe.verified.kaizen.models.configs.template.config.impl.AlwaysResumeGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.AlwaysSummaryViewGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.HopeRuleBasedSummaryView;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.SummaryViewBasedOnFlagGetTemplateConfig;

public interface GetTemplateConfigVisitor<T, J> {

    T visit(AlwaysSummaryViewGetTemplateConfig alwaysSummaryViewGetTemplateConfig,
            J data);

    T visit(SummaryViewBasedOnFlagGetTemplateConfig summaryViewBasedOnFlagGetTemplateConfig,
            J data);

    T visit(AlwaysResumeGetTemplateConfig alwaysResumeGetTemplateConfig,
            J data);

    T visit(HopeRuleBasedSummaryView hopeRuleBasedSummaryView,
            J data);
}
