package com.phonepe.verified.kaizen.models.data;

public enum MatchType {

    ANY_MATCH {
        @Override
        public <T, J> T accept(final MatchTypeVisitor<T, J> visitor,
                               J data) {
            return visitor.visitAnyMatch(data);
        }
    },
    ALL_MATCH {
        @Override
        public <T, J> T accept(final MatchTypeVisitor<T, J> visitor,
                               J data) {
            return visitor.visitAllMatch(data);
        }
    };

    public abstract <T, J> T accept(MatchTypeVisitor<T, J> visitor,
                                    J data);

    public interface MatchTypeVisitor<T, J> {

        T visitAnyMatch(J data);

        T visitAllMatch(J data);
    }
}