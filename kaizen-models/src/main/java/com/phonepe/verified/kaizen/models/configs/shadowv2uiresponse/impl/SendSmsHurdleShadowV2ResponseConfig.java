package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SendSmsHurdleShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String title;

    private String subtitle;

    private String smsContent;

    private String smsDestination;

    private Boolean cancelable;

    private ShadowV2ResponseConfig onSmsDeliveredShadowV2ResponseConfig;

    private String bottomButtonText;

    private String loadingText;

    public SendSmsHurdleShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.SEND_SMS_HURDLE);
    }

    @Builder
    public SendSmsHurdleShadowV2ResponseConfig(final String title,
                                               final String subtitle,
                                               final String smsContent,
                                               final String smsDestination,
                                               final Boolean cancelable,
                                               final ShadowV2ResponseConfig onSmsDeliveredShadowV2ResponseConfig,
                                               final String bottomButtonText,
                                               final String loadingText) {

        super(ShadowV2ResponseType.SEND_SMS_HURDLE);
        this.title = title;
        this.subtitle = subtitle;
        this.smsContent = smsContent;
        this.smsDestination = smsDestination;
        this.cancelable = cancelable;
        this.onSmsDeliveredShadowV2ResponseConfig = onSmsDeliveredShadowV2ResponseConfig;
        this.bottomButtonText = bottomButtonText;
        this.loadingText = loadingText;
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
