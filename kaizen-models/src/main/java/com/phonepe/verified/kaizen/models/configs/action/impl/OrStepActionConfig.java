package com.phonepe.verified.kaizen.models.configs.action.impl;

import com.phonepe.verified.kaizen.models.configs.action.StepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.StepActionConfigType;
import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrStepActionConfig extends StepActionConfig {

    private static final long serialVersionUID = 5340015905690790341L;

    @Valid
    @NotNull
    private StepActionConfig left;

    @Valid
    @NotNull
    private StepActionConfig right;

    public OrStepActionConfig() {
        super(StepActionConfigType.OR);
    }

    @Builder
    public OrStepActionConfig(final StepActionConfig left,
                              final StepActionConfig right) {
        this();
        this.left = left;
        this.right = right;
    }

    @Override
    public <T, J> T accept(final StepActionVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
