package com.phonepe.verified.kaizen.models.configs.textsource.impl;

import com.phonepe.verified.kaizen.models.configs.textsource.TextSourceConfig;
import com.phonepe.verified.kaizen.models.configs.textsource.TextSourceType;
import com.phonepe.verified.kaizen.models.configs.textsource.TextSourceTypeVisitor;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ActionHandleBarsTextSourceConfig extends TextSourceConfig {


    private static final long serialVersionUID = -423500120320269990L;


    @NotEmpty
    private String handlebarsTranslator;

    @NotEmpty
    private String actionMappingId;

    @NotEmpty
    private String profileStepMappingId;

    private boolean returnNullWhenActionNotFound;

    public ActionHandleBarsTextSourceConfig() {
        super(TextSourceType.ACTION_HANDLEBARS_TEXT_SOURCE);
    }

    @Builder
    public ActionHandleBarsTextSourceConfig(final String handlebarsTranslator,
                                            final String actionMappingId,
                                            final String profileStepMappingId,
                                            final boolean returnNullWhenActionNotFound) {
        this();
        this.handlebarsTranslator = handlebarsTranslator;
        this.actionMappingId = actionMappingId;
        this.profileStepMappingId = profileStepMappingId;
        this.returnNullWhenActionNotFound = returnNullWhenActionNotFound;
    }

    @Override
    public <T, U> T accept(final TextSourceTypeVisitor<T, U> visitor,
                           final U data) {
        return visitor.visit(this, data);
    }
}
