package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.persistforensics;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class PersistForensicsActionContext extends StepActionContext {

    private List<ForensicsValidationRule> forensicsValidationRules;

    public PersistForensicsActionContext() {
        super(ActionType.PERSIST_FORENSICS);
    }

    @Builder
    public PersistForensicsActionContext(final List<ForensicsValidationRule> forensicsValidationRules) {
        this();
        this.forensicsValidationRules = forensicsValidationRules;
    }

}
