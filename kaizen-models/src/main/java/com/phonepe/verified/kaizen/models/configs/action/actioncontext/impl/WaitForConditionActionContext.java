package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WaitForConditionActionContext extends StepActionContext {

    private static final long serialVersionUID = 4575471592387147751L;

    @NotEmpty
    private String evaluationRule;

    @NotNull
    private long timeoutInSeconds;

    private long retryIntervalInSeconds;

    @Builder
    public WaitForConditionActionContext(final String evaluationRule,
                                         final long timeoutInSeconds,
                                         final long retryIntervalInSeconds) {
        this();
        this.evaluationRule = evaluationRule;
        this.timeoutInSeconds = timeoutInSeconds;
        this.retryIntervalInSeconds = retryIntervalInSeconds;
    }

    protected WaitForConditionActionContext() {
        super(ActionType.WAIT_FOR_CONDITION);
    }
}
