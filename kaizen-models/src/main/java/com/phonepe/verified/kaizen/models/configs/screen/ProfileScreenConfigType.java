package com.phonepe.verified.kaizen.models.configs.screen;

import lombok.experimental.UtilityClass;

public enum ProfileScreenConfigType {

    STANDARD {
        @Override
        public <T, J> T accept(final ProfileScreenConfigTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitStandard(data);
        }
    },
    SEQUENTIAL {
        @Override
        public <T, J> T accept(final ProfileScreenConfigTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitSequential(data);
        }
    },
    ;

    public abstract <T, J> T accept(ProfileScreenConfigTypeVisitor<T, J> visitor,
                                    J data);

    public interface ProfileScreenConfigTypeVisitor<T, J> {

        T visitStandard(J data);

        T visitSequential(J data);
    }

    @UtilityClass
    public static final class Names {

        public static final String STANDARD = "STANDARD";
        public static final String SEQUENTIAL = "SEQUENTIAL";
    }

}
