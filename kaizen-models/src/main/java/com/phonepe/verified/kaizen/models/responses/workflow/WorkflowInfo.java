package com.phonepe.verified.kaizen.models.responses.workflow;

import com.phonepe.verified.kaizen.models.data.EntityType;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class WorkflowInfo {

    private final String workflowId;

    private final String entityId;

    private final EntityType entityType;

    private final String profileId;

    private final String tag;

    private final LocalDateTime createdAt;

    private final LocalDateTime lastUpdatedAt;
}
