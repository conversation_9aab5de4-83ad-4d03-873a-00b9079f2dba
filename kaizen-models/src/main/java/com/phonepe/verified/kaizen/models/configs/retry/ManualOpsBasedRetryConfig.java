package com.phonepe.verified.kaizen.models.configs.retry;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ManualOpsBasedRetryConfig extends RetryConfig {

    private boolean enabled;

    public ManualOpsBasedRetryConfig() {
        super(RetryConfigType.MANUAL_OPS_BASED);
    }

    @Override
    public <T, J> T accept(RetryConfigVisitor<T, J> visitor,
                           J data) {
        return visitor.visit(this, data);
    }
}
