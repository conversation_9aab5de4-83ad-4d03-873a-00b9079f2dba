package com.phonepe.verified.kaizen.models.configs.retry;

import lombok.experimental.UtilityClass;

public enum RetryConfigType {

    COUNT_BASED,

    HOPE_RULE_BASED,

    MANUAL_OPS_BASED,

    COMPOSITE;

    @UtilityClass
    public class Names {

        public static final String COUNT_BASED = "COUNT_BASED";
        public static final String HOPE_RULE_BASED = "HOPE_RULE_BASED";
        public static final String MANUAL_OPS_BASED = "MANUAL_OPS_BASED";
        public static final String COMPOSITE = "COMPOSITE";
    }
}
