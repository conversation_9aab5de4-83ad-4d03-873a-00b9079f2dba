package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse;

import lombok.experimental.UtilityClass;

public enum ShadowV2ResponseType {

    MOVE_TO_SECTION {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitMoveToSection();
        }
    },
    MOVE_TO_SECTION_AND_CLEAR_BACK_STACK {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitMoveToSectionAndClearBackStack();
        }
    },
    MOVE_BACK_ACTION {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitMoveBack();
        }
    },
    OPEN_BOTTOM_SHEET {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitOpenBottomSheet();
        }
    },
    OPEN_BOTTOM_SHEET_V2 {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitOpenBottomSheetV2();
        }
    },
    OPEN_GENERIC_DIALOG {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitOpenGenericDialog();
        }
    },
    OPEN_STATUS_PAGE {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitOpenStatusPage();
        }
    },
    OTP_HURDLE {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitOtpHurdle();
        }
    },
    VALIDATION_ACTION {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitRedirectionValidationAction();
        }
    },
    TERMINAL_ACTION {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitTerminalAction();
        }
    },
    EVALUATED {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitEvaluated();
        }
    },
    SECTION_REFRESH {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitSectionRefresh();
        }
    },
    RICH_TEXT_BOTTOM_SHEET {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitRichTextBottomSheet();
        }
    },
    MOVE_TO_PRE_SDK_SCREEN_ACTION {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitMoveToPreSdkScreenAction();
        }
    },
    SHADOW_V2_ACTION {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitShadowV2Action();
        }
    },
    UPDATE_FIELDS_ACTION {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitUpdateFieldsAction();
        }
    },
    API_CALL {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitApiCallAction();
        }
    },
    OPEN_WEB_VIEW_ACTION {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitOpenWebViewAction();
        }
    },
    SELFIE_HURDLE {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitSelfieHurdle();
        }
    },
    SEND_SMS_HURDLE {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitSendSmsHurdle();
        }
    },
    OPEN_POPUP_WITH_TIMER {
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitOpenPopupWithTimer();
        }
    },
    MOVE_TO_SECTION_WITH_SCREEN_MAPPING_ID{
        @Override
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitMoveToSectionWithScreenMappingId();
        }
    },
    MOVE_TO_SECTION_AND_CLEAR_BACK_STACK_WITH_SCREEN_MAPPING_ID {
        public <T> T accept(final UiResponseTypeVisitor<T> visitor) {
            return visitor.visitMoveToSectionAndClearBackStackWithScreenMappingId();
        }    };

    public abstract <T> T accept(UiResponseTypeVisitor<T> visitor);

    public interface UiResponseTypeVisitor<T> {

        T visitMoveToSection();

        T visitMoveToSectionAndClearBackStack();

        T visitMoveBack();

        T visitOpenBottomSheet();

        T visitOpenBottomSheetV2();

        T visitOpenGenericDialog();

        T visitOpenStatusPage();

        T visitOtpHurdle();

        T visitRedirectionValidationAction();

        T visitTerminalAction();

        T visitMoveToPreSdkScreenAction();

        T visitShadowV2Action();

        T visitEvaluated();

        T visitSectionRefresh();

        T visitRichTextBottomSheet();

        T visitUpdateFieldsAction();

        T visitApiCallAction();

        T visitOpenWebViewAction();

        T visitSelfieHurdle();

        T visitSendSmsHurdle();

        T visitOpenPopupWithTimer();

        T visitMoveToSectionWithScreenMappingId();

        T visitMoveToSectionAndClearBackStackWithScreenMappingId();
    }

    @UtilityClass
    public static final class Names {

        public static final String MOVE_TO_SECTION = "MOVE_TO_SECTION";
        public static final String MOVE_TO_SECTION_AND_CLEAR_BACK_STACK = "MOVE_TO_SECTION_AND_CLEAR_BACK_STACK";
        public static final String MOVE_BACK_ACTION = "MOVE_BACK_ACTION";
        public static final String OPEN_BOTTOM_SHEET = "OPEN_BOTTOM_SHEET";
        public static final String OPEN_BOTTOM_SHEET_V2 = "OPEN_BOTTOM_SHEET_V2";
        public static final String OPEN_GENERIC_DIALOG = "OPEN_GENERIC_DIALOG";
        public static final String OPEN_STATUS_PAGE = "OPEN_STATUS_PAGE";
        public static final String OTP_HURDLE = "OTP_HURDLE";
        public static final String TERMINAL_ACTION = "TERMINAL_ACTION";
        public static final String MOVE_TO_PRE_SDK_SCREEN_ACTION = "MOVE_TO_PRE_SDK_SCREEN_ACTION";
        public static final String SHADOW_V2_ACTION = "SHADOW_V2_ACTION";
        public static final String EVALUATED = "EVALUATED";
        public static final String VALIDATION_ACTION = "VALIDATION_ACTION";
        public static final String SECTION_REFRESH = "SECTION_REFRESH";
        public static final String RICH_TEXT_BOTTOM_SHEET = "RICH_TEXT_BOTTOM_SHEET";
        public static final String UPDATE_FIELDS_ACTION = "UPDATE_FIELDS_ACTION";
        public static final String API_CALL = "API_CALL";
        public static final String OPEN_WEB_VIEW_ACTION = "OPEN_WEB_VIEW_ACTION";
        public static final String SELFIE_HURDLE = "SELFIE_HURDLE";
        public static final String SEND_SMS_HURDLE = "SEND_SMS_HURDLE";
        public static final String OPEN_POPUP_WITH_TIMER = "OPEN_POPUP_WITH_TIMER";
        public static final String MOVE_TO_SECTION_WITH_SCREEN_MAPPING_ID = "MOVE_TO_SECTION_WITH_SCREEN_MAPPING_ID";
        public static final String MOVE_TO_SECTION_AND_CLEAR_BACK_STACK_WITH_SCREEN_MAPPING_ID = "MOVE_TO_SECTION_AND_CLEAR_BACK_STACK_WITH_SCREEN_MAPPING_ID";
    }
}
