package com.phonepe.verified.kaizen.models.configs.postcompletionaction;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.AndPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.ClientCallbackPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.OrPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.TagCalculationOnEntityDetailsPostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl.TagCalculationOnWorkflowContextPostCompletionActionConfig;

public interface PostCompletionActionConfigVisitor<T, J> {

    T visit(AndPostCompletionActionConfig andPostCompletionActionConfig,
            J data);

    T visit(OrPostCompletionActionConfig orPostCompletionActionConfig,
            J data);

    T visit(ClientCallbackPostCompletionActionConfig clientCallbackPostCompletionActionConfig,
            J data);

    T visit(TagCalculationOnWorkflowContextPostCompletionActionConfig tagCalculationOnWorkflowContextPostCompletionActionConfig,
            J data);

    T visit(TagCalculationOnEntityDetailsPostCompletionActionConfig tagCalculationOnEntityDetailsPostCompletionActionConfig,
            J data);
}
