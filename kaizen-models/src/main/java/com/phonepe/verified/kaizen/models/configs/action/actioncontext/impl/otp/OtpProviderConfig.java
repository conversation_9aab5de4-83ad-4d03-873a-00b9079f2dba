package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.OtpProviderType.Names;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true, defaultImpl = SentinelOtpProviderConfig.class)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.SENTINEL, value = SentinelOtpProviderConfig.class)})
public abstract class OtpProviderConfig {

    private OtpProviderType type;

    public abstract <T, J> T accept(final OtpProviderConfigVisitor<T, J> visitor,
                                    final J data);
}
