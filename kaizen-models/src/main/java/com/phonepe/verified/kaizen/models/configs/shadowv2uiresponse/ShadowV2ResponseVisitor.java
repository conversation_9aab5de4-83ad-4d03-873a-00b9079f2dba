package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl.*;

public interface ShadowV2ResponseVisitor<T, U> {

    T visit(MoveToSectionShadowV2ResponseConfig moveToSectionUiResponseConfig,
            U input);

    T visit(MoveToSectionAndClearBackstackShadowV2ResponseConfig moveToSectionAndClearBackstackShadowV2ResponseConfig,
            U input);

    T visit(MoveBackActionShadowV2ResponseConfig moveBackActionShadowV2ResponseConfig,
            U input);

    T visit(OpenBottomSheetShadowV2ResponseConfig openBottomSheetUiResponseConfig,
            U input);

    T visit(OpenBottomSheetV2ShadowV2ResponseConfig openBottomSheetV2UiResponseConfig,
            U input);

    T visit(OpenStatusPageShadowV2ResponseConfig openStatusPageUiResponseConfig,
            U input);

    T visit(OtpHurdleShadowV2ResponseConfig otpHurdleShadowV2ResponseConfig,
            U input);

    T visit(ValidationActionShadowV2ResponseConfig validationShadowV2ResponseConfig,
            U input);

    T visit(TerminalActionShadowV2ResponseConfig terminalActionShadowV2ResponseConfig,
            U input);

    T visit(EvaluatedShadowV2ResponseConfig evaluatedShadowV2ResponseConfig,
            U input);

    T visit(SectionRefreshShadowV2ResponseConfig sectionRefreshShadowV2ResponseConfig,
            U input);

    T visit(RichTextBottomSheetShadowV2ResponseConfig richTextBottomSheetShadowV2ResponseConfig,
            U input);

    T visit(OpenGenericDialogActionShadowV2ResponseConfig openGenericDialogShadowV2ResponseConfig,
            U input);

    T visit(MoveToPreSdkScreenActionShadowV2ResponseConfig moveToPreSdkScreenActionShadowV2ResponseConfig,
            U input);

    T visit(ShadowV2ActionShadowV2ResponseConfig shadowV2ActionShadowV2ResponseConfig,
            U input);

    T visit(UpdateFieldsActionShadowV2ResponseConfig updateFieldsActionShadowV2ResponseConfig,
            U input);

    T visit(ApiCallShadowV2ResponseConfig apiCallShadowV2ResponseConfig,
            U input);

    T visit(OpenWebViewActionShadowV2ResponseConfig openWebViewActionShadowV2ResponseConfig,
            U input);

    T visit(SelfieHurdleShadowV2ResponseConfig selfieHurdleShadowV2ResponseConfig,
            U input);

    T visit(SendSmsHurdleShadowV2ResponseConfig sendSmsHurdleShadowV2ResponseConfig,
            U input);

    T visit(OpenPopupWithTimerShadowV2ResponseConfig openPopupWithTimerShadowV2ResponseConfig,
            U input);

    T visit(MoveToSectionWithScreenMappingIdShadowV2ResponseConfig moveToSectionWithScreenMappingIdShadowV2ResponseConfig,
            U input);

    T visit(MoveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig moveToSectionAndClearBackstackWithScreenMappingIdShadowV2ResponseConfig,
            U input);
}
