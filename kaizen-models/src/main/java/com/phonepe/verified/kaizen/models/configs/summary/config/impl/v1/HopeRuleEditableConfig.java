package com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1;

import com.phonepe.verified.kaizen.models.configs.summary.config.EditableConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.EditableConfigType;
import com.phonepe.verified.kaizen.models.configs.summary.config.visitor.SummaryViewEditableConfigVisitor;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class HopeRuleEditableConfig extends EditableConfig {

    @NotEmpty
    private String rule;

    public HopeRuleEditableConfig() {
        super(EditableConfigType.HOPE_RULE);
    }

    @Builder
    public HopeRuleEditableConfig(String rule) {
        this();
        this.rule = rule;
    }

    @Override
    public <T, J> T evaluate(SummaryViewEditableConfigVisitor<T, J> visitor,
                             J data) {
        return visitor.visitHopeRule(this, data);
    }
}
