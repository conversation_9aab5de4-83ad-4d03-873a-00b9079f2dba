package com.phonepe.verified.kaizen.models.requests.workflow;

import com.phonepe.verified.kaizen.models.data.EntityType;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AbortWorkflowRequest {

    @NotEmpty
    private String entityId;

    @NotNull
    private EntityType entityType;

    @NotEmpty
    private String workflowId;
}
