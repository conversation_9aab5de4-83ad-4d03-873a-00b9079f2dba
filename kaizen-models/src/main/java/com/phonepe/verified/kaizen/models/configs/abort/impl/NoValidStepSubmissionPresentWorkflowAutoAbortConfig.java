package com.phonepe.verified.kaizen.models.configs.abort.impl;

import static com.phonepe.verified.kaizen.models.responses.State.ABORTED;
import static com.phonepe.verified.kaizen.models.responses.State.CREATED;
import static com.phonepe.verified.kaizen.models.responses.State.DISCARDED;
import static com.phonepe.verified.kaizen.models.responses.State.FAILURE;
import static com.phonepe.verified.kaizen.models.responses.State.INVALIDATED;
import static com.phonepe.verified.kaizen.models.responses.State.IN_PROGRESS;
import static com.phonepe.verified.kaizen.models.responses.State.SKIPPED;

import com.google.common.collect.ImmutableList;
import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfig;
import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfigType;
import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfigVisitor;
import com.phonepe.verified.kaizen.models.responses.State;
import io.dropwizard.util.Duration;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Getter
@EqualsAndHashCode(callSuper = true)
public class NoValidStepSubmissionPresentWorkflowAutoAbortConfig extends WorkflowAutoAbortConfig {

    private static final long serialVersionUID = 8796422851017036885L;

    @NotEmpty
    private final List<State> workflowStatesToConsider;

    @NotEmpty
    private final List<State> workflowStepStatesToConsider;

    private final boolean rescheduleIfStepInPseudoSuccess;

    @Builder
    @Jacksonized
    public NoValidStepSubmissionPresentWorkflowAutoAbortConfig(final Duration abortAfter,
                                                               @Nullable final List<State> workflowStatesToConsider,
                                                               @Nullable final List<State> workflowStepStatesToConsider,
                                                               final boolean rescheduleIfStepInPseudoSuccess) {

        super(WorkflowAutoAbortConfigType.NO_VALID_STEP_SUBMISSION_PRESENT,
                "NO_WORKFLOW_STEPS_COMPLETED_SUCCESSFULLY_WITHIN_TTL", abortAfter);

        this.workflowStatesToConsider = Optional.ofNullable(workflowStatesToConsider)
                .orElse(ImmutableList.of(State.IN_PROGRESS, State.PSEUDO_SUCCESS));

        this.workflowStepStatesToConsider = Optional.ofNullable(workflowStepStatesToConsider)
                .orElse(ImmutableList.of(IN_PROGRESS, CREATED, FAILURE, ABORTED, DISCARDED, INVALIDATED, SKIPPED));

        this.rescheduleIfStepInPseudoSuccess = rescheduleIfStepInPseudoSuccess;
    }

    @Override
    public <T, J> T accept(final WorkflowAutoAbortConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
