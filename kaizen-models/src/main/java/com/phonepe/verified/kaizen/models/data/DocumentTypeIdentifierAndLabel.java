package com.phonepe.verified.kaizen.models.data;

import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentTypeIdentifierAndLabel {

    @NotNull
    private DocumentType documentType;

    @Valid
    @NotEmpty
    private List<@NotNull DocumentIdentifierAndLabel> documents;

    @Nullable
    private Map<String, String> metadata;
}
