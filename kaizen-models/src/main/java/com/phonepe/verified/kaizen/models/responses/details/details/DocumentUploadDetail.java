package com.phonepe.verified.kaizen.models.responses.details.details;

import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.details.Detail;
import com.phonepe.verified.kaizen.models.responses.details.DetailVisitor;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentUploadDetail extends Detail {

    private String documentId;

    private DocumentType documentType;

    private String documentLabel;

    public DocumentUploadDetail() {
        super(DetailType.DOCUMENT_UPLOAD);
    }

    @Builder
    public DocumentUploadDetail(final String actionId,
                                final State actionCompletionState,
                                final String actionMappingId,
                                final LocalDateTime createdAt,
                                final LocalDateTime lastUpdatedAt,
                                final String documentId,
                                final DocumentType documentType,
                                final String documentLabel,
                                final String workflowId,
                                final String workflowStepId) {
        super(DetailType.DOCUMENT_UPLOAD, actionId, actionCompletionState, actionMappingId, createdAt, lastUpdatedAt,
                workflowId, workflowStepId);
        this.documentId = documentId;
        this.documentType = documentType;
        this.documentLabel = documentLabel;
    }

    @Override
    public <T> T accept(final DetailVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
