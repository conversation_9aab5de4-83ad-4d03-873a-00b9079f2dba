package com.phonepe.verified.kaizen.models.configs.session.impl;

import com.phonepe.verified.kaizen.models.configs.session.SessionManagementConfig;
import com.phonepe.verified.kaizen.models.configs.session.SessionManagementVisitor;
import com.phonepe.verified.kaizen.models.data.SessionType;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class ClientManagedSessionConfig extends SessionManagementConfig {

    @NotEmpty
    private String principalBuilder;

    private boolean validateTimer;

    private boolean validateToken;

    public ClientManagedSessionConfig() {
        super(SessionType.CLIENT_MANAGED);
    }

    @Builder
    public ClientManagedSessionConfig(@NonNull final String profileId,
                                      final boolean disabled,
                                      final String sourceType,
                                      @NonNull final String principalBuilder,
                                      final boolean validateTimer,
                                      final boolean validateToken) {
        super(SessionType.CLIENT_MANAGED, profileId, disabled, sourceType);
        this.principalBuilder = principalBuilder;
        this.validateTimer = validateTimer;
        this.validateToken = validateToken;
    }

    @Override
    public <T, U> T accept(final SessionManagementVisitor<T, U> visitor,
                           final U data) {
        return visitor.visit(this, data);
    }
}
