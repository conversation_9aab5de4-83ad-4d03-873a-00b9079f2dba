package com.phonepe.verified.kaizen.models.configs.action;

import lombok.experimental.UtilityClass;

public enum StepActionConfigType {
    STANDARD {
        @Override
        public <T, J> T accept(final StepActionConfigTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitStandard(data);
        }
    },
    AND {
        @Override
        public <T, J> T accept(final StepActionConfigTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitAnd(data);
        }
    },
    OR {
        @Override
        public <T, J> T accept(final StepActionConfigTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitOr(data);
        }
    },
    EVALUATED {
        @Override
        public <T, J> T accept(final StepActionConfigTypeVisitor<T, J> visitor,
                               final J data) {
            return visitor.visitEvaluated(data);
        }
    };

    public abstract <T, J> T accept(StepActionConfigTypeVisitor<T, J> visitor,
                                    J data);

    public interface StepActionConfigTypeVisitor<T, J> {

        T visitStandard(J data);

        T visitAnd(J data);

        T visitEvaluated(J data);

        T visitOr(J data);

    }

    @UtilityClass
    public static final class Names {

        public static final String STANDARD = "STANDARD";
        public static final String AND = "AND";
        public static final String OR = "OR";
        public static final String EVALUATED = "EVALUATED";
    }
}