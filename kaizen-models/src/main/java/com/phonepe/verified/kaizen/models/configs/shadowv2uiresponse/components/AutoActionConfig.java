package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components;

import io.dropwizard.util.Duration;
import javax.validation.constraints.AssertTrue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoActionConfig {

    private boolean enabled;

    private Duration delay;

    @AssertTrue(message = "When auto action config is enabled, delayInMs > 0")
    private boolean isValid() {
        return !enabled || delay.toMilliseconds() > 0;
    }
}
