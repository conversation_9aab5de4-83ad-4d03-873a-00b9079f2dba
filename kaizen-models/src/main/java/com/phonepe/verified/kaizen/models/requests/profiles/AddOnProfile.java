package com.phonepe.verified.kaizen.models.requests.profiles;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.models.configs.profile.ProfileType;
import com.phonepe.verified.kaizen.models.configs.summary.config.SummaryViewConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
public class AddOnProfile extends Profile {

    private String rule;

    private String addOnType;

    private Integer priority;

    protected AddOnProfile() {
        super(ProfileType.ADD_ON);
    }

    @Builder
    public AddOnProfile(final String profileId,
                        final String organization,
                        final String namespace,
                        final String type,
                        final String version,
                        final SummaryViewConfig summaryViewConfig,
                        final GetTemplateConfig getTemplateConfig,
                        final PostCompletionActionConfig postCompletionActionConfig,
                        @Nullable final List<PostWorkflowCreationActionConfig> postWorkflowCreationActionConfigs,
                        final LocalDateTime createdAt,
                        final LocalDateTime lastUpdatedAt,
                        final List<ProfileStep> profileSteps,
                        final String rule,
                        final String addOnType,
                        final Integer priority) {
        super(profileId, organization, namespace, type, version, summaryViewConfig, getTemplateConfig,
                postCompletionActionConfig, postWorkflowCreationActionConfigs, ProfileType.ADD_ON, createdAt,
                lastUpdatedAt, profileSteps);
        this.rule = rule;
        this.addOnType = addOnType;
        this.priority = priority;
    }

    @Override
    public <T, J> T accept(final ProfileVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }

    @Override
    public AddOnProfile withProfileSteps(@Valid @NotEmpty final List<ProfileStep> profileSteps) {
        return getProfileSteps() == profileSteps
               ? this
               : new AddOnProfile(getProfileId(), getOrganization(), getNamespace(), getType(), getVersion(),
                       getSummaryViewConfig(), getGetTemplateConfig(), getPostCompletionActionConfig(),
                       getPostWorkflowCreationActionConfigs(), getCreatedAt(), getLastUpdatedAt(), profileSteps,
                       getRule(), this.getAddOnType(), getPriority());
    }
}
