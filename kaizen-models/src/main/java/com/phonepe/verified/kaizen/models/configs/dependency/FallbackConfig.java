package com.phonepe.verified.kaizen.models.configs.dependency;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FallbackConfig {

    @NotEmpty
    private String fallbackActionMappingId;

    @NotEmpty
    private String fallbackRule;
}
