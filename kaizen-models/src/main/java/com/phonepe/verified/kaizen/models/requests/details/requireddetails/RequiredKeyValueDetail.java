package com.phonepe.verified.kaizen.models.requests.details.requireddetails;

import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetail;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetailVisitor;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RequiredKeyValueDetail extends RequiredDetail {

    @NotEmpty
    private Set<@NotEmpty String> requiredKeys;

    public RequiredKeyValueDetail() {
        super(DetailType.KEY_VALUE);
    }

    @Builder
    public RequiredKeyValueDetail(final Set<String> requiredKeys) {
        this();
        this.requiredKeys = requiredKeys;
    }

    @Override
    public <T, V> T accept(final RequiredDetailVisitor<T, V> visitor,
                           final V data) {
        return visitor.visit(this, data);
    }
}
