package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpenWebViewActionShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String url;

    private String title;

    @Builder
    public OpenWebViewActionShadowV2ResponseConfig(final String url,
                                                   final String title) {
        this();
        this.url = url;
        this.title = title;
    }

    public OpenWebViewActionShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.OPEN_WEB_VIEW_ACTION);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
