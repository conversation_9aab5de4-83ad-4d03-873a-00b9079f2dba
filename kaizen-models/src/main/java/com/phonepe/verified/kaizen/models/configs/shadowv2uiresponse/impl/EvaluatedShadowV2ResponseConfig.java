package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.EvaluationRuleResponseConfig;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvaluatedShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    @Valid
    private List<EvaluationRuleResponseConfig> evaluationRuleResponseConfigs;

    @Valid
    @NotNull
    private ShadowV2ResponseConfig defaultResponseConfig;

    public EvaluatedShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.EVALUATED);
    }

    @Builder
    public EvaluatedShadowV2ResponseConfig(final List<EvaluationRuleResponseConfig> evaluationRuleResponseConfigs,
                                           final ShadowV2ResponseConfig defaultResponseConfig) {
        this();
        this.evaluationRuleResponseConfigs = evaluationRuleResponseConfigs;
        this.defaultResponseConfig = defaultResponseConfig;
    }


    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
