package com.phonepe.verified.kaizen.models.configs.screen;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfigType.Names;
import com.phonepe.verified.kaizen.models.configs.screen.impl.SequentialProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.impl.StandardProfileScreenConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.STANDARD, value = StandardProfileScreenConfig.class),
        @JsonSubTypes.Type(name = Names.SEQUENTIAL, value = SequentialProfileScreenConfig.class)})
public abstract class ProfileScreenConfig {

    private ProfileScreenConfigType type;

    public abstract <T> T accept(ProfileScreenVisitor<T> visitor);

}
