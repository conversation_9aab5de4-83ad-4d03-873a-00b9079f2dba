package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp.OtpProviderConfig;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.Map;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class OtpHurdleV2ActionContext extends StepActionContext {

    private static final long serialVersionUID = -8675723998426200559L;

    @Valid
    @NotNull
    private OtpProviderConfig otpProviderConfig;

    @Valid
    @NotEmpty
    private Map<TemplateType, UiResponseConfig> otpResponseMap;

    public OtpHurdleV2ActionContext() {
        super(ActionType.OTP_HURDLE_V2);
    }

    @Builder
    public OtpHurdleV2ActionContext(final OtpProviderConfig otpProviderConfig,
                                    final Map<TemplateType, UiResponseConfig> otpResponseMap) {
        this();
        this.otpProviderConfig = otpProviderConfig;
        this.otpResponseMap = otpResponseMap;
    }

    @AssertTrue(message = "otpProviderConfig and otpResponseMap cannot be empty")
    private boolean isValid() {
        return Objects.nonNull(otpProviderConfig) && Objects.nonNull(otpResponseMap) && !otpResponseMap.isEmpty();
    }
}
