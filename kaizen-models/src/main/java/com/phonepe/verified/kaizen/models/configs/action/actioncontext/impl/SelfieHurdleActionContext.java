package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.SelfieActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.Map;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class SelfieHurdleActionContext extends StepActionContext implements SelfieActionContext {

    private static final long serialVersionUID = 1049310508982131526L;

    @Valid
    private Map<TemplateType, UiResponseConfig> selfieResponseMap;

    public SelfieHurdleActionContext() {
        super(ActionType.SELFIE_HURDLE);
    }

    @Builder
    public SelfieHurdleActionContext(final Map<TemplateType, UiResponseConfig> selfieResponseMap) {
        this.selfieResponseMap = selfieResponseMap;
    }

    @AssertTrue(message = "selfieResponseMap cannot be empty")
    private boolean isValid() {
        return Objects.nonNull(selfieResponseMap) && !selfieResponseMap.isEmpty();
    }
}
