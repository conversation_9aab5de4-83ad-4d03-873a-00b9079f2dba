package com.phonepe.verified.kaizen.models.data;

import lombok.Getter;

@Getter
public enum ActionFailureErrorCode {

    OTP_GENERATION_BANNED("User is banned from generating OTP for some time"),
    OTP_ATTEMPTS_EXHAUSTED("OTP Attempts Exhausted"),
    DEPENDENT_ACTION_FAILED("The action on which the current action is dependent has failed"),
    DOCUMENT_MASKING_FAILED("Document Masking Failed."),
    DOCUMENT_MASKING_FAILED_IMPROPER_IMAGE(
            "Document Masking failed because images were not proper or drishti gave non success response"),
    DOCUMENT_MASKING_FAILED_ID_NOT_VISIBLE("Document Masking failed because id number was not detected"),
    PERSIST_FORENSICS_FAILED("Failed to persist forensics."),
    ;

    @Getter
    private final String reason;

    ActionFailureErrorCode(final String reason) {
        this.reason = reason;
    }
}