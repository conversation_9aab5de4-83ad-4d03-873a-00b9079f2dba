package com.phonepe.verified.kaizen.models.configs.session;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.session.impl.ClientManagedSessionConfig;
import com.phonepe.verified.kaizen.models.data.SessionType;
import com.phonepe.verified.kaizen.models.data.SessionType.Names;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.CLIENT_MANAGED, value = ClientManagedSessionConfig.class)})
public abstract class SessionManagementConfig {

    @NotNull
    private SessionType type;

    @NotEmpty
    private String profileId;

    private boolean disabled;

    @NotEmpty
    private String sourceType;

    protected SessionManagementConfig(final SessionType type) {
        this.type = type;
    }

    public abstract <T, U> T accept(SessionManagementVisitor<T, U> visitor,
                                    U data);

}
