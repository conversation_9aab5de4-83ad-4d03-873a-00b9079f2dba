package com.phonepe.verified.kaizen.models.responses.workflow;

import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.responses.State;
import java.time.LocalDateTime;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LatestWorkflowResponse {

    private String entityId;

    private EntityType entityType;

    private String organization;

    private String namespace;

    private String workflowType;

    private String workflowVersion;

    private String workflowId;

    private State workflowState;

    /*
    Only present when workflowState is FAILURE/ABORTED/DISCARDED
*/
    @Nullable
    private ActionFailureErrorCode failureErrorCode;

    @Nullable
    private String failureReason;

    private String workflowTag;

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdatedAt;
}
