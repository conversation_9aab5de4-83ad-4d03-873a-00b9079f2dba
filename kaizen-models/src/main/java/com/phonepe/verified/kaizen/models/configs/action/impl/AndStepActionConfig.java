package com.phonepe.verified.kaizen.models.configs.action.impl;

import com.phonepe.verified.kaizen.models.configs.action.StepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.StepActionConfigType;
import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class AndStepActionConfig extends StepActionConfig {

    private static final long serialVersionUID = -3900279123249548617L;

    @Valid
    @NotNull
    private StepActionConfig left;

    @Valid
    @NotNull
    private StepActionConfig right;

    public AndStepActionConfig() {
        super(StepActionConfigType.AND);
    }

    @Builder
    public AndStepActionConfig(final StepActionConfig left,
                               final StepActionConfig right) {
        this();
        this.left = left;
        this.right = right;
    }

    @Override
    public <T, J> T accept(final StepActionVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
