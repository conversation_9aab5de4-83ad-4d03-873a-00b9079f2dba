package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.template.mapping.loader.SubmitLoader;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.ValidationRequestContext;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ValidationActionShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String validationUrl;

    private SubmitLoader submitLoader;

    private ValidationRequestContext validationRequestContext;

    public ValidationActionShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.VALIDATION_ACTION);
    }

    @Builder
    public ValidationActionShadowV2ResponseConfig(final String validationUrl,
                                                  final SubmitLoader submitLoader,
                                                  final ValidationRequestContext validationRequestContext) {
        this();
        this.submitLoader = submitLoader;
        this.validationUrl = validationUrl;
        this.validationRequestContext = validationRequestContext;
    }

    @Override
    public <T, U> T accept(ShadowV2ResponseVisitor<T, U> visitor,
                           U input) {
        return visitor.visit(this, input);
    }
}
