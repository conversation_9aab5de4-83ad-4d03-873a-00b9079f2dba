package com.phonepe.verified.kaizen.models.configs.retry.autoretry;

import io.dropwizard.util.Duration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CountLimitedWithFixedWaitAutoRetryConfig extends AutoRetryConfig {

    private int retryCount;

    private Duration waitTime;

    public CountLimitedWithFixedWaitAutoRetryConfig() {
        super(AutoRetryConfigType.COUNT_LIMITED_FIXED_WAIT);
    }

    @Override
    public <T, J> T accept(final AutoRetryConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
