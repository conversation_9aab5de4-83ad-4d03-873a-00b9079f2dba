package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl.otp;

import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SentinelOtpProviderConfig extends OtpProviderConfig {

    @NotEmpty
    private String otpProfile;

    public SentinelOtpProviderConfig() {
        super(OtpProviderType.SENTINEL);
    }

    @Override
    public <T, J> T accept(final OtpProviderConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
