package com.phonepe.verified.kaizen.models.configs.skip.workflows;

import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkipWorkflowConfig {

    @NotEmpty
    private String fieldValueHandlebarsTranslator;

    @NotEmpty
    private String expectedFieldValue;

    @Builder.Default
    private boolean invalidateAllActions = true;

    @Valid
    @NotEmpty
    private Map<TemplateType, UiResponseConfig> skipWorkflowResponseConfig;
}
