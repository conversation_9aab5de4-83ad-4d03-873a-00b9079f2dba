package com.phonepe.verified.kaizen.models.data.details;

import lombok.experimental.UtilityClass;

public enum DetailType {

    DOCUMENT_UPLOAD {
        @Override
        public <T> T accept(final DetailTypeVisitor<T> visitor) {
            return visitor.visitDocumentUpload();
        }
    },
    KEY_VALUE {
        @Override
        public <T> T accept(final DetailTypeVisitor<T> visitor) {
            return visitor.visitKeyValue();
        }
    },
    CONSENT {
        @Override
        public <T> T accept(final DetailTypeVisitor<T> visitor) {
            return visitor.visitConsent();
        }
    },
    OTP_DETAILS {
        @Override
        public <T> T accept(final DetailTypeVisitor<T> visitor) {
            return visitor.visitOtpDetails();
        }
    },
    ;

    public abstract <T> T accept(DetailType.DetailTypeVisitor<T> visitor);

    public interface DetailTypeVisitor<T> {

        T visitDocumentUpload();

        T visitKeyValue();

        T visitConsent();

        T visitOtpDetails();
    }

    @UtilityClass
    public static final class Names {

        public static final String DOCUMENT_UPLOAD = "DOCUMENT_UPLOAD";
        public static final String KEY_VALUE = "KEY_VALUE";
        public static final String CONSENT = "CONSENT";
        public static final String OTP_DETAILS = "OTP_DETAILS";
    }
}
