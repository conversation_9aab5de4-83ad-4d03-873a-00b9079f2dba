package com.phonepe.verified.kaizen.models.responses.details.details;

import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.details.Detail;
import com.phonepe.verified.kaizen.models.responses.details.DetailVisitor;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class KeyValueDetail extends Detail {

    private String key;

    private String value;

    public KeyValueDetail() {
        super(DetailType.KEY_VALUE);
    }

    @Builder
    public KeyValueDetail(final String actionId,
                          final State actionCompletionState,
                          final String actionMappingId,
                          final LocalDateTime createdAt,
                          final LocalDateTime lastUpdatedAt,
                          final String key,
                          final String value,
                          final String workflowId,
                          final String workflowStepId) {
        super(DetailType.KEY_VALUE, actionId, actionCompletionState, actionMappingId, createdAt, lastUpdatedAt,
                workflowId, workflowStepId);
        this.key = key;
        this.value = value;
    }

    @Override
    public <T> T accept(final DetailVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
