package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.UiResponseActionContext;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.Map;
import javax.validation.Valid;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class ConfirmationActionContext extends StepActionContext implements UiResponseActionContext {

    @Valid
    private Map<TemplateType, UiResponseConfig> uiResponseConfigMap;

    public ConfirmationActionContext() {
        super(ActionType.CONFIRMATION);
    }

    @Builder
    public ConfirmationActionContext(final Map<TemplateType, UiResponseConfig> uiResponseConfigMap) {
        this();
        this.uiResponseConfigMap = uiResponseConfigMap;
    }
}
