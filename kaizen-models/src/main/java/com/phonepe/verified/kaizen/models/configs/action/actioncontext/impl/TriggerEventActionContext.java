package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TriggerEventActionContext extends StepActionContext {

    @NotEmpty
    private String eventToTrigger;

    @NotEmpty
    private String actionMappingId;

    @NotEmpty
    private String profileStepMappingId;

    public TriggerEventActionContext() {
        super(ActionType.TRIGGER_EVENT_ACTION);
    }

    @Builder
    public TriggerEventActionContext(final String eventToTrigger,
                                     final String actionMappingId,
                                     final String profileStepMappingId) {
        this();
        this.eventToTrigger = eventToTrigger;
        this.actionMappingId = actionMappingId;
        this.profileStepMappingId = profileStepMappingId;
    }
}
