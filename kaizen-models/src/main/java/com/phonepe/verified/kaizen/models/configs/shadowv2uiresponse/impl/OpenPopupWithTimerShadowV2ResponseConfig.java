package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.data.action.popup.ImageDetail;
import com.phonepe.shadow.models.response.Action;
import com.phonepe.shadow.models.response.actions.IntentData;
import com.phonepe.shadow.models.response.actions.ValidationAction;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpenPopupWithTimerShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private IntentData intentData;

    private String timerLabel;

    private ImageDetail imageDetail;

    private Action sessionTimeoutAction;

    private ValidationAction statusCheckAction;

    public OpenPopupWithTimerShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.OPEN_POPUP_WITH_TIMER);
    }

    @Builder
    public OpenPopupWithTimerShadowV2ResponseConfig(final IntentData intentData,
                                                    final String timerLabel,
                                                    final ImageDetail imageDetail,
                                                    final Action sessionTimeoutAction,
                                                    final ValidationAction statusCheckAction) {
        this();
        this.intentData = intentData;
        this.timerLabel = timerLabel;
        this.imageDetail = imageDetail;
        this.sessionTimeoutAction = sessionTimeoutAction;
        this.statusCheckAction = statusCheckAction;
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}

