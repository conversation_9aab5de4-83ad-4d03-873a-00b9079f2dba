package com.phonepe.verified.kaizen.models.requests.document.prefill;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentPrefillRequest {

    @Valid
    @NotEmpty
    private List<DocumentSource> orderedRequiredDocumentSources;


    @AssertTrue(message = "Orders should be unique and document sources should be unique")
    private boolean isValid() {

        final Set<Integer> orderSet = orderedRequiredDocumentSources.stream()
                .map(DocumentSource::getOrder)
                .collect(Collectors.toSet());

        final Set<String> documentSourcesSet = orderedRequiredDocumentSources.stream()
                .map(DocumentSource::getKey)
                .collect(Collectors.toSet());

        return orderSet.size() == orderedRequiredDocumentSources.size()
                && documentSourcesSet.size() == orderedRequiredDocumentSources.size();
    }

}
