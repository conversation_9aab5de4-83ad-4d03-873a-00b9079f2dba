package com.phonepe.verified.kaizen.models.configs.tag;

import java.io.Serializable;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderedWorkflowTagRule implements Serializable {

    private static final long serialVersionUID = -5764222788442132706L;

    @Min(1)
    private int order;

    @NotEmpty
    private String tag;

    @NotEmpty
    private String evaluationRule;
}
