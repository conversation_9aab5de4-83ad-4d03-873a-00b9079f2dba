package com.phonepe.verified.kaizen.models.data;

import lombok.experimental.UtilityClass;

public enum NameMatchSource {
    SIGNZY {
        @Override
        public <T> T accept(final NameMatchSourceVisitor<T> visitor) {
            return visitor.visitSignzyNameMatch();
        }
    },
    PV {
        @Override
        public <T> T accept(final NameMatchSourceVisitor<T> visitor) {
            return visitor.visitPvNameMatch();
        }
    },
    DS_DEFAULT {
        @Override
        public <T> T accept(final NameMatchSourceVisitor<T> visitor) {
            return visitor.visitDsDefaultNameMatch();
        }
    },
    ;

    public abstract <T> T accept(NameMatchSourceVisitor<T> visitor);

    public interface NameMatchSourceVisitor<T> {

        T visitSignzyNameMatch();

        T visitPvNameMatch();

        T visitDsDefaultNameMatch();
    }

    @UtilityClass
    public class Names {

        public final String SIGNZY = "SIGNZY";
        public final String PV = "PV";
        public final String DS_DEFAULT = "DS_DEFAULT";
    }
}
