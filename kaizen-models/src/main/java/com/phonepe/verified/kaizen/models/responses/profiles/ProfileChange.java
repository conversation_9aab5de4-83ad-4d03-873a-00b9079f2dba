package com.phonepe.verified.kaizen.models.responses.profiles;

import com.phonepe.verified.kaizen.models.requests.profiles.Profile;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class ProfileChange {

    @Valid
    @NotNull
    private final Profile proposedProfile;

    @Nullable
    private final Profile previousProfile;

    @Valid
    @NotNull
    private final ProfileChangeMetadata profileChangeMetadata;
}
