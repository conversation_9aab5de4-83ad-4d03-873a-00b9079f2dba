package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.data.action.popup.ImageDetail;
import com.phonepe.shadow.models.Orientation;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.ButtonActionConfig;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpenGenericDialogActionShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String title;
    private String subtitle;
    private String iconUrl;
    private ImageDetail imageDetail;
    private boolean cancelable;
    private Orientation buttonStackType;
    private List<ButtonActionConfig> buttonActionConfigs;

    public OpenGenericDialogActionShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.OPEN_GENERIC_DIALOG);
    }

    @Builder
    public OpenGenericDialogActionShadowV2ResponseConfig(final String title,
                                                         final String subtitle,
                                                         final String iconUrl,
                                                         final ImageDetail imageDetail,
                                                         final boolean cancelable,
                                                         final Orientation buttonStackType,
                                                         final List<ButtonActionConfig> buttonActionConfigs) {
        this();
        this.title = title;
        this.subtitle = subtitle;
        this.iconUrl = iconUrl;
        this.imageDetail = imageDetail;
        this.cancelable = cancelable;
        this.buttonStackType = buttonStackType;
        this.buttonActionConfigs = buttonActionConfigs;
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
