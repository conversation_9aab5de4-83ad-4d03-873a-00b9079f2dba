package com.phonepe.verified.kaizen.models.responses.statemachines;

import com.phonepe.verified.kaizen.models.data.keys.StateMachineRegistryKey;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StateMachineCount {

    private int count;

    private Set<StateMachineRegistryKey> stateMachineRegistryKeys;

}
