package com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DoNotFailWorkflowPseudoSuccessActionFailureWorkflowBehaviourConfig extends
        PseudoSuccessActionFailureWorkflowBehaviourConfig {


    public DoNotFailWorkflowPseudoSuccessActionFailureWorkflowBehaviourConfig() {
        super(PseudoSuccessActionFailureWorkflowBehaviourConfigType.DO_NOT_FAIL_WORKFLOW);
    }

    @Override
    public <T, J> T accept(final PseudoSuccessActionFailureWorkflowBehaviourConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
