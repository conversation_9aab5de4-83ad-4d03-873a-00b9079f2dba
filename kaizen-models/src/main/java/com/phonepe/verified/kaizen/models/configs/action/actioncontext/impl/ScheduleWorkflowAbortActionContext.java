package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.data.ActionType;
import io.dropwizard.util.Duration;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ScheduleWorkflowAbortActionContext extends StepActionContext {

    private String abortReason;

    private Duration abortDuration;

    @Builder
    public ScheduleWorkflowAbortActionContext(final String abortReason,
                                              final Duration abortDuration) {
        this();
        this.abortReason = abortReason;
        this.abortDuration = abortDuration;
    }

    public ScheduleWorkflowAbortActionContext() {
        super(ActionType.SCHEDULE_WORKFLOW_ABORT);
    }
}
