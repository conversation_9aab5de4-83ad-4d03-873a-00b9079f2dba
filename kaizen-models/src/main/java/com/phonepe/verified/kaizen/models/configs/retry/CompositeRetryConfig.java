package com.phonepe.verified.kaizen.models.configs.retry;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompositeRetryConfig extends RetryConfig {

    private RetryConfig retryConfig;

    private RetryConfig opsBasedRetryConfig;

    public CompositeRetryConfig() {
        super(RetryConfigType.COMPOSITE);
    }

    @Override
    public <T, J> T accept(RetryConfigVisitor<T, J> visitor,
                           J data) {
        return visitor.visit(this, data);
    }
}
