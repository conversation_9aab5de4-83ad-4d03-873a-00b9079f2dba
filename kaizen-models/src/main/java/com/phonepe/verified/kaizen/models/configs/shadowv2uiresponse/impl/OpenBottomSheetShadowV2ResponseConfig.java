package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components.Info;
import java.util.List;
import javax.validation.Valid;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OpenBottomSheetShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String title;

    private List<Info> infoList;

    private String footer;

    private String leftButtonText;

    @Valid
    private ShadowV2ResponseConfig leftAction;

    private String rightButtonText;

    @Valid
    private ShadowV2ResponseConfig rightAction;

    private boolean cancelable;

    @Builder
    public OpenBottomSheetShadowV2ResponseConfig(final String title,
                                                 final List<Info> infoList,
                                                 final String footer,
                                                 final String leftButtonText,
                                                 final ShadowV2ResponseConfig leftAction,
                                                 final String rightButtonText,
                                                 final ShadowV2ResponseConfig rightAction,
                                                 final boolean cancelable) {
        this();
        this.title = title;
        this.infoList = infoList;
        this.footer = footer;
        this.leftButtonText = leftButtonText;
        this.leftAction = leftAction;
        this.rightButtonText = rightButtonText;
        this.rightAction = rightAction;
        this.cancelable = cancelable;
    }

    public OpenBottomSheetShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.OPEN_BOTTOM_SHEET);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
