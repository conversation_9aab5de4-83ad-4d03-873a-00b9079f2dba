package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.models.response.Action;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ShadowV2ActionShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private Action action;

    @Builder
    public ShadowV2ActionShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.SHADOW_V2_ACTION);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
