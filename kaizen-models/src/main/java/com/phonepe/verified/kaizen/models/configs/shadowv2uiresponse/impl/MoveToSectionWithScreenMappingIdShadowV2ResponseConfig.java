package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import java.util.Map;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MoveToSectionWithScreenMappingIdShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    @NotNull
    private String screenMappingId;

    @Valid
    @Nullable
    private ShadowV2ResponseConfig backButtonAction;

    private boolean checkExistingStack;

    private Map<String, Object> analyticsMetadata;

    @Builder
    public MoveToSectionWithScreenMappingIdShadowV2ResponseConfig(final String screenMappingId,
                                                                  @Nullable final ShadowV2ResponseConfig backButtonAction,
                                                                  final boolean checkExistingStack,
                                                                  final Map<String, Object> analyticsMetadata) {
        this();
        this.screenMappingId = screenMappingId;
        this.backButtonAction = backButtonAction;
        this.checkExistingStack = checkExistingStack;
        this.analyticsMetadata = analyticsMetadata;
    }

    public MoveToSectionWithScreenMappingIdShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.MOVE_TO_SECTION_WITH_SCREEN_MAPPING_ID);
    }

    @Override
    public <T, U> T accept(ShadowV2ResponseVisitor<T, U> visitor, U input) {
        return visitor.visit(this, input);
    }
}

