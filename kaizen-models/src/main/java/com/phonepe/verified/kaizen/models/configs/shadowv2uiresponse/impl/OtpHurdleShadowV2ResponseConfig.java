package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.page.field.OtpAutoReadType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OtpHurdleShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String title;

    private String subtitle;

    private boolean cancelable;

    private int otpCodeLength;

    private String otpRegex;

    private String autoReadRegex;

    private OtpAutoReadType autoReadType;

    private boolean autoVerify;

    private String resendOtpText;

    private String resendOtpUrl;

    private String verifyUrl;

    private String verifyButtonText;

    private String verifyFailureText;

    private String timerText;

    private long timerInMs;

    private boolean keyboardAllowedWhileTimer;

    public OtpHurdleShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.OTP_HURDLE);
    }

    @Builder
    public OtpHurdleShadowV2ResponseConfig(final String title,
                                           final String subtitle,
                                           final boolean cancelable,
                                           final int otpCodeLength,
                                           final String otpRegex,
                                           final String autoReadRegex,
                                           final OtpAutoReadType autoReadType,
                                           final boolean autoVerify,
                                           final String resendOtpText,
                                           final String resendOtpUrl,
                                           final String verifyUrl,
                                           final String verifyButtonText,
                                           final String verifyFailureText,
                                           final String timerText,
                                           final long timerInMs,
                                           final boolean keyboardAllowedWhileTimer) {
        this();
        this.title = title;
        this.subtitle = subtitle;
        this.cancelable = cancelable;
        this.otpCodeLength = otpCodeLength;
        this.otpRegex = otpRegex;
        this.autoReadRegex = autoReadRegex;
        this.autoReadType = autoReadType;
        this.autoVerify = autoVerify;
        this.resendOtpText = resendOtpText;
        this.resendOtpUrl = resendOtpUrl;
        this.verifyUrl = verifyUrl;
        this.verifyButtonText = verifyButtonText;
        this.verifyFailureText = verifyFailureText;
        this.timerText = timerText;
        this.timerInMs = timerInMs;
        this.keyboardAllowedWhileTimer = keyboardAllowedWhileTimer;
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
