package com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.impl;

import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionType;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.auto.WorkflowAutoSkipConfig;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Getter
@EqualsAndHashCode(callSuper = true)
public class ScheduleWorkflowAutoSkipActionConfig extends PostWorkflowCreationActionConfig {

    private final WorkflowAutoSkipConfig workflowAutoSkipConfig;

    @Builder
    @Jacksonized
    public ScheduleWorkflowAutoSkipActionConfig(final WorkflowAutoSkipConfig workflowAutoSkipConfig) {
        super(PostWorkflowCreationActionType.SCHEDULE_WORKFLOW_AUTO_SKIP);
        this.workflowAutoSkipConfig = workflowAutoSkipConfig;
    }

    @Override
    public <T, J> T accept(final PostWorkflowCreationActionConfigVisitor<T, J> visitor,
                           final J data) {

        return visitor.visit(this, data);
    }
}
