package com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.PseudoSuccessActionFailureWorkflowBehaviourConfigType.Names;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true, defaultImpl = AlwaysFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfig.class)
@JsonSubTypes({
        @JsonSubTypes.Type(name = Names.ALWAYS_FAIL_WORKFLOW, value = AlwaysFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfig.class),
        @JsonSubTypes.Type(name = Names.DO_NOT_FAIL_WORKFLOW_BASED_ON_ACTION_FAILURE_ERROR_CODE, value = DoNotFailWorkflowBasedOnActionFailureErrorCodePseudoSuccessActionFailureWorkflowBehaviourConfig.class),
        @JsonSubTypes.Type(name = Names.DO_NOT_FAIL_WORKFLOW, value = DoNotFailWorkflowPseudoSuccessActionFailureWorkflowBehaviourConfig.class)})
public abstract class PseudoSuccessActionFailureWorkflowBehaviourConfig {

    private PseudoSuccessActionFailureWorkflowBehaviourConfigType type;

    protected PseudoSuccessActionFailureWorkflowBehaviourConfig(final PseudoSuccessActionFailureWorkflowBehaviourConfigType type) {
        this.type = type;
    }

    public abstract <T, J> T accept(final PseudoSuccessActionFailureWorkflowBehaviourConfigVisitor<T, J> visitor,
                                    final J data);
}
