package com.phonepe.verified.kaizen.models.configs.retry.autoretry;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.retry.autoretry.AutoRetryConfigType.Names;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true, defaultImpl = CountLimitedAutoRetryConfig.class)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.COUNT_LIMITED, value = CountLimitedAutoRetryConfig.class),
        @JsonSubTypes.Type(name = Names.COUNT_LIMITED_FIXED_WAIT, value = CountLimitedWithFixedWaitAutoRetryConfig.class)})
public abstract class AutoRetryConfig {

    private AutoRetryConfigType type;

    public abstract <T, J> T accept(final AutoRetryConfigVisitor<T, J> visitor,
                                    final J data);

}
