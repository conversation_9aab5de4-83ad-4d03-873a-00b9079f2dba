package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.commons.HttpMethod;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class PersistKeyValuePairsFromApiCallActionContext extends StepActionContext {

    private static final long serialVersionUID = -9058961006139586722L;

    private String serviceName;

    private String urlPath;

    private HttpMethod method;

    private JsonNode payload;

    private Map<String, String> headers;

    private Map<String, String> keyValuePairs;

    public PersistKeyValuePairsFromApiCallActionContext() {
        super(ActionType.PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL);
    }

    @Builder
    public PersistKeyValuePairsFromApiCallActionContext(final String serviceName,
                                                        final String urlPath,
                                                        final HttpMethod method,
                                                        final JsonNode payload,
                                                        final Map<String, String> headers,
                                                        final Map<String, String> keyValuePairs) {
        this();
        this.serviceName = serviceName;
        this.urlPath = urlPath;
        this.method = method;
        this.payload = payload;
        this.headers = headers;
        this.keyValuePairs = keyValuePairs;
    }
}
