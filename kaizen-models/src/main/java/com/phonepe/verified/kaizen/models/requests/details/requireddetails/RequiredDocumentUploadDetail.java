package com.phonepe.verified.kaizen.models.requests.details.requireddetails;

import com.phonepe.verified.kaizen.models.data.DocumentType;
import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetail;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetailVisitor;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RequiredDocumentUploadDetail extends RequiredDetail {

    @NotEmpty
    private Set<@NotNull DocumentType> requiredDocumentTypes;

    public RequiredDocumentUploadDetail() {
        super(DetailType.DOCUMENT_UPLOAD);
    }

    @Builder
    public RequiredDocumentUploadDetail(final Set<DocumentType> requiredDocumentTypes) {
        this();
        this.requiredDocumentTypes = requiredDocumentTypes;
    }

    @Override
    public <T, V> T accept(final RequiredDetailVisitor<T, V> visitor,
                           final V data) {
        return visitor.visit(this, data);
    }
}
