package com.phonepe.verified.kaizen.models.configs.textsource.dynamic;

public enum DynamicTextSource {

    USER_PAYMENTS_CBS_NAME {
        @Override
        public <T, J> T accept(final DynamicTextSourceVisitor<T, J> visitor,
                               J data) {
            return visitor.visitUserPaymentsCbsName(data);
        }
    };

    public abstract <T, J> T accept(DynamicTextSourceVisitor<T, J> visitor,
                                    J data);

    public interface DynamicTextSourceVisitor<T, J> {

        T visitUserPaymentsCbsName(J data);
    }

}
