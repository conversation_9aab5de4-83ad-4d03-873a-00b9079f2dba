package com.phonepe.verified.kaizen.models.data.keys;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(staticName = "newInstance")
public class ProfileKey {

    @NonNull
    @NotEmpty
    private String organization;

    @NonNull
    @NotEmpty
    private String namespace;

    @NonNull
    @NotEmpty
    private String type;

    @NonNull
    @NotEmpty
    private String version;
}
