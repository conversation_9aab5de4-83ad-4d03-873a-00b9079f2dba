package com.phonepe.verified.kaizen.models.responses.details.details;

import com.phonepe.verified.kaizen.models.data.details.DetailType;
import com.phonepe.verified.kaizen.models.responses.State;
import com.phonepe.verified.kaizen.models.responses.details.Detail;
import com.phonepe.verified.kaizen.models.responses.details.DetailVisitor;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OtpDetails extends Detail {

    private String otpReferenceId;

    public OtpDetails() {
        super(DetailType.OTP_DETAILS);
    }

    @Builder
    public OtpDetails(final String actionId,
                      final State actionCompletionState,
                      final String actionMappingId,
                      final LocalDateTime createdAt,
                      final LocalDateTime lastUpdatedAt,
                      final String otpReferenceId,
                      final String workflowId,
                      final String workflowStepId) {
        super(DetailType.OTP_DETAILS, actionId, actionCompletionState, actionMappingId, createdAt, lastUpdatedAt,
                workflowId, workflowStepId);
        this.otpReferenceId = otpReferenceId;
    }

    @Override
    public <T> T accept(final DetailVisitor<T> visitor) {
        return visitor.visit(this);
    }

}
