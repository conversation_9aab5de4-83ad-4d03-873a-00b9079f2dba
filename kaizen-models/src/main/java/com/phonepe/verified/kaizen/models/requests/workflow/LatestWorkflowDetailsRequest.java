package com.phonepe.verified.kaizen.models.requests.workflow;

import com.phonepe.verified.kaizen.models.data.EntityType;
import com.phonepe.verified.kaizen.models.requests.details.RequiredDetail;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LatestWorkflowDetailsRequest {

    @NotEmpty
    private String entityId;

    @NotNull
    private EntityType entityType;

    @NotEmpty
    private String organization;

    @NotEmpty
    private String namespace;

    @NotEmpty
    private String type;

    @Valid
    private List<RequiredDetail> requiredDetails;
}
