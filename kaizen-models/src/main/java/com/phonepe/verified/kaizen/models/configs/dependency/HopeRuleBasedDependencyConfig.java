package com.phonepe.verified.kaizen.models.configs.dependency;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class HopeRuleBasedDependencyConfig extends DependencyConfig {

    @Valid
    @NotEmpty
    private List<OrderedActionMappingIdHopeRule> orderedActionMappingIdHopeRules;

    @NotEmpty
    private String defaultActionMappingId;

    public HopeRuleBasedDependencyConfig() {

        super(DependencyConfigType.HOPE_RULE_BASED_CONFIG);
    }

    @AssertTrue(message = "Orders should be unique")
    private boolean isValid() {

        final Set<Integer> orderSet = orderedActionMappingIdHopeRules.stream()
                .map(OrderedActionMappingIdHopeRule::getOrder)
                .collect(Collectors.toSet());

        return orderSet.size() == orderedActionMappingIdHopeRules.size();
    }

    @Override
    public <T, J> T accept(final DependencyConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
