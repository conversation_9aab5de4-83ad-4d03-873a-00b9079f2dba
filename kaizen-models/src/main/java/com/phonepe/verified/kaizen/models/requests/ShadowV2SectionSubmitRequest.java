package com.phonepe.verified.kaizen.models.requests;

import com.phonepe.shadow.data.sections.SectionInputData;
import com.phonepe.shadow.models.request.SectionInputSubmitRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ShadowV2SectionSubmitRequest extends SectionInputSubmitRequest {

    private long componentKitVersion;

    private String intent;

    @Builder(builderClassName = "ShadowV2SectionSubmitRequestBuilder", builderMethodName = "buildShadowV2SectionSubmitRequest")
    public ShadowV2SectionSubmitRequest(final String workflowId,
                                        final String userId,
                                        final SectionInputData sectionInputData,
                                        final long componentKitVersion,
                                        final String intent) {
        super(workflowId, userId, sectionInputData);
        this.componentKitVersion = componentKitVersion;
        this.intent = intent;
    }
}
