package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.impl;

import com.phonepe.shadow.page.field.Effect;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseConfig;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseType;
import com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.ShadowV2ResponseVisitor;
import java.util.List;
import javax.validation.Valid;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RichTextBottomSheetShadowV2ResponseConfig extends ShadowV2ResponseConfig {

    private String imageUrl;

    private String title;

    private String description;

    private String content;

    private List<Effect> effects;

    private String leftButtonText;

    @Valid
    private ShadowV2ResponseConfig leftAction;

    private String rightButtonText;

    @Valid
    private ShadowV2ResponseConfig rightAction;

    private boolean cancelable;

    @Builder
    public RichTextBottomSheetShadowV2ResponseConfig(final String imageUrl,
                                                     final String title,
                                                     final String description,
                                                     final String content,
                                                     final List<Effect> effects,
                                                     final String leftButtonText,
                                                     final ShadowV2ResponseConfig leftAction,
                                                     final String rightButtonText,
                                                     final ShadowV2ResponseConfig rightAction,
                                                     final boolean cancelable) {
        this();
        this.imageUrl = imageUrl;
        this.title = title;
        this.description = description;
        this.content = content;
        this.effects = effects;
        this.leftButtonText = leftButtonText;
        this.leftAction = leftAction;
        this.rightButtonText = rightButtonText;
        this.rightAction = rightAction;
        this.cancelable = cancelable;
    }


    public RichTextBottomSheetShadowV2ResponseConfig() {
        super(ShadowV2ResponseType.RICH_TEXT_BOTTOM_SHEET);
    }

    @Override
    public <T, U> T accept(final ShadowV2ResponseVisitor<T, U> visitor,
                           final U input) {
        return visitor.visit(this, input);
    }
}
