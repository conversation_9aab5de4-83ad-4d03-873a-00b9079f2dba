package com.phonepe.verified.kaizen.models.configs.action.actioncontext.impl;

import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.data.ActionType;
import java.util.Map;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class ApiCallActionContext extends StepActionContext {

    private static final long serialVersionUID = 5954188374916693859L;

    @Valid
    private Map<TemplateType, UiResponseConfig> apiCallActionResponseMap;

    public ApiCallActionContext() {
        super(ActionType.API_CALL_ACTION);
    }

    @Builder
    public ApiCallActionContext(final Map<TemplateType, UiResponseConfig> apiCallActionResponseMap) {
        this();
        this.apiCallActionResponseMap = apiCallActionResponseMap;
    }

    @AssertTrue(message = "apiCallActionResponseMap cannot be empty")
    private boolean isValid() {
        return Objects.nonNull(apiCallActionResponseMap) && !apiCallActionResponseMap.isEmpty();
    }
}
