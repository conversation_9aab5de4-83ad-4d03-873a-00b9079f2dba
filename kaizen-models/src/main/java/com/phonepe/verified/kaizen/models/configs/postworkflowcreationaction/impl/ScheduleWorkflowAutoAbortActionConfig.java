package com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.impl;

import com.phonepe.verified.kaizen.models.configs.abort.WorkflowAutoAbortConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfig;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postworkflowcreationaction.PostWorkflowCreationActionType;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Getter
@EqualsAndHashCode(callSuper = true)
public class ScheduleWorkflowAutoAbortActionConfig extends PostWorkflowCreationActionConfig {

    private static final long serialVersionUID = -6779139171077693207L;

    private final WorkflowAutoAbortConfig workflowAutoAbortConfig;

    @Builder
    @Jacksonized
    public ScheduleWorkflowAutoAbortActionConfig(final WorkflowAutoAbortConfig workflowAutoAbortConfig) {
        super(PostWorkflowCreationActionType.SCHEDULE_WORKFLOW_AUTO_ABORT);
        this.workflowAutoAbortConfig = workflowAutoAbortConfig;
    }

    @Override
    public <T, J> T accept(final PostWorkflowCreationActionConfigVisitor<T, J> visitor,
                           final J data) {

        return visitor.visit(this, data);
    }
}
