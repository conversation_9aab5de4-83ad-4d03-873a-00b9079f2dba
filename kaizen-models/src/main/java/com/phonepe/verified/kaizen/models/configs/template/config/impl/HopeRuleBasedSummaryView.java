package com.phonepe.verified.kaizen.models.configs.template.config.impl;

import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfigType;
import com.phonepe.verified.kaizen.models.configs.template.config.visitor.GetTemplateConfigVisitor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
public class HopeRuleBasedSummaryView extends GetTemplateConfig {

    private static final long serialVersionUID = 4720003592968816429L;

    private String evaluationRule;

    private String summaryViewScreenMappingId;

    @Builder
    public HopeRuleBasedSummaryView(final String evaluationRule,
                                    final String summaryViewScreenMappingId) {
        super(GetTemplateConfigType.HOPE_RULE_BASED_SUMMARY_VIEW);
        this.evaluationRule = evaluationRule;
        this.summaryViewScreenMappingId = summaryViewScreenMappingId;
    }

    protected HopeRuleBasedSummaryView() {
        super(GetTemplateConfigType.HOPE_RULE_BASED_SUMMARY_VIEW);
    }

    @Override
    public <T, J> T accept(final GetTemplateConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
