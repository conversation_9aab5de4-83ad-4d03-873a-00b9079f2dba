package com.phonepe.verified.kaizen.models.data;

import lombok.experimental.UtilityClass;

public enum ActionMetadataType {
    DOCUMENT_UPLOAD_WITH_METADATA {
        @Override
        public <T> T accept(final ActionMetadataTypeVisitor<T> visitor) {
            return visitor.visitDocumentUploadWithMetaData();
        }
    },
    DOCUMENT_UPLOAD {
        @Override
        public <T> T accept(final ActionMetadataTypeVisitor<T> visitor) {
            return visitor.visitDocumentUpload();
        }
    },
    KEY_VALUE {
        @Override
        public <T> T accept(final ActionMetadataTypeVisitor<T> visitor) {
            return visitor.visitKeyValue();
        }
    },
    CONSENT {
        @Override
        public <T> T accept(final ActionMetadataTypeVisitor<T> visitor) {
            return visitor.visitConsent();
        }
    },
    WAIT_FOR_CONDITION {
        @Override
        public <T> T accept(final ActionMetadataTypeVisitor<T> visitor) {
            return visitor.visitWaitForConditionAction();
        }
    },
    OTP_DETAILS {
        @Override
        public <T> T accept(final ActionMetadataTypeVisitor<T> visitor) {
            return visitor.visitOtpDetails();
        }
    },
    ;

    public abstract <T> T accept(ActionMetadataType.ActionMetadataTypeVisitor<T> visitor);

    public interface ActionMetadataTypeVisitor<T> {

        T visitDocumentUploadWithMetaData();
        T visitDocumentUpload();

        T visitKeyValue();

        T visitConsent();

        T visitWaitForConditionAction();

        T visitOtpDetails();
    }

    @UtilityClass
    public static final class Names {
        public static final String DOCUMENT_UPLOAD_WITH_METADATA = "DOCUMENT_UPLOAD_WITH_METADATA";
        public static final String DOCUMENT_UPLOAD = "DOCUMENT_UPLOAD";
        public static final String KEY_VALUE = "KEY_VALUE";
        public static final String CONSENT = "CONSENT";
        public static final String WAIT_FOR_CONDITION = "WAIT_FOR_CONDITION";
        public static final String OTP_DETAILS = "OTP_DETAILS";
    }
}
