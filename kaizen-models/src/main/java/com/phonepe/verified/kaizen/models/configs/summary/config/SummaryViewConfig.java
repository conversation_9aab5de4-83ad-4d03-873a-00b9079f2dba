package com.phonepe.verified.kaizen.models.configs.summary.config;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.summary.SummaryViewVersion;
import com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1.SummaryViewConfigV1;
import com.phonepe.verified.kaizen.models.configs.summary.config.visitor.SummaryViewConfigVisitor;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "version")
@JsonSubTypes({@JsonSubTypes.Type(value = SummaryViewConfigV1.class, name = SummaryViewVersion.Names.V1)})
public abstract class SummaryViewConfig implements Serializable {

    private static final long serialVersionUID = -4037594792322573403L;

    private final SummaryViewVersion version;

    protected SummaryViewConfig(final SummaryViewVersion version) {
        this.version = version;
    }

    public abstract <T, J> T accept(SummaryViewConfigVisitor<T, J> visitor,
                                    J data);
}
