package com.phonepe.verified.kaizen.models.configs.screen.impl;

import com.phonepe.verified.kaizen.models.configs.action.StepActionConfig;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfig;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenConfigType;
import com.phonepe.verified.kaizen.models.configs.screen.ProfileScreenVisitor;
import com.phonepe.verified.kaizen.models.configs.skip.workflows.SkipWorkflowConfig;
import com.phonepe.verified.kaizen.models.configs.skip.workflowsteps.SkipWorkflowStepConfig;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class StandardProfileScreenConfig extends ProfileScreenConfig {

    @NotEmpty
    private String screenMappingId;

    @Valid
    @NotNull
    private StepActionConfig stepActionConfig;

    @Valid
    @NotEmpty
    private Map<TemplateType, UiResponseConfig> successResponseConfig;

    @Valid
    private SkipWorkflowConfig skipWorkflowConfig;

    @Valid
    private SkipWorkflowStepConfig skipWorkflowStepConfig;

    public StandardProfileScreenConfig() {
        super(ProfileScreenConfigType.STANDARD);
    }

    @Builder
    public StandardProfileScreenConfig(final String screenMappingId,
                                       final StepActionConfig stepActionConfig,
                                       final Map<TemplateType, UiResponseConfig> successResponseConfig,
                                       final SkipWorkflowConfig skipWorkflowConfig,
                                       final SkipWorkflowStepConfig skipWorkflowStepConfig) {
        this();
        this.screenMappingId = screenMappingId;
        this.stepActionConfig = stepActionConfig;
        this.successResponseConfig = successResponseConfig;
        this.skipWorkflowConfig = skipWorkflowConfig;
        this.skipWorkflowStepConfig = skipWorkflowStepConfig;
    }

    @Override
    public <T> T accept(final ProfileScreenVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
