package com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig;

public interface PseudoSuccessActionFailureWorkflowBehaviourConfigVisitor<T, J> {

    T visit(DoNotFailWorkflowBasedOnActionFailureErrorCodePseudoSuccessActionFailureWorkflowBehaviourConfig doNotFailWorkflowBasedOnActionFailureErrorCodePseudoSuccessActionFailureWorkflowBehaviourConfig,
            J data);

    T visit(AlwaysFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfig alwaysFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfig,
            J data);

    T visit(DoNotFailWorkflowPseudoSuccessActionFailureWorkflowBehaviourConfig doNotFailWorkflowPseudoSuccessActionFailureWorkflowBehaviourConfig,
            J data);
}
