package com.phonepe.verified.kaizen.models.configs.template.config;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.template.config.GetTemplateConfigType.Names;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.AlwaysResumeGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.AlwaysSummaryViewGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.HopeRuleBasedSummaryView;
import com.phonepe.verified.kaizen.models.configs.template.config.impl.SummaryViewBasedOnFlagGetTemplateConfig;
import com.phonepe.verified.kaizen.models.configs.template.config.visitor.GetTemplateConfigVisitor;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(value = AlwaysResumeGetTemplateConfig.class, name = Names.ALWAYS_RESUME),
        @JsonSubTypes.Type(value = AlwaysSummaryViewGetTemplateConfig.class, name = Names.ALWAYS_SUMMARY_VIEW),
        @JsonSubTypes.Type(value = SummaryViewBasedOnFlagGetTemplateConfig.class, name = Names.SUMMARY_VIEW_BASED_ON_FLAG),
        @JsonSubTypes.Type(value = HopeRuleBasedSummaryView.class, name = Names.HOPE_RULE_BASED_SUMMARY_VIEW)})
public abstract class GetTemplateConfig implements Serializable {

    private static final long serialVersionUID = -378193434866718537L;

    private final GetTemplateConfigType type;

    protected GetTemplateConfig(final GetTemplateConfigType type) {
        this.type = type;
    }

    public abstract <T, J> T accept(GetTemplateConfigVisitor<T, J> visitor,
                                    J data);

}
