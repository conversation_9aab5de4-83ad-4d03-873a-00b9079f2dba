package com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig;

import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DoNotFailWorkflowBasedOnActionFailureErrorCodePseudoSuccessActionFailureWorkflowBehaviourConfig extends
        PseudoSuccessActionFailureWorkflowBehaviourConfig {

    private List<ActionFailureErrorCode> actionFailureErrorCodes;

    public DoNotFailWorkflowBasedOnActionFailureErrorCodePseudoSuccessActionFailureWorkflowBehaviourConfig() {
        super(PseudoSuccessActionFailureWorkflowBehaviourConfigType.DO_NOT_FAIL_WORKFLOW_BASED_ON_ACTION_FAILURE_ERROR_CODE);
    }

    @Override
    public <T, J> T accept(final PseudoSuccessActionFailureWorkflowBehaviourConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
