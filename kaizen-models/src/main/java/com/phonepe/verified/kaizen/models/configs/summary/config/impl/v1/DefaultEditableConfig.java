package com.phonepe.verified.kaizen.models.configs.summary.config.impl.v1;

import com.phonepe.verified.kaizen.models.configs.summary.config.EditableConfig;
import com.phonepe.verified.kaizen.models.configs.summary.config.EditableConfigType;
import com.phonepe.verified.kaizen.models.configs.summary.config.visitor.SummaryViewEditableConfigVisitor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DefaultEditableConfig extends EditableConfig {

    protected DefaultEditableConfig() {
        super(EditableConfigType.DEFAULT);
    }

    @Override
    public <T, J> T evaluate(SummaryViewEditableConfigVisitor<T, J> visitor,
                             J data) {
        return visitor.visitDefault(this, data);
    }
}
