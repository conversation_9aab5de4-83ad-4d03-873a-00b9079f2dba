package com.phonepe.verified.kaizen.models.configs.postcompletionaction.impl;

import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfig;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionConfigVisitor;
import com.phonepe.verified.kaizen.models.configs.postcompletionaction.PostCompletionActionType;
import com.phonepe.zeus.models.Farm;
import javax.annotation.Nullable;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@EqualsAndHashCode(callSuper = true)
public class ClientCallbackPostCompletionActionConfig extends PostCompletionActionConfig {

    private static final long serialVersionUID = 5471146485107088409L;
    @Nullable
    private String callbackService;
    @Nullable
    private String callbackServiceUrl;
    @Nullable
    private Farm farm;

    protected ClientCallbackPostCompletionActionConfig() {
        super(PostCompletionActionType.CLIENT_CALLBACK);
    }

    @Builder
    public ClientCallbackPostCompletionActionConfig(@Nullable final String callbackService,
                                                    @Nullable final String callbackServiceUrl,
                                                    @Nullable final Farm farm) {
        this();
        this.callbackService = callbackService;
        this.callbackServiceUrl = callbackServiceUrl;
        this.farm = farm;
    }

    @Override
    public <T, J> T accept(final PostCompletionActionConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
