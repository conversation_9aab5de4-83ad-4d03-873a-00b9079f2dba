package com.phonepe.verified.kaizen.models.configs.action.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.verified.kaizen.models.configs.action.StepActionConfig;
import com.phonepe.verified.kaizen.models.configs.action.StepActionConfigType;
import com.phonepe.verified.kaizen.models.configs.action.StepActionVisitor;
import com.phonepe.verified.kaizen.models.configs.action.actioncontext.StepActionContext;
import com.phonepe.verified.kaizen.models.configs.dependency.DependencyConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.AlwaysFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfig;
import com.phonepe.verified.kaizen.models.configs.dependency.pseudosuccessactionfailureworkflowbehaviourconfig.PseudoSuccessActionFailureWorkflowBehaviourConfig;
import com.phonepe.verified.kaizen.models.configs.retry.RetryConfig;
import com.phonepe.verified.kaizen.models.configs.retry.autoretry.AutoRetryConfig;
import com.phonepe.verified.kaizen.models.configs.template.TemplateType;
import com.phonepe.verified.kaizen.models.configs.template.UiResponseConfig;
import com.phonepe.verified.kaizen.models.configs.ttl.TtlConfig;
import com.phonepe.verified.kaizen.models.data.ActionFailureErrorCode;
import com.phonepe.verified.kaizen.models.data.ActionType;
import io.dropwizard.util.Strings;
import java.util.Map;
import java.util.Objects;
import javax.validation.Valid;
import javax.validation.constraints.AssertFalse;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S1948")
@EqualsAndHashCode(callSuper = true)
public class StandardStepActionConfig extends StepActionConfig {

    private static final long serialVersionUID = -1440854634705123101L;

    @NotNull
    private ActionType actionType;

    @NotEmpty
    private String stateMachineVersion;

    @NotEmpty
    private String actionMappingId;

    private JsonNode transitionContextTemplate;

    private String transitionContextTemplateAsString;

    @Valid
    @NotNull
    private RetryConfig retryConfig;

    @Valid
    private AutoRetryConfig autoRetryConfig;

    @Valid
    private StepActionContext stepActionContext;

    @Valid
    private TtlConfig ttlConfig;

    @Valid
    private DependencyConfig dependencyConfig;

    @Valid
    private Map<ActionFailureErrorCode, Map<TemplateType, UiResponseConfig>> failureResponseConfig;

    @Valid
    private PseudoSuccessActionFailureWorkflowBehaviourConfig pseudoSuccessActionFailureWorkflowBehaviourConfig = AlwaysFailWorkflowOnPseudoSuccessActionFailureWorkflowBehaviourConfig.INSTANCE;


    public StandardStepActionConfig() {
        super(StepActionConfigType.STANDARD);
    }

    @Builder
    public StandardStepActionConfig(final ActionType actionType,
                                    final String stateMachineVersion,
                                    final String actionMappingId,
                                    final JsonNode transitionContextTemplate,
                                    final String transitionContextTemplateAsString,
                                    final RetryConfig retryConfig,
                                    final Map<ActionFailureErrorCode, Map<TemplateType, UiResponseConfig>> failureResponseConfig,
                                    final StepActionContext stepActionContext,
                                    final TtlConfig ttlConfig,
                                    final DependencyConfig dependencyConfig,
                                    final AutoRetryConfig autoRetryConfig,
                                    final PseudoSuccessActionFailureWorkflowBehaviourConfig pseudoSuccessActionFailureWorkflowBehaviourConfig) {
        this();
        this.actionType = actionType;
        this.stateMachineVersion = stateMachineVersion;
        this.actionMappingId = actionMappingId;
        this.transitionContextTemplate = transitionContextTemplate;
        this.transitionContextTemplateAsString = transitionContextTemplateAsString;
        this.retryConfig = retryConfig;
        this.failureResponseConfig = failureResponseConfig;
        this.stepActionContext = stepActionContext;
        this.ttlConfig = ttlConfig;
        this.dependencyConfig = dependencyConfig;
        this.autoRetryConfig = autoRetryConfig;
        this.pseudoSuccessActionFailureWorkflowBehaviourConfig = pseudoSuccessActionFailureWorkflowBehaviourConfig;
    }

    @Override
    public <T, J> T accept(final StepActionVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }

    @AssertTrue(message = "Either actionContext is null or actionContext.actionType mismatched")
    private boolean isActionContextValid() {
        return !ActionType.CONTEXT_REQUIRED_ACTION_TYPES.contains(actionType) || (stepActionContext != null
                && actionType == stepActionContext.getType());
    }

    @AssertFalse(message = "Atleast one of transitionContextTemplate or transitionContextTemplateAsString should be present")
    private boolean isTransitionContextPresent() {
        return Strings.isNullOrEmpty(transitionContextTemplateAsString) && Objects.isNull(transitionContextTemplate);
    }
}
