package com.phonepe.verified.kaizen.models.requests.details;

import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredConsentDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredDocumentUploadDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredKeyValueDetail;
import com.phonepe.verified.kaizen.models.requests.details.requireddetails.RequiredOtpDetails;

public interface RequiredDetailVisitor<T, V> {

    T visit(RequiredKeyValueDetail requiredKeyValueDetail,
            V data);

    T visit(RequiredDocumentUploadDetail requiredDocumentUploadDetail,
            V data);

    T visit(RequiredConsentDetail requiredConsentDetail,
            V data);

    T visit(RequiredOtpDetails requiredOtpDetails,
            V data);
}
