package com.phonepe.verified.kaizen.models.configs.screen;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderedProfileScreen {

    @Min(1)
    private int order;

    @Valid
    @NotNull
    private ProfileScreenConfig profileScreenConfig;

}
