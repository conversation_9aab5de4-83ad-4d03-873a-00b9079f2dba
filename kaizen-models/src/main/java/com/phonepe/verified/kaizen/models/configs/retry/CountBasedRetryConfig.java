package com.phonepe.verified.kaizen.models.configs.retry;

import javax.validation.constraints.AssertTrue;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CountBasedRetryConfig extends RetryConfig {

    private boolean enabled;

    private int retryCount;

    public CountBasedRetryConfig() {
        super(RetryConfigType.COUNT_BASED);
    }

    @AssertTrue(message = "When retry is enabled, retryCount > 0")
    private boolean isValid() {
        return !enabled || retryCount > 0;
    }

    @Override
    public <T, J> T accept(final RetryConfigVisitor<T, J> visitor,
                           final J data) {
        return visitor.visit(this, data);
    }
}
