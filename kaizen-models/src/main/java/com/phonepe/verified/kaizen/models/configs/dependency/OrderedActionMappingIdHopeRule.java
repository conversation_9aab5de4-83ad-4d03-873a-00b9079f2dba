package com.phonepe.verified.kaizen.models.configs.dependency;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderedActionMappingIdHopeRule {

    @Min(1)
    private int order;

    @NotEmpty
    private String actionMappingId;

    @NotEmpty
    private String evaluationRule;  // Rule to be dependent on a particular actionMappingId and "not" negation of it.
}
