package com.phonepe.verified.kaizen.models.configs.documentsource;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.documentsource.DocumentSourceType.Names;
import com.phonepe.verified.kaizen.models.configs.documentsource.impl.WorkflowHandleBarsDocumentSourceConfig;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(name = Names.WORKFLOW_HANDLEBARS_DOCUMENT_SOURCE, value = WorkflowHandleBarsDocumentSourceConfig.class)})
public abstract class DocumentSourceConfig {

    @NotNull
    private DocumentSourceType type;

    public abstract <T, U> T accept(DocumentSourceTypeVisitor<T, U> visitor,
                                    U data);
}
