package com.phonepe.verified.kaizen.models.data;

import java.util.Set;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(staticName = "newInstance")
public class FetchDocumentsResponse {

    @NonNull
    @NotEmpty
    private Set<String> documentIds;

}
