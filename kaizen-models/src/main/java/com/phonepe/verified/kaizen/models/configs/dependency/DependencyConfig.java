package com.phonepe.verified.kaizen.models.configs.dependency;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.verified.kaizen.models.configs.dependency.DependencyConfigType.Names;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", visible = true, defaultImpl = StandardDependencyConfig.class)
@JsonSubTypes({@JsonSubTypes.Type(name = Names.STANDARD_CONFIG, value = StandardDependencyConfig.class),
        @JsonSubTypes.Type(name = Names.HOPE_RULE_BASED_CONFIG, value = HopeRuleBasedDependencyConfig.class)})
public abstract class DependencyConfig {

    private DependencyConfigType type;

    @Valid
    private FallbackConfig fallbackConfig;

    protected DependencyConfig(final DependencyConfigType type) {
        this.type = type;
    }

    public abstract <T, J> T accept(final DependencyConfigVisitor<T, J> visitor,
                                    final J data);
}
