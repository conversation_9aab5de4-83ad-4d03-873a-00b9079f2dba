package com.phonepe.verified.kaizen.models.requests.statemachines;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StateMachineTransition {

    @NotEmpty
    private String source;

    @NotEmpty
    private String target;

    @NotEmpty
    private String event;

    @NotEmpty
    private String actionKey;

}
