package com.phonepe.verified.kaizen.models.configs.shadowv2uiresponse.components;

import lombok.experimental.UtilityClass;

public enum MoveToSectionScreen {

    CURRENT {
        @Override
        public <T> T accept(final MoveToSectionScreenVisitor<T> visitor) {
            return visitor.visitCurrent();
        }
    },
    NEXT {
        @Override
        public <T> T accept(final MoveToSectionScreenVisitor<T> visitor) {
            return visitor.visitNext();
        }
    },
    ;

    public abstract <T> T accept(MoveToSectionScreenVisitor<T> visitor);

    public interface MoveToSectionScreenVisitor<T> {

        T visitCurrent();

        T visitNext();

    }

    @UtilityClass
    public static final class Names {

        public static final String CURRENT = "CURRENT";
        public static final String NEXT = "NEXT";
    }

}
