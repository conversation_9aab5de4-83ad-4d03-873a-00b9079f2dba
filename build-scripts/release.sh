#!/bin/bash

# Setting bash flag to terminate the process as soon as any command returns non success return code
set -e

# Setting bash flag to print all commands from the script while executing them
set -x

# Set remote URL for the repository
git remote set-url origin "**********************:${CI_PROJECT_PATH}.git"

# Setting Git pull strategy to FF only mode to avoid warning message from Git of missing strategy
git config pull.ff only

# Checkout main and develop branches
git checkout main && git reset --hard origin/main && git pull
git checkout develop && git reset --hard origin/develop && git pull

# Evaluate release version using Maven command
RELEASE_VERSION=`mvn -U help:evaluate -Dexpression=project.version -q -DforceStdout | sed -e "s/-SNAPSHOT//g"`

# Updating poms with release version so that release process can start
mvn versions:set -DnewVersion=${RELEASE_VERSION} -DgenerateBackupPoms=false

# Based on whether JIRA ID is available as First Argument
# Commit release version with JIRA ID or without JIRA ID
if [ -z "$1" ]
then
  git add . && git commit -m "[CI] Updating poms for ${RELEASE_VERSION} release"
else
  git add . && git commit -m "[$1] [CI] Updating poms for ${RELEASE_VERSION} release"
fi

# Merge release version to main branch
git checkout main
git merge develop --ff-only

# Perform the release
mvn clean deploy -U -Pdocker -DperformRelease=true

# Creating a release tag
# Release tag can be referred at any point of time in future
#   to check the code went live as a part of specific release
git tag -a ${RELEASE_VERSION} -m "[CI] Tagging release ${RELEASE_VERSION}"

# Checkout develop and update poms with next snapshot version
git checkout develop
mvn build-helper:parse-version versions:set -DnewVersion=\${parsedVersion.majorVersion}.\${parsedVersion.minorVersion}.\${parsedVersion.nextIncrementalVersion}-SNAPSHOT -DgenerateBackupPoms=false
SNAPSHOT_VERSION=`mvn -U help:evaluate -Dexpression=project.version -q -DforceStdout`

# Based on whether JIRA ID is available as First Argument
# Commit next snapshot version with JIRA ID or without JIRA ID
if [ -z "$1" ]
then
  git add . && git commit -m "[CI] Updating poms for ${SNAPSHOT_VERSION} development"
else
  git add . && git commit -m "[$1] [CI] Updating poms for ${SNAPSHOT_VERSION} development"
fi

# Push commits and tags created during release process to origin
git push origin develop main
git push --tags origin